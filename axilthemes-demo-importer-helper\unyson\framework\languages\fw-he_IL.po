msgid ""
msgstr ""
"Project-Id-Version: Unyson\n"
"POT-Creation-Date: 2017-09-14 23:00+0000\n"
"PO-Revision-Date: 2017-09-19 11:44+0000\n"
"Last-Translator: admin <<EMAIL>>\n"
"Language-Team: Hebrew\n"
"Language: he-IL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco - https://localise.biz/\n"
"X-Poedit-KeywordsList: _:1;gettext:1;dgettext:2;ngettext:1,2;dngettext:2,3;"
"__:1;_e:1;_c:1;_n:1,2;_n_noop:1,2;_nc:1,2;__ngettext:1,2;__ngettext_noop:1,2;"
"_x:1,2c;_ex:1,2c;_nx:1,2,4c;_nx_noop:1,2,3c;_n_js:1,2;_nx_js:1,2,3c;"
"esc_attr__:1;esc_html__:1;esc_attr_e:1;esc_html_e:1;esc_attr_x:1,2c;"
"esc_html_x:1,2c;comments_number_link:2,3;t:1;st:1;trans:1;transChoice:1,2\n"
"X-Poedit-Basepath: .\n"
"Plural-Forms: nplurals=2; plural=n != 1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ..\n"
"Report-Msgid-Bugs-To: "

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:143
#, php-format
msgid ""
"%s manifest has invalid \"github_update\" parameter. Please use "
"\"user/repo\" format."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:264
#, php-format
msgid "%s github repository \"%s\" does not have the \"%s\" release."
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:218
msgid "Date achieves Meta Keywords"
msgstr "תיארוך של Meta Keywords"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:219
msgid "Set date achieves meta keywords"
msgstr "הוסף תאריך ל meta keywords"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:83
msgid "After adding content the extension will automatically ping to:"
msgstr "לאחר שתוסיפו תכן התוסף ישלח פינג אוטומטי אל: "

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:63
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:53
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:56
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:122
msgid "Your comment is awaiting moderation."
msgstr "התגובה שלך מחכה לאישור מנהלים"

#: ../extensions/events/class-fw-extension-events.php:310
msgid "Multi Interval Event"
msgstr "אירוע מרובה משתנים"

#: ../extensions/backups/helpers.php:123 ../extensions/backups/helpers.php:145
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:363
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:431
#, php-format
msgid "Failed to create dir: %s"
msgstr "תקלה ביצירת תיקיה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:56
msgid "Destination dir is not empty"
msgstr "תיקיית היעד לא ריקה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:32
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:41
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:45
msgid "Source dir not specified"
msgstr "לא צוינה תקיית המקור"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:39
msgid "Invalid source dir"
msgstr "תקיית מקור לא תקינה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:46
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:25
msgid "Source dirs not specified"
msgstr "לא צוינה תקיית המקור"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:197
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:322
#, php-format
msgid "Failed to list dir: %s"
msgstr "תקלה ברשימת התיקיות"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:227
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:281
#, php-format
msgid "Failed to remove dir: %s"
msgstr "תקלה בהסרת תיקיה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:103
msgid "Cannot move zip in destination dir"
msgstr "לא מצליח להעביר קובץ ZIP לתיקית היעד"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:74
#, php-format
msgid "Source dir %s is empty"
msgstr "תיקיית המקור  % ריקה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:116
msgid "Failed to get dir chmod"
msgstr "לא מצליח לקבל CHMOD לתקיה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:123
msgid "Failed to create destination dir"
msgstr "תקלה בשחזור רשימת תיקיות"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:214
#, php-format
msgid "Failed to restore dir listing from: %s"
msgstr "תקלה בשחזור רשימת תיקיות"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:21
msgid "Dir not specified"
msgstr "תיקייה לא נבחרה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:23
msgid "Uploads dir not specified"
msgstr "לא נבחרה תיקייה להעלאת הקבצים"

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:119
msgid "Invalid destination dir"
msgstr "תיקיית יעד לא תקינה"

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:141
#, php-format
msgid "Args not specified for type: %s"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:4
msgid "Population Method Categories opt 1"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:10
msgid "Population Method Categories opt 2"
msgstr ""

#: ../core/components/backend.php:1440
msgid "Unknown collected group"
msgstr "קבוצה לא מוכרת"

#: ../core/components/backend.php:1826
#, php-format
msgid "Undefined container type: %s"
msgstr "סוג קונטיינר לא מוכר"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:351
msgid "Cannot remove the old extensions backup dir"
msgstr "תקלה בהסרה של תקיית גיבוי תוספים ישנה"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:357
msgid "Cannot create the extensions backup dir"
msgstr "לא ניתן להקים תיקיית גיבוי לתוספים"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2477
#, php-format
msgid "\"%s\" extension github repository \"%s\" has no releases."
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:155
msgid "Post categories, separated by coma"
msgstr ""
"קטגוריות פוסט, מופרדות על ידי coma\n"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:76
msgid "Send Method"
msgstr "שלח שיטה"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:77
msgid "Select the send form method"
msgstr ""
"בחר בשיטת שלח טופס\n"

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:9
msgid "Frontend Style Switcher"
msgstr "משנה עיצוב Frontend "

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:10
msgid "Enable frontend style switcher"
msgstr "הפעל משנה עיצוב frontend "

#: ../extensions/shortcodes/shortcodes/map/options.php:13
#: ../extensions/shortcodes/shortcodes/calendar/options.php:13
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:214
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:473
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:11
msgid "Population Method"
msgstr "שיטת אוכלוסין"

#: ../extensions/shortcodes/shortcodes/map/options.php:14
msgid "Select map population method (Ex: events, custom)"
msgstr ""
"בחר שיטת אוכלוסין למפה (לדוגמה: אירועים, התאמה אישית)\n"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:14
msgid "Select calendar population method (Ex: events, custom)"
msgstr ""
"בחר יומן לשיטת אוכלוסין (לדוגמה: אירועים, מותאם אישית)\n"

#: ../extensions/shortcodes/includes/class-fw-shortcode.php:194
#, php-format
msgid "No default view (views/view.php) found for shortcode: %s"
msgstr ""
"לא נמצאה תצוגת ברירת מחדל (views / view.php) עבור קוד קיצור: % s\n"

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:269
#, php-format
msgid "The class %s must extend from FW_Shortcode"
msgstr ""
"המחלקה % s חייבת להמשך מ- FW_Shortcode\n"

#: ../extensions/backups/helpers.php:44
msgid ""
"HTTP Loopback Connections are not enabled on this server. If you need to "
"contact your web host, tell them that when PHP tries to connect back to the "
"site at the URL `{url}` and it gets the error `{error}`. There may be a "
"problem with the server configuration (eg local DNS problems, mod_security, "
"etc) preventing connections from working properly."
msgstr ""
"חיבורי HTTP Loopback  אינם מופעלים בשרת זה. אם אתה צריך ליצור קשר עם אחסון "
"האינטרנט שלך, יש לציין בפניהם שכאשר PHP מנסה להתחבר חזרה אל האתר בכתובת 'url}"
"' והוא מקבל את השגיאה `{error}`. ייתכן שקיימת בעיה בתצורת השרת (לדוגמה, "
"בעיות DNS מקומיות, mod_security וכו ') המונעת מחיבורים לעבוד כראוי.\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:141
msgid "Cannot export CREATE TABLE sql"
msgstr "לא ניתן לייצא CREATE TABLE sql"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:126
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:135
msgid "Filesystem init failed"
msgstr "קובץ מערכת init נכשל"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:154
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:377
#, php-format
msgid "Failed to decode line %d from db file."
msgstr ""
"נכשל לפענח שורה % d מקובץ db.\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:186
#, php-format
msgid "Cannot read line %d from db file"
msgstr ""
"לא ניתן לקרוא שורה % d מקובץ db\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:396
#, php-format
msgid "Failed to drop tmp table %s"
msgstr ""
"נכשל בהורדת טבלת tmp % s\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:435
#, php-format
msgid "Failed to create tmp table %s"
msgstr ""
"נכשל ביצירת טבלת tmp % s\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:447
#, php-format
msgid "Tried to insert data in table that was not imported %s"
msgstr ""
"נעשה ניסיון להכניס נתונים בטבלה שלא יובאה % s\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:642
#, php-format
msgid "Invalid json type %s in db file"
msgstr ""
"סוג json לא חוקי בקובץ  db\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:70
msgid "Unhandled type"
msgstr "סוג לא מוכר"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:105
#, php-format
msgid "Zip open failed (code %d). Please try again"
msgstr ""
"פתיחת zip נכשלה (קוד% d). אנא נסה שוב\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:112
msgid "Zip extract failed"
msgstr ""
"מחיקת ה- Zip נכשלה\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:152
#, php-format
msgid "Request failed. Error code: %d"
msgstr ""
"בקשה נכשלה. קוד שגיאה: % d\n"

#: ../extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:332
msgid "The term translation does already exists.ACTION +++ "
msgstr ""
"התרגום כבר קיים. ACTION +++\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:231
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:234
#, php-format
msgid "%s updated."
msgstr ""
"% s עודכן.\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:235
#, php-format
msgid "%s restored to revision from %s"
msgstr ""
"% s שוחזר לגרסה מ -% s\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:237
#, php-format
msgid "%s published."
msgstr ""
"% s פורסם.\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:239
#, php-format
msgid "%s submitted."
msgstr ""
"% s נשלח.\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:240
#, php-format
msgid "%s scheduled for: %s."
msgstr ""
"% s מתוזמן עבור: % s.\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:242
#, php-format
msgid "%s draft updated."
msgstr ""
"% s טיוטה עודכנה.\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:342
msgid ""
"This slider was created correctly, but the code implementation was delete "
"from source code or blocked from filter.Delete this post or recovery slider "
"implementation"
msgstr ""
"הסלייר נוצר בצורה נכונה, אך יישום הקוד נמחק מקוד המקור או נחם על ידי מסנן. "
"מחק את הפוסט או את בצע רענון של הסליידר\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:351
msgid ""
"This slider was created correctly, but the multimedia_types from config.php "
"file was deleted, please set multimedia_types for this slider type."
msgstr ""
"הסליידר נוצר בצורה נכונה, אך הקובץ multimedia_types מתוך config.php נמחק, "
"אנא הגדר multimedia_types לסוג סליידר זה.\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:377
msgid "Choose a title for your slider only for internal use: Ex: \"Homepage\"."
msgstr ""
"בחר כותרת עבור הסליידר רק לצורך שימוש פנימי: לדוגמה: \"עמוד הבית\".\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:461
msgid ""
"You don't have slider extensions, please create at least one extension for "
"properly work"
msgstr ""
"אין לך תוספי סליידר, צור לפחות תוסף אחד על מנת לבצע את תהליך העבודה כראוי. \n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:474
msgid "Choose the population method for your slider"
msgstr ""
"בחר בשיטת האוכלוסין עבור הסליידר\n"

#: ../extensions/media/extensions/slider/manifest.php:6
msgid ""
"Adds the Sliders extension to your website. You'll be able to create "
"different built in jQuery sliders for your homepage and all the other "
"website pages."
msgstr ""
"מוסיף את תוסף הסליידרים לאתר שלך. תוכל ליצור מבני סליידרים jQuery שונים עבור "
"עמוד הבית שלך ועבור כל דפי האתר האחרים.\n"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:49
msgid ""
"No Sliders created yet. Please go to the {br}Sliders page and "
"{add_slider_link}."
msgstr ""
"עדיין לא נוצרו סליידרים. אנא עבור אל הדף {br} הסליידרים ו- {add_slider_link}."
"\n"

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:5
msgid ""
"Note that the type and population can't be changed later. You'll need to "
"create a new slider to have a different slider type or population method."
msgstr ""
"שים לב שלא ניתן יהיה לשנות את הסוג והאוכלוסייה במועד מאוחר יותר. יהיה עליך "
"ליצור סליידר חדש על מנת שיהיו סליידר או שיטת אוכלוסין אחרים."

#: ../extensions/media/extensions/population-method/class-fw-extension-population-method.php:36
#, php-format
msgid "Specified population method does not exists: %s"
msgstr ""
"שיטת האוכלוסין שצוינה אינה קיימת: % s\n"

#: ../extensions/media/extensions/population-method/class-fw-extension-population-method.php:55
#, php-format
msgid "Population method %s does not exist"
msgstr ""
"שיטת האוכלוסין % s אינה קיימת\n"

#: ../extensions/media/extensions/population-method/manifest.php:3
msgid "Population Methods"
msgstr "שיטות אוכלוסין"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:21
msgid "Automatically, fetch images from categories"
msgstr ""
"באופן אוטומטי, ייבא תמונות מקטגוריות\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:29
#, php-format
msgid "%s extension needs configured categories in post types"
msgstr ""
"סיומת % s זקוקה לקטגוריות מוגדרות בסוגי פוסט\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:34
msgid "Categories Population Method"
msgstr "קטגוריות שיטת אוכלוסין"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:4
msgid "Population Method - Categories"
msgstr "שיטת אוכלוסין - קטגוריות"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:21
msgid "Automatically, fetch images from tags"
msgstr ""
"באופן אוטומטי, ייבא תמונות מתגיות\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:29
#, php-format
msgid "%s extension needs configured tags in post types"
msgstr ""
"הארכת % s זקוקה לתגיות שהוגדרו בסוגי פוסט\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:34
msgid "Tags Population Method"
msgstr "תגיות שיטת אוכלוסין"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:4
msgid "Population Method - Tags"
msgstr "שיטת אוכלוסין - תגיות"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:21
msgid "Automatically, fetch images from posts"
msgstr ""
"באופן אוטומטי, ייבא תמונות מפוסטים\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:29
#, php-format
msgid "%s extension needs configured post categories in post types"
msgstr ""
"תוסף % s זקוק לקטגוריות פוסט מוגדרות בסוגי פוסט\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:34
msgid "Posts Population Method"
msgstr "פוסטים שיטת אוכלוסין"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:4
msgid "Population Method - Posts"
msgstr "שיטת אוכלוסין - פוסטים"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:22
msgid "Manually, I'll upload the images myself"
msgstr ""
"באופן ידני, אעלה את התמונות בעצמי\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:33
msgid "Click to edit / Drag to reorder"
msgstr ""
"לחץ כדי לערוך / לגרור על מנת לסדר מחדש\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:4
msgid "Population Method - Custom"
msgstr "שיטת אוכלוסין - התאמה אישית"

#: ../core/Fw.php:73
msgid "Framework requirements not met:"
msgstr ""
"דרישות המסגרת לא התקיימו:\n"

#: ../core/class-fw-manifest.php:308
#, php-format
msgid "Current WordPress version is %s, %s"
msgstr ""
"הגירסה הנוכחית של וורדפרס היא % s,% s\n"

#: ../core/class-fw-manifest.php:314
#, php-format
msgid "Current Framework version is %s, %s"
msgstr ""
"גרסת המסגרת הנוכחית היא % s,% s\n"

#: ../core/class-fw-manifest.php:323
#, php-format
msgid "Current version of the %s extension is %s, %s"
msgstr ""
"הגירסה הנוכחית של התוסף % s היא % s,% s\n"

#: ../core/class-fw-manifest.php:329
#, php-format
msgid "%s extension is required"
msgstr ""
"יש צורך בתוסף % s\n"

#: ../core/class-fw-manifest.php:334
#, php-format
msgid "%s extension is required (%s)"
msgstr ""
"יש צורך בתוסף % s (% s)\n"

#: ../core/components/backend.php:594
msgid ""
"If you like Unyson, {wp_review_link}, share on {facebook_share_link} or "
"{twitter_share_link}."
msgstr ""
"אם אתה אוהב את Unyson, {wp_review_link}, שתף ב- {facebook_share_link} או "
"{twitter_share_link}.\n"

#: ../core/components/backend.php:1779
#, php-format
msgid "Undefined option type: %s"
msgstr ""
"סוג אופציה לא מוגדר: % s\n"

#: ../core/components/theme.php:206
msgid "Theme requirements not met:"
msgstr ""
"דרישות התבנית לא התקיימו:\n"

#: ../core/components/extensions/manager/available-extensions.php:36
msgid "Population method"
msgstr "שיטת אוכלוסין"

#: ../core/extends/class-fw-option-type.php:283
#, php-format
msgid "Option type %s has no default value"
msgstr ""
"לסוג האפשרות % s אין ערך ברירת מחדל\n"

#: ../core/components/backend.php:588
msgid ""
"Unyson WordPress Framework is the fastest and easiest way to develop a "
"premium theme. I highly recommend it"
msgstr ""
"Unyson WordPress Framework היא הדרך המהירה והקלה ביותר לפתח תבנית פרימיום. "
"אני מאוד ממליץ על כך מאוד\n"

#: ../core/components/backend.php:1303
msgid "You have no permissions to change settings options"
msgstr ""
"אין לך הרשאות לשנות את אפשרויות ההגדרות\n"

#: ../core/components/extensions.php:447 ../core/components/extensions.php:525
#, php-format
msgid "Extension %s is invalid."
msgstr ""
"תוסף % s אינו חוקי.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1053
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1527
msgid "WP Filesystem is not initialized"
msgstr "קבצי מערכת WP אינם מאותחלים"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1079
#, php-format
msgid "Extension \"%s\" is already installed."
msgstr ""
"התוסף \"% s\" כבר מותקן.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1094
#, php-format
msgid "Extension \"%s\" is not available for install."
msgstr ""
"התוסף \"% s\" אינו זמין להתקנה.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1122
#, php-format
msgid "Parent extension \"%s\" not available."
msgstr ""
"תוסף האב \"% s\" אינו זמין.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1156
#, php-format
msgid "Downloading the \"%s\" extension..."
msgstr ""
"מוריד את התוסף \"% s\" ...\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1200
#, php-format
msgid "Installing the \"%s\" extension..."
msgstr ""
"מתקין את התוסף \"% s\" ...\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1238
#, php-format
msgid "The %s extension has been successfully installed."
msgstr ""
"התוסף % s הותקן בהצלחה.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1399
msgid "You are not allowed to delete extensions."
msgstr ""
"אינך רשאי למחוק תוספים.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1510
msgid "You have no permissions to uninstall extensions"
msgstr ""
"אין לך הרשאות להסרת תוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1578
#, php-format
msgid "Deleting the \"%s\" extension..."
msgstr ""
"מוחק את התוסף \"% s\" ...\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1590
#, php-format
msgid "Cannot delete the \"%s\" extension."
msgstr ""
"לא ניתן למחוק את התוסף \"% s\".\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1602
#, php-format
msgid "The %s extension has been successfully deleted."
msgstr ""
"התוסף% s נמחק בהצלחה.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1703
msgid "Extension not specified."
msgstr ""
"התוסף לא צוין.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1708
#, php-format
msgid "Extension \"%s\" is not installed."
msgstr ""
"התוסף \"% s\" אינו מותקן.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1776
#, php-format
msgid "Extension \"%s\" does not exist or is not active."
msgstr ""
"תוסף \"% s\" אינו קיים או שאינו פעיל.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1785
#, php-format
msgid "%s extension does not have settings."
msgstr ""
"לתוסף% s אין הגדרות.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2403
#, php-format
msgid "\"%s\" extension github source \"user_repo\" parameter is required"
msgstr "\"% s\" הפרמטר  \"user_repo\" הינו סיומת מקור github נדרשת"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2510
#, php-format
msgid "Cannot download the \"%s\" extension zip. (Response code: %d)"
msgstr ""
"לא ניתן  להוריד את הקובץ % s עם הסיומת zip. (קוד מותאם:% d)\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2517
#, php-format
msgid "Cannot download the \"%s\" extension zip. %s"
msgstr ""
"לא ניתן  להוריד את הקובץ % s עם הסיומת zip % s.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2525
#, php-format
msgid "Cannot download the \"%s\" extension zip."
msgstr ""
"לא ניתן  להוריד את הקובץ % s עם הסיומת zip.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2538
#, php-format
msgid "Cannot save the \"%s\" extension zip."
msgstr ""
"לא ניתן לשמור את הקובץ % s עם הסיומת zip.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2582
#, php-format
msgid "The unzipped \"%s\" extension directory not found."
msgstr ""
"ספריית התוספים הדחוסה \"% s\" לא נמצאה.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1806
msgid "Extension has no Install Instructions"
msgstr ""
"לתוסף אין הוראות התקנה\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1830
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2047
msgid "Invalid request method."
msgstr ""
"שיטת בקשה לא חוקית.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1914
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2132
#, php-format
msgid "Extension \"%s\" does not exist."
msgstr ""
"התוסף \"% s\" אינו קיים.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2109
msgid "You have no permissions to deactivate extensions"
msgstr ""
"אין לך הרשאות לביטול תוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2297
msgid "You are not allowed to save extensions settings."
msgstr ""
"אינך מורשה לשמור הגדרות של תוספים.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2368
#, php-format
msgid "Extension \"%s\" has no download sources."
msgstr ""
"לתוסף \"% s\" אין מקורות להורדה.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2392
#, php-format
msgid "Cannot create temporary directory: %s"
msgstr ""
"אין אפשרות ליצור ספריה זמנית: % s\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2454
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (%s)"
msgstr ""
"נכשל בגישה למאגר Github שוחררו \"%s\". \n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2557
#, php-format
msgid "Cannot remove the \"%s\" extension downloaded zip."
msgstr ""
"אין אפשרות להסיר את הסיומת \"% s\" של הקובץ שהורד.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2589
#, php-format
msgid "Unknown \"%s\" extension download source \"%s\""
msgstr ""
"לא ידוע \"% s\" מקור ההורדה \"% s\"\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2615
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2634
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2697
#, php-format
msgid "Cannot read directory \"%s\"."
msgstr ""
"אין אפשרות לקרוא את הספריה \"% s\".\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2649
#, php-format
msgid "Cannot delete \"%s\"."
msgstr ""
"לא ניתן למחוק את \"% s\".\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2660
#, php-format
msgid "Cannot create the \"%s\" directory."
msgstr ""
"אין אפשרות ליצור את הספריה % s.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2678
#, php-format
msgid "Cannot move \"%s\" to \"%s\"."
msgstr ""
"אין אפשרות להעביר את \"% s\" ל - \"% s\".\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3103
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3150
#, php-format
msgid "Cannot activate the %s extension because it is not installed. %s"
msgstr ""
"לא ניתן להפעיל את התוסף % s מכיוון שהוא אינו מותקן. % s\n"

#: ../core/components/extensions/manager/available-extensions.php:13
msgid ""
"Adds a sliders module to your website from where you'll be able to create "
"different built in jQuery sliders for your homepage and rest of the pages."
msgstr ""
"הוסף מודול סליידר לאתר האינטרנט שלך אשר בעזתו תוכל ליצור מבנים שונים של "
"סליידרים  ב-jQuery עבור עמוד הבית שלך ועבור שאר העמודים.\n"

#: ../core/components/extensions/manager/available-extensions.php:85
msgid ""
"Let's you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr ""
"תוכל לבנות בקלות אינספור עמודים בעזרת כלי ה-drag and drop של עורך העומדים "
"הויזואלי שמגיע עם הרבה קודי קיצור קיימים.\n"

#: ../core/components/extensions/manager/available-extensions.php:157
msgid ""
"This extension lets you set up daily, weekly or monthly backup schedule. You "
"can choose between a full backup or a data base only backup."
msgstr ""
"תוסף זה מאפשר לך להגדיר גיבוי יומי, שבועי או חודשי. ניתן לבחור בין גיבוי מלא "
"או גיבוי נתונים בלבד.\n"

#: ../core/components/extensions/manager/views/extension.php:174
#, php-format
msgid "Parent extension \"%s\" is disabled"
msgstr ""
"תוסף האב \"% s\" מושבת\n"

#: ../core/components/extensions/manager/views/extension.php:199
#, php-format
msgid "You need to update WordPress to %s: %s"
msgstr ""
"עליך לעדכן את WordPress ל-% s:% s\n"

#: ../core/components/extensions/manager/views/extension.php:205
#, php-format
msgid "WordPress needs to be updated to %s"
msgstr ""
"וורדפרס צריך להיות מעודכן ל% s\n"

#: ../core/components/extensions/manager/views/extension.php:215
#, php-format
msgid "Maximum supported WordPress version is %s"
msgstr ""
"גירסת WordPress הנתמכת המקסימלית היא % s\n"

#: ../core/components/extensions/manager/views/extension.php:230
#, php-format
msgid "You need to update %s to %s: %s"
msgstr ""
"עליך לעדכן את % s ל-% s:% s\n"

#: ../core/components/extensions/manager/views/extension.php:239
#, php-format
msgid "%s needs to be updated to %s"
msgstr ""
"גרסת % s הנתמכת המקסימלית היא % s\n"

#: ../core/components/extensions/manager/views/extension.php:250
#, php-format
msgid "Maximum supported %s version is %s"
msgstr ""
"גרסת % s הנתמכת המקסימלית היא % s\n"

#: ../core/components/extensions/manager/views/extension.php:268
#, php-format
msgid "You need to update the %s extension to %s: %s"
msgstr ""
"עליך לעדכן את התוסף % s ל-% s:% s\n"

#: ../core/components/extensions/manager/views/extension.php:277
#, php-format
msgid "The %s extension needs to be updated to %s"
msgstr ""
"התוסף % s צריך להיות מעודכן ל-% s\n"

#: ../core/components/extensions/manager/views/extension.php:288
#, php-format
msgid "Maximum supported %s extension version is %s"
msgstr ""
"גרסת התוסף % s המקסימלית הנתמכת היא % s\n"

#: ../core/components/extensions/manager/views/extension.php:303
#, php-format
msgid "The %s extension is disabled"
msgstr ""
"התוסף % s מושבת\n"

#: ../core/components/extensions/manager/views/extension.php:313
#, php-format
msgid "The %s extension is not installed: %s"
msgstr ""
"התוסף % s אינו מותקן: % s\n"

#: ../core/components/extensions/manager/views/extension.php:321
#, php-format
msgid "The %s extension is not installed"
msgstr ""
"התוסף % s אינו מותקן\n"

#: ../core/components/extensions/manager/views/extension-page-header.php:43
#, php-format
msgid "%s Install Instructions"
msgstr ""
"% s הוראות התקנה\n"

#: ../includes/option-types/multi-picker/class-fw-option-type-multi-picker.php:179
#, php-format
msgid "No 'picker' key set for multi-picker option: %s"
msgstr ""
"לא מוגדר מפתח 'בורר' עבור אפשרות בחירה מרובה: % s\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:119
msgid "Failed to close the zip after extract"
msgstr ""
"נכשל בסגירת קובץ ה- zip לאחר החילוץ\n"

#: ../manifest.php:5
msgid "Unyson"
msgstr "Unyson"

#: ../helpers/class-fw-wp-filesystem.php:43
msgid "Cannot connect to Filesystem directly"
msgstr "לא יכול להתחבר למערכת הקבצים ישירות"

#: ../helpers/class-fw-wp-filesystem.php:271
#, php-format
msgid "Cannot create directory \"%s\". It must be inside \"%s\""
msgstr ""
"אין אפשרות ליצור ספרייה \"% s\". זה חייב להיות בתוך \"% s\"\n"

#: ../helpers/class-fw-wp-filesystem.php:273
msgid "\" or \""
msgstr "\"או\""

#: ../helpers/class-fw-flash-messages.php:95
#, php-format
msgid "Invalid flash message type: %s"
msgstr ""
"סוג הודעת flash לא חוקית:% s\n"

#: ../helpers/class-fw-wp-list-table.php:185
msgid "No items found."
msgstr "לא נמצאו פריטים"

#: ../helpers/class-fw-wp-list-table.php:309
msgid "Bulk Actions"
msgstr ""
"פעולות גורפת\n"

#: ../helpers/class-fw-wp-list-table.php:319
msgid "Apply"
msgstr "החל"

#: ../helpers/class-fw-wp-list-table.php:403
msgid "All dates"
msgstr "כל התאריכים"

#: ../helpers/class-fw-wp-list-table.php:416
#, php-format
msgid "%1$s %2$d"
msgstr "%1$s %2$d"

#: ../helpers/class-fw-wp-list-table.php:432
msgid "List View"
msgstr ""
"תצוגת רשימה\n"

#: ../helpers/class-fw-wp-list-table.php:433
msgid "Excerpt View"
msgstr "תצוגת קטע"

#: ../helpers/class-fw-wp-list-table.php:459
#, php-format
msgid "%s pending"
msgstr "ממתין ל- %s"

#: ../helpers/class-fw-wp-list-table.php:714
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:33
msgid "Select All"
msgstr "בחר הכל"

#: ../helpers/general.php:1150
msgid "year"
msgstr "שנה"

#: ../helpers/general.php:1151
msgid "years"
msgstr "שנים"

#: ../helpers/general.php:1153
msgid "month"
msgstr "חודש"

#: ../helpers/general.php:1154
msgid "months"
msgstr "חודשים"

#: ../helpers/general.php:1156
msgid "week"
msgstr "שבוע"

#: ../helpers/general.php:1157
msgid "weeks"
msgstr "שבועות"

#: ../helpers/general.php:1159
msgid "day"
msgstr "יום"

#: ../helpers/general.php:1160
msgid "days"
msgstr "ימים"

#: ../helpers/general.php:1162
msgid "hour"
msgstr "שעה"

#: ../helpers/general.php:1163
msgid "hours"
msgstr "שעות"

#: ../helpers/general.php:1165
msgid "minute"
msgstr "דקה"

#: ../helpers/general.php:1166
msgid "minutes"
msgstr "דקות"

#: ../helpers/general.php:1168
msgid "second"
msgstr "שניה"

#: ../helpers/general.php:1169
msgid "seconds"
msgstr "שניות"

#: ../helpers/general.php:1558
msgid "Maximum stack depth exceeded"
msgstr "סוף הקיבולת של המחסנית"

#: ../helpers/general.php:1561
msgid "Underflow or the modes mismatch"
msgstr ""
"תת-זרימה או אי-התאמה בין המצבים\n"

#: ../helpers/general.php:1564
msgid "Unexpected control character found"
msgstr ""
"נמצא תו בקרה לא צפוי\n"

#: ../helpers/general.php:1567
msgid "Syntax error, malformed JSON"
msgstr ""
"שגיאת תחביר, JSON פגום\n"

#: ../helpers/general.php:1570
msgid "Malformed UTF-8 characters, possibly incorrectly encoded"
msgstr ""
"תווי UTF-8 פגומים, ייתכן ומקודדים באופן שגוי\n"

#: ../helpers/general.php:1573
#: ../extensions/backups/class-fw-extension-backups.php:550
msgid "Unknown error"
msgstr "תקלה לא ידועה"

#: ../helpers/class-fw-form.php:80
#, php-format
msgid "Form with id \"%s\" was already defined"
msgstr ""
"טופס עם מזהה \"% s\" כבר הוגדר\n"

#: ../helpers/class-fw-form.php:168
msgid "Nonce verification failed"
msgstr ""
"האימות נכשל\n"

#: ../helpers/class-fw-form.php:331
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/views/form.php:13
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:66
msgid "Submit"
msgstr "שלח"

#: ../extensions/update/class-fw-extension-update.php:285
#: ../extensions/update/class-fw-extension-update.php:602
msgid "Cannot delete: "
msgstr "לא יכול למחוק"

#: ../extensions/update/class-fw-extension-update.php:292
msgid "Cannot create: "
msgstr "לא יכול לצור:"

#: ../extensions/update/class-fw-extension-update.php:370
msgid "Cannot remove old temporary directory: "
msgstr "לא יכול להסיר תיקייה ישנה זמנית:"

#: ../extensions/update/class-fw-extension-update.php:376
#: ../extensions/update/class-fw-extension-update.php:572
msgid "Cannot create directory: "
msgstr "לא יכול לצור מחיצה"

#: ../extensions/update/class-fw-extension-update.php:381
#, php-format
msgid "Downloading the %s..."
msgstr ""
"מוריד את% s ...\n"

#: ../extensions/update/class-fw-extension-update.php:386
#, php-format
msgid "Cannot download the %s."
msgstr ""
"לא ניתן להוריד את% s.\n"

#: ../extensions/update/class-fw-extension-update.php:396
#, php-format
msgid "Installing the %s..."
msgstr ""
"מתקין את% s ...\n"

#: ../extensions/update/class-fw-extension-update.php:402
#: ../extensions/update/class-fw-extension-update.php:431
#: ../extensions/update/class-fw-extension-update.php:531
#: ../extensions/update/class-fw-extension-update.php:552
#: ../extensions/update/class-fw-extension-update.php:582
msgid "Cannot access directory: "
msgstr "לא יכול לגשת לתיקייה"

#: ../extensions/update/class-fw-extension-update.php:421
msgid "Cannot remove: "
msgstr "לא יכול להסיר"

#: ../extensions/update/class-fw-extension-update.php:456
#: ../extensions/update/class-fw-extension-update.php:522
#: ../extensions/update/class-fw-extension-update.php:628
#: ../extensions/update/class-fw-extension-update.php:652
#, php-format
msgid "Cannot move \"%s\" to \"%s\""
msgstr ""
"לא ניתן להעביר את \"% s\" ל \"% s\"\n"

#: ../extensions/update/class-fw-extension-update.php:472
#, php-format
msgid "Cannot merge \"%s\" with \"%s\""
msgstr ""
"אין אפשרות למזג את \"% s\" עם \"% s\"\n"

#: ../extensions/update/class-fw-extension-update.php:485
#, php-format
msgid "The %s has been successfully updated."
msgstr ""
"% S עודכן בהצלחה.\n"

#: ../extensions/update/class-fw-extension-update.php:492
#, php-format
msgid "Cannot remove temporary directory \"%s\"."
msgstr ""
"אין אפשרות להסיר את הספרייה הזמנית \"% s\".\n"

#: ../extensions/update/class-fw-extension-update.php:672
#: ../extensions/update/class-fw-extension-update.php:740
#: ../extensions/update/class-fw-extension-update.php:808
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:930
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1427
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1837
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2054
msgid "Invalid nonce."
msgstr "לא חוקי"

#: ../extensions/update/class-fw-extension-update.php:683
msgid "Framework Update"
msgstr "מסגרת עודכנה"

#: ../extensions/update/class-fw-extension-update.php:699
msgid "Failed to get framework latest version."
msgstr ""
"נכשל בהשגת הגרסה האחרונה של המסגרת.\n"

#: ../extensions/update/class-fw-extension-update.php:716
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:360
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:374
#: ../extensions/update/views/updates-list.php:10
#: ../core/class-fw-manifest.php:353
msgid "Framework"
msgstr "מסגרת"

#: ../extensions/update/class-fw-extension-update.php:751
msgid "Theme Update"
msgstr "עדכון תבנית"

#: ../extensions/update/class-fw-extension-update.php:767
msgid "Failed to get theme latest version."
msgstr ""
"ניסיון קבלת הגרסה האחרונה של התבנית נכשל.\n"

#: ../extensions/update/class-fw-extension-update.php:784
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:393
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:407
msgid "Theme"
msgstr "תבנית"

#: ../extensions/update/class-fw-extension-update.php:817
msgid "Please check the extensions you want to update."
msgstr ""
"אנא בדוק את התוספים שברצונך לעדכן.\n"

#: ../extensions/update/class-fw-extension-update.php:841
msgid "Extensions Update"
msgstr ""
"עדכוני הרחבות\n"

#: ../extensions/update/class-fw-extension-update.php:879
msgid "No extensions updates found."
msgstr "לא נמצאו עדכוני הרחבות"

#: ../extensions/update/class-fw-extension-update.php:886
#, php-format
msgid "Extension \"%s\" does not exist or is disabled."
msgstr ""
"התוסף \"% s\" אינו קיים או מושבת.\n"

#: ../extensions/update/class-fw-extension-update.php:893
#, php-format
msgid "No update found for the \"%s\" extension."
msgstr ""
"לא נמצא עדכון עבור התוסף \"% s\".\n"

#: ../extensions/update/class-fw-extension-update.php:915
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:426
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:440
#, php-format
msgid "%s extension"
msgstr ""
"תוסף% s\n"

#: ../extensions/update/manifest.php:5
#: ../extensions/builder/includes/option-types/builder/includes/fullscreen.php:82
msgid "Update"
msgstr "עדכון"

#: ../extensions/update/manifest.php:6
msgid "Keep you framework, extensions and theme up to date."
msgstr ""
"שמור  מסגרת, הרחבות ותבנית מעודכנים.\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:91
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:232
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2439
msgid "Github error:"
msgstr "Github שגוי"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:100
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:241
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2446
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (Response code: %d)"
msgstr ""
"נכשל בגישה למאגר \"% s\" של מאגר Github. (קוד תגובה:% d)\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:108
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:249
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2462
#, php-format
msgid "Failed to access Github repository \"%s\" releases."
msgstr ""
"נכשל בגישה למאגר \"% s\" של מאגר Github.\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:122
#, php-format
msgid "No releases found in repository \"%s\"."
msgstr ""
"לא נמצאו גרסאות במאגר \"% s\".\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:171
#, php-format
msgid "Failed to fetch %s latest version from github \"%s\"."
msgstr ""
"אחזור% s לגרסה האחרונה של github \"% s\".\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:284
#, php-format
msgid "Cannot download %s zip."
msgstr ""
"לא ניתן להוריד%s זיפ.\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:297
#, php-format
msgid "Cannot save %s zip."
msgstr ""
"לא ניתן לשמור%s זיפ.\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:316
#, php-format
msgid "Cannot remove %s zip."
msgstr ""
"לא ניתן להסיר%s זיפ.\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:325
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2566
msgid "Cannot access the unzipped directory files."
msgstr ""
"אין אפשרות לגשת לקבצי הספריות שנפתחו מכיווץ.\n"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:341
#, php-format
msgid "The unzipped %s directory not found."
msgstr "ספריית  %s שהוצאה מכיווץ לא נמצאה"

#: ../extensions/update/views/updates-list.php:12
#, php-format
msgid "You have the latest version of %s."
msgstr ""
"יש לך את הגירסה האחרונה של% s.\n"

#: ../extensions/update/views/updates-list.php:25
msgid "Update Framework"
msgstr "עדכן מסגרת"

#: ../extensions/update/views/updates-list.php:37
msgid "Your theme is up to date."
msgstr "התבנית שלך מעודכנת"

#: ../extensions/update/views/updates-list.php:50
msgid "Update Theme"
msgstr "עדכן את האתר"

#: ../extensions/update/views/updates-list.php:60
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:844
#, php-format
msgid "%s Extensions"
msgstr ""
"% s תוספים\n"

#: ../extensions/update/views/updates-list.php:62
#, php-format
msgid "You have the latest version of %s Extensions."
msgstr ""
"יש לך את הגירסה האחרונה של התוספים % s.\n"

#: ../extensions/update/views/updates-list.php:80
#: ../extensions/update/views/updates-list.php:95
#: ../extensions/update/views/updates-list.php:100
msgid "Update Extensions"
msgstr "עדכן הרחבות"

#: ../extensions/update/views/updates-list.php:99
msgid "New extensions updates available."
msgstr ""
"עדכוני תוספים חדשים זמינים.\n"

#: ../extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:16
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:14
#: ../extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:16
msgid "Go to updates page"
msgstr "גש אל עמוד עדכונים"

#: ../extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:19
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:17
#: ../extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:19
msgid "Return to Updates page"
msgstr ""
"חזור לעמוד עדכונים\n"

#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:84
#, php-format
msgid "You have version %s installed. Update to %s."
msgstr ""
"יש לך גרסה % s מותקנת. עד % s.\n"

#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:126
msgid "No Extensions for update."
msgstr "אין תוספים לעדכון."

#: ../extensions/portfolio/manifest.php:7
#: ../extensions/portfolio/class-fw-extension-portfolio.php:260
#: ../core/components/extensions/manager/available-extensions.php:72
msgid "Portfolio"
msgstr "תיק עבודות"

#: ../extensions/portfolio/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:73
msgid ""
"This extension will add a fully fledged portfolio module that will let you "
"display your projects using the built in portfolio pages."
msgstr ""
"תוסף זה יוסיף מודול פורטפוליו מלא אשר יאפשר לך להציג את הפרויקטים שלך "
"באמצעות עמודי תיקי עבודות מובנים.\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:115
#: ../extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Project"
msgstr "פרוייקט"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:116
msgid "Projects"
msgstr "פרוייקטים"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:123
#: ../extensions/learning/class-fw-extension-learning.php:63
#: ../extensions/learning/class-fw-extension-learning.php:127
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:57
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:58
#: ../extensions/events/class-fw-extension-events.php:76
#: ../extensions/media/extensions/slider/posts.php:8
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:32
msgid "Add New"
msgstr "הוסף חדש"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:124
#: ../extensions/portfolio/class-fw-extension-portfolio.php:198
#: ../extensions/learning/class-fw-extension-learning.php:64
#: ../extensions/learning/class-fw-extension-learning.php:128
#: ../extensions/events/class-fw-extension-events.php:77
#: ../extensions/events/class-fw-extension-events.php:131
#, php-format
msgid "Add New %s"
msgstr ""
"הוסף% s חדש\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:125
#: ../extensions/learning/class-fw-extension-learning.php:129
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:89
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:76
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:88
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:86
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:68
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:34
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:50
#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:110
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:45
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:37
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:73
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:81
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:57
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:50
#: ../extensions/events/class-fw-extension-events.php:78
#: ../includes/option-types/popup/class-fw-option-type-popup.php:158
#: ../includes/option-types/upload/class-fw-option-type-upload.php:151
#: ../includes/option-types/upload/class-fw-option-type-upload.php:177
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:161
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:187
msgid "Edit"
msgstr "ערוך"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:126
#: ../extensions/portfolio/class-fw-extension-portfolio.php:196
#: ../extensions/learning/class-fw-extension-learning.php:65
#: ../extensions/learning/class-fw-extension-learning.php:66
#: ../extensions/learning/class-fw-extension-learning.php:130
#: ../extensions/learning/class-fw-extension-learning.php:192
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:59
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:60
#: ../extensions/events/class-fw-extension-events.php:79
#: ../extensions/events/class-fw-extension-events.php:129
#, php-format
msgid "Edit %s"
msgstr ""
"ערוך% s\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:127
#: ../extensions/learning/class-fw-extension-learning.php:67
#: ../extensions/learning/class-fw-extension-learning.php:131
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:61
#: ../extensions/events/class-fw-extension-events.php:80
#, php-format
msgid "New %s"
msgstr ""
"% S חדש\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:128
#: ../extensions/portfolio/class-fw-extension-portfolio.php:193
#: ../extensions/learning/class-fw-extension-learning.php:68
#: ../extensions/learning/class-fw-extension-learning.php:132
#: ../extensions/learning/class-fw-extension-learning.php:189
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:62
#: ../extensions/events/class-fw-extension-events.php:81
#: ../extensions/events/class-fw-extension-events.php:126
#, php-format
msgid "All %s"
msgstr ""
"כל % s\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:129
#: ../extensions/portfolio/class-fw-extension-portfolio.php:130
#: ../extensions/learning/class-fw-extension-learning.php:69
#: ../extensions/learning/class-fw-extension-learning.php:70
#: ../extensions/learning/class-fw-extension-learning.php:133
#: ../extensions/learning/class-fw-extension-learning.php:134
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:63
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:64
#: ../extensions/events/class-fw-extension-events.php:82
#: ../extensions/events/class-fw-extension-events.php:83
#, php-format
msgid "View %s"
msgstr ""
"הצג % s\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:131
#: ../extensions/portfolio/class-fw-extension-portfolio.php:192
#: ../extensions/learning/class-fw-extension-learning.php:71
#: ../extensions/learning/class-fw-extension-learning.php:135
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:65
#: ../extensions/events/class-fw-extension-events.php:84
#: ../extensions/events/class-fw-extension-events.php:125
#, php-format
msgid "Search %s"
msgstr ""
"חפש % s\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:132
#: ../extensions/learning/class-fw-extension-learning.php:72
#: ../extensions/learning/class-fw-extension-learning.php:136
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:66
#: ../extensions/events/class-fw-extension-events.php:85
#, php-format
msgid "No %s Found"
msgstr ""
"לא נמצא ו% s\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:133
#: ../extensions/learning/class-fw-extension-learning.php:73
#: ../extensions/learning/class-fw-extension-learning.php:137
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:67
#: ../extensions/events/class-fw-extension-events.php:86
#, php-format
msgid "No %s Found In Trash"
msgstr ""
"% S לא נמצאו באשפה\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:136
msgid "Create a portfolio item"
msgstr "צור פריט בתיק עבודות"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:183
#: ../extensions/events/class-fw-extension-events.php:116
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:268
msgid "Category"
msgstr "קטגוריה"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:184
#: ../extensions/events/class-fw-extension-events.php:117
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:168
msgid "Categories"
msgstr "קטגוריות"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:194
#: ../extensions/learning/class-fw-extension-learning.php:190
#: ../extensions/events/class-fw-extension-events.php:127
#, php-format
msgid "Parent %s"
msgstr ""
"הורה % s\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:195
#: ../extensions/learning/class-fw-extension-learning.php:191
#: ../extensions/events/class-fw-extension-events.php:128
#, php-format
msgid "Parent %s:"
msgstr ""
"הורה % s:\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:197
#: ../extensions/learning/class-fw-extension-learning.php:193
#: ../extensions/events/class-fw-extension-events.php:130
#: ../core/components/extensions/manager/views/extension.php:234
#: ../core/components/extensions/manager/views/extension.php:272
#, php-format
msgid "Update %s"
msgstr ""
"עדכן % s\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:199
#: ../extensions/learning/class-fw-extension-learning.php:195
#: ../extensions/events/class-fw-extension-events.php:132
#, php-format
msgid "New %s Name"
msgstr ""
"שם % s חדש\n"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:200
#: ../extensions/learning/class-fw-extension-learning.php:196
#: ../extensions/events/class-fw-extension-events.php:133
#, php-format
msgid "%s"
msgstr "%s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Gallery"
msgstr "גלריה"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:240
msgid "Set project gallery"
msgstr "הגדר גלריית פרוייקט"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:241
msgid "Edit project gallery"
msgstr "ערוך גלריה בפרוייקט"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:271
msgid "Project Cover Image"
msgstr "תמונת נושא לפרוייקט"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:287
msgid "Edit this item"
msgstr "ערוך פריט זה"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:348
#: ../extensions/portfolio/class-fw-extension-portfolio.php:361
#: ../extensions/learning/class-fw-extension-learning.php:320
#: ../extensions/learning/class-fw-extension-learning.php:333
msgid "View all categories"
msgstr "צפה בכל הקטגוריות"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:381
msgid "Cover Image"
msgstr "תמונת נושא"

#: ../extensions/seo/settings-options.php:17
#: ../extensions/social/settings-options.php:11
msgid "General"
msgstr "כללי"

#: ../extensions/seo/settings-options.php:21
#: ../extensions/social/settings-options.php:15
msgid "General Settings"
msgstr "הגדרות כלליות"

#: ../extensions/seo/class-fw-extension-seo.php:89
msgid "Site name"
msgstr "שם אתר"

#: ../extensions/seo/class-fw-extension-seo.php:95
msgid "Site description"
msgstr "תיאור אתר"

#: ../extensions/seo/class-fw-extension-seo.php:101
msgid "Current time"
msgstr "זמן נוכחי"

#: ../extensions/seo/class-fw-extension-seo.php:107
msgid "Current date"
msgstr "תאריך נוכחי"

#: ../extensions/seo/class-fw-extension-seo.php:113
msgid "Current month"
msgstr "חודש נוכחי"

#: ../extensions/seo/class-fw-extension-seo.php:119
msgid "Current year"
msgstr "שנה נוכחית"

#: ../extensions/seo/class-fw-extension-seo.php:125
msgid "Date of the post/page"
msgstr "תאריך העמוד/פוסט"

#: ../extensions/seo/class-fw-extension-seo.php:131
msgid "Title of the post/page/term"
msgstr "כותרת הפוסט/עמוד/תנאי"

#: ../extensions/seo/class-fw-extension-seo.php:137
msgid "Excerpt of the current post, of auto-generate if it is not set"
msgstr ""
"קטע של הפוסט הנוכחי, של יוצר אוטומטי -  אם הוא אינו מוגדר\n"

#: ../extensions/seo/class-fw-extension-seo.php:143
msgid "Excerpt of the current post, without auto-generation"
msgstr ""
"קטע של הפוסט הנוכחי, ללא יצירה אוטומטית\n"

#: ../extensions/seo/class-fw-extension-seo.php:149
msgid "Post tags, separated by coma"
msgstr ""
"תגיות פוסט, מופרדות על ידי coma\n"

#: ../extensions/seo/class-fw-extension-seo.php:161
msgid "Category/tag/term description"
msgstr "תיאור קטגוריה/תגית/טווח"

#: ../extensions/seo/class-fw-extension-seo.php:167
msgid "Term title"
msgstr "כותרת טווח"

#: ../extensions/seo/class-fw-extension-seo.php:173
msgid "Post modified time"
msgstr "פרסם מועד השינוי"

#: ../extensions/seo/class-fw-extension-seo.php:179
msgid "Post/page id"
msgstr "כתובת פוסט/עמוד"

#: ../extensions/seo/class-fw-extension-seo.php:185
msgid "Post/page author \"nicename\""
msgstr "\"nicename\" כותב פוסט/עמוד"

#: ../extensions/seo/class-fw-extension-seo.php:191
msgid "Post/page author id"
msgstr "מזהה כותב הפוסט/עמוד"

#: ../extensions/seo/class-fw-extension-seo.php:197
msgid "Search phrase in search page"
msgstr ""
"חפש ביטוי בעמוד החיפוש\n"

#: ../extensions/seo/class-fw-extension-seo.php:203
#: ../extensions/seo/class-fw-extension-seo.php:209
msgid "Page number"
msgstr "מספר עמוד"

#: ../extensions/seo/class-fw-extension-seo.php:215
msgid "Attachment caption"
msgstr ""
"כיתוב מצורף\n"

#: ../extensions/seo/class-fw-extension-seo.php:435
#: ../extensions/seo/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:120
msgid "SEO"
msgstr "SEO"

#: ../extensions/seo/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:121
msgid ""
"This extension will enable you to have a fully optimized WordPress website "
"by adding optimized meta titles, keywords and descriptions."
msgstr ""
"תוסף זה יאפשר לך לקבל אתר אינטרנט מותאם באופן מלא על ידי הוספת כותרות meta "
"מותאמות, מילות מפתח ותיאורים.\n"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:22
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:310
msgid "Titles & Meta"
msgstr "כותרות ומטא"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:26
#: ../extensions/breadcrumbs/settings-options.php:6
#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:9
msgid "Homepage"
msgstr "עמוד הבית"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:30
msgid "Homepage Title"
msgstr "כותרת עמוד הבית"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:31
msgid "Set homepage title format"
msgstr "הגדר פורמט כותרת עמוד הבית"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:36
msgid "Homepage Description"
msgstr "תיאור עמוד הבית"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:37
msgid "Set homepage description"
msgstr "הגדר תיאור עמוד הבית"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:44
msgid "Homepage Meta Keywords"
msgstr "מילות מפתח מטא לעמוד הבית"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:45
msgid "Set homepage meta keywords"
msgstr "הגדר מילות מפתח מטא לעמוד הבית"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:54
msgid "Custom Posts"
msgstr "פוסטים מותאמים"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:59
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:111
#: ../extensions/learning/includes/class-fw-widget-learning.php:120
#: ../extensions/shortcodes/shortcodes/testimonials/options.php:7
#: ../extensions/shortcodes/shortcodes/accordion/options.php:15
#: ../extensions/shortcodes/shortcodes/icon/options.php:12
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:8
#: ../extensions/shortcodes/shortcodes/tabs/options.php:15
#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:56
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:480
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:62
msgid "Title"
msgstr "כותרת"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:60
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:112
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:287
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:315
msgid "Set title format"
msgstr "הגדר פורמט כותרת"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:63
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:74
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:87
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:115
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:126
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:139
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:247
msgid "Here are some tags examples:"
msgstr "אלו דוגמאות לתגיות:"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:70
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:122
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:320
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:66
msgid "Description"
msgstr "תיאור"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:71
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:123
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:293
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:321
msgid "Set description format"
msgstr "הגדר פורמט תיאור"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:83
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:135
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:328
msgid "Meta Keywords"
msgstr "מילות מפתח מטא"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:84
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:136
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:329
msgid "Set meta keywords"
msgstr "הגדר מילות מפתח מטא"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:96
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:148
msgid "Meta Robots"
msgstr "רובוטי מטא"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:97
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:149
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:188
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:227
msgid "noindex, follow"
msgstr "אין אינדקס, עקוב"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:105
msgid "Taxonomies"
msgstr "טקסונומיות"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:158
msgid "Other"
msgstr "אחר"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:165
msgid "Author Page Title"
msgstr "כותב כותרת העמוד"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:166
msgid "Set author page title format"
msgstr "הגדר פורמט כותב כותרת העמוד"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:171
msgid "Author Page Description"
msgstr "כותב תיאור העמוד"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:172
msgid "Set author page description"
msgstr "הגדר כותב תיאור העמוד"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:179
msgid "Author Meta Keywords"
msgstr "מחבר מילות מפתח מטא"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:180
msgid "Set author page meta keywords"
msgstr "הגדר מחבר עמוד מילות מפתח מטא"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:187
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:226
msgid "Metarobots"
msgstr "רובוטי מטא"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:193
msgid "Disable Author Archives"
msgstr "השבת ארכיון מחבר"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:194
msgid "Disable Author archives SEO settings"
msgstr ""
"השבת ארכיון מחבר הגדרות SEO\n"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:204
msgid "Date Achieves Title"
msgstr ""
"תאריך השגת כותרת\n"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:205
msgid "Set date achieves title format"
msgstr "הגדר פורמט תאריך השגת כותרת"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:210
msgid "Date Achieves Description"
msgstr "תיאור השגת תאריך"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:211
msgid "Set date achieves description"
msgstr "הגדר תיאור השגת תאריך"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:232
msgid "Disable Date Archives"
msgstr ""
"השבת ארכיון תאריך\n"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:233
msgid "Disable date archives SEO settings"
msgstr "השבת ארכיוני תאריך והגדרות SEO"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:243
msgid "Search Page Title"
msgstr "חפש כותרת עמוד"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:244
msgid "Set search page title format"
msgstr "הגדר פורמט חיפוש כותרת עמוד"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:259
msgid "404 Page Title"
msgstr "כותרת עמוד 404"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:260
msgid "Set 404 page title format"
msgstr "הגדר פורמט כותרת עמוד 404"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:262
#: ../extensions/breadcrumbs/settings-options.php:8
msgid "404 Not Found"
msgstr "לא נמצא 404"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:286
msgid "SEO Title"
msgstr "כותרת SEO"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:292
msgid "SEO Description"
msgstr "תיאור SEO"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:314
msgid "Page Title"
msgstr "כותרת עמוד"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:356
msgid "Use Meta Keywords"
msgstr "השתמש במילות מפתח מטא"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:357
msgid "Allow the use of meta keywords in posts and taxonomies"
msgstr ""
"אפשר שימוש במילות מפתח מטא בפוסטים ובטקסונומיות\n"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:33
msgid "Google Webmasters"
msgstr ""
"מנהלי אתרים של Google\n"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:34
msgid "Insert Google Webmasters verification code"
msgstr ""
"הוסף קוד אימות של מנהלי אתרים של Google\n"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:41
msgid "Bing Webmasters"
msgstr "מנהלי אתרי בינג"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:42
msgid "Insert Bing Webmasters verification code"
msgstr ""
"הכנס קוד אימות של מנהלי אתר בינג\n"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:80
msgid "Webmasters"
msgstr "מנהלי אתרים"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:89
#, php-format
msgid "Webmaster %s already exists"
msgstr ""
"% מנהל אתר כבר קיים\n"

#: ../extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:634
msgid "Google"
msgstr "גוגל"

#: ../extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:638
msgid "Bing"
msgstr "בינג"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:19
msgid "Check if you want to exclude this page"
msgstr ""
"בדוק אם ברצונך לבצע אי הכללה של דף זה\n"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:40
msgid "Check if you want to exclude this category"
msgstr ""
"בדוק אם ברצונך לבצע אי הכללה של קטגוריה זו\n"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:57
msgid "Sitemap"
msgstr "מפת אתר"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:68
msgid "View Sitemap"
msgstr "צפה במפת האתר"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:69
msgid "Press button to view sitemap file"
msgstr "לחץ על הכפתור להצגת קובץ מפת האתר"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:71
msgid "XML Sitemap"
msgstr "מפת אתר XML"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:81
msgid "Search Engines"
msgstr "מנועי חיפוש"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:93
msgid "Exclude Pages"
msgstr ""
"אל תכלול דפים\n"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:95
msgid "Please check the pages you do not want to include in sitemap"
msgstr ""
"בדוק את הדפים שאינך מעוניין לכלול במפת אתר\n"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:106
msgid "Exclude Categories"
msgstr "אל תכלול קטגוריות"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:108
msgid "Please check the categories you do not want to include in sitemap"
msgstr ""
"בדוק את הקטגוריות שאינך מעוניין לכלול במפת אתר\n"

#: ../extensions/mailer/manifest.php:5
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:187
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:188
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:103
#: ../core/components/extensions/manager/available-extensions.php:240
msgid "Mailer"
msgstr "שולח המייל"

#: ../extensions/mailer/manifest.php:6
#: ../core/components/extensions/manager/available-extensions.php:241
msgid ""
"This extension will let you set some global email options and it is used by "
"other extensions (like Forms) to send emails."
msgstr ""
"תוסף זה נותן לך להגדיר כמה אפשרויות דוא\"ל גלובליות, והוא משמש תוספים אחרים "
"(כגון טפסים) כדי לשלוח הודעות אימייל.\n"

#: ../extensions/mailer/class-fw-extension-mailer.php:48
msgid "Invalid send method"
msgstr "שיטת שליחה לא חוקית"

#: ../extensions/mailer/class-fw-extension-mailer.php:81
msgid "The message has been successfully sent!"
msgstr ""
"ההודעה נשלחה בהצלחה!\n"

#: ../extensions/mailer/includes/class-mailer-sender.php:27
msgid "Invalid email configuration"
msgstr ""
"תצורת דוא\"ל לא חוקית\n"

#: ../extensions/mailer/includes/class-mailer-sender.php:145
#: ../extensions/mailer/includes/class-mailer-sender.php:161
msgid "Email sent"
msgstr "אימייל נשלח"

#: ../extensions/mailer/includes/class-mailer-sender.php:146
msgid "Could not send via smtp"
msgstr ""
"לא ניתן לשלוח דרך smtp\n"

#: ../extensions/mailer/includes/class-mailer-sender.php:162
msgid "Could not send via wp_mail"
msgstr ""
"לא ניתן לשלוח דרך wp_mail\n"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:25
msgid "Server Address"
msgstr "כתובת שרת"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:26
msgid "Enter your email server"
msgstr "הכנס את דואר השרת"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:31
msgid "Username"
msgstr "שם משתמש"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:32
msgid "Enter your username"
msgstr "הכנס את שם המשתמש שלך"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:37
msgid "Password"
msgstr "סיסמה"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:38
msgid "Enter your password"
msgstr "הכנס את הסיסמה שלך"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:43
msgid "Secure Connection"
msgstr ""
"חיבור מאובטח\n"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:54
msgid "Custom Port"
msgstr ""
"יציאה מותאמת אישית\n"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:55
msgid "Optional - SMTP port number to use."
msgstr ""
"אופציונלי - מספר יציאת SMTP לשימוש.\n"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:56
msgid "Leave blank for default (SMTP - 25, SMTPS - 465)"
msgstr ""
"השאר ריק עבור ברירת המחדל (SMTP - 25, SMTPS - 465)\n"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:82
msgid "Username cannot be empty"
msgstr "שם משתמש אינו יכול להיות ריק"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:89
msgid "Password cannot be empty"
msgstr "סיסמה אינה יכול להיות ריקה"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:96
msgid "Invalid host"
msgstr "אחסון לא חוקי"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:176
#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-wpmail.php:56
msgid "Could not send the email"
msgstr "לא יכול לשלוח את המייל"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:94
msgid "From Name"
msgstr "שם טופס"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:95
msgid "The name you'll see in the From filed in your email client."
msgstr ""
"השם שתראה בשדה \"מאת\" בעת קבלת דוא\"ל מהלקוח שלך.\n"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:102
msgid "From Address"
msgstr "כתובת טופס"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:103
msgid "The form will look like was sent from this email address."
msgstr ""
"הטופס ייראה כאילו נשלח מכתובת דוא\"ל זו.\n"

#: ../extensions/learning/class-fw-extension-learning.php:56
msgid "Lesson"
msgstr "שיעור"

#: ../extensions/learning/class-fw-extension-learning.php:57
#: ../extensions/learning/views/content-course.php:14
msgid "Lessons"
msgstr "שיעורים"

#: ../extensions/learning/class-fw-extension-learning.php:78
msgid "Create a lesson"
msgstr "צור שיעור"

#: ../extensions/learning/class-fw-extension-learning.php:120
#: ../extensions/learning/hooks.php:53
msgid "Course"
msgstr "קורס"

#: ../extensions/learning/class-fw-extension-learning.php:121
#: ../extensions/learning/includes/class-fw-widget-learning.php:108
msgid "Courses"
msgstr "קורסים"

#: ../extensions/learning/class-fw-extension-learning.php:142
msgid "Create a course"
msgstr "צור קורס"

#: ../extensions/learning/class-fw-extension-learning.php:181
#: ../extensions/learning/class-fw-extension-learning.php:222
msgid "Course Category"
msgstr "קטגוריית קורס"

#: ../extensions/learning/class-fw-extension-learning.php:182
#: ../extensions/learning/class-fw-extension-learning.php:223
msgid "Course Categories"
msgstr "קטגוריות קורס"

#: ../extensions/learning/class-fw-extension-learning.php:188
msgid "Search categories"
msgstr "חפש קטגוריות"

#: ../extensions/learning/class-fw-extension-learning.php:194
msgid "Add New category"
msgstr "הוסף קטגוריה חדשה"

#: ../extensions/learning/class-fw-extension-learning.php:285
msgid "View all courses"
msgstr "צפה בכל הקורסים"

#: ../extensions/learning/class-fw-extension-learning.php:511
msgid "No courses available"
msgstr "אין קורסים זמינים"

#: ../extensions/learning/class-fw-extension-learning.php:513
msgid "Without Course"
msgstr "ללא קורס"

#: ../extensions/learning/class-fw-extension-learning.php:520
msgid "Select Course"
msgstr "בחר קורס"

#: ../extensions/learning/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:216
msgid "Learning"
msgstr "לימוד"

#: ../extensions/learning/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:217
msgid ""
"This extension adds a Learning module to your theme. Using this extension "
"you can add courses, lessons and tests for your users to take."
msgstr ""
"תוסף זה מוסיף מודול למידה לתבנית שלך. באמצעות תוסף זה תוכל להוסיף קורסים, "
"שיעורים ומבחנים עבור המשתמשים שלך.\n"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:118
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:145
msgid "Quiz"
msgstr "חידון"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:259
msgid "Quiz Elements"
msgstr "אלמנטים לחידון"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:272
msgid "Quiz settings"
msgstr "הגדרות חידון"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:276
msgid "Quiz Passmark Points"
msgstr ""
"חידון נקודות ציון\n"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:278
msgid "The points number at which the test will be passed."
msgstr ""
"מספר הנקודות שבו המבחן יהיה עובר.\n"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:286
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:296
msgid "Lesson Quiz"
msgstr "חידון שיעור"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:535
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:544
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:552
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:559
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:568
msgid "Invalid Quiz"
msgstr ""
"חידון לא חוקי\n"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:21
#, php-format
msgid "You require %d points in oder to pass the test"
msgstr ""
"נדרשות לך % d נקודות על מנת לעבור את המבחן\n"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:27
msgid "Sorry, you did not pass the test"
msgstr "סליחה, לא הצלחת לעבור את המבחן"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:29
msgid "Congratulation, you passed the test"
msgstr "ברכות, עברת את המבחן"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:43
#, php-format
msgid "You answered correctly %s questions from %s"
msgstr "ענית ל%s שאלות נכונות מתוך %s"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:69
#: ../extensions/learning/views/content-lesson.php:18
msgid "Back to"
msgstr "חזרה אל"

#: ../extensions/learning/extensions/learning-quiz/views/start-quiz.php:11
msgid "Start Quiz"
msgstr "התחל חידון"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:20
msgid "Correct answers"
msgstr "תשובות נכונות"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:21
msgid "Add correct answer variants"
msgstr "הוסף גרסאות לתשובה נכונה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:24
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:23
msgid "Set Correct Answer"
msgstr "הגדר תשובה נכונה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:34
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:31
msgid "Wrong answers"
msgstr "תשובות שגויות"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:35
msgid "Add wrong answer variants"
msgstr "הוסף גרסאות לתשובה שגויה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:38
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:35
msgid "Set Wrong Answer"
msgstr "הגדר תשובה שגויה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:42
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:55
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:53
msgid "Creates a"
msgstr "צור"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:59
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:91
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:82
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:180
msgid "Multiple Choice"
msgstr ""
"בחירה מרובה\n"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
msgid "item"
msgstr "פריט"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:87
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:73
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:86
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:84
#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:76
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:75
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:80
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:80
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:48
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:75
msgid "Label"
msgstr "שדה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:88
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:75
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:87
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:85
msgid "Add/Edit Question"
msgstr "הוסף/עדכן תשובה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:90
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:77
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:90
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:87
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:58
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:51
msgid "Delete"
msgstr "מחק"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:92
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:90
msgid "More"
msgstr "עוד"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:93
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:91
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:57
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:57
msgid "Close"
msgstr "סגור"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:94
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:78
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:91
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:56
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:52
msgid "Edit Label"
msgstr "עדכון שדה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:96
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:80
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:93
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:93
msgid "The question label is empty"
msgstr "שדה השאלה ריק"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:97
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:81
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:94
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:94
msgid "Invalid mark point number"
msgstr "סימון מספר נקודות לא חוקי"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:98
msgid "There needs to be at least one correct answer"
msgstr ""
"צריכה להיות לפחות תשובה אחת נכונה\n"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:18
msgid "Correct answer"
msgstr "תשובה נכונה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:19
msgid "The question answer will be true or false"
msgstr "התשובה לשאלה תהיה נכון או לא נכון"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:22
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:20
msgid "True"
msgstr "נכון"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:26
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:24
msgid "False"
msgstr "שגוי"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:45
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:74
msgid "True/False"
msgstr "נכון/שגוי"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:19
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:21
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:97
msgid "Text before gap"
msgstr "טקסט לפני מרווח"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:28
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:30
msgid "Gap"
msgstr "מרווח"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:37
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:39
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:98
msgid "Text after gap"
msgstr "טקסט אחרי מרווח"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:58
msgid "Gap Fill"
msgstr "מילוי מרווח"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:89
msgid "Gap _____ Fill"
msgstr "רווח_____מילוי"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:96
#, php-format
msgid "At least one of the fields ( %s or %s ) has to ve filled with text"
msgstr ""
"לפחות את אחד מהשדות (% s או% s) חייב למלא בטקסט\n"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:19
msgid "Correct Answer"
msgstr "תשובה נכונה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:20
msgid "Write the correct answer text"
msgstr "הכנס את טקסט התשובה הנכונה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:32
msgid "Add wrong answers variants"
msgstr "הוסף גרסאות לתשובות שגויות"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:82
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:180
msgid "Single Choice"
msgstr "אפשרות יחידה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:95
msgid "Correct answer cannot be empty"
msgstr ""
"התשובה הנכונה אינה יכולה להיות ריקה\n"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:96
msgid "There are not any wrong answers set"
msgstr "לא הוגדרו תשובות שגויות כלשהן"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:51
msgid "Question"
msgstr "שאלה"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:53
msgid "Type the question..."
msgstr ""
"הקלד את השאלה ...\n"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:58
msgid "Points"
msgstr "נקודות"

#: ../extensions/learning/includes/class-fw-widget-learning.php:18
msgid "Get list of courses"
msgstr "קבל רשימת קורסים"

#: ../extensions/learning/includes/class-fw-widget-learning.php:20
msgid "Lesson Courses"
msgstr ""
"קורסי שיעור\n"

#: ../extensions/learning/includes/class-fw-widget-learning.php:126
msgid "Number of courses"
msgstr "מספר קורסים"

#: ../extensions/analytics/settings-options.php:11
msgid "Google Analytics"
msgstr "גוגל אנליטיקס"

#: ../extensions/analytics/settings-options.php:12
msgid "Enter your Google Analytics code (Ex: UA-XXXXX-X)"
msgstr ""
"הזן את קוד Google Analytics שלך (לדוגמה: UA-XXXXX-X)\n"

#: ../extensions/analytics/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:192
msgid "Analytics"
msgstr "אנליטיקס"

#: ../extensions/analytics/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:193
msgid ""
"Enables the possibility to add the Google Analytics tracking code that will "
"let you get all the analytics about visitors, page views and more."
msgstr ""
"מפעיל את האפשרות להוסיף את קוד המעקב של גוגל אנליטיקס אשר מאפשר לך לקבל את "
"כל הניתוחים לגבי מבקרים, צפיות בדף ועוד.\n"

#: ../extensions/blog/class-fw-extension-blog.php:36
#: ../extensions/blog/class-fw-extension-blog.php:37
#: ../extensions/breadcrumbs/settings-options.php:7
#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:10
msgid "Blog"
msgstr "בלוג"

#: ../extensions/blog/class-fw-extension-blog.php:38
msgid "Add blog post"
msgstr "הוסף פוסט בלוג"

#: ../extensions/blog/class-fw-extension-blog.php:39
msgid "Add new blog post"
msgstr "הוסף פוסט בלוג חדש"

#: ../extensions/blog/class-fw-extension-blog.php:40
msgid "All blog posts"
msgstr "כל הפוסטים בבלוג"

#: ../extensions/blog/class-fw-extension-blog.php:41
msgid "Edit blog post"
msgstr "עדכון פוסט בלוג"

#: ../extensions/blog/class-fw-extension-blog.php:42
#: ../extensions/blog/class-fw-extension-blog.php:43
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:49
msgid "Blog Post"
msgstr "פוסט בלוג"

#: ../extensions/blog/class-fw-extension-blog.php:44
msgid "New blog post"
msgstr "פוסט בלוג חדש"

#: ../extensions/blog/class-fw-extension-blog.php:45
msgid "No blog posts found"
msgstr "לא נמצא פוסט בלוג"

#: ../extensions/blog/class-fw-extension-blog.php:46
msgid "No blog posts found in trash"
msgstr "לא נמצאו פוסטים של הבלוג באשפה"

#: ../extensions/blog/class-fw-extension-blog.php:47
msgid "Search blog posts"
msgstr "חפש פוסט בלוג"

#: ../extensions/blog/class-fw-extension-blog.php:48
msgid "View blog post"
msgstr "הצג פוסט בלוג"

#: ../extensions/blog/class-fw-extension-blog.php:67
#: ../extensions/blog/class-fw-extension-blog.php:87
#: ../extensions/blog/manifest.php:7 ../extensions/blog/manifest.php:8
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:50
msgid "Blog Posts"
msgstr "פוסטים בבלוג"

#: ../extensions/blog/class-fw-extension-blog.php:76
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:74
msgid "Blog Categories"
msgstr "קטגוריות בלוג"

#: ../extensions/styling/class-fw-extension-styling.php:60
#: ../extensions/styling/class-fw-extension-styling.php:61
#: ../extensions/styling/class-fw-extension-styling.php:78
#: ../extensions/styling/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:48
msgid "Styling"
msgstr "עיצוב"

#: ../extensions/styling/class-fw-extension-styling.php:104
#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:278
#: ../core/components/backend.php:357
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2277
msgid "Save"
msgstr "שמור"

#: ../extensions/styling/class-fw-extension-styling.php:118
msgid "You have no permission to change Styling options"
msgstr ""
"אין לך הרשאה לשנות את אפשרויות העיצוב\n"

#: ../extensions/styling/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:49
msgid ""
"This extension lets you control the website visual style. Starting from "
"predefined styles to changing specific fonts and colors across the website."
msgstr ""
"תוסף זה מאפשר לך לשלוט בעיצוב החזותי של האתר. החל מסגנונות מוגדרים מראש "
"לשינוי גופנים וצבעים ספציפיים ברחבי האתר.\n"

#: ../extensions/styling/extensions/switch-style-panel/manifest.php:7
msgid "Switch Style Panel"
msgstr "שנה את עיצוב לוח הבקרה"

#: ../extensions/styling/extensions/switch-style-panel/manifest.php:8
msgid ""
"Show on the front-end a panel that allows the user to make the switch "
"between predefined styles."
msgstr ""
"הצג בחזית לוח בקרה שמאפשר למשתמש לבצע את המעבר בין עיצובים מוגדרים מראש.\n"

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:13
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:274
#: ../extensions/shortcodes/shortcodes/map/options.php:45
#: ../extensions/shortcodes/shortcodes/button/options.php:24
#: ../extensions/shortcodes/shortcodes/media-image/options.php:42
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:34
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:48
#: ../includes/option-types/simple.php:454
#: ../includes/option-types/switch/class-fw-option-type-switch.php:151
msgid "Yes"
msgstr "כן"

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:17
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:278
#: ../extensions/shortcodes/shortcodes/map/options.php:49
#: ../extensions/shortcodes/shortcodes/button/options.php:28
#: ../extensions/shortcodes/shortcodes/media-image/options.php:46
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:38
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:52
#: ../includes/option-types/switch/class-fw-option-type-switch.php:147
msgid "No"
msgstr "לא"

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:24
msgid "The text that will be displayed at the top of the panel."
msgstr ""
"הטקסט שיוצג בחלק העליון של לוח הבקרה.\n"

#: ../extensions/styling/includes/option-types/style/views/settings.php:119
msgid "Background"
msgstr "רקע"

#: ../extensions/styling/includes/option-types/style/views/predefined.php:26
msgid "Predefined Styles"
msgstr ""
"עיצובים מוגדרים מראש\n"

#: ../extensions/styling/includes/option-types/style/views/preview.php:41
msgid "This is a simplified preview, not changes are reflected."
msgstr ""
"זוהי תצוגה מקדימה פשוטה, אין שינויים שמשתקפים.\n"

#: ../extensions/feedback/class-fw-extension-feedback.php:64
#: ../core/components/extensions/manager/available-extensions.php:144
msgid "Feedback"
msgstr "משוב"

#: ../extensions/feedback/class-fw-extension-feedback.php:111
msgid "Reviews"
msgstr "ביקורות"

#: ../extensions/feedback/settings-options.php:10
#: ../extensions/shortcodes/extensions/page-builder/settings-options.php:11
msgid "Activate for"
msgstr ""
"הפעל עבור\n"

#: ../extensions/feedback/settings-options.php:16
msgid "Select the options you want the Feedback extension to be activated for"
msgstr ""
"בחר את האפשרויות שברצונך להפעיל את תוסף המשוב עבורן\n"

#: ../extensions/feedback/manifest.php:7
msgid "FeedBack"
msgstr "משוב"

#: ../extensions/feedback/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:145
msgid ""
"Adds the possibility to leave feedback (comments, reviews and rating) about "
"your products, articles, etc. This replaces the default comments system."
msgstr ""
"מוסיף את האפשרות להשאיר משוב (תגובות, ביקורות ודירוג) על המוצרים שלך, מאמרים "
"וכו'. אפשרות זו מחליפה את מערכת תגובות ברירת המחדל.\n"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:84
msgid "Rating:"
msgstr "דירוג:"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:195
msgid "Feedback Stars"
msgstr "כוכבי פידבק"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:221
#: ../extensions/feedback/extensions/feedback-stars/views/rate.php:12
msgid "Rating"
msgstr "דירוג"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "ERROR"
msgstr "שגיאה"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "please rate the post."
msgstr "אנא דרג את הפוסט"

#: ../extensions/feedback/extensions/feedback-stars/manifest.php:7
msgid "FeedBack Stars"
msgstr "כוכבי פידבק"

#: ../extensions/feedback/extensions/feedback-stars/manifest.php:8
msgid "Allows visitors to appreciate a post using star rating"
msgstr ""
"מאפשר למבקרים להעריך פוסט באמצעות דירוג כוכבים\n"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
msgid "Pingback:"
msgstr "Pingback:"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
msgid "(Edit)"
msgstr "(ערוך)"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:37
msgid "Post author"
msgstr "מחבר הפוסט"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:57
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
#, php-format
msgid "%1$s at %2$s"
msgstr "%1$s מתוך %2$s"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:24
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:40
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:119
msgid "says"
msgstr "אומר"

#: ../extensions/feedback/extensions/feedback-stars/views/view-rates.php:24
#, php-format
msgid "Based on %s Votes"
msgstr ""
"מבוסס על % s קולות\n"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:6
msgid "Rating System"
msgstr "מערכת דירוג"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:9
msgid "Enter the number of stars you want in the rating system"
msgstr ""
"הזן את מספר הכוכבים הרצוי במערכת הדירוג\n"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:11
msgid "5 stars"
msgstr "5 כוכבים"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:12
msgid "7 stars"
msgstr "7 כוכבים"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:13
msgid "10 stars"
msgstr "10 כוכבים"

#: ../extensions/feedback/views/reviews.php:32
#: ../extensions/feedback/views/reviews.php:53
msgid "Comment navigation"
msgstr ""
"ניווט בתגובות\n"

#: ../extensions/feedback/views/reviews.php:35
#: ../extensions/feedback/views/reviews.php:56
msgid "&larr; Older Comments"
msgstr "&larr; תגובות קודמות"

#: ../extensions/feedback/views/reviews.php:36
#: ../extensions/feedback/views/reviews.php:57
msgid "Newer Comments &rarr;"
msgstr "&larr; תגובות חדשות"

#: ../extensions/feedback/views/reviews.php:62
msgid "Comments are closed."
msgstr ""
"תגובות סגורות.\n"

#: ../extensions/shortcodes/extensions/page-builder/settings-options.php:18
msgid ""
"Select the posts you want the Page Builder extension to be activated for"
msgstr ""
"בחר את הפוסטים שעבורם שברצונך להפעיל את תוסף עורך העמודים\n"

#: ../extensions/shortcodes/extensions/page-builder/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:84
msgid "Page Builder"
msgstr "עורך עמודים"

#: ../extensions/shortcodes/extensions/page-builder/manifest.php:9
msgid ""
"Lets you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr ""
"מאפשר לך לבנות בקלות אינספור עמודים בעזרת שימוש באופציה של  drag and drop "
"הקיימת בעורך העמודים  הויזואלי אשר מגיע עם מגוון  קודי קיצור קיימים.\n"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:51
msgid ""
"There must not be more than one page Editor integrated with the wp post "
"editor per page"
msgstr ""
"לא מוכרח להיות יותר מעורך עמודים אחד אשר משולב עם עורך  WP לפוסט עבור כל  "
"עמוד\n"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:96
msgid "Visual Page Builder"
msgstr "עורך עמודים ויזואלי"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:97
msgid "Default Editor"
msgstr ""
"עורך ברירת מחדל\n"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:126
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:117
#: ../extensions/shortcodes/shortcodes/section/config.php:5
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:22
msgid "Layout Elements"
msgstr "פריסת אלמנטים"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:99
#, php-format
msgid "No Page Builder tab specified for shortcode: %s"
msgstr ""
"לא צוינה לשונית עורך עמודים עבור קוד קיצור:% s\n"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:111
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:75
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:83
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:57
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:238
#: ../core/components/extensions/manager/views/extension.php:141
#: ../core/components/extensions/manager/views/extension.php:345
msgid "Remove"
msgstr "העבר"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:112
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:74
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:82
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:56
msgid "Duplicate"
msgstr "שכפל"

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:8
#: ../extensions/shortcodes/shortcodes/testimonials/options.php:12
msgid "Testimonials"
msgstr "המלצות"

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:9
msgid "Add some Testimonials"
msgstr "הוסף המלצות"

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:10
#: ../extensions/shortcodes/shortcodes/accordion/config.php:10
#: ../extensions/shortcodes/shortcodes/table/config.php:10
#: ../extensions/shortcodes/shortcodes/map/config.php:10
#: ../extensions/shortcodes/shortcodes/special-heading/config.php:8
#: ../extensions/shortcodes/shortcodes/team-member/config.php:10
#: ../extensions/shortcodes/shortcodes/icon/config.php:8
#: ../extensions/shortcodes/shortcodes/icon-box/config.php:8
#: ../extensions/shortcodes/shortcodes/button/config.php:10
#: ../extensions/shortcodes/shortcodes/calendar/config.php:10
#: ../extensions/shortcodes/shortcodes/notification/config.php:10
#: ../extensions/shortcodes/shortcodes/widget-area/config.php:10
#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:10
#: ../extensions/shortcodes/shortcodes/text-block/config.php:10
#: ../extensions/shortcodes/shortcodes/divider/config.php:8
#: ../extensions/shortcodes/shortcodes/tabs/config.php:10
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:10
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:106
msgid "Content Elements"
msgstr ""
"אלמנטים של תוכן\n"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:8
msgid "Option Testimonials Title"
msgstr "כותרת המלצות אפשרית"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:13
msgid "Add/Edit Testimonial"
msgstr "הוסף/ערוף המלצה"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:14
msgid "Here you can add, remove and edit your Testimonials."
msgstr "כאן אתה יכול להוסיף, להסיר או להוסיף את ההמלצות שלך"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:19
msgid "Quote"
msgstr "ציטוט"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:20
msgid "Enter the testimonial here"
msgstr "הכנס את ההמלצה כאן"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:25
#: ../extensions/shortcodes/shortcodes/media-image/config.php:6
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:102
msgid "Image"
msgstr "תמונה"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:26
#: ../extensions/shortcodes/shortcodes/team-member/options.php:8
#: ../extensions/shortcodes/shortcodes/media-image/options.php:9
msgid ""
"Either upload a new, or choose an existing image from your media library"
msgstr ""
"העלה פריט חדש או בחר תמונה קיימת מספריית המדיה שלך\n"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:30
msgid "Name"
msgstr "שם"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:31
msgid "Enter the Name of the Person to quote"
msgstr "הכנס את שם האדם לציטוט"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:35
msgid "Position"
msgstr "מיקום"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:36
msgid "Can be used for a job description"
msgstr "יכול לשמש כתיאור תפקיד"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:40
msgid "Website Name"
msgstr "שם אתר"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:41
msgid "Linktext for the above Link"
msgstr ""
"Linktext עבור הקישור שמעל\n"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:45
msgid "Website Link"
msgstr "קישור אתר"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:46
msgid "Link to the Persons website"
msgstr "קישור לאתר של האנשים"

#: ../extensions/shortcodes/shortcodes/accordion/config.php:8
msgid "Accordion"
msgstr "אקורדיון"

#: ../extensions/shortcodes/shortcodes/accordion/config.php:9
msgid "Add an Accordion"
msgstr "הוסף אקורדיון"

#: ../extensions/shortcodes/shortcodes/accordion/options.php:8
#: ../extensions/shortcodes/shortcodes/tabs/config.php:8
#: ../extensions/shortcodes/shortcodes/tabs/options.php:8
msgid "Tabs"
msgstr "לשוניות"

#: ../extensions/shortcodes/shortcodes/accordion/options.php:9
msgid "Add/Edit Tabs"
msgstr "הוסף/ערוך לשוניות"

#: ../extensions/shortcodes/shortcodes/accordion/options.php:10
#: ../extensions/shortcodes/shortcodes/tabs/options.php:10
msgid "Create your tabs"
msgstr "צור את הלשוניות שלך"

#: ../extensions/shortcodes/shortcodes/accordion/options.php:19
#: ../extensions/shortcodes/shortcodes/icon-box/options.php:24
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:13
#: ../extensions/shortcodes/shortcodes/text-block/options.php:10
#: ../extensions/shortcodes/shortcodes/tabs/options.php:19
msgid "Content"
msgstr "תוכן"

#: ../extensions/shortcodes/shortcodes/table/config.php:8
msgid "Table"
msgstr "טבלה"

#: ../extensions/shortcodes/shortcodes/table/config.php:9
msgid "Add a Table"
msgstr "הוסף טבלה"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:64
msgid "table-builder option type must be inside the table shortcode"
msgstr "סוג אפשרות בונה הטבלה חייב להיות בתוך קוד הקיצור של הטבלה"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:171
msgid "Table Styling"
msgstr "עיצוב טבלה"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:172
msgid "Choose the table styling options"
msgstr "בחר את אפשרויות העיצוב עבר הטבלה"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:174
msgid "Use the table as a pricing table"
msgstr "השתמש בטבלה כטבלת מחירים"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:175
msgid "Use the table to display tabular data"
msgstr ""
"השתמש בטבלה כדי להציג נתונים טבלאיים\n"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:197
msgid "Default row"
msgstr "שורת ברירת מחדל"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:198
msgid "Heading row"
msgstr ""
"כותרת שורה\n"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:199
msgid "Pricing row"
msgstr "שורת מחיר"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:200
msgid "Button row"
msgstr "שורת כפתור"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:201
msgid "Row switch"
msgstr ""
"מעבר שורה\n"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:211
msgid "Default column"
msgstr "עמודת ברירת מחדל"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:212
msgid "Description column"
msgstr "עמודת תיאור"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:213
msgid "Highlight column"
msgstr "עמודה מודגשת"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:214
msgid "Center text column"
msgstr "עמודת טקסט ממורכז"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:251
msgid "per month"
msgstr "לחודש"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:262
#: ../extensions/shortcodes/shortcodes/button/config.php:8
msgid "Button"
msgstr "כפתור"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:263
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:39
#: ../extensions/sidebars/class-fw-extension-sidebars.php:148
#: ../includes/option-types/addable-option/class-fw-option-type-addable-option.php:20
#: ../includes/option-types/addable-popup/class-fw-option-type-addable-popup.php:153
#: ../includes/option-types/addable-box/class-fw-option-type-addable-box.php:186
msgid "Add"
msgstr "הוסף"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-head-template.php:27
msgid "Add Column"
msgstr "הוסף טור"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/view.php:161
msgid "Add Row"
msgstr "הוסף שורה"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:24
#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:25
msgid "Custom"
msgstr "מותאם"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:27
msgid "Locations"
msgstr "מיקומים"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:28
msgid "Add/Edit Location"
msgstr "הוסף/עדכן מיקום"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:31
msgid "Note: Please set location"
msgstr "שים לב: יש להגדיר מיקום"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:35
#: ../extensions/events/class-fw-extension-events.php:187
msgid "Location"
msgstr "מיקום"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:39
msgid "Location Title"
msgstr "כותרת מיקום"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:40
msgid "Set location title"
msgstr "הגדר כותרת מיקום"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:44
msgid "Location Description"
msgstr "תיאור מיקום"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:45
msgid "Set location description"
msgstr "הגדר תיאור מיקום"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:49
msgid "Location Url"
msgstr "מיקום Url"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:50
msgid "Set page url (Ex: http://example.com)"
msgstr "הגדר עמוד url (לדוגמה: http://example.com)"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:53
msgid "Location Image"
msgstr "מיקום תמונה"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:54
msgid "Add location image"
msgstr "הוסף מיקום תמונה"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:113
msgid "No location provider specified for map shortcode"
msgstr ""
"לא צוין ספק מיקום עבור קוד קיצור למפה\n"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:115
msgid "Map Placeholder"
msgstr ""
"מציין מיקום במפה\n"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:122
#, php-format
msgid "Unknown location provider \"%s\" specified for map shortcode"
msgstr ""
"ספק מיקום לא ידוע \"% s\" צויינו עבור קוד קיצור של מפה\n"

#: ../extensions/shortcodes/shortcodes/map/config.php:8
msgid "Map"
msgstr "מפה"

#: ../extensions/shortcodes/shortcodes/map/config.php:9
msgid "Add a Map"
msgstr "הוסף מפה"

#: ../extensions/shortcodes/shortcodes/map/options.php:24
msgid "Map Type"
msgstr "סוג מפה"

#: ../extensions/shortcodes/shortcodes/map/options.php:25
msgid "Select map type"
msgstr "בחר סוג מפה"

#: ../extensions/shortcodes/shortcodes/map/options.php:27
msgid "Roadmap"
msgstr ""
"מפת דרכים\n"

#: ../extensions/shortcodes/shortcodes/map/options.php:28
msgid "Terrain"
msgstr ""
"פני שטח\n"

#: ../extensions/shortcodes/shortcodes/map/options.php:29
msgid "Satellite"
msgstr "לוויין "

#: ../extensions/shortcodes/shortcodes/map/options.php:30
msgid "Hybrid"
msgstr "משולב"

#: ../extensions/shortcodes/shortcodes/map/options.php:34
msgid "Map Height"
msgstr "גובה מפה"

#: ../extensions/shortcodes/shortcodes/map/options.php:35
msgid "Set map height (Ex: 300)"
msgstr "הגדר גובה מפה (לדוגמה: 300)"

#: ../extensions/shortcodes/shortcodes/map/options.php:41
msgid "Disable zoom on scroll"
msgstr "זום בגלילה לא אפשרי"

#: ../extensions/shortcodes/shortcodes/map/options.php:42
msgid "Prevent the map from zooming when scrolling until clicking on the map"
msgstr ""
"מנע את הגדלת המפה בעת גלילה עד ללחיצה על המפה\n"

#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:76
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:65
msgid "Column"
msgstr "טור"

#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:120
#, php-format
msgid "Add a %s column"
msgstr ""
"הוסף עמודה % s\n"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:12
msgid "Columns"
msgstr "עמודות"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:47
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:47
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:51
msgid "No Templates Saved"
msgstr "לא נשמרו תבניות"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:50
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:50
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:54
msgid "Load Template"
msgstr "טען תבנית"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:91
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:91
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:94
msgid "Template Name"
msgstr "שם תבנית"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:92
msgid "Save Column"
msgstr "שמור טור"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:93
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:93
msgid "Save as Template"
msgstr "שמור כתבנית"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:174
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:174
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:206
msgid "No Title"
msgstr "אין כותרת"

#: ../extensions/shortcodes/shortcodes/special-heading/config.php:6
msgid "Special Heading"
msgstr "כותרת מיוחדת"

#: ../extensions/shortcodes/shortcodes/special-heading/config.php:7
msgid "Add a Special Heading"
msgstr "הוסף כותרת מיוחדת"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:8
msgid "Heading Title"
msgstr "כותרת ראשית"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:9
msgid "Write the heading title content"
msgstr "כתוב את תוכן הכותרת הראשית"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:13
msgid "Heading Subtitle"
msgstr "תת כותרת ראשית"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:14
msgid "Write the heading subtitle content"
msgstr "כתוב את תוכן תת הכותרת הראשית"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:18
msgid "Heading Size"
msgstr "מידות כותרת"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:30
msgid "Centered"
msgstr "מיושר"

#: ../extensions/shortcodes/shortcodes/team-member/config.php:8
msgid "Team Member"
msgstr "איש צוות"

#: ../extensions/shortcodes/shortcodes/team-member/config.php:9
msgid "Add a Team Member"
msgstr "הוסף איש צוות"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:7
msgid "Team Member Image"
msgstr "תמונת איש צוות"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:12
msgid "Team Member Name"
msgstr "שם איש צוות"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:13
msgid "Name of the person"
msgstr "שם האדם"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:18
msgid "Team Member Job Title"
msgstr "תיאור תפקיד איש צוות"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:19
msgid "Job title of the person."
msgstr "תיאור התפקיד של האדם"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:24
msgid "Team Member Description"
msgstr "תיאור איש צוות"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:25
msgid "Enter a few words that describe the person"
msgstr "הכנס מספר מילות תיאור על האדם"

#: ../extensions/shortcodes/shortcodes/icon/config.php:6
#: ../extensions/shortcodes/shortcodes/icon/options.php:8
#: ../extensions/megamenu/class-fw-extension-megamenu.php:115
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:188
msgid "Icon"
msgstr "אייקון"

#: ../extensions/shortcodes/shortcodes/icon/config.php:7
msgid "Add an Icon"
msgstr "הוסף אייקון"

#: ../extensions/shortcodes/shortcodes/icon/options.php:13
msgid "Icon title"
msgstr "כותרת אייקון"

#: ../extensions/shortcodes/shortcodes/icon-box/config.php:6
msgid "Icon Box"
msgstr "תיבת אייקון"

#: ../extensions/shortcodes/shortcodes/icon-box/config.php:7
msgid "Add an Icon Box"
msgstr "הוסף תיבת אייקון"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:8
msgid "Box Style"
msgstr "עיצוב תיבה"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:10
msgid "Icon above title"
msgstr "אייקון מעל כותרת "

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:11
msgid "Icon in line with title"
msgstr "אייקון מיושר עם כותרת "

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:16
msgid "Choose an Icon"
msgstr "בחר אייקון"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:20
msgid "Title of the Box"
msgstr "כותרת התיבה"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:25
msgid "Enter the desired content"
msgstr "הזן את התוכן הרצוי"

#: ../extensions/shortcodes/shortcodes/button/config.php:9
msgid "Add a Button"
msgstr "הוסף כפתור"

#: ../extensions/shortcodes/shortcodes/button/options.php:7
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:17
msgid "Button Label"
msgstr "תווית כפתור"

#: ../extensions/shortcodes/shortcodes/button/options.php:8
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:18
msgid "This is the text that appears on your button"
msgstr "זהו הטקסט שיופיע על הכפתור שלך"

#: ../extensions/shortcodes/shortcodes/button/options.php:13
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:23
msgid "Button Link"
msgstr "קישור כפתור"

#: ../extensions/shortcodes/shortcodes/button/options.php:14
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:24
msgid "Where should your button link to"
msgstr "לאן הכפתור שלך צריך לקשר"

#: ../extensions/shortcodes/shortcodes/button/options.php:20
#: ../extensions/shortcodes/shortcodes/media-image/options.php:38
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:30
msgid "Open Link in New Window"
msgstr "פתח קישור בחלון חדש"

#: ../extensions/shortcodes/shortcodes/button/options.php:21
#: ../extensions/shortcodes/shortcodes/media-image/options.php:39
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:31
msgid "Select here if you want to open the linked page in a new window"
msgstr "בחר כאן אם אתה רוצה לפתוח את העמוד המקושר בחלון חדש"

#: ../extensions/shortcodes/shortcodes/button/options.php:32
msgid "Button Color"
msgstr "צבע כפתור"

#: ../extensions/shortcodes/shortcodes/button/options.php:33
msgid "Choose a color for your button"
msgstr "בחר צבע עבור הכפתור שלך"

#: ../extensions/shortcodes/shortcodes/button/options.php:36
msgid "Default"
msgstr "ברירת מחדל"

#: ../extensions/shortcodes/shortcodes/button/options.php:37
msgid "Black"
msgstr "שחור"

#: ../extensions/shortcodes/shortcodes/button/options.php:38
msgid "Blue"
msgstr "כחול"

#: ../extensions/shortcodes/shortcodes/button/options.php:39
msgid "Green"
msgstr "ירוק"

#: ../extensions/shortcodes/shortcodes/button/options.php:40
msgid "Red"
msgstr "אדום"

#: ../extensions/shortcodes/shortcodes/media-video/config.php:6
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:108
msgid "Video"
msgstr "וידאו"

#: ../extensions/shortcodes/shortcodes/media-video/config.php:7
msgid "Add a Video"
msgstr "הוסף וידאו "

#: ../extensions/shortcodes/shortcodes/media-video/config.php:8
#: ../extensions/shortcodes/shortcodes/media-image/config.php:8
#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:10
msgid "Media Elements"
msgstr "אלמנטים של מדיה"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:8
msgid "Insert Video URL"
msgstr "הכנס URL עבור סרטון"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:9
#: ../extensions/shortcodes/shortcodes/section/options.php:24
msgid "Insert Video URL to embed this video"
msgstr "הוסף URL של סרטון על מנת להטמיע אותו"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:13
msgid "Video Width"
msgstr "רוחב וידאו"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:14
msgid "Enter a value for the width"
msgstr "הכנס ערך עבור הרוחב"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:19
msgid "Video Height"
msgstr "גובה וידאו"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:20
msgid "Enter a value for the height"
msgstr "הכנס ערך עבור הגובה"

#: ../extensions/shortcodes/shortcodes/calendar/config.php:8
#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:16
msgid "Calendar"
msgstr "יומן"

#: ../extensions/shortcodes/shortcodes/calendar/config.php:9
msgid "Add a Calendar"
msgstr "הוסף יומן"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:23
msgid "Calendar Type"
msgstr "סוג יומן"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:24
msgid "Select calendar type"
msgstr "בחר סוג יומן"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:27
#: ../extensions/backups/includes/module/schedule/settings-options.php:23
msgid "Daily"
msgstr "יומי"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:28
#: ../extensions/backups/includes/module/schedule/settings-options.php:22
msgid "Weekly"
msgstr "שבועי"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:29
#: ../extensions/backups/includes/module/schedule/settings-options.php:21
msgid "Monthly"
msgstr "חודשי"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:33
msgid "Start Week On"
msgstr "התחל שבוע"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:34
msgid "Select first day of week"
msgstr "בחר את היום הראשון בשבוע"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:37
#: ../extensions/shortcodes/shortcodes/calendar/static.php:110
msgid "Monday"
msgstr "שני"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:38
#: ../extensions/shortcodes/shortcodes/calendar/static.php:109
msgid "Sunday"
msgstr "ראשון"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:29
#: ../extensions/shortcodes/shortcodes/calendar/static.php:56
#: ../extensions/shortcodes/shortcodes/calendar/static.php:82
#: ../extensions/events/class-fw-extension-events.php:69
#: ../extensions/events/class-fw-extension-events.php:74
#: ../extensions/events/manifest.php:7
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:77
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:185
#: ../core/components/extensions/manager/available-extensions.php:180
msgid "Events"
msgstr "אירועים"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:30
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:59
msgid "Add/Edit Date & Time"
msgstr "הוסף/ערוך תאריך וזמן"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:33
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:63
msgid "Note: Please set start & end event datetime"
msgstr ""
"הערה: נא להגדיר את תאריך ההתחלה והסיום של datetime\n"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:37
msgid "Event Title"
msgstr "כותרת אירוע"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:38
msgid "Enter the event title"
msgstr "הכנס את כותרת האירוע"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:42
msgid "Event URL"
msgstr "URL אירוע"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:43
msgid "Enter the event URL (Ex: http://your-domain.com/event)"
msgstr ""
"הזן את כתובת האתר של האירוע (לדוגמה: http://your-domain.com/event)\n"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:47
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:58
msgid "Date & Time"
msgstr "תאריך וזמן"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:48
msgid "Enter the event date & time"
msgstr "הכנס את תאריך הארוע והזמן"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:129
msgid "No events provider specified for calendar shortcode"
msgstr ""
"לא הוגדר ספק אירועים עבור הקוד קיצור של לוח השנה\n"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:138
#, php-format
msgid "Unknown events provider \"%s\" specified for calendar shortcode"
msgstr ""
"ספק אירועים לא ידוע \"% s\" שצוין עבור קוד הקיצור של לוח השנה\n"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:55
#: ../extensions/events/class-fw-extension-events.php:68
#: ../extensions/events/class-fw-extension-events.php:75
msgid "Event"
msgstr "אירוע"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:57
#: ../extensions/shortcodes/shortcodes/calendar/views/view.php:23
msgid "Today"
msgstr "היום"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:66
#, php-format
msgid "Calendar: View %s not found"
msgstr ""
"לוח שנה:% s לא נמצא\n"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:67
#, php-format
msgid ""
"Calendar: Wrong date format %s. Should be either \"now\" or \"yyyy-mm-dd\""
msgstr ""
"לוח שנה: פורמט תאריך שגוי % s. צריך להיות \"עכשיו\" או \"yyyy-mm-dd\"\n"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:69
msgid "Calendar: Event URL is not set"
msgstr "לוח שנה: URL אירוע לא הוגדר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:70
#, php-format
msgid ""
"Calendar: Wrong navigation direction %s. Can be only \"next\" or \"prev\" or "
"\"today\""
msgstr ""
"לוח שנה: כיוון ניווט שגוי % s. יכול להיות רק \"הבא\" או \"הקודם\" או "
"\"היום\"\n"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:72
msgid ""
"Calendar: Time split parameter should divide 60 without decimals. Something "
"like 10, 15, 30"
msgstr ""
"לוח שנה: את פרמטר זמן הפיצול יש לחלק ל-60 ללא מספר עשרוני. כמו למשל 10, 15, "
"30\n"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:74
msgid "No events in this day."
msgstr "אין אירועים ביום זה"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:77
#, php-format
msgid "week %s of %s"
msgstr "שבוע %s מתוך %s"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:79
msgid "Week "
msgstr "שבוע"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:80
msgid "All day"
msgstr "כל היום"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:81
msgid "Time"
msgstr "זמן"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:83
msgid "Ends before timeline"
msgstr ""
"מסתיים לפני ציר הזמן\n"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:84
msgid "Starts after timeline"
msgstr "מתחיל אחרי ציר הזמן"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:85
msgid "January"
msgstr "ינואר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:86
msgid "February"
msgstr "פברואר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:87
msgid "March"
msgstr "מרץ"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:88
msgid "April"
msgstr "אפריל"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:89
#: ../extensions/shortcodes/shortcodes/calendar/static.php:101
msgid "May"
msgstr "מאי"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:90
msgid "June"
msgstr "יוני"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:91
msgid "July"
msgstr "יולי"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:92
msgid "August"
msgstr "אוגוסט"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:93
msgid "September"
msgstr "ספטמבר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:94
msgid "October"
msgstr "אוקטובר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:95
msgid "November"
msgstr "נובמבר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:96
msgid "December"
msgstr "דצמבר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:97
msgid "Jan"
msgstr "ינואר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:98
msgid "Feb"
msgstr "פברואר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:99
msgid "Mar"
msgstr "מרץ"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:100
msgid "Apr"
msgstr "אפריל"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:102
msgid "Jun"
msgstr "יוני"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:103
msgid "Jul"
msgstr "יולי"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:104
msgid "Aug"
msgstr "אוגוסט"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:105
msgid "Sep"
msgstr "ספטמבר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:106
msgid "Oct"
msgstr "אוקטובר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:107
msgid "Nov"
msgstr "נובמבר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:108
msgid "Dec"
msgstr "דצמבר"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:111
msgid "Tuesday"
msgstr "שלישי"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:112
msgid "Wednesday"
msgstr "רביעי"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:113
msgid "Thursday"
msgstr "חמישי"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:114
msgid "Friday"
msgstr "שישי"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:115
msgid "Saturday"
msgstr "שבת"

#: ../extensions/shortcodes/shortcodes/media-image/config.php:7
msgid "Add an Image"
msgstr "הוסף תמונה"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:8
msgid "Choose Image"
msgstr "בחר תמונה"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:16
msgid "Width"
msgstr "רוחב"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:17
msgid "Set image width"
msgstr "הגדר רוחב תמונה"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:22
#: ../extensions/shortcodes/shortcodes/divider/options.php:24
msgid "Height"
msgstr "גובה"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:23
msgid "Set image height"
msgstr "הגדר גובה תמונה"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:33
msgid "Image Link"
msgstr "קישור תמונה"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:34
msgid "Where should your image link to?"
msgstr "לאן התמונה שלך צריכה לקשר?"

#: ../extensions/shortcodes/shortcodes/notification/config.php:8
msgid "Notification"
msgstr "התראה"

#: ../extensions/shortcodes/shortcodes/notification/config.php:9
msgid "Add a Notification Box"
msgstr "הוסף תיבת התראה"

#: ../extensions/shortcodes/shortcodes/notification/options.php:7
msgid "Message"
msgstr "הודעה"

#: ../extensions/shortcodes/shortcodes/notification/options.php:8
msgid "Notification message"
msgstr "התראת הודעה"

#: ../extensions/shortcodes/shortcodes/notification/options.php:10
msgid "Message!"
msgstr "הודעה!"

#: ../extensions/shortcodes/shortcodes/notification/options.php:13
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:444
msgid "Type"
msgstr "סוג"

#: ../extensions/shortcodes/shortcodes/notification/options.php:14
msgid "Notification type"
msgstr "סוג התראה"

#: ../extensions/shortcodes/shortcodes/notification/options.php:17
msgid "Congratulations"
msgstr "מזל טוב"

#: ../extensions/shortcodes/shortcodes/notification/options.php:18
msgid "Information"
msgstr "מידע"

#: ../extensions/shortcodes/shortcodes/notification/options.php:19
msgid "Alert"
msgstr "שעון מעורר"

#: ../extensions/shortcodes/shortcodes/notification/options.php:20
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:456
msgid "Error"
msgstr "שגיאה"

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:9
msgid "Congratulations!"
msgstr ""
"מזל טוב!\n"

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:13
msgid "Information!"
msgstr "מידע!"

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:17
msgid "Alert!"
msgstr "התרעה!"

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:21
msgid "Error!"
msgstr "שגיאה!"

#: ../extensions/shortcodes/shortcodes/widget-area/config.php:8
msgid "Widget Area"
msgstr "אזור וידג'ט"

#: ../extensions/shortcodes/shortcodes/widget-area/config.php:9
msgid "Add a Widget Area"
msgstr "הוסף אזור ווידג'ט"

#: ../extensions/shortcodes/shortcodes/widget-area/options.php:5
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:5
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:7
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:40
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:91
msgid "Sidebar"
msgstr "סרגל צדי "

#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:8
msgid "Call To Action"
msgstr "קריאה לפעולה"

#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:9
msgid "Add a Call to Action"
msgstr "הוסף קריאה לפעולה"

#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:9
msgid "This can be left blank"
msgstr ""
"זה יכול להישאר ריק\n"

#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:14
msgid "Enter some content for this Info Box"
msgstr "הכנס תוכן כלשהו לתיבת מידע זו"

#: ../extensions/shortcodes/shortcodes/text-block/config.php:8
msgid "Text Block"
msgstr ""
"בלוק טקסט\n"

#: ../extensions/shortcodes/shortcodes/text-block/config.php:9
msgid "Add a Text Block"
msgstr "הוסף בלוק טקסט"

#: ../extensions/shortcodes/shortcodes/text-block/options.php:11
msgid "Enter some content for this texblock"
msgstr "הכנס תוכן כלשהו עבור בלוק טקסט זה"

#: ../extensions/shortcodes/shortcodes/divider/config.php:6
msgid "Divider"
msgstr "מפריד"

#: ../extensions/shortcodes/shortcodes/divider/config.php:7
msgid "Add a Divider"
msgstr "הוסף מפריד"

#: ../extensions/shortcodes/shortcodes/divider/options.php:13
msgid "Ruler Type"
msgstr "סוג סרגל"

#: ../extensions/shortcodes/shortcodes/divider/options.php:14
msgid "Here you can set the styling and size of the HR element"
msgstr ""
"כאן אתה יכול להגדיר את העיצוב ואת הגודל של אלמנט HR\n"

#: ../extensions/shortcodes/shortcodes/divider/options.php:16
msgid "Line"
msgstr "קו"

#: ../extensions/shortcodes/shortcodes/divider/options.php:17
msgid "Whitespace"
msgstr "אזורים ריקים"

#: ../extensions/shortcodes/shortcodes/divider/options.php:25
msgid ""
"How much whitespace do you need? Enter a pixel value. Positive value will "
"increase the whitespace, negative value will reduce it. eg: '50', '-25', "
"'200'"
msgstr ""
"כמה שטח לבן אתה צריך? הזן ערך בפיקסלים. ערך חיובי יגדיל את השטח הלבן, ערך "
"שלילי יפחית אותו. לדוגמה: '50', '-25', '200'\n"

#: ../extensions/shortcodes/shortcodes/section/config.php:6
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:23
msgid "Section"
msgstr "אזור"

#: ../extensions/shortcodes/shortcodes/section/config.php:7
msgid "Add a Section"
msgstr "הוסף אזור"

#: ../extensions/shortcodes/shortcodes/section/options.php:7
msgid "Full Width"
msgstr "רוחב מלא"

#: ../extensions/shortcodes/shortcodes/section/options.php:11
msgid "Background Color"
msgstr "צבע רקע"

#: ../extensions/shortcodes/shortcodes/section/options.php:12
msgid "Please select the background color"
msgstr "אנא בחר את צבע הרקע"

#: ../extensions/shortcodes/shortcodes/section/options.php:16
msgid "Background Image"
msgstr "תמונת רקע"

#: ../extensions/shortcodes/shortcodes/section/options.php:17
msgid "Please select the background image"
msgstr "אנא בחר את תמונת הרקע"

#: ../extensions/shortcodes/shortcodes/section/options.php:23
msgid "Background Video"
msgstr "סרטון רקע"

#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:12
msgid "Sections"
msgstr "אזורים"

#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:92
msgid "Save Section"
msgstr "שמור אזור"

#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:24
msgid "Creates a section"
msgstr "יצירת אזור"

#: ../extensions/shortcodes/shortcodes/tabs/config.php:9
msgid "Add some Tabs"
msgstr "הוסף תגיות"

#: ../extensions/shortcodes/shortcodes/tabs/options.php:9
msgid "Add/Edit Tab"
msgstr "הוסף/עדכן תגית"

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:203
#, php-format
msgid "Shortcode \"%s\" from %s was already defined at %s"
msgstr ""
"קוד קיצור \"% s\" מ-% s כבר הוגדר ב-% s\n"

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:264
#, php-format
msgid "Class file found for shortcode %s but no class %s found"
msgstr ""
"קובץ המחלקה נמצא עבור קוד קיצור % s אך לא נמצאו % s מחלקה\n"

#: ../extensions/builder/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:204
msgid "Builder"
msgstr "Builder"

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:306
msgid "Full Screen"
msgstr "מסך מלא"

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:307
msgid "Exit Full Screen"
msgstr "צא ממסך מלא"

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:334
msgid "Undo"
msgstr "בטל פעולה"

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:335
msgid "Redo"
msgstr "חזור על פעולה"

#: ../extensions/builder/includes/option-types/builder/includes/fullscreen.php:81
msgid "Preview Changes"
msgstr "שינויים קודמים"

#: ../extensions/builder/includes/option-types/builder/includes/templates/class-fw-ext-builder-templates.php:119
msgid "Templates"
msgstr "תבניות"

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:12
msgid "Full Templates"
msgstr "תבניות מלאות "

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:61
msgid "Save Full Template"
msgstr "שמור תבנית מלאה"

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:95
msgid "Save Builder Template"
msgstr "שמור תבנית בילדר"

#: ../extensions/social/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:252
msgid "Social"
msgstr "חברתי"

#: ../extensions/social/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:253
msgid ""
"Use this extension to configure all your social related APIs. Other "
"extensions will use the Social extension to connect to your social accounts."
msgstr ""
"השתמש בתוסף זה כדי להגדיר את כל ממשקי ה- API הקשורים לרשתות חברתיות. תוספים "
"אחרים ישתמשו בתוסף החברתי כדי להתחבר לחשבונות החברתיים שלך.\n"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:62
#: ../core/components/backend.php:584
msgid "Facebook"
msgstr "פייסבוק"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:66
#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:33
msgid "API Settings"
msgstr "הגדרות API"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:72
msgid "App ID/API Key:"
msgstr "מפתח ID/API עבור אפליקציה:"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:73
msgid "Enter Facebook App ID / API Key."
msgstr "הכנס מפתח  ID / API עבור אפליקציית פייסבוק."

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:78
msgid "App Secret:"
msgstr "קוד אפליקציה:"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:79
msgid "Enter Facebook App Secret."
msgstr "הזן קוד אפליקציית פייסבוק."

#: ../extensions/social/extensions/social-facebook/manifest.php:7
#: ../extensions/social/extensions/social-facebook/manifest.php:8
msgid "Social Facebook"
msgstr ""
"פייסבוק חברתי\n"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:29
#: ../core/components/backend.php:592
msgid "Twitter"
msgstr "טוויטר"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:39
msgid "Consumer Key"
msgstr ""
"מפתח הצרכן\n"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:40
msgid "Enter Twitter Consumer Key."
msgstr ""
"הזן את מפתח הצרכן של טוויטר.\n"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:45
msgid "Consumer Secret"
msgstr ""
"סוד צרכן\n"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:46
msgid "Enter Twitter App Secret."
msgstr "הזן קוד אפליקציית טוויטר."

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:51
msgid "Access Token"
msgstr ""
"אסימון גישה\n"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:52
msgid "Enter Twitter Access Token."
msgstr ""
"הזן אסימון גישה של Twitter.\n"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:57
msgid "Access Token Secret"
msgstr "גישה לקוד אסימון"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:58
msgid "Enter Twitter Access Token Secret."
msgstr "הכנס קוד גישה לטוויטר"

#: ../extensions/social/extensions/social-twitter/manifest.php:7
#: ../extensions/social/extensions/social-twitter/manifest.php:8
msgid "Social Twitter"
msgstr "טוויטר חברתי"

#: ../extensions/forms/class-fw-extension-forms.php:112
#: ../extensions/forms/class-fw-extension-forms.php:123
#: ../extensions/forms/class-fw-extension-forms.php:131
#: ../extensions/forms/class-fw-extension-forms.php:142
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:102
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:114
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:124
msgid "Unable to process the form"
msgstr ""
"לא ניתן לעבד את הטופס\n"

#: ../extensions/forms/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:228
msgid "Forms"
msgstr "טפסים"

#: ../extensions/forms/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:229
msgid ""
"This extension adds the possibility to create a contact form. Use the drag & "
"drop form builder to create any contact form you'll ever want or need."
msgstr ""
"תוסף זה מוסיף את האפשרות ליצור טופס יצירת קשר. השתמש ב-drag & drop של בונה "
"הטופס על מנת ליצור כל טופס יצירת קשר שאתה אי פעם רצית או צריך.\n"

#: ../extensions/forms/extensions/contact-forms/manifest.php:5
msgid "Contact Forms"
msgstr "טופס צור קשר"

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:134
msgid "Invalid destination email (please contact the site administrator)"
msgstr ""
"כתובת אתר יעד לא חוקית (צור קשר עם מנהל האתר)\n"

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:158
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:77
msgid "Message sent!"
msgstr "ההודעה נשלחה!"

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:164
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:85
msgid "Oops something went wrong."
msgstr "אופס, משהו השתבש."

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:190
msgid "Please configure the {mailer_link} extension."
msgstr ""
"הגדר את התוסף {mailer_link}.\n"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:8
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:107
msgid "Contact form"
msgstr "טופס צור קשר"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:9
msgid "Build contact forms"
msgstr "בנה טפסי צור קשר"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:15
msgid "Form Fields"
msgstr "שדות טופס"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:39
#: ../core/components/extensions/manager/views/extension.php:82
#: ../core/components/extensions/manager/views/extension-page-header.php:31
msgid "Settings"
msgstr "הגדרות"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:42
msgid "Options"
msgstr "אפשרויות"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:53
msgid "Subject Message"
msgstr ""
"נושא ההודעה\n"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:54
msgid "This text will be used as subject message for the email"
msgstr ""
"טקסט זה ישמש כהודעת נושא עבור הדוא\"ל\n"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:55
msgid "New message"
msgstr "הודעה חדשה"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:64
msgid "Submit Button"
msgstr "כפתור שלח"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:65
msgid "This text will appear in submit button"
msgstr "טקסט זה יופיע בכפתור שלח"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:66
msgid "Send"
msgstr "שלח"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:75
msgid "Success Message"
msgstr "הודעת הצלחה"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:76
msgid "This text will be displayed when the form will successfully send"
msgstr ""
"טקסט זה יוצג כאשר הטופס ישלח בהצלחה\n"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:83
msgid "Failure Message"
msgstr ""
"הודעת שגיאה\n"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:84
msgid "This text will be displayed when the form will fail to be sent"
msgstr ""
"טקסט זה יוצג כאשר הטופס לא יישלח\n"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:94
msgid "Email To"
msgstr "דו\"אל ל"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:95
msgid "We recommend you to use an email that you verify often"
msgstr ""
"אנו ממליצים לך להתשתמש בהודעת דוא\"ל שתאמת לעתים קרובות\n"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:96
msgid "The form will be sent to this email address."
msgstr ""
"הטופס יישלח לכתובת דוא\"ל זו.\n"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:52
msgid "Contact Form"
msgstr "טופס צור קשר"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:54
msgid "Configure Mailer"
msgstr "הגדר את שולח המייל"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:108
msgid "Add a Contact Form"
msgstr "הוסף טופס יצירת קשר"

#: ../extensions/forms/views/backend/submit-box-add.php:9
msgid "Note that the type can't be changed later."
msgstr ""
"שים לב שלא ניתן לשנות את הסוג מאוחר יותר.\n"

#: ../extensions/forms/views/backend/submit-box-add.php:11
msgid ""
"You will need to create a new form in order to have a different form type."
msgstr ""
"יהיה עליך ליצור טופס חדש כדי לקבל סוג טופס שונה.\n"

#: ../extensions/forms/views/backend/submit-box-add.php:20
#: ../extensions/forms/views/backend/submit-box-edit.php:16
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:14
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:31
#: ../extensions/media/extensions/slider/views/backend/submit-box-error.php:8
msgid "Delete Permanently"
msgstr ""
"מחק לצמיתות\n"

#: ../extensions/forms/views/backend/submit-box-add.php:22
#: ../extensions/forms/views/backend/submit-box-edit.php:18
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:16
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:33
#: ../extensions/media/extensions/slider/views/backend/submit-box-error.php:10
msgid "Move to Trash"
msgstr ""
"העבר לאשפה\n"

#: ../extensions/forms/views/backend/submit-box-add.php:33
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:35
msgid "Create"
msgstr "צור"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:24
msgid "Add a Recaptcha field"
msgstr ""
"הוסף שדה Recaptcha\n"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:26
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:78
msgid "Recaptcha"
msgstr "Recaptcha"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:59
msgid "Set site key"
msgstr "הגדר מפתח אתר"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:60
msgid "Set secret key"
msgstr "הגדר מפתח סודי"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:76
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:81
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:81
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:76
msgid "Enter field label (it will be displayed on the web site)"
msgstr ""
"הזן תווית שדה (היא תוצג באתר האינטרנט)\n"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:133
msgid "Security Code"
msgstr "קוד אבטחה"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:147
msgid "Could not validate the form"
msgstr ""
"לא ניתן לאמת את הטופס\n"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:148
msgid "Please fill the recaptcha"
msgstr ""
"נא למלא את recaptcha\n"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:37
msgid "Site key"
msgstr "מפתח אתר"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:38
msgid "Your website key. More on how to configure ReCaptcha"
msgstr ""
"מפתח האתר שלך. עוד על אופן הגדרת ReCaptcha\n"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:42
msgid "Secret key"
msgstr "מפתח סודי"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:43
msgid "Your secret key. More on how to configure ReCaptcha"
msgstr ""
"המפתח הסודי שלך. עוד על אופן הגדרת ReCaptcha\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:18
msgid "Add a Paragraph Text"
msgstr "הוסף טקסט פסקה"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:20
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:48
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:77
msgid "Paragraph Text"
msgstr "טקסט פסקה"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:49
msgid "Toggle mandatory field"
msgstr ""
"שדה חובה דו-מצבי\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:83
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:83
msgid "Mandatory Field"
msgstr ""
"שדה חובה\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:84
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:84
msgid "Make this field mandatory?"
msgstr "לעדכן כשדה חובה?"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:98
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:98
msgid "Placeholder"
msgstr "מציין מיקום"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:99
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:99
msgid "This text will be used as field placeholder"
msgstr ""
"טקסט זה ישמש כמציין מיקום של שדה\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:105
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:107
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:107
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:105
msgid "Default Value"
msgstr "ערך ברירת מחדל"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:106
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:108
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:108
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:106
msgid "This text will be used as field default value"
msgstr ""
"טקסט זה ישמש כערך ברירת המחדל של השדה\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:126
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:128
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:126
msgid "Restrictions"
msgstr "הגבלות"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:127
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:127
msgid "Set characters or words restrictions for this field"
msgstr ""
"הגדר תווים או הגבלות מילים עבור שדה זה\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:131
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:131
msgid "Characters"
msgstr "מאפיינים"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:132
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:132
msgid "Words"
msgstr "מילים"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:140
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:154
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:142
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:156
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:140
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:154
msgid "Min"
msgstr "מינימום"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:141
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:155
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:143
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:157
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:141
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:155
msgid "Minim value"
msgstr "ערך מינימלי"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:146
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:160
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:148
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:162
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:146
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:160
msgid "Max"
msgstr "מקסימום"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:147
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:161
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:149
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:163
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:147
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:161
msgid "Maxim value"
msgstr "ערך מקסימלי"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:178
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:180
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:121
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:137
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:137
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:114
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:121
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:178
msgid "Instructions for Users"
msgstr ""
"הוראות למשתמשים\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:179
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:181
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:122
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:138
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:138
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:115
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:122
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:179
msgid "The users will see these instructions in the tooltip near the field"
msgstr ""
"המשתמשים יראו הוראות אלה בהסבר ליד השדה\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:331
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:368
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:230
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:244
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:244
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:210
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:217
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:332
msgid "The {label} field is required"
msgstr ""
"השדה {label} הינו שדה נדרש\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:336
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:337
#, php-format
msgid "The {label} field must contain minimum %d character"
msgstr ""
"השדה {label} חייב להכיל מינימום % d תווים\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:341
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:342
#, php-format
msgid "The {label} field must contain minimum %d characters"
msgstr ""
"השדה {label} חייב להכיל % d תווים לפחות\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:346
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:347
#, php-format
msgid "The {label} field must contain maximum %d character"
msgstr ""
"השדה {label} חייב להכיל מקסימום % d תווים\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:351
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:352
#, php-format
msgid "The {label} field must contain maximum %d characters"
msgstr ""
"השדה {label} חייב להכיל % d תווים לכל היותר\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:356
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:357
#, php-format
msgid "The {label} field must contain minimum %d word"
msgstr ""
"השדה {label} חייב להכיל מינימום % d מילים\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:361
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:362
#, php-format
msgid "The {label} field must contain minimum %d words"
msgstr ""
"השדה {label} חייב להכיל % d מילים לפחות\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:366
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:367
#, php-format
msgid "The {label} field must contain maximum %d word"
msgstr ""
"השדה {label} חייב להכיל מקסימום % d מילים\n"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:371
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:372
#, php-format
msgid "The {label} field must contain maximum %d words"
msgstr ""
"השדה {label} חייב להכיל מקסימום % d מילים\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:20
msgid "Add a Number field"
msgstr ""
"הוסף שדה מספר\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:79
msgid "Number"
msgstr "מספר"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:129
msgid "Set digits or values restrictions of this field"
msgstr ""
"הגדר מגבלות ספרות או ערכים של שדה זה\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:133
msgid "Digits"
msgstr "ספרות"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:134
msgid "Value"
msgstr "ערך"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:363
msgid "The {label} field must be a valid number"
msgstr ""
"השדה {label} חייב להיות מספר חוקי\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:373
#, php-format
msgid "The {label} field must have minimum %d digit"
msgstr ""
"השדה {label} חייב לכלול % d ספרות לפחות\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:378
#, php-format
msgid "The {label} field must have minimum %d digits"
msgstr ""
"השדה {label} חייב לכלול % d ספרות לפחות\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:383
#, php-format
msgid "The {label} field must have maximum %d digit"
msgstr ""
"השדה {label} חייב להכיל % d ספרות לכל היותר\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:388
#, php-format
msgid "The {label} field must have maximum %d digits"
msgstr ""
"השדה {label} חייב להכיל % d ספרות לכל היותר\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:393
#, php-format
msgid "The {label} field minimum value must be %s"
msgstr ""
"הערך המינימלי של השדה {label} חייב להיות % s\n"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:398
#, php-format
msgid "The {label} field maximum value must be %s"
msgstr ""
"הערך המקסימלי של השדה {label} חייב להיות % s\n"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:18
msgid "Add a Dropdown"
msgstr ""
"הוסף תפריט נפתח\n"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:79
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:164
msgid "Dropdown"
msgstr "תפריט נפתח"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:103
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:103
msgid "Choices"
msgstr "אפשרויות בחירה"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:104
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:104
msgid "Add choice"
msgstr "הוסף בחירה"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:110
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:113
msgid "Randomize"
msgstr "אקראי"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:111
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:114
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:114
msgid "Do you want choices to be displayed in random order?"
msgstr ""
"האם ברצונך להציג את האפשרויות בסדר אקראי?\n"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:235
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:249
msgid "{label}: Submitted data contains not existing choice"
msgstr ""
"{label}: הנתונים שנשלחו לא מכילים אפשרות בחירה קיימת\n"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:38
msgid "Edit Title"
msgstr ""
"ערוך כותרת\n"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:39
msgid "Edit Subtitle"
msgstr "ערוך כותרת משנה"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:57
msgid "The title will be displayed on contact form header"
msgstr ""
"הכותרת תוצג בראש טופס יצירת קשר\n"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:62
#: ../extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:7
msgid "Subtitle"
msgstr "כותרת משנה"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:63
msgid "The form header subtitle text"
msgstr "טקסט כותרת המשנה בראש הטופס"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:18
msgid "Add a Single Choice field"
msgstr ""
"הוסף שדה בחירה בודדת\n"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:56
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:56
msgid "{x} More"
msgstr "עוד {x}"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:113
msgid "Randomize?"
msgstr ""
"אקראי?\n"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:124
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:124
msgid "Field Layout"
msgstr "פריסת שדה"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:125
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:125
msgid "Select choice display layout"
msgstr ""
"בחר פריסת תצוגה נבחרת\n"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:127
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:127
msgid "One column"
msgstr "טור אחד"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:128
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:128
msgid "Two columns"
msgstr "שני טורים"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:129
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:129
msgid "Three columns"
msgstr "שלושה טורים"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:130
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:130
msgid "Side by side"
msgstr "זה לצד זה"

#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:18
msgid "Add a Multiple Choices field"
msgstr "הוסף שדה בחירות מרובות"

#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:249
msgid "{label}: Submitted data contains not existing choices"
msgstr ""
"{תגית}: הנתונים שנשלחו מכילים אפשרויות שאינן קיימות\n"

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:18
msgid "Add an Email field"
msgstr "הוסף שדה דוא\"ל"

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:79
msgid "Email"
msgstr "דוא\"ל"

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:215
msgid "The {label} field must contain a valid email"
msgstr ""
"השדה {תגיד} חייב להכיל כתובת דוא\"ל חוקית\n"

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:18
msgid "Add a Website field"
msgstr ""
"הוסף שדה אתר\n"

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:79
msgid "Website"
msgstr "אתר"

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:222
msgid "The {label} field must be a valid website name"
msgstr ""
"השדה {תגית} חייב להיות שם אתר חוקי\n"

#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:18
msgid "Add a Single Line Text"
msgstr ""
"הוסף טקסט בשורה אחת\n"

#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:20
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:47
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:77
msgid "Single Line Text"
msgstr "טקסט בשורה אחת"

#: ../extensions/breadcrumbs/settings-options.php:17
msgid "Text for Homepage"
msgstr "טקסט לעמוד הבית"

#: ../extensions/breadcrumbs/settings-options.php:18
msgid "The homepage anchor will have this text"
msgstr ""
"עוגן דף הבית יהיה טקסט זה\n"

#: ../extensions/breadcrumbs/settings-options.php:23
msgid "Text for Blog Page"
msgstr "טקסט לעמוד בלוג"

#: ../extensions/breadcrumbs/settings-options.php:24
msgid ""
"The blog page anchor will have this text. In case homepage will be set as "
"blog page, will be taken the homepage text"
msgstr ""
"עוגן עמוד הבלוג יהיה טקסט זה. במקרה שעמוד הבית יוגדר כעמוד הבלוג, ישויך אליו "
"הטקסט בדף הבית\n"

#: ../extensions/breadcrumbs/settings-options.php:29
msgid "Text for 404 Page"
msgstr "טקסט עבור עמוד 404"

#: ../extensions/breadcrumbs/settings-options.php:30
msgid "The 404 anchor will have this text"
msgstr ""
"העוגן עבור 404 יהיה טקסט זה\n"

#: ../extensions/breadcrumbs/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:108
msgid "Breadcrumbs"
msgstr ""
"פרורי לחם\n"

#: ../extensions/breadcrumbs/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:109
msgid ""
"Creates a simplified navigation menu for the pages that can be placed "
"anywhere in the theme. This will make navigating the website much easier."
msgstr ""
"יצירת תפריט ניווט פשוט עבור העמודים שניתן להציב בכל מקום בתבנית. זה יגרום "
"לנווט באתר בצורה הרבה יותר קלה.\n"

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:11
msgid "404 Not found"
msgstr "404 לא נמצא"

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:124
msgid "Searching for:"
msgstr ""
"מחפש אחר:\n"

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:248
#: ../includes/option-types/multi-select/class-fw-option-type-multi-select.php:472
msgid "No title"
msgstr "אין כותרת"

#: ../extensions/events/class-fw-extension-events.php:89
msgid "Create a event item"
msgstr "צור פריט אירוע"

#: ../extensions/events/class-fw-extension-events.php:186
msgid "Date"
msgstr "תאריך"

#: ../extensions/events/class-fw-extension-events.php:204
msgid "Event Options"
msgstr "אפשרויות אירוע"

#: ../extensions/events/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:181
msgid ""
"This extension adds a fully fledged Events module to your theme. It comes "
"with built in pages that contain a calendar where events can be added."
msgstr ""
"תוסף זה מוסיף מודול אירועים שלם לתבנית שלך. הוא מגיע עם עמודים מובנים "
"הכוללים לוח שנה שבו ניתן להוסיף אירועים.\n"

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:81
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:189
msgid "Event Categories"
msgstr "קטגוריות אירוע"

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:82
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:190
msgid "Select an event category"
msgstr "בחר קטגוריית אירוע"

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:84
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:192
msgid "All Events"
msgstr "כל האירועים"

#: ../extensions/events/extensions/events-tags/manifest.php:7
msgid "Event-search-tags"
msgstr "תגיות חיפוש אירוע"

#: ../extensions/events/extensions/events-tags/manifest.php:8
msgid "Connect extension event with shortcodes map & calendar"
msgstr ""
"התחבר לאירוע מורחב באמצעות קוד קיצור של מפה & לוח שנה\n"

#: ../extensions/events/views/content.php:16
msgid "Google Calendar"
msgstr "יומן גוגל"

#: ../extensions/events/views/content.php:17
msgid "Ical Export"
msgstr "ייצוא Ical "

#: ../extensions/events/views/content.php:20
msgid "Start"
msgstr "התחלה"

#: ../extensions/events/views/content.php:21
msgid "End"
msgstr "סיום"

#: ../extensions/events/views/content.php:25
msgid "Speakers"
msgstr "דוברים"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:37
msgid "Event Location"
msgstr "מיקום אירוע"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:39
msgid "Where does the event take place?"
msgstr "היכן האירוע עומד להתקיים?"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:43
msgid "All Day Event?"
msgstr "אירוע יום שלם?"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:44
msgid "Is your event an all day event?"
msgstr "האם האירוע שלך הוא לאורך יום שלם?"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:67
msgid "Start & End of Event"
msgstr "התחלה וסוף האירוע"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:68
msgid "Set start and end events datetime"
msgstr "הגדר התחלה וסיום מועד אירועים"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:97
msgid "Associated User"
msgstr ""
"משתמש משויך\n"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:99
msgid "Link this event to a specific user"
msgstr ""
"קשר אירוע זה למשתמש ספציפי\n"

#: ../extensions/sidebars/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:132
msgid "Sidebars"
msgstr "סרגל צדי"

#: ../extensions/sidebars/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:133
msgid ""
"Brings a new layer of customization freedom to your website by letting you "
"add more than one sidebar to a page, or different sidebars on different "
"pages."
msgstr ""
"מביא שכבה חדשה עם חופש בהתאמה אישית עבור האתר שלך, על ידי אפשרות להוסיף יותר "
"מסרגל צד אחד לעמוד, או סרגלי צד שונים עבור עמודים שונים.\n"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:131
msgid "No matches found"
msgstr "לא נמצאו התאמות"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:139
msgid "Do you realy want to change without saving?"
msgstr ""
"האם אתה באמת רוצה לשנות ללא שמירה?\n"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:141
msgid "Missing ID. Check that you provided all mandatory data."
msgstr ""
"מזהה חסר. ודא שסיפקת את כל נתוני החובה.\n"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:142
#: ../extensions/sidebars/views/backend-main-view.php:27
msgid "Created"
msgstr "נוצר"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:143
msgid "(For Grouped Pages)"
msgstr "(עבור קבוצת עמודים)"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:144
msgid "(For Specific Pages)"
msgstr "(עבור עמודים מסויימים)"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:145
msgid "No sidebar name specified"
msgstr "לא צויין שם סרגל צד"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:146
msgid "Sidebar Name"
msgstr "שם סרגל צד"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:147
msgid "New Sidebar"
msgstr "סרגל צד חדש"

#: ../extensions/sidebars/views/frontend-no-widgets.php:5
msgid "Widgets Page"
msgstr "עמוד ווידג'ט"

#: ../extensions/sidebars/views/backend-tab-specific.php:5
msgid "For specific"
msgstr "עבור מסויים"

#: ../extensions/sidebars/views/backend-tab-specific.php:23
msgid "Type to search ..."
msgstr ""
"הקלד לחיפוש ...\n"

#: ../extensions/sidebars/views/backend-tab-specific.php:30
msgid "Search for a specific page you want to set a sidebar for"
msgstr ""
"חפש עמוד מסוים שברצונך להגדיר עבורו סרגל צד\n"

#: ../extensions/sidebars/views/backend-tab-grouped.php:6
msgid "For group"
msgstr "עבור קבוצה"

#: ../extensions/sidebars/views/backend-tab-grouped.php:25
msgid "Select group of pages you want to set a sidebar for."
msgstr "בחר קבוצת עמודים שתרצה להגדיר עבורם סרגל צד"

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:18
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:60
msgid "Choose the position for your sidebar(s)"
msgstr "בחר את מיקום סרגל הצד שלך (s)."

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:43
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:40
msgid "Add Sidebar"
msgstr "הוסף סרגל צד"

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:4
msgid "Sidebars for"
msgstr "סרגל צד עבור"

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Specific Page"
msgstr "עבור עמוד מסויים"

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Grouped Page"
msgstr "עבור קבוצת עמודים"

#: ../extensions/sidebars/views/backend-main-view.php:11
#: ../extensions/sidebars/views/backend-main-view.php:15
msgid "Manage Sidebars"
msgstr "ניהול סרגל צד"

#: ../extensions/sidebars/views/backend-main-view.php:18
msgid ""
"Use this section to create and/or set different sidebar(s) for different "
"page(s)"
msgstr ""
"השתמש בקטע זה כדי ליצור ו/או להגדיר סרגל צדדי שונה עבור עמוד/ים שונים\n"

#: ../extensions/sidebars/views/backend-main-view.php:24
msgid "For Grouped Pages"
msgstr "עבור קבוצת עמודים"

#: ../extensions/sidebars/views/backend-main-view.php:25
msgid "For Specific Pages"
msgstr "עבור עמוד מסויים"

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:32
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:121
msgid "Select sidebar you wish to replace."
msgstr "בחר סרגל צד שאותו תרצה להחליף."

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:241
msgid "No sidebar name specified."
msgstr "לא צוין שם עבור סרגל צד"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:270
msgid "Dynamic sidebar doesn't exixt"
msgstr "סרגל צד דינמי לא קיים"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:277
msgid ""
"The placeholder can't be deleted because it is used in one of sidebars below."
msgstr ""
"לא ניתן למחוק את מציין המיקום משום שנעשה בו שימוש באחד מסרגלי צד אלו.\n"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:280
msgid ""
"Please replace it first so that you will not have visual gaps in your layout."
msgstr ""
"אנא החלף תחילה על מנת שלא תתקל בפערים חזותיים בפריסה שלך.\n"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:293
msgid "Successfully removed"
msgstr "הוסר בהצלחה"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:543
msgid "Default for all pages"
msgstr "ברירת מחדל עבור כל העמודים"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:613
msgid " (no title)"
msgstr "(אין כותרת)"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:780
msgid "Error: Type or sub_type error"
msgstr ""
"שגיאה: סוג או תת-סוג שגיאה\n"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:838
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:869
msgid "Error: Sidebars not set"
msgstr "שגיאה: סרגלי צד לא הוגדרו."

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:865
msgid "Error: Position doesn't exists. Please check config file."
msgstr ""
"שגיאה: המיקום אינו קיים. אנא בדוק את קובץ ההגדרות.\n"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:55
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:253
msgid "Page"
msgstr "עמוד"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:56
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:153
msgid "Pages"
msgstr "עמודים"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:61
msgid "Portfolio Project"
msgstr "פרוייקט תיק עבודות"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:62
msgid "Portfolio Projects"
msgstr "פרוייקטים בתיק עבדות"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:73
msgid "Blog Category"
msgstr "קטגוריית בלוג"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:79
msgid "Portfolio Category"
msgstr "קטגוריית תיק עבודות"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:85
msgid "Home Page"
msgstr "עמוד הבית"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:90
msgid "Search Page"
msgstr "חפש עמוד"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:95
msgid "404 Page"
msgstr "עמוד 404"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:100
msgid "Author Page"
msgstr "מחבר העמוד"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:105
msgid "Archive Page"
msgstr "עמוד ארכיון"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:149
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:308
msgid "All Pages"
msgstr "כל העמודים"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:184
msgid "Others"
msgstr "אחרים"

#: ../extensions/megamenu/manifest.php:7
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:64
#: ../core/components/extensions/manager/available-extensions.php:60
msgid "Mega Menu"
msgstr "מגה תפריט"

#: ../extensions/megamenu/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:61
msgid ""
"The Mega Menu extension adds a user-friendly drop down menu that will let "
"you easily create highly customized menu configurations."
msgstr ""
"המגה תפריט המורחב מוסיף תפריט ידידותי למשתמש אשר מאפשר לך ליצור בקלות תצורות "
"שונות של תפריט מותאם אישית.\n"

#: ../extensions/megamenu/class-fw-extension-megamenu.php:58
msgid "Select Icon"
msgstr "בחר אייקון"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:44
#, php-format
msgid "%s (Invalid)"
msgstr ""
"%s (לא חוקי)\n"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:48
#, php-format
msgid "%s (Pending)"
msgstr ""
"%s (בהמתנה)\n"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:61
msgid "sub item"
msgstr ""
"פריט משנה\n"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:97
msgid "Edit Menu Item"
msgstr "עדכן פריט בתפריט"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:108
msgid "URL"
msgstr "URL"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:117
msgid "Navigation Label"
msgstr "תגית ניווט"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:125
msgid "Title Attribute"
msgstr ""
"תכונת כותרת\n"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:134
msgid "Open link in a new window/tab"
msgstr "פתח קישור בחלון/טאב חדש"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:141
msgid "CSS Classes (optional)"
msgstr "CSS Classes (אופציונלי)"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:149
msgid "Link Relationship (XFN)"
msgstr "יחס קישורים (XFN)"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:157
msgid "Mega Menu Column Title"
msgstr "כותרת עמודת מגה תפריט"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:158
msgid "Item Title"
msgstr ""
"כותרת פריט\n"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:166
msgid "Hide"
msgstr "הסתר"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:172
msgid "This column should start a new row"
msgstr ""
"עמודה זו צריכה להתחיל שורה חדשה\n"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:178
msgid "Description (HTML)"
msgstr "תיאור (HTML)"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:181
msgid ""
"The description will be displayed in the menu if the current theme supports "
"it."
msgstr ""
"התיאור יוצג בתפריט במידה והתבנית הנוכחית תומכת בכך.\n"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:190
msgid "Add Icon"
msgstr "הוסף אייקון"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:191
msgid "Edit Icon"
msgstr "ערוך אייקון"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:205
msgid "Use as Mega Menu"
msgstr "השתמש כמגה תפריט"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:211
msgid "Move"
msgstr "העבר"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:212
msgid "Up one"
msgstr "אחד למעלה"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:213
msgid "Down one"
msgstr "אחד למטה"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:216
msgid "To the top"
msgstr "לעליון"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:225
#, php-format
msgid "Original: %s"
msgstr ""
"מקורי: % s\n"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:239
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:60
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2282
msgid "Cancel"
msgstr "ביטול"

#: ../extensions/backups/class-fw-extension-backups.php:299
#: ../extensions/backups/class-fw-extension-backups.php:366
msgid "File not specified"
msgstr "לא נבחר קובץ"

#: ../extensions/backups/class-fw-extension-backups.php:399
#: ../extensions/backups/class-fw-extension-backups.php:400
#: ../core/components/extensions/manager/available-extensions.php:156
msgid "Backup"
msgstr "גיבוי"

#: ../extensions/backups/class-fw-extension-backups.php:554
#: ../extensions/backups/class-fw-extension-backups.php:568
msgid "Access Denied"
msgstr ""
"גישה נדחתה\n"

#: ../extensions/backups/class-fw-extension-backups.php:561
msgid "Archive not found"
msgstr "ארכיון לא נמצא"

#: ../extensions/backups/class-fw-extension-backups.php:575
#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:202
msgid "Failed to open file"
msgstr ""
"נכשל בפתיחת הקובץ\n"

#: ../extensions/backups/helpers.php:37
msgid "Invalid JSON response"
msgstr ""
"תגובת JSON לא חוקית\n"

#: ../extensions/backups/helpers.php:152
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:137
#, php-format
msgid "Failed to copy: %s"
msgstr ""
"נכשל בהעתקה: % s\n"

#: ../extensions/backups/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:168
msgid "Backup & Demo Content"
msgstr ""
"גיבוי & הדגמת תוכן\n"

#: ../extensions/backups/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:169
msgid ""
"This extension lets you create an automated backup schedule, import demo "
"content or even create a demo content archive for migration purposes."
msgstr ""
"תוסף זה מאפשר לך ליצור תזמון גיבוי אוטומטי, לייבא תוכן דמה או אפילו ליצור "
"ארכיון תוכן דמה למטרות העברה.\n"

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:97
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:98
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:385
msgid "Demo Content Install"
msgstr "התקנת תוכן דמה"

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:282
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:329
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:362
msgid "Forbidden"
msgstr ""
"אסור\n"

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:342
msgid "Invalid demo"
msgstr ""
"הדגמה לא חוקית\n"

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:349
msgid "A content install is currently running"
msgstr "A content install is currently running"

#: ../extensions/backups/extensions/backups-demo/views/page.php:28
#: ../extensions/backups/extensions/backups-demo/views/page.php:39
#: ../extensions/backups/views/page.php:17
#: ../extensions/backups/views/page.php:28
msgid "Important"
msgstr "חשוב"

#: ../extensions/backups/extensions/backups-demo/views/page.php:30
#: ../extensions/backups/views/page.php:19
#, php-format
msgid "You need to activate %s."
msgstr ""
"עליך להפעיל % s.\n"

#: ../extensions/backups/extensions/backups-demo/views/page.php:31
#: ../extensions/backups/views/page.php:20
msgid "zip extension"
msgstr "הרחבת ZIP"

#: ../extensions/backups/views/page.php:70
msgid "Archives"
msgstr "ארכיון"

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../extensions/backups/includes/module/schedule/settings-options.php:76
msgid "Full Backup"
msgstr "גיבוי מלא"

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../extensions/backups/includes/module/schedule/settings-options.php:85
msgid "Content Backup"
msgstr ""
"גיבוי תוכן\n"

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:88
msgid ""
"Warning! \n"
"You are about to delete a backup, it will be lost forever. \n"
"Are you sure?"
msgstr ""
"\n"
"אַזהָרָה!\n"
"אתה עומד למחוק גיבוי, הוא יאבד לצמיתות.\n"
"האם אתה בטוח?"

#: ../extensions/backups/includes/module/schedule/settings-options.php:16
msgid "Interval"
msgstr "הפסקה"

#: ../extensions/backups/includes/module/schedule/settings-options.php:20
#: ../core/components/extensions/manager/views/extension.php:180
msgid "Disabled"
msgstr "לקוי"

#: ../extensions/backups/includes/module/schedule/settings-options.php:25
msgid "Select how often do you want to backup your website."
msgstr ""
"בחר באיזו תדירות ברצונך לגבות את האתר שלך.\n"

#: ../extensions/backups/includes/module/schedule/settings-options.php:32
#: ../extensions/backups/includes/module/schedule/settings-options.php:45
#: ../extensions/backups/includes/module/schedule/settings-options.php:58
msgid "Age Limit"
msgstr ""
"מגבלת גיל\n"

#: ../extensions/backups/includes/module/schedule/settings-options.php:33
msgid "Age limit of backups in months"
msgstr ""
"מגבלת גיל של גיבויים בחודשים\n"

#: ../extensions/backups/includes/module/schedule/settings-options.php:46
msgid "Age limit of backups in weeks"
msgstr ""
"מגבלת גיל של גיבויים בשבועות\n"

#: ../extensions/backups/includes/module/schedule/settings-options.php:59
msgid "Age limit of backups in days"
msgstr ""
"מגבלת גיל של גיבויים בימים\n"

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:186
msgid "Backup Schedule"
msgstr ""
"לוח זמנים לגיבוי\n"

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:288
msgid "Once Weekly"
msgstr "אחת לשבוע"

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:292
msgid "Once a month"
msgstr "אחת לחודש"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:139
msgid "undefined"
msgstr ""
"לא מוגדר\n"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:279
msgid "Task type not registered"
msgstr ""
"סוג המשימה אינו רשום\n"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:286
msgid "Execution stopped (next step did not started)"
msgstr ""
"הביצוע הופסק (השלב ​​הבא לא התחיל)\n"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:306
msgid "Timed out"
msgstr ""
"נגמר הזמן\n"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:366
msgid "Invalid execution end time"
msgstr ""
"זמן סיום לא חוקי של הביצוע\n"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:537
msgid "Invalid execution result"
msgstr ""
"תוצאה לא חוקית של ביצוע\n"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:639
msgid "Invalid token"
msgstr ""
"אסימון לא חוקי\n"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:658
msgid "Invalid tasks hash"
msgstr ""
"חשיפת משימות לא חוקית\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-restore.php:9
msgid "Image Sizes Restore"
msgstr ""
"שחזור גודל תמונה \n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:12
msgid "Archive Unzip"
msgstr "הוצאה מכיווץ של ארכיון"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:39
msgid "Zip file not specified"
msgstr ""
"קובץ zip לא צוין\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:47
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:50
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:49
#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:114
msgid "Destination dir not specified"
msgstr ""
"יעד dir לא צוין\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:63
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:59
#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:50
msgid "Zip extension missing"
msgstr ""
"סיומת Zip חסרה\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:71
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:70
#, php-format
msgid "Cannot open zip (Error code: %s)"
msgstr ""
"לא ניתן לפתוח zip (קוד שגיאה: % s)\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:38
msgid "Database export"
msgstr ""
"ייצוא מסד נתונים\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:71
msgid "Database table disappeared"
msgstr ""
"טבלת מסד הנתונים נעלמה\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:91
msgid "Cannot create file"
msgstr ""
"לא יכול ליצור קובץ\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:97
msgid "Cannot reopen file"
msgstr ""
"אין אפשרות לפתוח את הקובץ מחדש\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:211
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:267
msgid "Cannot get next database table"
msgstr ""
"לא ניתן להציג את טבלת מסד הנתונים הבאה\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:9
msgid "Files Restore"
msgstr ""
"שחזור קבצים\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:116
msgid "No filesystem access, credentials required"
msgstr ""
"אין גישה למערכת הקבצים, נדרשים אישורים\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:120
msgid "No filesystem access, invalid credentials"
msgstr ""
"אין גישה למערכת קבצים, אישורים לא חוקיים\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:192
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:317
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:341
#, php-format
msgid "Cannot convert Filesystem path: %s"
msgstr ""
"לא ניתן להמיר נתיב מערכת קבצים: % s\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:234
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:288
#, php-format
msgid "Failed to remove file: %s"
msgstr "הסרת הקובץ נכשלה: % s "

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:376
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:444
#, php-format
msgid "Failed to copy file: %s"
msgstr "נכשל בהעתקת הקובץ: %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:12
msgid "Archive Zip"
msgstr "ארכיון Zip"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:97
msgid "Cannot close the zip file"
msgstr ""
"לא ניתן לסגור את הקובץ zip\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:9
msgid "Files Export"
msgstr ""
"ייצוא קבצים\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:33
msgid "Destination not specified"
msgstr ""
"יעד לא צוין\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:12
msgid "Database restore"
msgstr ""
"שחזור מסד נתונים\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:63
msgid "Database file not found"
msgstr ""
"קובץ מסד הנתונים לא נמצא\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:111
#, php-format
msgid "Cannot drop temporary table: %s"
msgstr ""
"אין אפשרות להוריד טבלה זמנית: % s\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:129
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:285
msgid "Cannot open db file"
msgstr ""
"אין אפשרות לפתוח קובץ db\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:138
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:294
msgid "Cannot move cursor in db file"
msgstr ""
"לא ניתן להעביר סמן בקובץ db\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:195
msgid "Required params not found"
msgstr ""
"פרמטרים נדרשים לא נמצאו\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:232
msgid "Cannot do full db restore because backup is missing some tables"
msgstr ""
"לא ניתן לבצע שחזור DB מלא, מכיוון שחסרות טבלאות עבור הגיבוי\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:606
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:628
#, php-format
msgid "Failed insert row from line %d"
msgstr ""
"הוספת שורה נכשלה משורה % d\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:649
msgid "Cannot read line from db file"
msgstr ""
"לא ניתן לקרוא שורה מקובץ db\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:688
msgid "Failed to restore options keeping step"
msgstr ""
"נכשל בשחזור אפשרויות שמירת שלב\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:723
#, php-format
msgid "Failed to keep option: %s"
msgstr ""
"שמירת האפשרות נכשלה: % s \n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:774
msgid "Tables drop failed"
msgstr "הורדת טבלאות נכשלה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:787
msgid "Tables rename failed."
msgstr ""
"שינוי שם הטבלאות נכשל.\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:799
#, php-format
msgid "Invalid sub task %s"
msgstr ""
"משימת משנה לא חוקית % s\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:12
msgid "Directory Cleanup"
msgstr "ניקיון ספריה"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:30
msgid "Cannot remove directory"
msgstr ""
"לא ניתן להסיר את הספרייה\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:37
msgid "Cannot create directory"
msgstr ""
"לא ניתן ליצור ספריה\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:55
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:70
#, php-format
msgid "Cannot create file: %s"
msgstr ""
"אין אפשרות ליצור קובץ: % s\n"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:9
msgid "Image Sizes Remove"
msgstr "גדלי תמונה הוסרו"

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:18
msgid "Download"
msgstr "הורדה"

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:132
#, php-format
msgid "Invalid type: %s"
msgstr ""
"סוג לא חוקי: % s\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:9
msgid "Local Download"
msgstr ""
"הורדה מקומית\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:24
msgid "Source not specified"
msgstr ""
"המקור לא צוין\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:29
msgid "Invalid source"
msgstr "Invalid source"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:38
msgid "Invalid source type"
msgstr ""
"סוג מקור לא חוקי\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:59
#, php-format
msgid "Cannot open zip: %s"
msgstr "zip לא ניתן לפתיחה"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:13
msgid "Downloading..."
msgstr "מוריד..."

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:16
msgid "Download finished. Doing unzip..."
msgstr ""
"ההורדה הסתיימה. מבצע דחיסה ...\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:19
#, php-format
msgid "Downloading... %s of %s"
msgstr ""
"מוריד ...% s מתוך % s\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:24
#, php-format
msgid "Downloading... %s"
msgstr ""
"מוריד ... % s\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:67
msgid "Url not specified"
msgstr "Url לא צוין "

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:72
msgid "Invalid url"
msgstr "url לא קיים"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:79
msgid "File id not specified"
msgstr ""
"לא צוין מזהה קובץ\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:84
msgid "Invalid file id"
msgstr "כתובת קובץ לא קיימת"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:163
msgid "Invalid byte position"
msgstr "מיקום ביט לא חוקי"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:168
msgid "Empty response body"
msgstr ""
"גוף מותאם ריק\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:188
msgid "File ended without content"
msgstr ""
"הקובץ הסתיים ללא תוכן\n"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:213
msgid "Failed to write data to file"
msgstr ""
"נכשל בכתיבת נתונים לקובץ\n"

#: ../extensions/translation/settings-options.php:11
msgid "Default Language"
msgstr "שפת ברירת מחדל"

#: ../extensions/translation/settings-options.php:12
msgid "This is the default language of your website."
msgstr ""
"זוהי שפת ברירת המחדל של אתר האינטרנט שלך.\n"

#: ../extensions/translation/settings-options.php:19
msgid "Translate to"
msgstr "תרגם ל"

#: ../extensions/translation/settings-options.php:20
msgid "Choose the languages you want your website translated to."
msgstr ""
"בחר את השפות שלהן תרצה לתרגם את האתר שלך.\n"

#: ../extensions/translation/settings-options.php:27
msgid "Convert data"
msgstr ""
"המרת נתונים\n"

#: ../extensions/translation/settings-options.php:28
msgid ""
"Set to default language the posts, pages categories or tags that don't have "
"a language set ?"
msgstr ""
"להגדיר את שפת ברירת המחדל עבור הפוסטים, קטגוריות, דפים או תגיות שהם ללא שפה?"
"\n"

#: ../extensions/translation/manifest.php:7
#: ../extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:336
#: ../core/components/extensions/manager/available-extensions.php:264
msgid "Translations"
msgstr "תרגומים"

#: ../extensions/translation/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:265
msgid ""
"This extension lets you translate your website in any language or even add "
"multiple languages for your users to change at their will from the front-end."
msgstr ""
"תוסף זה מאפשר לך לתרגם את האתר שלך לכל שפה, או אפילו להוסיף מספר שפות כדי "
"שהמשתמשים שלך יוכלו לשנות את בחירת השפה בהתאם לרצונם.\n"

#: ../extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:114
msgid "All Languages"
msgstr "כל השפות"

#: ../extensions/translation/extensions/translate-terms/manifest.php:7
msgid "Translate Terms"
msgstr "תרגם מונחים"

#: ../extensions/translation/extensions/translate-terms/manifest.php:8
msgid "This extension translate terms"
msgstr ""
"תוסף זה מתרגם מונחים\n"

#: ../extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:341
msgid "Language of this post"
msgstr "השפה עבור פוסט זה"

#: ../extensions/translation/extensions/translate-posts/manifest.php:7
msgid "Translate Posts"
msgstr "תרגם פוסטים"

#: ../extensions/translation/extensions/translate-posts/manifest.php:8
msgid "This extension translate posts"
msgstr ""
"תוסף זה מתרגם פוסטים\n"

#: ../extensions/translation/extensions/translate-widgets/manifest.php:7
msgid "Translate Widgets"
msgstr ""
"תרגם ווידג'טים\n"

#: ../extensions/translation/extensions/translate-widgets/manifest.php:8
msgid "This extension translate Widgets"
msgstr ""
"תוסף זה מתרגם ווידג'טים\n"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:10
msgid "Language Switcher"
msgstr ""
"מחליף שפה\n"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:11
msgid "A Language Switcher Widget"
msgstr "ווידג'ט מחליף שפה"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:42
msgid "New title"
msgstr "כותרת חדשה"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:45
#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:49
msgid "Title:"
msgstr "כותרת:"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:14
msgid "A calendar of your site&#8217;s Posts."
msgstr "לוח השנה של הפוסטים באתר שלך"

#: ../extensions/translation/extensions/translate-menus/manifest.php:7
msgid "Translate Menus"
msgstr "תרגם תפריטים"

#: ../extensions/translation/extensions/translate-menus/manifest.php:8
msgid "This extension translate menus"
msgstr ""
"תוסף זה מתרגם תפריטים\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:211
msgid "Slider Design"
msgstr "עיצוב סליידר"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:213
msgid "Number of Images"
msgstr "מספר התמונות"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:232
msgid "Custom field updated."
msgstr ""
"שדה מותאם עודכן.\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:233
msgid "Custom field deleted."
msgstr ""
"שדה מותאם אישית נמחק.\n"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:238
msgid "Page saved."
msgstr "עמוד נשמר."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:272
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:52
msgid "Publish"
msgstr "פרסם"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:361
msgid "Slider Configuration"
msgstr "תצורת סליידר"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:375
msgid "Slider Title"
msgstr "כותרת סליידר"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:433
msgid "Slider Settings"
msgstr "הגדרות סליידר"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:542
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:94
msgid "(no title)"
msgstr "(ללא כותרת)"

#: ../extensions/media/extensions/slider/posts.php:6
#: ../extensions/media/extensions/slider/posts.php:12
#: ../extensions/media/extensions/slider/posts.php:18
#: ../extensions/media/extensions/slider/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:12
msgid "Sliders"
msgstr "סליידרים"

#: ../extensions/media/extensions/slider/posts.php:7
#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:8
msgid "Slider"
msgstr "סליידר"

#: ../extensions/media/extensions/slider/posts.php:9
msgid "Add New Slider"
msgstr "הוסף סליידר חדש"

#: ../extensions/media/extensions/slider/posts.php:10
msgid "Edit Slider"
msgstr "עדכן סליידר"

#: ../extensions/media/extensions/slider/posts.php:11
msgid "New Slider"
msgstr "סליידר חדש"

#: ../extensions/media/extensions/slider/posts.php:13
msgid "View Slider"
msgstr "הצג סליידר"

#: ../extensions/media/extensions/slider/posts.php:14
msgid "Search Sliders"
msgstr "חפש סליידרים"

#: ../extensions/media/extensions/slider/posts.php:15
msgid "No Sliders found"
msgstr "לא נמצאו סליידרים"

#: ../extensions/media/extensions/slider/posts.php:16
msgid "No Sliders found in Trash"
msgstr "לא נמצאו סליידרים באשפה"

#: ../extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:6
msgid "Nivo Slider"
msgstr ""
"ניבו סליידר\n"

#: ../extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:6
msgid "Owl Slider"
msgstr "סליידר ינשוף"

#: ../extensions/media/extensions/slider/extensions/bx-slider/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/bx-slider/manifest.php:6
msgid "Bx-Slider"
msgstr "סליידר-Bx"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:5
#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:11
msgid "Option description"
msgstr ""
"תיאור אפשרות\n"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:5
msgid "Type of Transition"
msgstr "סוג מעבר"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:6
msgid "Type of transition between slides"
msgstr "סוג מעבר בין שקופיות סליידר"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:9
msgid "Horizontal"
msgstr "אופקי"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:10
msgid "Vertical"
msgstr "אנכי "

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:11
msgid "Fade"
msgstr "דועך"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:9
msgid "Choose a subtitle for your slide."
msgstr "הוסף כותרת משנה לשקופית סליידר"

#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:9
msgid "Add a Slider"
msgstr "הוסף סליידר"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:9
msgid "Select Slider"
msgstr "הגדר סליידר"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:14
msgid "Set width"
msgstr "הגדר רוחב"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:19
msgid "Set height"
msgstr "הגדר גובה"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:34
msgid "No Sliders Available"
msgstr "אין סליידר זמין"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:47
msgid "create a new Slider"
msgstr "צור סליידר חדש"

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:31
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:48
msgid "Schedule"
msgstr "לוח זמנים"

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:40
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:57
msgid "Submit for Review"
msgstr ""
"שליחה לסקירה\n"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:44
msgid "Choose Category"
msgstr "בחר קטגוריה"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:53
#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:53
msgid "Number of Images in the slider"
msgstr "מספר התמונות בסליידר"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:107
msgid "Select Specific Categories"
msgstr "בחר קטגוריות ספציפיות"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:44
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:44
msgid "Choose Tag"
msgstr "בחר תגית"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:107
msgid "Select Specific tags"
msgstr "בחר תגיות ספציפיות"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:101
msgid "Select Specific posts"
msgstr "בחר פוסטים ספציפיים"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:54
msgid "Choose"
msgstr "בחר"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:57
msgid "Add Slide"
msgstr "הוסף שקופית לסליידר"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:59
#: ../views/backend-settings-form.php:47
msgid "Save Changes"
msgstr "שמור שינויים"

#: ../core/class-fw-manifest.php:293
msgid "minimum required version is"
msgstr "הגרסה המינימלית הנדרשת היא"

#: ../core/class-fw-manifest.php:296
msgid "maximum required version is"
msgstr "הגרסה המקסימלית הנדרשת היא"

#: ../core/class-fw-manifest.php:301
msgid "and"
msgstr "ו"

#: ../core/components/backend.php:355
msgid "Done"
msgstr "הסתיים"

#: ../core/components/backend.php:356
msgid "Ah, Sorry"
msgstr "הא, מצטערים"

#: ../core/components/backend.php:358
#: ../includes/option-types/color-picker/class-fw-option-type-color-picker.php:39
#: ../includes/option-types/color-picker/class-fw-option-type-color-picker.php:40
#: ../includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:41
#: ../includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:42
msgid "Reset"
msgstr "איפוס"

#: ../core/components/backend.php:541 ../core/components/backend.php:542
#: ../core/components/backend.php:650
msgid "Theme Settings"
msgstr "הגדרות תבנית"

#: ../core/components/backend.php:577
msgid "leave a review"
msgstr "השאר ביקורת"

#: ../core/components/backend.php:1316
msgid "The options were successfully reset"
msgstr ""
"האפשרויות אופסו בהצלחה\n"

#: ../core/components/backend.php:1327
msgid "The options were successfully saved"
msgstr ""
"האפשרויות נשמרו בהצלחה\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:362
msgid "Cannot backup the extensions"
msgstr ""
"לא ניתן לגבות את התוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:408
msgid "Cannot clear the extensions directory"
msgstr ""
"אין אפשרות לנקות את ספריית התוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:413
msgid "Cannot recreate the extensions directory"
msgstr ""
"לא ניתן ליצור מחדש את ספריית התוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:419
msgid "Cannot recover the extensions"
msgstr ""
"לא ניתן לשחזר את התוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:645
#, php-format
msgid "Cannot activate hidden standalone extension %s"
msgstr ""
"אין אפשרות להפעיל תוסף מוסתר עצמאי % s\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:874
msgid "You are not allowed to install extensions."
msgstr "אין לך הרשאה להתקין תוספים."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:891
msgid "All supported extensions are already installed."
msgstr ""
"כל התוספים הנתמכים כבר מותקנים.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:967
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2384
#, php-format
msgid "Cannot remove temporary directory: %s"
msgstr ""
"אין אפשרות להסיר ספריה זמנית: % s\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1037
msgid "You have no permissions to install extensions"
msgstr ""
"אין לך הרשאות להתקין תוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1044
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1517
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1901
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2116
msgid "No extensions provided"
msgstr ""
"לא סופקו תוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1842
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2059
msgid "No extension specified."
msgstr ""
"לא צוין תוסף.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1894
msgid "You have no permissions to activate extensions"
msgstr ""
"אין לך הרשאות להפעיל תוספים\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2304
msgid "Invalid extension."
msgstr ""
"תוסף לא חוקי.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2309
msgid "Extension does not have settings options."
msgstr ""
"לתוסף אין אפשרויות הגדרות.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2341
msgid "Extensions settings successfully saved."
msgstr ""
"הגדרות ההרחבות נשמרו בהצלחה.\n"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3107
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3154
msgid "Install"
msgstr "התקנה"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3197
msgid "Install theme compatible extensions"
msgstr ""
"התקן הרחבות תואמות לתבנית\n"

#: ../core/components/extensions/manager/available-extensions.php:24
msgid "Media"
msgstr "מדיה"

#: ../core/components/extensions/manager/available-extensions.php:96
msgid "Shortcodes"
msgstr "קודי קיצור"

#: ../core/components/extensions/manager/views/extension.php:89
#: ../core/components/extensions/manager/views/extension-page-header.php:25
msgid "Install Instructions"
msgstr ""
"התקן הוראות\n"

#: ../core/components/extensions/manager/views/extension.php:102
msgid "Compatible"
msgstr "תואם"

#: ../core/components/extensions/manager/views/extension.php:102
msgid "with your current theme"
msgstr "עם התבנית הנוכחית שלך"

#: ../core/components/extensions/manager/views/extension.php:201
msgid "Update WordPress"
msgstr "עדכן וורדפרס"

#: ../core/components/extensions/manager/views/extension.php:304
#, php-format
msgid "Activate %s"
msgstr ""
"הפעל % s\n"

#: ../core/components/extensions/manager/views/extension.php:316
#, php-format
msgid "Install %s"
msgstr ""
"התקן % s\n"

#: ../core/components/extensions/manager/views/extension.php:342
msgid "View Requirements"
msgstr ""
"הצג תנאים נדרשים\n"

#: ../core/components/extensions/manager/views/extension-page-header.php:40
#, php-format
msgid "%s Settings"
msgstr ""
"%s הגדרות\n"

#: ../core/components/extensions/manager/views/extension-page-header.php:46
msgid "Unknown tab:"
msgstr "לשונית לא מוכרת:"

#: ../core/components/extensions/manager/views/delete-form.php:42
#: ../core/components/extensions/manager/views/install-form.php:51
msgid "No, Return me to the extension list"
msgstr ""
"לא, החזירו אותי לרשימת ההרחבות\n"

#: ../core/components/extensions/manager/views/delete-form.php:45
msgid "Click to view entire list of directories which will be deleted"
msgstr ""
"לחץ כדי להציג את כל רשימת הספריות שימחקו\n"

#: ../core/components/extensions/manager/views/extensions-page.php:17
msgid "Active Extensions"
msgstr ""
"תוספים פעילים\n"

#: ../core/components/extensions/manager/views/extensions-page.php:33
msgid "No extensions activated yet"
msgstr ""
"עדיין לא הופעלו תוספים\n"

#: ../core/components/extensions/manager/views/extensions-page.php:33
msgid "Check the available extensions below"
msgstr ""
"בדוק את התוספים הזמינים מטה\n"

#: ../core/components/extensions/manager/views/extensions-page.php:59
msgid "Available Extensions"
msgstr ""
"תוספים זמינים\n"

#: ../core/components/extensions/manager/views/extensions-page.php:184
msgid "Show other extensions"
msgstr ""
"הצג תוספים אחרים\n"

#: ../core/components/extensions/manager/views/extensions-page.php:185
msgid "Hide other extensions"
msgstr ""
"הסתר תוספים אחרים\n"

#: ../core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:14
#: ../core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:14
msgid "Go to extensions page"
msgstr ""
"עבור אל עמוד התוספים\n"

#: ../core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:17
#: ../core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:17
msgid "Return to Extensions page"
msgstr "חזור לעמוד תוספים"

#: ../views/backend-settings-form.php:48
msgid "Reset Options"
msgstr ""
"אפס אפשרויות\n"

#: ../views/backend-settings-form.php:62
msgid "by"
msgstr "באמצעות"

#: ../views/backend-settings-form.php:155
msgid ""
"Click OK to reset.\n"
"All settings will be lost and replaced with default settings!"
msgstr ""
"\n"
"לחץ על אישור כדי לאפס.\n"
"כל ההגדרות יאבדו ויוחלפו בהגדרות ברירת המחדל!"

#: ../views/backend-settings-form.php:202
msgid "Resetting"
msgstr "מאפס"

#: ../views/backend-settings-form.php:204
msgid "We are currently resetting your settings."
msgstr ""
"אנו מאפסים כעת את ההגדרות שלך.\n"

#: ../views/backend-settings-form.php:206
#: ../views/backend-settings-form.php:212
msgid "This may take a few moments."
msgstr ""
"פעולה זו עשויה להימשך מספר דקות.\n"

#: ../views/backend-settings-form.php:208
msgid "Saving"
msgstr "שומר"

#: ../views/backend-settings-form.php:210
msgid "We are currently saving your settings."
msgstr ""
"אנו שומרים כרגע את ההגדרות שלך.\n"

#: ../includes/option-types/class-fw-option-type-undefined.php:25
msgid "UNDEFINED OPTION TYPE"
msgstr "סוג אפשרות לא נקבע"

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:22
msgid "25%"
msgstr "25%"

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:23
msgid "50%"
msgstr "50%"

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:24
msgid "100%"
msgstr "100%"

#: ../includes/option-types/map/views/view.php:12
msgid "Specify location"
msgstr "ציין מיקום"

#: ../includes/option-types/map/views/view.php:27
msgid "Location Venue"
msgstr "מיקום אירוע"

#: ../includes/option-types/map/views/view.php:42
msgid "Address"
msgstr "כתובת "

#: ../includes/option-types/map/views/view.php:57
msgid "City"
msgstr "עיר"

#: ../includes/option-types/map/views/view.php:72
msgid "Country"
msgstr "ארץ"

#: ../includes/option-types/map/views/view.php:87
msgid "State"
msgstr "מדינה"

#: ../includes/option-types/map/views/view.php:103
msgid "Zip Code"
msgstr "מיקוד"

#: ../includes/option-types/map/views/view.php:138
msgid "Cannot find the location?"
msgstr "לא מצליח למצוא מיקום?"

#: ../includes/option-types/map/views/view.php:150
msgid "Reset location"
msgstr "אפס מיקום"

#: ../includes/option-types/upload/class-fw-option-type-upload.php:150
msgid "Add Image"
msgstr "הוסף תמונה"

#: ../includes/option-types/upload/class-fw-option-type-upload.php:176
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:186
msgid "Upload"
msgstr "העלאה"

#: ../includes/option-types/typography-v2/view.php:39
msgid "Font face"
msgstr "סוג גופן"

#: ../includes/option-types/typography-v2/view.php:48
msgid "Normal"
msgstr "רגיל"

#: ../includes/option-types/typography-v2/view.php:49
msgid "Italic"
msgstr "נטוי"

#: ../includes/option-types/typography-v2/view.php:50
msgid "Oblique"
msgstr "אלכסוני"

#: ../includes/option-types/typography-v2/view.php:59
#: ../includes/option-types/typography-v2/view.php:118
msgid "Style"
msgstr "עיצוב"

#: ../includes/option-types/typography-v2/view.php:85
msgid "Weight"
msgstr "משקל"

#: ../includes/option-types/typography-v2/view.php:101
msgid "Script"
msgstr "תיאור"

#: ../includes/option-types/typography-v2/view.php:128
msgid "Size"
msgstr "מידה"

#: ../includes/option-types/typography-v2/view.php:139
msgid "Line height"
msgstr "גובה קו"

#: ../includes/option-types/typography-v2/view.php:150
msgid "Letter spacing"
msgstr "ריווח אותיות"

#: ../includes/option-types/typography-v2/view.php:173
msgid "Color"
msgstr "צבע"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:156
msgid "Unknown Set"
msgstr "קבוצה לא מוכרת"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:186
msgid "Web Application Icons"
msgstr ""
"אייקונים של יישומי אינטרנט\n"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:187
msgid "Hand Icons"
msgstr "אייקונים של יד"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:188
msgid "Transportation Icons"
msgstr "אייקונים של תחבורה"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:189
msgid "Gender Icons"
msgstr "אייקונים של מגדר"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:190
msgid "File Type Icons"
msgstr "אייקונים של סוג קובץ"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:193
msgid "Payment Icons"
msgstr "אייקונים של תשלום"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:195
msgid "Currency Icons"
msgstr "אייקונים של מטבע"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:196
msgid "Text Editor Icons"
msgstr "אייקונים לעורך טקסט"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:197
msgid "Directional Icons"
msgstr "אייקונים כיווניים"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:198
msgid "Video Player Icons"
msgstr "אייקון נגן וידאו"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:199
msgid "Brand Icons"
msgstr ""
"אייקונים של מותגים\n"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:200
msgid "Medical Icons"
msgstr "אייקונים רפואיים"

#: ../includes/option-types/icon/view.php:39
msgid "All Categories"
msgstr "כל הקטגוריות"

#: ../includes/option-types/datetime-range/view.php:41
#: ../includes/option-types/gradient/view.php:46
msgid "to"
msgstr "אל"

#: ../includes/option-types/background-image/view.php:37
msgid "Predefined images"
msgstr ""
"תמונות מוגדרות מראש\n"

#: ../includes/option-types/background-image/view.php:38
msgid "Custom image"
msgstr "תמונה מותאמת"

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:160
msgid "Add Images"
msgstr "הוסף תמונה"

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:188
msgid "1 File"
msgstr "קובץ 1"

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:189
#, php-format
msgid "%u Files"
msgstr "קבצי %u"
