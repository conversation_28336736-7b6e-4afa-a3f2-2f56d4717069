<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
?>
<div class="testimonial-slick-activation-wrapper2">
	<div class="testimonial-slick-activation-two testimonial-style-two-wrapper testimonial-style-two axil-slick-arrow arrow-bottom-slide axil-slick-dots">    
	<?php foreach ( $settings['list_testimonial'] as $testimonial ):  
			$has_designation  = $testimonial['designation'] == 'yes' ? true : false;			
			$designation  	  = $testimonial['designation'];
			$title  			= $testimonial['title'];
			$content  			= $testimonial['content']; 
			$size 				= 'axil-thumbnail-sm'; 
			$img 				= wp_get_attachment_image( $testimonial['testimonial_image']['id'], $size ); 
			?>
	    <div class="slick-single-layout slick-slide testimonial-style-two">
	    	<?php if ($img) { ?>
				<div class="thumbnail">								
						<?php echo wp_kses_post($img);?>
				</div>
			<?php } ?>	
	        <div class="thumb-content">
	            <h5 class="item-title title"><?php echo esc_html($title);?></h5>
	            <p>“<?php echo esc_html($content);?>”</p>
				
	        </div>
	    </div> 
	<?php  endforeach;?>	
	</div>
</div>
