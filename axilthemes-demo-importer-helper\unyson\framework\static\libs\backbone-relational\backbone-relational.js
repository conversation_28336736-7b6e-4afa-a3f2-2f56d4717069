/* vim: set tabstop=4 softtabstop=4 shiftwidth=4 noexpandtab: */
/**
 * Backbone-relational.js 0.10.0
 * (c) 2011-2014 <PERSON> and contributors (https://github.com/PaulUithol/Backbone-relational/graphs/contributors)
 *
 * Backbone-relational may be freely distributed under the MIT license; see the accompanying LICENSE.txt.
 * For details and documentation: https://github.com/PaulUithol/Backbone-relational.
 * Depends on Backbone (and thus on Underscore as well): https://github.com/documentcloud/backbone.
 *
 * Example:
 *
	Zoo = Backbone.RelationalModel.extend({
		relations: [ {
			type: Backbone.HasMany,
			key: 'animals',
			relatedModel: 'Animal',
			reverseRelation: {
				key: 'livesIn',
				includeInJSON: 'id'
				// 'relatedModel' is automatically set to 'Zoo'; the 'relationType' to 'HasOne'.
			}
		} ],

		toString: function() {
			return this.get( 'name' );
		}
	});

	Animal = Backbone.RelationalModel.extend({
		toString: function() {
			return this.get( 'species' );
		}
	});

	// Creating the zoo will give it a collection with one animal in it: the monkey.
	// The animal created after that has a relation `livesIn` that points to the zoo it's currently associated with.
	// If you instantiate (or fetch) the zebra later, it will automatically be added.

	var zoo = new Zoo({
		name: 'Artis',
		animals: [ { id: 'monkey-1', species: 'Chimp' }, 'lion-1', 'zebra-1' ]
	});

	var lion = new Animal( { id: 'lion-1', species: 'Lion' } ),
		monkey = zoo.get( 'animals' ).first(),
		sameZoo = lion.get( 'livesIn' );
 */
( function( root, factory ) {
	// Set up Backbone-relational for the environment. Start with AMD.
	if ( typeof define === 'function' && define.amd ) {
		define( [ 'exports', 'backbone', 'underscore' ], factory );
	}
	// Next for Node.js or CommonJS.
	else if ( typeof exports !== 'undefined' ) {
		factory( exports, require( 'backbone' ), require( 'underscore' ) );
	}
	// Finally, as a browser global. Use `root` here as it references `window`.
	else {
		factory( root, root.Backbone, root._ );
	}
}( this, function( exports, Backbone, _ ) {
	"use strict";

	Backbone.Relational = {
		showWarnings: true
	};

	/**
	 * Semaphore mixin; can be used as both binary and counting.
	 **/
	Backbone.Semaphore = {
		_permitsAvailable: null,
		_permitsUsed: 0,

		acquire: function() {
			if ( this._permitsAvailable && this._permitsUsed >= this._permitsAvailable ) {
				throw new Error( 'Max permits acquired' );
			}
			else {
				this._permitsUsed++;
			}
		},

		release: function() {
			if ( this._permitsUsed === 0 ) {
				throw new Error( 'All permits released' );
			}
			else {
				this._permitsUsed--;
			}
		},

		isLocked: function() {
			return this._permitsUsed > 0;
		},

		setAvailablePermits: function( amount ) {
			if ( this._permitsUsed > amount ) {
				throw new Error( 'Available permits cannot be less than used permits' );
			}
			this._permitsAvailable = amount;
		}
	};

	/**
	 * A BlockingQueue that accumulates items while blocked (via 'block'),
	 * and processes them when unblocked (via 'unblock').
	 * Process can also be called manually (via 'process').
	 */
	Backbone.BlockingQueue = function() {
		this._queue = [];
	};
	_.extend( Backbone.BlockingQueue.prototype, Backbone.Semaphore, {
		_queue: null,

		add: function( func ) {
			if ( this.isBlocked() ) {
				this._queue.push( func );
			}
			else {
				func();
			}
		},

		// Some of the queued events may trigger other blocking events. By
		// copying the queue here it allows queued events to process closer to
		// the natural order.
		//
		// queue events [ 'A', 'B', 'C' ]
		// A handler of 'B' triggers 'D' and 'E'
		// By copying `this._queue` this executes:
		// [ 'A', 'B', 'D', 'E', 'C' ]
		// The same order the would have executed if they didn't have to be
		// delayed and queued.
		process: function() {
			var queue = this._queue;
			this._queue = [];
			while ( queue && queue.length ) {
				queue.shift()();
			}
		},

		block: function() {
			this.acquire();
		},

		unblock: function() {
			this.release();
			if ( !this.isBlocked() ) {
				this.process();
			}
		},

		isBlocked: function() {
			return this.isLocked();
		}
	});
	/**
	 * Global event queue. Accumulates external events ('add:<key>', 'remove:<key>' and 'change:<key>')
	 * until the top-level object is fully initialized (see 'Backbone.RelationalModel').
	 */
	Backbone.Relational.eventQueue = new Backbone.BlockingQueue();

	/**
	 * Backbone.Store keeps track of all created (and destruction of) Backbone.RelationalModel.
	 * Handles lookup for relations.
	 */
	Backbone.Store = function() {
		this._collections = [];
		this._reverseRelations = [];
		this._orphanRelations = [];
		this._subModels = [];
		this._modelScopes = [ exports ];
	};
	_.extend( Backbone.Store.prototype, Backbone.Events, {
		/**
		 * Create a new `Relation`.
		 * @param {Backbone.RelationalModel} [model]
		 * @param {Object} relation
		 * @param {Object} [options]
		 */
		initializeRelation: function( model, relation, options ) {
			var type = !_.isString( relation.type ) ? relation.type : Backbone[ relation.type ] || this.getObjectByName( relation.type );
			if ( type && type.prototype instanceof Backbone.Relation ) {
				var rel = new type( model, relation, options ); // Also pushes the new Relation into `model._relations`
			}
			else {
				Backbone.Relational.showWarnings && typeof console !== 'undefined' && console.warn( 'Relation=%o; missing or invalid relation type!', relation );
			}
		},

		/**
		 * Add a scope for `getObjectByName` to look for model types by name.
		 * @param {Object} scope
		 */
		addModelScope: function( scope ) {
			this._modelScopes.push( scope );
		},

		/**
		 * Remove a scope.
		 * @param {Object} scope
		 */
		removeModelScope: function( scope ) {
			this._modelScopes = _.without( this._modelScopes, scope );
		},

		/**
		 * Add a set of subModelTypes to the store, that can be used to resolve the '_superModel'
		 * for a model later in 'setupSuperModel'.
		 *
		 * @param {Backbone.RelationalModel} subModelTypes
		 * @param {Backbone.RelationalModel} superModelType
		 */
		addSubModels: function( subModelTypes, superModelType ) {
			this._subModels.push({
				'superModelType': superModelType,
				'subModels': subModelTypes
			});
		},

		/**
		 * Check if the given modelType is registered as another model's subModel. If so, add it to the super model's
		 * '_subModels', and set the modelType's '_superModel', '_subModelTypeName', and '_subModelTypeAttribute'.
		 *
		 * @param {Backbone.RelationalModel} modelType
		 */
		setupSuperModel: function( modelType ) {
			_.find( this._subModels, function( subModelDef ) {
				return _.filter( subModelDef.subModels || [], function( subModelTypeName, typeValue ) {
					var subModelType = this.getObjectByName( subModelTypeName );

					if ( modelType === subModelType ) {
						// Set 'modelType' as a child of the found superModel
						subModelDef.superModelType._subModels[ typeValue ] = modelType;

						// Set '_superModel', '_subModelTypeValue', and '_subModelTypeAttribute' on 'modelType'.
						modelType._superModel = subModelDef.superModelType;
						modelType._subModelTypeValue = typeValue;
						modelType._subModelTypeAttribute = subModelDef.superModelType.prototype.subModelTypeAttribute;
						return true;
					}
				}, this ).length;
			}, this );
		},

		/**
		 * Add a reverse relation. Is added to the 'relations' property on model's prototype, and to
		 * existing instances of 'model' in the store as well.
		 * @param {Object} relation
		 * @param {Backbone.RelationalModel} relation.model
		 * @param {String} relation.type
		 * @param {String} relation.key
		 * @param {String|Object} relation.relatedModel
		 */
		addReverseRelation: function( relation ) {
			var exists = _.any( this._reverseRelations, function( rel ) {
				return _.all( relation || [], function( val, key ) {
					return val === rel[ key ];
				});
			});

			if ( !exists && relation.model && relation.type ) {
				this._reverseRelations.push( relation );
				this._addRelation( relation.model, relation );
				this.retroFitRelation( relation );
			}
		},

		/**
		 * Deposit a `relation` for which the `relatedModel` can't be resolved at the moment.
		 *
		 * @param {Object} relation
		 */
		addOrphanRelation: function( relation ) {
			var exists = _.any( this._orphanRelations, function( rel ) {
				return _.all( relation || [], function( val, key ) {
					return val === rel[ key ];
				});
			});

			if ( !exists && relation.model && relation.type ) {
				this._orphanRelations.push( relation );
			}
		},

		/**
		 * Try to initialize any `_orphanRelation`s
		 */
		processOrphanRelations: function() {
			// Make sure to operate on a copy since we're removing while iterating
			_.each( this._orphanRelations.slice( 0 ), function( rel ) {
				var relatedModel = Backbone.Relational.store.getObjectByName( rel.relatedModel );
				if ( relatedModel ) {
					this.initializeRelation( null, rel );
					this._orphanRelations = _.without( this._orphanRelations, rel );
				}
			}, this );
		},

		/**
		 *
		 * @param {Backbone.RelationalModel.constructor} type
		 * @param {Object} relation
		 * @private
		 */
		_addRelation: function( type, relation ) {
			if ( !type.prototype.relations ) {
				type.prototype.relations = [];
			}
			type.prototype.relations.push( relation );

			_.each( type._subModels || [], function( subModel ) {
				this._addRelation( subModel, relation );
			}, this );
		},

		/**
		 * Add a 'relation' to all existing instances of 'relation.model' in the store
		 * @param {Object} relation
		 */
		retroFitRelation: function( relation ) {
			var coll = this.getCollection( relation.model, false );
			coll && coll.each( function( model ) {
				if ( !( model instanceof relation.model ) ) {
					return;
				}

				var rel = new relation.type( model, relation );
			}, this );
		},

		/**
		 * Find the Store's collection for a certain type of model.
		 * @param {Backbone.RelationalModel} type
		 * @param {Boolean} [create=true] Should a collection be created if none is found?
		 * @return {Backbone.Collection} A collection if found (or applicable for 'model'), or null
		 */
		getCollection: function( type, create ) {
			if ( type instanceof Backbone.RelationalModel ) {
				type = type.constructor;
			}

			var rootModel = type;
			while ( rootModel._superModel ) {
				rootModel = rootModel._superModel;
			}

			var coll = _.find( this._collections, function( item ) {
				return item.model === rootModel;
			});

			if ( !coll && create !== false ) {
				coll = this._createCollection( rootModel );
			}

			return coll;
		},

		/**
		 * Find a model type on one of the modelScopes by name. Names are split on dots.
		 * @param {String} name
		 * @return {Object}
		 */
		getObjectByName: function( name ) {
			var parts = name.split( '.' ),
				type = null;

			_.find( this._modelScopes, function( scope ) {
				type = _.reduce( parts || [], function( memo, val ) {
					return memo ? memo[ val ] : undefined;
				}, scope );

				if ( type && type !== scope ) {
					return true;
				}
			}, this );

			return type;
		},

		_createCollection: function( type ) {
			var coll;

			// If 'type' is an instance, take its constructor
			if ( type instanceof Backbone.RelationalModel ) {
				type = type.constructor;
			}

			// Type should inherit from Backbone.RelationalModel.
			if ( type.prototype instanceof Backbone.RelationalModel ) {
				coll = new Backbone.Collection();
				coll.model = type;

				this._collections.push( coll );
			}

			return coll;
		},

		/**
		 * Find the attribute that is to be used as the `id` on a given object
		 * @param type
		 * @param {String|Number|Object|Backbone.RelationalModel} item
		 * @return {String|Number}
		 */
		resolveIdForItem: function( type, item ) {
			var id = _.isString( item ) || _.isNumber( item ) ? item : null;

			if ( id === null ) {
				if ( item instanceof Backbone.RelationalModel ) {
					id = item.id;
				}
				else if ( _.isObject( item ) ) {
					id = item[ type.prototype.idAttribute ];
				}
			}

			// Make all falsy values `null` (except for 0, which could be an id.. see '/issues/179')
			if ( !id && id !== 0 ) {
				id = null;
			}

			return id;
		},

		/**
		 * Find a specific model of a certain `type` in the store
		 * @param type
		 * @param {String|Number|Object|Backbone.RelationalModel} item
		 */
		find: function( type, item ) {
			var id = this.resolveIdForItem( type, item ),
				coll = this.getCollection( type );

			// Because the found object could be of any of the type's superModel
			// types, only return it if it's actually of the type asked for.
			if ( coll ) {
				var obj = coll.get( id );

				if ( obj instanceof type ) {
					return obj;
				}
			}

			return null;
		},

		/**
		 * Add a 'model' to its appropriate collection. Retain the original contents of 'model.collection'.
		 * @param {Backbone.RelationalModel} model
		 */
		register: function( model ) {
			var coll = this.getCollection( model );

			if ( coll ) {
				var modelColl = model.collection;
				coll.add( model );
				model.collection = modelColl;
			}
		},

		/**
		 * Check if the given model may use the given `id`
		 * @param model
		 * @param [id]
		 */
		checkId: function( model, id ) {
			var coll = this.getCollection( model ),
				duplicate = coll && coll.get( id );

			if ( duplicate && model !== duplicate ) {
				if ( Backbone.Relational.showWarnings && typeof console !== 'undefined' ) {
					console.warn( 'Duplicate id! Old RelationalModel=%o, new RelationalModel=%o', duplicate, model );
				}

				throw new Error( "Cannot instantiate more than one Backbone.RelationalModel with the same id per type!" );
			}
		},

		/**
		 * Explicitly update a model's id in its store collection
		 * @param {Backbone.RelationalModel} model
		 */
		update: function( model ) {
			var coll = this.getCollection( model );

			// Register a model if it isn't yet (which happens if it was created without an id).
			if ( !coll.contains( model ) ) {
				this.register( model );
			}

			// This triggers updating the lookup indices kept in a collection
			coll._onModelEvent( 'change:' + model.idAttribute, model, coll );

			// Trigger an event on model so related models (having the model's new id in their keyContents) can add it.
			model.trigger( 'relational:change:id', model, coll );
		},

		/**
		 * Unregister from the store: a specific model, a collection, or a model type.
		 * @param {Backbone.RelationalModel|Backbone.RelationalModel.constructor|Backbone.Collection} type
		 */
		unregister: function( type ) {
			var coll,
				models;

			if ( type instanceof Backbone.Model ) {
				coll = this.getCollection( type );
				models = [ type ];
			}
			else if ( type instanceof Backbone.Collection ) {
				coll = this.getCollection( type.model );
				models = _.clone( type.models );
			}
			else {
				coll = this.getCollection( type );
				models = _.clone( coll.models );
			}

			_.each( models, function( model ) {
				this.stopListening( model );
				_.invoke( model.getRelations(), 'stopListening' );
			}, this );


			// If we've unregistered an entire store collection, reset the collection (which is much faster).
			// Otherwise, remove each model one by one.
			if ( _.contains( this._collections, type ) ) {
				coll.reset( [] );
			}
			else {
				_.each( models, function( model ) {
					if ( coll.get( model ) ) {
						coll.remove( model );
					}
					else {
						coll.trigger( 'relational:remove', model, coll );
					}
				}, this );
			}
		},

		/**
		 * Reset the `store` to it's original state. The `reverseRelations` are kept though, since attempting to
		 * re-initialize these on models would lead to a large amount of warnings.
		 */
		reset: function() {
			this.stopListening();

			// Unregister each collection to remove event listeners
			_.each( this._collections, function( coll ) {
				this.unregister( coll );
			}, this );

			this._collections = [];
			this._subModels = [];
			this._modelScopes = [ exports ];
		}
	});
	Backbone.Relational.store = new Backbone.Store();

	/**
	 * The main Relation class, from which 'HasOne' and 'HasMany' inherit. Internally, 'relational:<key>' events
	 * are used to regulate addition and removal of models from relations.
	 *
	 * @param {Backbone.RelationalModel} [instance] Model that this relation is created for. If no model is supplied,
	 *      Relation just tries to instantiate it's `reverseRelation` if specified, and bails out after that.
	 * @param {Object} options
	 * @param {string} options.key
	 * @param {Backbone.RelationalModel.constructor} options.relatedModel
	 * @param {Boolean|String} [options.includeInJSON=true] Serialize the given attribute for related model(s)' in toJSON, or just their ids.
	 * @param {Boolean} [options.createModels=true] Create objects from the contents of keys if the object is not found in Backbone.store.
	 * @param {Object} [options.reverseRelation] Specify a bi-directional relation. If provided, Relation will reciprocate
	 *    the relation to the 'relatedModel'. Required and optional properties match 'options', except that it also needs
	 *    {Backbone.Relation|String} type ('HasOne' or 'HasMany').
	 * @param {Object} opts
	 */
	Backbone.Relation = function( instance, options, opts ) {
		this.instance = instance;
		// Make sure 'options' is sane, and fill with defaults from subclasses and this object's prototype
		options = _.isObject( options ) ? options : {};
		this.reverseRelation = _.defaults( options.reverseRelation || {}, this.options.reverseRelation );
		this.options = _.defaults( options, this.options, Backbone.Relation.prototype.options );

		this.reverseRelation.type = !_.isString( this.reverseRelation.type ) ? this.reverseRelation.type :
			Backbone[ this.reverseRelation.type ] || Backbone.Relational.store.getObjectByName( this.reverseRelation.type );

		this.key = this.options.key;
		this.keySource = this.options.keySource || this.key;
		this.keyDestination = this.options.keyDestination || this.keySource || this.key;

		this.model = this.options.model || this.instance.constructor;

		this.relatedModel = this.options.relatedModel;

		// No 'relatedModel' is interpreted as self-referential
		if ( _.isUndefined( this.relatedModel ) ) {
			this.relatedModel = this.model;
		}

		// Otherwise, try to resolve the given value to an object
		if ( _.isFunction( this.relatedModel ) && !( this.relatedModel.prototype instanceof Backbone.RelationalModel ) ) {
			this.relatedModel = _.result( this, 'relatedModel' );
		}
		if ( _.isString( this.relatedModel ) ) {
			this.relatedModel = Backbone.Relational.store.getObjectByName( this.relatedModel );
		}


		if ( !this.checkPreconditions() ) {
			return;
		}

		// Add the reverse relation on 'relatedModel' to the store's reverseRelations
		if ( !this.options.isAutoRelation && this.reverseRelation.type && this.reverseRelation.key ) {
			Backbone.Relational.store.addReverseRelation( _.defaults( {
					isAutoRelation: true,
					model: this.relatedModel,
					relatedModel: this.model,
					reverseRelation: this.options // current relation is the 'reverseRelation' for its own reverseRelation
				},
				this.reverseRelation // Take further properties from this.reverseRelation (type, key, etc.)
			) );
		}

		if ( instance ) {
			var contentKey = this.keySource;
			if ( contentKey !== this.key && _.isObject( this.instance.get( this.key ) ) ) {
				contentKey = this.key;
			}

			this.setKeyContents( this.instance.get( contentKey ) );
			this.relatedCollection = Backbone.Relational.store.getCollection( this.relatedModel );

			// Explicitly clear 'keySource', to prevent a leaky abstraction if 'keySource' differs from 'key'.
			if ( this.keySource !== this.key ) {
				delete this.instance.attributes[ this.keySource ];
			}

			// Add this Relation to instance._relations
			this.instance._relations[ this.key ] = this;

			this.initialize( opts );

			if ( this.options.autoFetch ) {
				this.instance.getAsync( this.key, _.isObject( this.options.autoFetch ) ? this.options.autoFetch : {} );
			}

			// When 'relatedModel' are created or destroyed, check if it affects this relation.
			this.listenTo( this.instance, 'destroy', this.destroy )
				.listenTo( this.relatedCollection, 'relational:add relational:change:id', this.tryAddRelated )
				.listenTo( this.relatedCollection, 'relational:remove', this.removeRelated );
		}
	};
	// Fix inheritance :\
	Backbone.Relation.extend = Backbone.Model.extend;
	// Set up all inheritable **Backbone.Relation** properties and methods.
	_.extend( Backbone.Relation.prototype, Backbone.Events, Backbone.Semaphore, {
		options: {
			createModels: true,
			includeInJSON: true,
			isAutoRelation: false,
			autoFetch: false,
			parse: false
		},

		instance: null,
		key: null,
		keyContents: null,
		relatedModel: null,
		relatedCollection: null,
		reverseRelation: null,
		related: null,

		/**
		 * Check several pre-conditions.
		 * @return {Boolean} True if pre-conditions are satisfied, false if they're not.
		 */
		checkPreconditions: function() {
			var i = this.instance,
				k = this.key,
				m = this.model,
				rm = this.relatedModel,
				warn = Backbone.Relational.showWarnings && typeof console !== 'undefined';

			if ( !m || !k || !rm ) {
				warn && console.warn( 'Relation=%o: missing model, key or relatedModel (%o, %o, %o).', this, m, k, rm );
				return false;
			}
			// Check if the type in 'model' inherits from Backbone.RelationalModel
			if ( !( m.prototype instanceof Backbone.RelationalModel ) ) {
				warn && console.warn( 'Relation=%o: model does not inherit from Backbone.RelationalModel (%o).', this, i );
				return false;
			}
			// Check if the type in 'relatedModel' inherits from Backbone.RelationalModel
			if ( !( rm.prototype instanceof Backbone.RelationalModel ) ) {
				warn && console.warn( 'Relation=%o: relatedModel does not inherit from Backbone.RelationalModel (%o).', this, rm );
				return false;
			}
			// Check if this is not a HasMany, and the reverse relation is HasMany as well
			if ( this instanceof Backbone.HasMany && this.reverseRelation.type === Backbone.HasMany ) {
				warn && console.warn( 'Relation=%o: relation is a HasMany, and the reverseRelation is HasMany as well.', this );
				return false;
			}
			// Check if we're not attempting to create a relationship on a `key` that's already used.
			if ( i && _.keys( i._relations ).length ) {
				var existing = _.find( i._relations, function( rel ) {
					return rel.key === k;
				}, this );

				if ( existing ) {
					warn && console.warn( 'Cannot create relation=%o on %o for model=%o: already taken by relation=%o.',
						this, k, i, existing );
					return false;
				}
			}

			return true;
		},

		/**
		 * Set the related model(s) for this relation
		 * @param {Backbone.Model|Backbone.Collection} related
		 */
		setRelated: function( related ) {
			this.related = related;
			this.instance.attributes[ this.key ] = related;
		},

		/**
		 * Determine if a relation (on a different RelationalModel) is the reverse
		 * relation of the current one.
		 * @param {Backbone.Relation} relation
		 * @return {Boolean}
		 */
		_isReverseRelation: function( relation ) {
			return relation.instance instanceof this.relatedModel && this.reverseRelation.key === relation.key &&
				this.key === relation.reverseRelation.key;
		},

		/**
		 * Get the reverse relations (pointing back to 'this.key' on 'this.instance') for the currently related model(s).
		 * @param {Backbone.RelationalModel} [model] Get the reverse relations for a specific model.
		 *    If not specified, 'this.related' is used.
		 * @return {Backbone.Relation[]}
		 */
		getReverseRelations: function( model ) {
			var reverseRelations = [];
			// Iterate over 'model', 'this.related.models' (if this.related is a Backbone.Collection), or wrap 'this.related' in an array.
			var models = !_.isUndefined( model ) ? [ model ] : this.related && ( this.related.models || [ this.related ] ),
				relations = null,
				relation = null;

			for( var i = 0; i < ( models || [] ).length; i++ ) {
				relations = models[ i ].getRelations() || [];

				for( var j = 0; j < relations.length; j++ ) {
					relation = relations[ j ];

					if ( this._isReverseRelation( relation ) ) {
						reverseRelations.push( relation );
					}
				}
			}

			return reverseRelations;
		},

		/**
		 * When `this.instance` is destroyed, cleanup our relations.
		 * Get reverse relation, call removeRelated on each.
		 */
		destroy: function() {
			this.stopListening();

			if ( this instanceof Backbone.HasOne ) {
				this.setRelated( null );
			}
			else if ( this instanceof Backbone.HasMany ) {
				this.setRelated( this._prepareCollection() );
			}

			_.each( this.getReverseRelations(), function( relation ) {
				relation.removeRelated( this.instance );
			}, this );
		}
	});

	Backbone.HasOne = Backbone.Relation.extend({
		options: {
			reverseRelation: { type: 'HasMany' }
		},

		initialize: function( opts ) {
			this.listenTo( this.instance, 'relational:change:' + this.key, this.onChange );

			var related = this.findRelated( opts );
			this.setRelated( related );

			// Notify new 'related' object of the new relation.
			_.each( this.getReverseRelations(), function( relation ) {
				relation.addRelated( this.instance, opts );
			}, this );
		},

		/**
		 * Find related Models.
		 * @param {Object} [options]
		 * @return {Backbone.Model}
		 */
		findRelated: function( options ) {
			var related = null;

			options = _.defaults( { parse: this.options.parse }, options );

			if ( this.keyContents instanceof this.relatedModel ) {
				related = this.keyContents;
			}
			else if ( this.keyContents || this.keyContents === 0 ) { // since 0 can be a valid `id` as well
				var opts = _.defaults( { create: this.options.createModels }, options );
				related = this.relatedModel.findOrCreate( this.keyContents, opts );
			}

			// Nullify `keyId` if we have a related model; in case it was already part of the relation
			if ( related ) {
				this.keyId = null;
			}

			return related;
		},

		/**
		 * Normalize and reduce `keyContents` to an `id`, for easier comparison
		 * @param {String|Number|Backbone.Model} keyContents
		 */
		setKeyContents: function( keyContents ) {
			this.keyContents = keyContents;
			this.keyId = Backbone.Relational.store.resolveIdForItem( this.relatedModel, this.keyContents );
		},

		/**
		 * Event handler for `change:<key>`.
		 * If the key is changed, notify old & new reverse relations and initialize the new relation.
		 */
		onChange: function( model, attr, options ) {
			// Don't accept recursive calls to onChange (like onChange->findRelated->findOrCreate->initializeRelations->addRelated->onChange)
			if ( this.isLocked() ) {
				return;
			}
			this.acquire();
			options = options ? _.clone( options ) : {};

			// 'options.__related' is set by 'addRelated'/'removeRelated'. If it is set, the change
			// is the result of a call from a relation. If it's not, the change is the result of
			// a 'set' call on this.instance.
			var changed = _.isUndefined( options.__related ),
				oldRelated = changed ? this.related : options.__related;

			if ( changed ) {
				this.setKeyContents( attr );
				var related = this.findRelated( options );
				this.setRelated( related );
			}

			// Notify old 'related' object of the terminated relation
			if ( oldRelated && this.related !== oldRelated ) {
				_.each( this.getReverseRelations( oldRelated ), function( relation ) {
					relation.removeRelated( this.instance, null, options );
				}, this );
			}

			// Notify new 'related' object of the new relation. Note we do re-apply even if this.related is oldRelated;
			// that can be necessary for bi-directional relations if 'this.instance' was created after 'this.related'.
			// In that case, 'this.instance' will already know 'this.related', but the reverse might not exist yet.
			_.each( this.getReverseRelations(), function( relation ) {
				relation.addRelated( this.instance, options );
			}, this );

			// Fire the 'change:<key>' event if 'related' was updated
			if ( !options.silent && this.related !== oldRelated ) {
				var dit = this;
				this.changed = true;
				Backbone.Relational.eventQueue.add( function() {
					dit.instance.trigger( 'change:' + dit.key, dit.instance, dit.related, options, true );
					dit.changed = false;
				});
			}
			this.release();
		},

		/**
		 * If a new 'this.relatedModel' appears in the 'store', try to match it to the last set 'keyContents'
		 */
		tryAddRelated: function( model, coll, options ) {
			if ( ( this.keyId || this.keyId === 0 ) && model.id === this.keyId ) { // since 0 can be a valid `id` as well
				this.addRelated( model, options );
				this.keyId = null;
			}
		},

		addRelated: function( model, options ) {
			// Allow 'model' to set up its relations before proceeding.
			// (which can result in a call to 'addRelated' from a relation of 'model')
			var dit = this;
			model.queue( function() {
				if ( model !== dit.related ) {
					var oldRelated = dit.related || null;
					dit.setRelated( model );
					dit.onChange( dit.instance, model, _.defaults( { __related: oldRelated }, options ) );
				}
			});
		},

		removeRelated: function( model, coll, options ) {
			if ( !this.related ) {
				return;
			}

			if ( model === this.related ) {
				var oldRelated = this.related || null;
				this.setRelated( null );
				this.onChange( this.instance, model, _.defaults( { __related: oldRelated }, options ) );
			}
		}
	});

	Backbone.HasMany = Backbone.Relation.extend({
		collectionType: null,

		options: {
			reverseRelation: { type: 'HasOne' },
			collectionType: Backbone.Collection,
			collectionKey: true,
			collectionOptions: {}
		},

		initialize: function( opts ) {
			this.listenTo( this.instance, 'relational:change:' + this.key, this.onChange );

			// Handle a custom 'collectionType'
			this.collectionType = this.options.collectionType;
			if ( _.isFunction( this.collectionType ) && this.collectionType !== Backbone.Collection && !( this.collectionType.prototype instanceof Backbone.Collection ) ) {
				this.collectionType = _.result( this, 'collectionType' );
			}
			if ( _.isString( this.collectionType ) ) {
				this.collectionType = Backbone.Relational.store.getObjectByName( this.collectionType );
			}
			if ( this.collectionType !== Backbone.Collection && !( this.collectionType.prototype instanceof Backbone.Collection ) ) {
				throw new Error( '`collectionType` must inherit from Backbone.Collection' );
			}

			var related = this.findRelated( opts );
			this.setRelated( related );
		},

		/**
		 * Bind events and setup collectionKeys for a collection that is to be used as the backing store for a HasMany.
		 * If no 'collection' is supplied, a new collection will be created of the specified 'collectionType' option.
		 * @param {Backbone.Collection} [collection]
		 * @return {Backbone.Collection}
		 */
		_prepareCollection: function( collection ) {
			if ( this.related ) {
				this.stopListening( this.related );
			}

			if ( !collection || !( collection instanceof Backbone.Collection ) ) {
				var options = _.isFunction( this.options.collectionOptions ) ?
					this.options.collectionOptions( this.instance ) : this.options.collectionOptions;

				collection = new this.collectionType( null, options );
			}

			// if is default model, use our custom (if is custom created by user, do not touch it)
			if (collection.model === Backbone.Collection.prototype.model) {
				collection.model = this.relatedModel;
			}

			if ( this.options.collectionKey ) {
				var key = this.options.collectionKey === true ? this.options.reverseRelation.key : this.options.collectionKey;

				if ( collection[ key ] && collection[ key ] !== this.instance ) {
					if ( Backbone.Relational.showWarnings && typeof console !== 'undefined' ) {
						console.warn( 'Relation=%o; collectionKey=%s already exists on collection=%o', this, key, this.options.collectionKey );
					}
				}
				else if ( key ) {
					collection[ key ] = this.instance;
				}
			}

			this.listenTo( collection, 'relational:add', this.handleAddition )
				.listenTo( collection, 'relational:remove', this.handleRemoval )
				.listenTo( collection, 'relational:reset', this.handleReset );

			return collection;
		},

		/**
		 * Find related Models.
		 * @param {Object} [options]
		 * @return {Backbone.Collection}
		 */
		findRelated: function( options ) {
			var related = null;

			options = _.defaults( { parse: this.options.parse }, options );

			// Replace 'this.related' by 'this.keyContents' if it is a Backbone.Collection
			if ( this.keyContents instanceof Backbone.Collection ) {
				this._prepareCollection( this.keyContents );
				related = this.keyContents;
			}
			// Otherwise, 'this.keyContents' should be an array of related object ids.
			// Re-use the current 'this.related' if it is a Backbone.Collection; otherwise, create a new collection.
			else {
				var toAdd = [];

				_.each( this.keyContents, function( attributes ) {
					var model = null;

					if ( attributes instanceof this.relatedModel ) {
						model = attributes;
					}
					else {
						// If `merge` is true, update models here, instead of during update.
						model = ( _.isObject( attributes ) && options.parse && this.relatedModel.prototype.parse ) ?
							this.relatedModel.prototype.parse( _.clone( attributes ), options ) : attributes;
					}

					model && toAdd.push( model );
				}, this );

				if ( this.related instanceof Backbone.Collection ) {
					related = this.related;
				}
				else {
					related = this._prepareCollection();
				}

				// By now, `parse` will already have been executed just above for models if specified.
				// Disable to prevent additional calls.
				related.set( toAdd, _.defaults( { parse: false }, options ) );
			}

			// Remove entries from `keyIds` that were already part of the relation (and are thus 'unchanged')
			this.keyIds = _.difference( this.keyIds, _.pluck( related.models, 'id' ) );

			return related;
		},

		/**
		 * Normalize and reduce `keyContents` to a list of `ids`, for easier comparison
		 * @param {String|Number|String[]|Number[]|Backbone.Collection} keyContents
		 */
		setKeyContents: function( keyContents ) {
			this.keyContents = keyContents instanceof Backbone.Collection ? keyContents : null;
			this.keyIds = [];

			if ( !this.keyContents && ( keyContents || keyContents === 0 ) ) { // since 0 can be a valid `id` as well
				// Handle cases the an API/user supplies just an Object/id instead of an Array
				this.keyContents = _.isArray( keyContents ) ? keyContents : [ keyContents ];

				_.each( this.keyContents, function( item ) {
					var itemId = Backbone.Relational.store.resolveIdForItem( this.relatedModel, item );
					if ( itemId || itemId === 0 ) {
						this.keyIds.push( itemId );
					}
				}, this );
			}
		},

		/**
		 * Event handler for `change:<key>`.
		 * If the contents of the key are changed, notify old & new reverse relations and initialize the new relation.
		 */
		onChange: function( model, attr, options ) {
			options = options ? _.clone( options ) : {};
			this.setKeyContents( attr );
			this.changed = false;

			var related = this.findRelated( options );
			this.setRelated( related );

			if ( !options.silent ) {
				var dit = this;
				Backbone.Relational.eventQueue.add( function() {
					// The `changed` flag can be set in `handleAddition` or `handleRemoval`
					if ( dit.changed ) {
						dit.instance.trigger( 'change:' + dit.key, dit.instance, dit.related, options, true );
						dit.changed = false;
					}
				});
			}
		},

		/**
		 * When a model is added to a 'HasMany', trigger 'add' on 'this.instance' and notify reverse relations.
		 * (should be 'HasOne', must set 'this.instance' as their related).
		 */
		handleAddition: function( model, coll, options ) {
			//console.debug('handleAddition called; args=%o', arguments);
			options = options ? _.clone( options ) : {};
			this.changed = true;

			_.each( this.getReverseRelations( model ), function( relation ) {
				relation.addRelated( this.instance, options );
			}, this );

			// Only trigger 'add' once the newly added model is initialized (so, has its relations set up)
			var dit = this;
			!options.silent && Backbone.Relational.eventQueue.add( function() {
				dit.instance.trigger( 'add:' + dit.key, model, dit.related, options );
			});
		},

		/**
		 * When a model is removed from a 'HasMany', trigger 'remove' on 'this.instance' and notify reverse relations.
		 * (should be 'HasOne', which should be nullified)
		 */
		handleRemoval: function( model, coll, options ) {
			//console.debug('handleRemoval called; args=%o', arguments);
			options = options ? _.clone( options ) : {};
			this.changed = true;

			_.each( this.getReverseRelations( model ), function( relation ) {
				relation.removeRelated( this.instance, null, options );
			}, this );

			var dit = this;
			!options.silent && Backbone.Relational.eventQueue.add( function() {
				dit.instance.trigger( 'remove:' + dit.key, model, dit.related, options );
			});
		},

		handleReset: function( coll, options ) {
			var dit = this;
			options = options ? _.clone( options ) : {};
			!options.silent && Backbone.Relational.eventQueue.add( function() {
				dit.instance.trigger( 'reset:' + dit.key, dit.related, options );
			});
		},

		tryAddRelated: function( model, coll, options ) {
			var item = _.contains( this.keyIds, model.id );

			if ( item ) {
				this.addRelated( model, options );
				this.keyIds = _.without( this.keyIds, model.id );
			}
		},

		addRelated: function( model, options ) {
			// Allow 'model' to set up its relations before proceeding.
			// (which can result in a call to 'addRelated' from a relation of 'model')
			var dit = this;
			model.queue( function() {
				if ( dit.related && !dit.related.get( model ) ) {
					dit.related.add( model, _.defaults( { parse: false }, options ) );
				}
			});
		},

		removeRelated: function( model, coll, options ) {
			if ( this.related.get( model ) ) {
				this.related.remove( model, options );
			}
		}
	});

	/**
	 * A type of Backbone.Model that also maintains relations to other models and collections.
	 * New events when compared to the original:
	 *  - 'add:<key>' (model, related collection, options)
	 *  - 'remove:<key>' (model, related collection, options)
	 *  - 'change:<key>' (model, related model or collection, options)
	 */
	Backbone.RelationalModel = Backbone.Model.extend({
		relations: null, // Relation descriptions on the prototype
		_relations: null, // Relation instances
		_isInitialized: false,
		_deferProcessing: false,
		_queue: null,
		_attributeChangeFired: false, // Keeps track of `change` event firing under some conditions (like nested `set`s)

		subModelTypeAttribute: 'type',
		subModelTypes: null,

		constructor: function( attributes, options ) {
			// Nasty hack, for cases like 'model.get( <HasMany key> ).add( item )'.
			// Defer 'processQueue', so that when 'Relation.createModels' is used we trigger 'HasMany'
			// collection events only after the model is really fully set up.
			// Example: event for "p.on( 'add:jobs' )" -> "p.get('jobs').add( { company: c.id, person: p.id } )".
			if ( options && options.collection ) {
				var dit = this,
					collection = this.collection = options.collection;

				// Prevent `collection` from cascading down to nested models; they shouldn't go into this `if` clause.
				delete options.collection;

				this._deferProcessing = true;

				var processQueue = function( model ) {
					if ( model === dit ) {
						dit._deferProcessing = false;
						dit.processQueue();
						collection.off( 'relational:add', processQueue );
					}
				};
				collection.on( 'relational:add', processQueue );

				// So we do process the queue eventually, regardless of whether this model actually gets added to 'options.collection'.
				_.defer( function() {
					processQueue( dit );
				});
			}

			Backbone.Relational.store.processOrphanRelations();
			Backbone.Relational.store.listenTo( this, 'relational:unregister', Backbone.Relational.store.unregister );

			this._queue = new Backbone.BlockingQueue();
			this._queue.block();
			Backbone.Relational.eventQueue.block();

			try {
				Backbone.Model.apply( this, arguments );
			}
			finally {
				// Try to run the global queue holding external events
				Backbone.Relational.eventQueue.unblock();
			}
		},

		/**
		 * Override 'trigger' to queue 'change' and 'change:*' events
		 */
		trigger: function( eventName ) {
			if ( eventName.length > 5 && eventName.indexOf( 'change' ) === 0 ) {
				var dit = this,
					args = arguments;

				if ( !Backbone.Relational.eventQueue.isBlocked() ) {
					// If we're not in a more complicated nested scenario, fire the change event right away
					Backbone.Model.prototype.trigger.apply( dit, args );
				}
				else {
					Backbone.Relational.eventQueue.add( function() {
						// Determine if the `change` event is still valid, now that all relations are populated
						var changed = true;
						if ( eventName === 'change' ) {
							// `hasChanged` may have gotten reset by nested calls to `set`.
							changed = dit.hasChanged() || dit._attributeChangeFired;
							dit._attributeChangeFired = false;
						}
						else {
							var attr = eventName.slice( 7 ),
								rel = dit.getRelation( attr );

							if ( rel ) {
								// If `attr` is a relation, `change:attr` get triggered from `Relation.onChange`.
								// These take precedence over `change:attr` events triggered by `Model.set`.
								// The relation sets a fourth attribute to `true`. If this attribute is present,
								// continue triggering this event; otherwise, it's from `Model.set` and should be stopped.
								changed = ( args[ 4 ] === true );

								// If this event was triggered by a relation, set the right value in `this.changed`
								// (a Collection or Model instead of raw data).
								if ( changed ) {
									dit.changed[ attr ] = args[ 2 ];
								}
								// Otherwise, this event is from `Model.set`. If the relation doesn't report a change,
								// remove attr from `dit.changed` so `hasChanged` doesn't take it into account.
								else if ( !rel.changed ) {
									delete dit.changed[ attr ];
								}
							}
							else if ( changed ) {
								dit._attributeChangeFired = true;
							}
						}

						changed && Backbone.Model.prototype.trigger.apply( dit, args );
					});
				}
			}
			else if ( eventName === 'destroy' ) {
				Backbone.Model.prototype.trigger.apply( this, arguments );
				Backbone.Relational.store.unregister( this );
			}
			else {
				Backbone.Model.prototype.trigger.apply( this, arguments );
			}

			return this;
		},

		/**
		 * Initialize Relations present in this.relations; determine the type (HasOne/HasMany), then creates a new instance.
		 * Invoked in the first call so 'set' (which is made from the Backbone.Model constructor).
		 */
		initializeRelations: function( options ) {
			this.acquire(); // Setting up relations often also involve calls to 'set', and we only want to enter this function once
			this._relations = {};

			_.each( this.relations || [], function( rel ) {
				Backbone.Relational.store.initializeRelation( this, rel, options );
			}, this );

			this._isInitialized = true;
			this.release();
			this.processQueue();
		},

		/**
		 * When new values are set, notify this model's relations (also if options.silent is set).
		 * (called from `set`; Relation.setRelated locks this model before calling 'set' on it to prevent loops)
		 * @param {Object} [changedAttrs]
		 * @param {Object} [options]
		 */
		updateRelations: function( changedAttrs, options ) {
			if ( this._isInitialized && !this.isLocked() ) {
				_.each( this._relations, function( rel ) {
					if ( !changedAttrs || ( rel.keySource in changedAttrs || rel.key in changedAttrs ) ) {
						// Fetch data in `rel.keySource` if data got set in there, or `rel.key` otherwise
						var value = this.attributes[ rel.keySource ] || this.attributes[ rel.key ],
							attr = changedAttrs && ( changedAttrs[ rel.keySource ] || changedAttrs[ rel.key ] );

						// Update a relation if its value differs from this model's attributes, or it's been explicitly nullified.
						// Which can also happen before the originally intended related model has been found (`val` is null).
						if ( rel.related !== value || ( value === null && attr === null ) ) {
							this.trigger( 'relational:change:' + rel.key, this, value, options || {} );
						}
					}

					// Explicitly clear 'keySource', to prevent a leaky abstraction if 'keySource' differs from 'key'.
					if ( rel.keySource !== rel.key ) {
						delete this.attributes[ rel.keySource ];
					}
				}, this );
			}
		},

		/**
		 * Either add to the queue (if we're not initialized yet), or execute right away.
		 */
		queue: function( func ) {
			this._queue.add( func );
		},

		/**
		 * Process _queue
		 */
		processQueue: function() {
			if ( this._isInitialized && !this._deferProcessing && this._queue.isBlocked() ) {
				this._queue.unblock();
			}
		},

		/**
		 * Get a specific relation.
		 * @param {string} attr The relation key to look for.
		 * @return {Backbone.Relation} An instance of 'Backbone.Relation', if a relation was found for 'attr', or null.
		 */
		getRelation: function( attr ) {
			return this._relations[ attr ];
		},

		/**
		 * Get all of the created relations.
		 * @return {Backbone.Relation[]}
		 */
		getRelations: function() {
			return _.values( this._relations );
		},


		/**
		 * Get a list of ids that will be fetched on a call to `getAsync`.
		 * @param {string|Backbone.Relation} attr The relation key to fetch models for.
		 * @param [refresh=false] Add ids for models that are already in the relation, refreshing them?
		 * @return {Array} An array of ids that need to be fetched.
		 */
		getIdsToFetch: function( attr, refresh ) {
			var rel = attr instanceof Backbone.Relation ? attr : this.getRelation( attr ),
				ids = rel ? ( rel.keyIds && rel.keyIds.slice( 0 ) ) || ( ( rel.keyId || rel.keyId === 0 ) ? [ rel.keyId ] : [] ) : [];

			// On `refresh`, add the ids for current models in the relation to `idsToFetch`
			if ( refresh ) {
				var models = rel.related && ( rel.related.models || [ rel.related ] );
				_.each( models, function( model ) {
					if ( model.id || model.id === 0 ) {
						ids.push( model.id );
					}
				});
			}

			return ids;
		},

		/**
		 * Get related objects. Returns a single promise, which can either resolve immediately (if the related model[s])
		 * are already present locally, or after fetching the contents of the requested attribute.
		 * @param {string} attr The relation key to fetch models for.
		 * @param {Object} [options] Options for 'Backbone.Model.fetch' and 'Backbone.sync'.
		 * @param {Boolean} [options.refresh=false] Fetch existing models from the server as well (in order to update them).
		 * @return {jQuery.Deferred} A jQuery promise object. When resolved, its `done` callback will be called with
		 *  contents of `attr`.
		 */
		getAsync: function( attr, options ) {
			// Set default `options` for fetch
			options = _.extend( { add: true, remove: false, refresh: false }, options );

			var dit = this,
				requests = [],
				rel = this.getRelation( attr ),
				idsToFetch = rel && this.getIdsToFetch( rel, options.refresh ),
				coll = rel.related instanceof Backbone.Collection ? rel.related : rel.relatedCollection;

			if ( idsToFetch && idsToFetch.length ) {
				var models = [],
					createdModels = [],
					setUrl,
					createModels = function() {
						// Find (or create) a model for each one that is to be fetched
						models = _.map( idsToFetch, function( id ) {
							var model = rel.relatedModel.findModel( id );

							if ( !model ) {
								var attrs = {};
								attrs[ rel.relatedModel.prototype.idAttribute ] = id;
								model = rel.relatedModel.findOrCreate( attrs, options );
								createdModels.push( model );
							}

							return model;
						}, this );
					};

				// Try if the 'collection' can provide a url to fetch a set of models in one request.
				// This assumes that when 'Backbone.Collection.url' is a function, it can handle building of set urls.
				// To make sure it can, test if the url we got by supplying a list of models to fetch is different from
				// the one supplied for the default fetch action (without args to 'url').
				if ( coll instanceof Backbone.Collection && _.isFunction( coll.url ) ) {
					var defaultUrl = coll.url();
					setUrl = coll.url( idsToFetch );

					if ( setUrl === defaultUrl ) {
						createModels();
						setUrl = coll.url( models );

						if ( setUrl === defaultUrl ) {
							setUrl = null;
						}
					}
				}

				if ( setUrl ) {
					// Do a single request to fetch all models
					var opts = _.defaults(
						{
							error: function() {
								_.each( createdModels, function( model ) {
									model.trigger( 'destroy', model, model.collection, options );
								});

								options.error && options.error.apply( models, arguments );
							},
							url: setUrl
						},
						options
					);

					requests = [ coll.fetch( opts ) ];
				}
				else {
					// Make a request per model to fetch
					if  ( !models.length ) {
						createModels();
					}

					requests = _.map( models, function( model ) {
						var opts = _.defaults(
							{
								error: function() {
									if ( _.contains( createdModels, model ) ) {
										model.trigger( 'destroy', model, model.collection, options );
									}
									options.error && options.error.apply( models, arguments );
								}
							},
							options
						);
						return model.fetch( opts );
					}, this );
				}
			}

			return this.deferArray(requests).then(
				function() {
					return Backbone.Model.prototype.get.call( dit, attr );
				}
			);
		},

		deferArray: function(deferArray) {
			return Backbone.$.when.apply(null, deferArray);
		},

		set: function( key, value, options ) {
			Backbone.Relational.eventQueue.block();

			// Duplicate backbone's behavior to allow separate key/value parameters, instead of a single 'attributes' object
			var attributes,
				result;

			if ( _.isObject( key ) || key == null ) {
				attributes = key;
				options = value;
			}
			else {
				attributes = {};
				attributes[ key ] = value;
			}

			try {
				var id = this.id,
					newId = attributes && this.idAttribute in attributes && attributes[ this.idAttribute ];

				// Check if we're not setting a duplicate id before actually calling `set`.
				Backbone.Relational.store.checkId( this, newId );

				result = Backbone.Model.prototype.set.apply( this, arguments );

				// Ideal place to set up relations, if this is the first time we're here for this model
				if ( !this._isInitialized && !this.isLocked() ) {
					this.constructor.initializeModelHierarchy();

					// Only register models that have an id. A model will be registered when/if it gets an id later on.
					if ( newId || newId === 0 ) {
						Backbone.Relational.store.register( this );
					}

					this.initializeRelations( options );
				}
				// The store should know about an `id` update asap
				else if ( newId && newId !== id ) {
					Backbone.Relational.store.update( this );
				}

				if ( attributes ) {
					this.updateRelations( attributes, options );
				}
			}
			finally {
				// Try to run the global queue holding external events
				Backbone.Relational.eventQueue.unblock();
			}

			return result;
		},

		clone: function() {
			var attributes = _.clone( this.attributes );
			if ( !_.isUndefined( attributes[ this.idAttribute ] ) ) {
				attributes[ this.idAttribute ] = null;
			}

			_.each( this.getRelations(), function( rel ) {
				delete attributes[ rel.key ];
			});

			return new this.constructor( attributes );
		},

		/**
		 * Convert relations to JSON, omits them when required
		 */
		toJSON: function( options ) {
			// If this Model has already been fully serialized in this branch once, return to avoid loops
			if ( this.isLocked() ) {
				return this.id;
			}

			this.acquire();
			var json = Backbone.Model.prototype.toJSON.call( this, options );

			if ( this.constructor._superModel && !( this.constructor._subModelTypeAttribute in json ) ) {
				json[ this.constructor._subModelTypeAttribute ] = this.constructor._subModelTypeValue;
			}

			_.each( this._relations, function( rel ) {
				var related = json[ rel.key ],
					includeInJSON = rel.options.includeInJSON,
					value = null;

				if ( includeInJSON === true ) {
					if ( related && _.isFunction( related.toJSON ) ) {
						value = related.toJSON( options );
					}
				}
				else if ( _.isString( includeInJSON ) ) {
					if ( related instanceof Backbone.Collection ) {
						value = related.pluck( includeInJSON );
					}
					else if ( related instanceof Backbone.Model ) {
						value = related.get( includeInJSON );
					}

					// Add ids for 'unfound' models if includeInJSON is equal to (only) the relatedModel's `idAttribute`
					if ( includeInJSON === rel.relatedModel.prototype.idAttribute ) {
						if ( rel instanceof Backbone.HasMany ) {
							value = value.concat( rel.keyIds );
						}
						else if ( rel instanceof Backbone.HasOne ) {
							value = value || rel.keyId;

							if ( !value && !_.isObject( rel.keyContents ) ) {
								value = rel.keyContents || null;
							}
						}
					}
				}
				else if ( _.isArray( includeInJSON ) ) {
					if ( related instanceof Backbone.Collection ) {
						value = [];
						related.each( function( model ) {
							var curJson = {};
							_.each( includeInJSON, function( key ) {
								curJson[ key ] = model.get( key );
							});
							value.push( curJson );
						});
					}
					else if ( related instanceof Backbone.Model ) {
						value = {};
						_.each( includeInJSON, function( key ) {
							value[ key ] = related.get( key );
						});
					}
				}
				else {
					delete json[ rel.key ];
				}

				// In case of `wait: true`, Backbone will simply push whatever's passed into `save` into attributes.
				// We'll want to get this information into the JSON, even if it doesn't conform to our normal
				// expectations of what's contained in it (no model/collection for a relation, etc).
				if ( value === null && options && options.wait ) {
					value = related;
				}

				if ( includeInJSON ) {
					json[ rel.keyDestination ] = value;
				}

				if ( rel.keyDestination !== rel.key ) {
					delete json[ rel.key ];
				}
			});

			this.release();
			return json;
		}
	},
	{
		/**
		 *
		 * @param superModel
		 * @returns {Backbone.RelationalModel.constructor}
		 */
		setup: function( superModel ) {
			// We don't want to share a relations array with a parent, as this will cause problems with reverse
			// relations. Since `relations` may also be a property or function, only use slice if we have an array.
			this.prototype.relations = ( this.prototype.relations || [] ).slice( 0 );

			this._subModels = {};
			this._superModel = null;

			// If this model has 'subModelTypes' itself, remember them in the store
			if ( this.prototype.hasOwnProperty( 'subModelTypes' ) ) {
				Backbone.Relational.store.addSubModels( this.prototype.subModelTypes, this );
			}
			// The 'subModelTypes' property should not be inherited, so reset it.
			else {
				this.prototype.subModelTypes = null;
			}

			// Initialize all reverseRelations that belong to this new model.
			_.each( this.prototype.relations || [], function( rel ) {
				if ( !rel.model ) {
					rel.model = this;
				}

				if ( rel.reverseRelation && rel.model === this ) {
					var preInitialize = true;
					if ( _.isString( rel.relatedModel ) ) {
						/**
						 * The related model might not be defined for two reasons
						 *  1. it is related to itself
						 *  2. it never gets defined, e.g. a typo
						 *  3. the model hasn't been defined yet, but will be later
						 * In neither of these cases do we need to pre-initialize reverse relations.
						 * However, for 3. (which is, to us, indistinguishable from 2.), we do need to attempt
						 * setting up this relation again later, in case the related model is defined later.
						 */
						var relatedModel = Backbone.Relational.store.getObjectByName( rel.relatedModel );
						preInitialize = relatedModel && ( relatedModel.prototype instanceof Backbone.RelationalModel );
					}

					if ( preInitialize ) {
						Backbone.Relational.store.initializeRelation( null, rel );
					}
					else if ( _.isString( rel.relatedModel ) ) {
						Backbone.Relational.store.addOrphanRelation( rel );
					}
				}
			}, this );

			return this;
		},

		/**
		 * Create a 'Backbone.Model' instance based on 'attributes'.
		 * @param {Object} attributes
		 * @param {Object} [options]
		 * @return {Backbone.Model}
		 */
		build: function( attributes, options ) {
			// 'build' is a possible entrypoint; it's possible no model hierarchy has been determined yet.
			this.initializeModelHierarchy();

			// Determine what type of (sub)model should be built if applicable.
			var model = this._findSubModelType( this, attributes ) || this;

			return new model( attributes, options );
		},

		/**
		 * Determines what type of (sub)model should be built if applicable.
		 * Looks up the proper subModelType in 'this._subModels', recursing into
		 * types until a match is found.  Returns the applicable 'Backbone.Model'
		 * or null if no match is found.
		 * @param {Backbone.Model} type
		 * @param {Object} attributes
		 * @return {Backbone.Model}
		 */
		_findSubModelType: function( type, attributes ) {
			if ( type._subModels && type.prototype.subModelTypeAttribute in attributes ) {
				var subModelTypeAttribute = attributes[ type.prototype.subModelTypeAttribute ];
				var subModelType = type._subModels[ subModelTypeAttribute ];
				if ( subModelType ) {
					return subModelType;
				}
				else {
					// Recurse into subModelTypes to find a match
					for ( subModelTypeAttribute in type._subModels ) {
						subModelType = this._findSubModelType( type._subModels[ subModelTypeAttribute ], attributes );
						if ( subModelType ) {
							return subModelType;
						}
					}
				}
			}

			return null;
		},

		/**
		 *
		 */
		initializeModelHierarchy: function() {
			// Inherit any relations that have been defined in the parent model.
			this.inheritRelations();

			// If we came here through 'build' for a model that has 'subModelTypes' then try to initialize the ones that
			// haven't been resolved yet.
			if ( this.prototype.subModelTypes ) {
				var resolvedSubModels = _.keys( this._subModels );
				var unresolvedSubModels = _.omit( this.prototype.subModelTypes, resolvedSubModels );
				_.each( unresolvedSubModels, function( subModelTypeName ) {
					var subModelType = Backbone.Relational.store.getObjectByName( subModelTypeName );
					subModelType && subModelType.initializeModelHierarchy();
				});
			}
		},

		inheritRelations: function() {
			// Bail out if we've been here before.
			if ( !_.isUndefined( this._superModel ) && !_.isNull( this._superModel ) ) {
				return;
			}
			// Try to initialize the _superModel.
			Backbone.Relational.store.setupSuperModel( this );

			// If a superModel has been found, copy relations from the _superModel if they haven't been inherited automatically
			// (due to a redefinition of 'relations').
			if ( this._superModel ) {
				// The _superModel needs a chance to initialize its own inherited relations before we attempt to inherit relations
				// from the _superModel. You don't want to call 'initializeModelHierarchy' because that could cause sub-models of
				// this class to inherit their relations before this class has had chance to inherit it's relations.
				this._superModel.inheritRelations();
				if ( this._superModel.prototype.relations ) {
					// Find relations that exist on the '_superModel', but not yet on this model.
					var inheritedRelations = _.filter( this._superModel.prototype.relations || [], function( superRel ) {
						return !_.any( this.prototype.relations || [], function( rel ) {
							return superRel.relatedModel === rel.relatedModel && superRel.key === rel.key;
						}, this );
					}, this );

					this.prototype.relations = inheritedRelations.concat( this.prototype.relations );
				}
			}
			// Otherwise, make sure we don't get here again for this type by making '_superModel' false so we fail the
			// isUndefined/isNull check next time.
			else {
				this._superModel = false;
			}
		},

		/**
		 * Find an instance of `this` type in 'Backbone.Relational.store'.
		 * A new model is created if no matching model is found, `attributes` is an object, and `options.create` is true.
		 * - If `attributes` is a string or a number, `findOrCreate` will query the `store` and return a model if found.
		 * - If `attributes` is an object and is found in the store, the model will be updated with `attributes` unless `options.merge` is `false`.
		 * @param {Object|String|Number} attributes Either a model's id, or the attributes used to create or update a model.
		 * @param {Object} [options]
		 * @param {Boolean} [options.create=true]
		 * @param {Boolean} [options.merge=true]
		 * @param {Boolean} [options.parse=false]
		 * @return {Backbone.RelationalModel}
		 */
		findOrCreate: function( attributes, options ) {
			options || ( options = {} );
			var parsedAttributes = ( _.isObject( attributes ) && options.parse && this.prototype.parse ) ?
				this.prototype.parse( _.clone( attributes ), options ) : attributes;

			// If specified, use a custom `find` function to match up existing models to the given attributes.
			// Otherwise, try to find an instance of 'this' model type in the store
			var model = this.findModel( parsedAttributes );

			// If we found an instance, update it with the data in 'item' (unless 'options.merge' is false).
			// If not, create an instance (unless 'options.create' is false).
			if ( _.isObject( attributes ) ) {
				if ( model && options.merge !== false ) {
					// Make sure `options.collection` and `options.url` doesn't cascade to nested models
					delete options.collection;
					delete options.url;

					model.set( parsedAttributes, options );
				}
				else if ( !model && options.create !== false ) {
					model = this.build( parsedAttributes, _.defaults( { parse: false }, options ) );
				}
			}

			return model;
		},

		/**
		 * Find an instance of `this` type in 'Backbone.Relational.store'.
		 * - If `attributes` is a string or a number, `find` will query the `store` and return a model if found.
		 * - If `attributes` is an object and is found in the store, the model will be updated with `attributes` unless `options.merge` is `false`.
		 * @param {Object|String|Number} attributes Either a model's id, or the attributes used to create or update a model.
		 * @param {Object} [options]
		 * @param {Boolean} [options.merge=true]
		 * @param {Boolean} [options.parse=false]
		 * @return {Backbone.RelationalModel}
		 */
		find: function( attributes, options ) {
			options || ( options = {} );
			options.create = false;
			return this.findOrCreate( attributes, options );
		},

		/**
		 * A hook to override the matching when updating (or creating) a model.
		 * The default implementation is to look up the model by id in the store.
		 * @param {Object} attributes
		 * @returns {Backbone.RelationalModel}
		 */
		findModel: function( attributes ) {
			return Backbone.Relational.store.find( this, attributes );
		}
	});
	_.extend( Backbone.RelationalModel.prototype, Backbone.Semaphore );

	/**
	 * Override Backbone.Collection._prepareModel, so objects will be built using the correct type
	 * if the collection.model has subModels.
	 * Attempts to find a model for `attrs` in Backbone.store through `findOrCreate`
	 * (which sets the new properties on it if found), or instantiates a new model.
	 */
	Backbone.Collection.prototype.__prepareModel = Backbone.Collection.prototype._prepareModel;
	Backbone.Collection.prototype._prepareModel = function( attrs, options ) {
		var model;

		if ( attrs instanceof Backbone.Model ) {
			if ( !attrs.collection ) {
				attrs.collection = this;
			}
			model = attrs;
		}
		else {
			options = options ? _.clone( options ) : {};
			options.collection = this;

			if ( typeof this.model.findOrCreate !== 'undefined' ) {
				model = this.model.findOrCreate( attrs, options );
			}
			else {
				model = new this.model( attrs, options );
			}

			if ( model && model.validationError ) {
				this.trigger( 'invalid', this, attrs, options );
				model = false;
			}
		}

		return model;
	};


	/**
	 * Override Backbone.Collection.set, so we'll create objects from attributes where required,
	 * and update the existing models. Also, trigger 'relational:add'.
	 */
	var set = Backbone.Collection.prototype.__set = Backbone.Collection.prototype.set;
	Backbone.Collection.prototype.set = function( models, options ) {
		// Short-circuit if this Collection doesn't hold RelationalModels
		if ( !( this.model.prototype instanceof Backbone.RelationalModel ) ) {
			return set.call( this, models, options );
		}

		if ( options && options.parse ) {
			models = this.parse( models, options );
		}

		var singular = !_.isArray( models ),
			newModels = [],
			toAdd = [],
			model = null;

		models = singular ? ( models ? [ models ] : [] ) : _.clone( models );

		//console.debug( 'calling add on coll=%o; model=%o, options=%o', this, models, options );
		for ( var i = 0; i < models.length; i++ ) {
			model = models[i];
			if ( !( model instanceof Backbone.Model ) ) {
				model = Backbone.Collection.prototype._prepareModel.call( this, model, options );
			}
			if ( model ) {
				toAdd.push( model );
				if ( !( this.get( model ) || this.get( model.cid ) ) ) {
					newModels.push( model );
				}
				// If we arrive in `add` while performing a `set` (after a create, so the model gains an `id`),
				// we may get here before `_onModelEvent` has had the chance to update `_byId`.
				else if ( model.id !== null && model.id !== undefined ) {
					this._byId[ model.id ] = model;
				}
			}
		}

		// Add 'models' in a single batch, so the original add will only be called once (and thus 'sort', etc).
		// If `parse` was specified, the collection and contained models have been parsed now.
		toAdd = singular ? ( toAdd.length ? toAdd[ 0 ] : null ) : toAdd;
		var result = set.call( this, toAdd, _.defaults( { merge: false, parse: false }, options ) );

		for ( i = 0; i < newModels.length; i++ ) {
			model = newModels[i];
			// Fire a `relational:add` event for any model in `newModels` that has actually been added to the collection.
			if ( this.get( model ) || this.get( model.cid ) ) {
				this.trigger( 'relational:add', model, this, options );
			}
		}

		return result;
	};

	/**
	 * Override 'Backbone.Collection._removeModels' to trigger 'relational:remove'.
	 */
	var _removeModels = Backbone.Collection.prototype.___removeModels = Backbone.Collection.prototype._removeModels;
	Backbone.Collection.prototype._removeModels = function( models, options ) {
		// Short-circuit if this Collection doesn't hold RelationalModels
		if ( !( this.model.prototype instanceof Backbone.RelationalModel ) ) {
			return _removeModels.call( this, models, options );
		}

		var toRemove = [];

		//console.debug('calling remove on coll=%o; models=%o, options=%o', this, models, options );
		_.each( models, function( model ) {
			model = this.get( model ) || ( model && this.get( model.cid ) );
			model && toRemove.push( model );
		}, this );

		var result = _removeModels.call( this, toRemove, options );

		_.each( toRemove, function( model ) {
			this.trigger( 'relational:remove', model, this, options );
		}, this );

		return result;
	};

	/**
	 * Override 'Backbone.Collection.reset' to trigger 'relational:reset'.
	 */
	var reset = Backbone.Collection.prototype.__reset = Backbone.Collection.prototype.reset;
	Backbone.Collection.prototype.reset = function( models, options ) {
		options = _.extend( { merge: true }, options );
		var result = reset.call( this, models, options );

		if ( this.model.prototype instanceof Backbone.RelationalModel ) {
			this.trigger( 'relational:reset', this, options );
		}

		return result;
	};

	/**
	 * Override 'Backbone.Collection.sort' to trigger 'relational:reset'.
	 */
	var sort = Backbone.Collection.prototype.__sort = Backbone.Collection.prototype.sort;
	Backbone.Collection.prototype.sort = function( options ) {
		var result = sort.call( this, options );

		if ( this.model.prototype instanceof Backbone.RelationalModel ) {
			this.trigger( 'relational:reset', this, options );
		}

		return result;
	};

	/**
	 * Override 'Backbone.Collection.trigger' so 'add', 'remove' and 'reset' events are queued until relations
	 * are ready.
	 */
	var trigger = Backbone.Collection.prototype.__trigger = Backbone.Collection.prototype.trigger;
	Backbone.Collection.prototype.trigger = function( eventName ) {
		// Short-circuit if this Collection doesn't hold RelationalModels
		if ( !( this.model.prototype instanceof Backbone.RelationalModel ) ) {
			return trigger.apply( this, arguments );
		}

		if ( eventName === 'add' || eventName === 'remove' || eventName === 'reset' || eventName === 'sort' ) {
			var dit = this,
				args = arguments;

			if ( _.isObject( args[ 3 ] ) ) {
				args = _.toArray( args );
				// the fourth argument is the option object.
				// we need to clone it, as it could be modified while we wait on the eventQueue to be unblocked
				args[ 3 ] = _.clone( args[ 3 ] );
			}

			Backbone.Relational.eventQueue.add( function() {
				trigger.apply( dit, args );
			});
		}
		else {
			trigger.apply( this, arguments );
		}

		return this;
	};

	// Override .extend() to automatically call .setup()
	Backbone.RelationalModel.extend = function( protoProps, classProps ) {
		var child = Backbone.Model.extend.call( this, protoProps, classProps );

		child.setup( this );

		return child;
	};
}));