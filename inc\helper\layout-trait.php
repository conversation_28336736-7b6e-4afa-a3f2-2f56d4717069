<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package etrade
 */
trait LayoutTrait {

    // Sidebar
    public static function get_sidebar_http_options() {
        if ( isset( $_GET['sidebar'] ) ) {
            if ( $_GET['sidebar'] == 'left' ) {
                $layout = 'left-sidebar';
            }
            if ( $_GET['sidebar'] == 'right' ) {
                $layout = 'right-sidebar';
            }
            if ( $_GET['sidebar'] == 'full' ) {
                $layout = 'full-width';
            }
            return $layout;

        }
    }

    public static function wooc_axil_sidebar_options() {

        $axil_options = Helper::axil_get_options();

        /**
         * Set Condition
         */
        $sidebar = $axil_options['shop_sidebar'];

        return $sidebar;

    }

    public static function wooc_has_sidebar() {

        $axil_options = self::axil_get_options();
        $condipfix = self::wooc_layout_settings();
        $layout = $axil_options['shop_layout'];

        if ( isset( $_GET['sidebar'] ) ) {
            if ( $_GET['sidebar'] == 'left' ) {
                $layout = 'left-sidebar';
            }
            if ( $_GET['sidebar'] == 'right' ) {
                $layout = 'right-sidebar';
            }
            if ( $_GET['sidebar'] == 'full' ) {
                $layout = 'full-width';
            }
        }

        $sidebar = Helper::wooc_axil_sidebar_options();
        $has_sidebar_widgets = false;

        if ( $sidebar ) {
            if ( is_active_sidebar( $sidebar ) ) {
                $has_sidebar_widgets = true;
            }
        } else {
            if ( is_active_sidebar( 'sidebar' ) ) {
                $has_sidebar_widgets = true;
            }
        }

        if ( $layout != 'full-width' ) {
            return true;
        } else {
            return false;
        }
    }

    public static function wooc_the_layout_class() {

        $layout_class = self::wooc_has_sidebar() ? 'col-xl-9 col-lg-9 col-12' : 'col-12';
        echo apply_filters( 'etrade_layout_class', $layout_class );
    }

    public static function wooc_the_sidebar_class() {

        echo apply_filters( 'etrade_sidebar_class', 'wooc-shop-sidebar col-xl-3 col-lg-3 col-12' );
    }

    /** layout settings */
    public static function wooc_layout_settings() {
        if ( is_single() || is_page() ) {
            $post_type = get_post_type();
            switch ( $post_type ) {
            case 'product':
                $themepfix = 'product';
                break;
            default:
                $themepfix = 'product';
                break;
            }
        } elseif ( Helper::is_page( 'is_woocommerce' ) ) {
            $themepfix = 'shop';
        } else {
            $themepfix = 'shop';
        }

        return $themepfix;
    }

    // layout style
    public static function axil_wooc_layout() {
        $axil_options = self::axil_get_options();
        $layout = $axil_options["shop_layout"];

        $sidebar = Helper::axil_sidebar_options();

        $has_sidebar_contnet = ( is_active_sidebar( $sidebar ) || is_active_sidebar( 'sidebar' ) ) ? 'col-xl-8 axil-main' : 'col-xl-12 axil-main';

        if ( isset( $_GET['sidebar'] ) ) {
            if ( $_GET['sidebar'] == 'left' ) {
                $layout = 'left-sidebar';
            }
            if ( $_GET['sidebar'] == 'right' ) {
                $layout = 'right-sidebar';
            }
            if ( $_GET['sidebar'] == 'full' ) {
                $layout = 'full-width';
            }
        }

        // Layout class
        if ( $layout == 'full-width' ) {
            $layout_class = 'col-12 full-shop-layout';
            $post_class = 'col-lg-4';
        } else {
            $layout_class = $has_sidebar_contnet;
            $post_class = 'col-lg-6';
        }
        $layout = array(
            'layout'       => $layout,
            'layout_class' => $layout_class,
            'post_class'   => $post_class,
        );
        return $layout;
    }

    public static function wooc_axil_left_get_sidebar() {
        $layout_abj = Helper::axil_wooc_layout();
        $layout = $layout_abj['layout'];
        $sidebar = Helper::wooc_axil_sidebar_options();
        if ( $layout == 'left-sidebar' ) {?>
            <div class="col-lg-3">
            <div class="axil-shop-sidebar">
                <div class="d-lg-none">
                    <button class="sidebar-close filter-close-btn"><i class="fas fa-times"></i></button>
                </div>
                <?php
if ( $sidebar ) {
            dynamic_sidebar( $sidebar );
        } else {
            dynamic_sidebar( 'sidebar-1' );
        }
            ?>
            </div>
            </div>
        <?php
}
        return;
    }

    public static function wooc_axil_right_get_sidebar() {

        $layout_abj = Helper::axil_wooc_layout();
        $layout = $layout_abj['layout'];
        $sidebar = Helper::wooc_axil_sidebar_options();
        if ( $layout == 'right-sidebar' ) {?>
            <div class="col-lg-3">
            <div class="axil-shop-sidebar">
                <div class="d-lg-none">
                    <button class="sidebar-close filter-close-btn"><i class="fas fa-times"></i></button>
                </div>
                <?php
if ( $sidebar ) {
            dynamic_sidebar( $sidebar );
        } else {
            dynamic_sidebar( 'sidebar-1' );
        }
            ?>
            </div>
            </div>
        <?php
}
        return;
    }

    public static function axil_left_get_sidebar() {
        $layout_abj = Helper::axil_layout_style_all();
        $layout = $layout_abj['layout'];
        if ( $layout == 'left-sidebar' ) {
            get_sidebar();
        }
        return;
    }

    public static function axil_right_get_sidebar() {
        $layout_abj = Helper::axil_layout_style_all();
        $layout = $layout_abj['layout'];
        if ( $layout == 'right-sidebar' ) {
            get_sidebar();
        }
        return;
    }

    /**
     * @return array
     * Header Layout
     */
    public static function axil_header_layout() {
        $axil_options = Helper::axil_get_options();
        $themepfix = AXIL_THEME_FIX;

        /**
         * Get Page Options value
         */
        if ( class_exists( 'ACF' ) ) {
            $header_area = get_field( $themepfix . '_show_header' );
            $header_style = get_field( $themepfix . "_select_header_style" );
            $header_sticky = get_field( $themepfix . "_header_sticky" );

        }

        /**
         * Set Condition
         */
        $header_area = ( empty( $header_area ) ) ? $axil_options['axil_enable_header'] : $header_area;
        $header_style = ( empty( $header_style ) || $header_style == "0" ) ? $axil_options['axil_select_header_template'] : $header_style;
        $header_sticky = ( empty( $header_sticky ) ) ? $axil_options['axil_header_sticky'] : $header_sticky;

        /**
         * Load Value
         */
        $header_layout = array(
            'header_area'   => $header_area,
            'header_style'  => $header_style,
            'header_sticky' => $header_sticky,

        );
        return $header_layout;

    }

    /**
     * @return array
     * Header Top Layout
     */
    public static function axil_header_top_layout() {
        $axil_options = Helper::axil_get_options();
        $themepfix = AXIL_THEME_FIX;

        /**
         * Get Page Options value
         */
        if ( class_exists( 'ACF' ) ) {
            $header_area = get_field( $themepfix . '_show_header' );
            $header_style = get_field( $themepfix . "_select_header_style" );
            $header_sticky = get_field( $themepfix . "_header_sticky" );

        }

        /**
         * Set Condition
         */
        $header_top_area = ( empty( $header_top_area ) ) ? $axil_options['axil_header_top_enable'] : $header_top_area;
        $header_top_style = ( empty( $header_top_style ) || $header_top_style == "0" ) ? $axil_options['axil_select_header_top_template'] : $header_top_style;

        /**
         * Load Value
         */
        $header_layout = array(
            'header_area'   => $header_area,
            'header_style'  => $header_style,
            'header_sticky' => $header_sticky,

        );
        return $header_layout;

    }

    /**
     * @return array
     * Footer Layout
     */
    public static function axil_footer_layout() {
        $axil_options = Helper::axil_get_options();

        /**
         * Get Page Options value
         */

        $footer_area = axil_get_acf_data( 'axil_show_footer' );
        $footer_style = axil_get_acf_data( 'axil_select_footer_style' );

        /**
         * Set Condition
         */
        $footer_area = ( empty( $footer_area ) ) ? $axil_options['axil_footer_enable'] : $footer_area;
        $footer_style = ( empty( $footer_style ) || $footer_style == "0" ) ? $axil_options['axil_select_footer_template'] : $footer_style;

        /**
         * Load Value
         */
        $footer_layout = array(
            'footer_area'  => $footer_area,
            'footer_style' => $footer_style,
        );
        return $footer_layout;

    }

    /**
     * @return array
     * Footer Layout
     */
    public static function axil_shop_notification_enable() {
        $axil_options = Helper::axil_get_options();

        /**
         * Get Page Options value
         */
        $axil_shop_notification_enable = axil_get_acf_data( 'axil_shop_notification_enable' );

        /**
         * Set Condition
         */
        $axil_shop_notification_enable = ( empty( $axil_shop_notification_enable ) ) ? $axil_options['axil_shop_notification_enable'] : $axil_shop_notification_enable;

        /**
         * Load Value
         */
        $axil_shop_notification_enable = array(
            'shop_notification' => $axil_shop_notification_enable,
        );
        return $axil_shop_notification_enable;
    }

    /**
     * @return array
     * Footer Layout
     */
    public static function axil_post_banner_style() {
        $axil_options = Helper::axil_get_options();

        /**
         * Get Page Options value
         */
        $post_banner_style = axil_get_acf_data( 'select_banner_style' );
        $axil_single_post_style = ( isset( $axil_options['axil_single_post_style'] ) ) ? $axil_options['axil_single_post_style'] : "";

        /**
         * Set Condition
         */
        $post_banner_style = ( empty( $post_banner_style ) || $post_banner_style == "0" ) ? $axil_single_post_style : $post_banner_style;

        /**
         * Load Value
         */
        $post_banner_layout = array(
            'post_banner_style' => $post_banner_style,
        );
        return $post_banner_layout;

    }

    /**
     * @return array
     * Footer Layout
     */
    public static function axil_product_layout_style() {
        $axil_options = Helper::axil_get_options();

        /**
         * Get Page Options value
         */

        $product_wc_single_layout = axil_get_acf_data( 'product_wc_single_layout' );
        $axil_product_wc_single_layout = ( isset( $axil_options['product_wc_single_layout'] ) ) ? $axil_options['product_wc_single_layout'] : "";

        /**
         * Set Condition
         */
        $layout = ( empty( $product_wc_single_layout ) || $product_wc_single_layout == "0" ) ? $axil_product_wc_single_layout : $product_wc_single_layout;

        return $layout;

    }

    /**
     * @return array
     * Footer Layout
     */
    public static function axil_footer_top_layout() {
        $axil_options = Helper::axil_get_options();

        /**
         * Get Page Options value
         */
        $footer_top_area = axil_get_acf_data( 'field_show_footer_top' );
        /**
         * Set Condition
         */
        $footer_top_area = ( empty( $footer_top_area ) ) ? $axil_options['axil_footer_top_enable'] : $footer_top_area;

        /**
         * Load Value
         */
        $footer_top_layout = array(
            'footer_top_area' => $footer_top_area,
        );
        return $footer_top_layout;

    }

    // Sidebar
    public static function axil_sidebar_options() {

        $axil_options = Helper::axil_get_options();

        /**
         * Get Page Options value
         */
        $select_sidebar = axil_get_acf_data( 'select_sidebar' );

        /**
         * Set Condition
         */
        $sidebar = ( empty( $select_sidebar ) || $select_sidebar == "0" ) ? $axil_options['axil_single_pos'] : $select_sidebar;

        return $sidebar;

    }

    // Menu Option
    public static function axil_logos() {
        $axil_options = self::axil_get_options();
        // Logo
        $axil_dark_logo = empty( $axil_options['logo']['url'] ) ? self::get_img( 'logo-black.svg' ) : $axil_options['logo']['url'];
        $axil_light_logo = empty( $axil_options['logo_light']['url'] ) ? self::get_img( 'logo-white.svg' ) : $axil_options['logo_light']['url'];
        $axil_logo_symbol = empty( $axil_options['logo_symbol']['url'] ) ? self::get_img( 'logo-symbol.svg' ) : $axil_options['logo_symbol']['url'];

        $menu_option = array(
            'axil_dark_logo'   => $axil_dark_logo,
            'axil_light_logo'  => $axil_light_logo,
            'axil_logo_symbol' => $axil_logo_symbol,
        );
        return $menu_option;
    }

    // Page layout style
    public static function axil_layout_style() {
        $themepfix = AXIL_THEME_FIX;
        $axil_options = self::axil_get_options();
        $condipfix = self::layout_settings();

        if ( is_single() || is_page() ) {
            $layout = get_post_meta( get_the_ID(), $themepfix . "_layout", true );
            $layout = ( empty( $layout ) || $layout == 'default' ) ? $axil_options[$condipfix . "_layout"] : $layout;

        } elseif ( is_home() || is_archive() || is_search() || is_404() ) {
            $layout = ( empty( $layout ) || $layout == 'default' ) ? $axil_options[$condipfix . "_layout"] : $layout;
        }

        return $layout;
    }

    // layout style
    public static function axil_layout_style_all() {
        $themepfix = AXIL_THEME_FIX;
        $axil_options = self::axil_get_options();
        $condipfix = self::layout_settings();
        $sidebar = Helper::axil_sidebar_options();
        $has_sidebar_contnet = ( is_active_sidebar( $sidebar ) || is_active_sidebar( 'sidebar' ) ) ? 'col-xl-8 axil-main' : 'col-xl-12 axil-main';

        if ( is_single() || is_page() ) {
            $layout = get_post_meta( get_the_ID(), $themepfix . "_layout", true );
            $layout = ( empty( $layout ) || $layout == 'default' ) ? $axil_options[$condipfix . "_layout"] : $layout;

        } elseif ( is_home() || is_archive() || is_search() || is_404() ) {
            $layout = ( empty( $layout ) || $layout == 'default' ) ? $axil_options[$condipfix . "_layout"] : $layout;
        }

        if ( isset( $_GET['sidebar'] ) ) {
            if ( $_GET['sidebar'] == 'left' ) {
                $layout = 'left-sidebar';
            }
            if ( $_GET['sidebar'] == 'right' ) {
                $layout = 'right-sidebar';
            }
            if ( $_GET['sidebar'] == 'full' ) {
                $layout = 'full-width';
            }
        }

        // Layout class
        if ( $layout == 'full-width' ) {
            $layout_class = 'col-12';
            $post_class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12 masonry-item';
        } else {
            $layout_class = $has_sidebar_contnet;
            $post_class = 'col-12';
        }

        $layout = array(
            'layout'       => $layout,
            'layout_class' => $layout_class,
            'post_class'   => $post_class,
        );
        return $layout;
    }

    // layout style
    public static function axil_layout_custom_taxonomy() {
        $axil_options = self::axil_get_options();
        $condipfix = self::layout_settings();
        $layout = $axil_options[$condipfix . "_layout"];
        $sidebar = Helper::axil_sidebar_options();
        $has_sidebar_contnet = ( is_active_sidebar( $sidebar ) || is_active_sidebar( 'sidebar' ) ) ? 'col-xl-8 axil-main' : 'col-xl-12 axil-main';

        // Layout class
        if ( $layout == 'full-width' ) {
            $layout_class = 'col-12';
            $post_class = 'col-lg-4';
        } else {
            $layout_class = $has_sidebar_contnet;
            $post_class = 'col-lg-6';
        }

        if ( isset( $_GET['sidebar'] ) ) {
            if ( $_GET['sidebar'] == 'left' ) {
                $layout = 'left-sidebar';
            }
            if ( $_GET['sidebar'] == 'right' ) {
                $layout = 'right-sidebar';
            }
            if ( $_GET['sidebar'] == 'full' ) {
                $layout = 'full-width';
            }
        }

        $layout = array(
            'layout'       => $layout,
            'layout_class' => $layout_class,
            'post_class'   => $post_class,
        );
        return $layout;
    }

    /**  Footer Options */
    public static function axil_active_footer() {
        $axil_options = Helper::axil_get_options();
        if ( !$axil_options['footer_area'] ) {
            return false;
        }
        $footer_column = $axil_options['footer_column'];
        for ( $i = 1; $i <= $footer_column; $i++ ) {
            if ( is_active_sidebar( 'footer-' . $i ) ) {
                return true;
            }
        }
        return false;
    }

    /**
     * Custom Sidebar
     */
    public static function get_custom_sidebar_fields() {
        $themepfix = 'etrade';
        $sidebar_fields = array();
        $sidebar_fields['sidebar'] = esc_html__( 'Sidebar', 'etrade' );
        $sidebar_fields['widgets-shop'] = esc_html__( 'shop', 'etrade' );
        $sidebars = get_option( "{$themepfix}_custom_sidebars", array() );
        if ( $sidebars ) {
            foreach ( $sidebars as $sidebar ) {
                $sidebar_fields[$sidebar['id']] = $sidebar['name'];
            }
        }
        return $sidebar_fields;
    }

    /** layout settings */
    public static function layout_settings() {
        if ( is_single() || is_page() ) {
            $post_type = get_post_type();

            switch ( $post_type ) {
            case 'page':
                $themepfix = 'page';
                break;
            case 'post':
                $themepfix = 'single_post';
                break;
            default:
                $themepfix = 'single_post';
                break;
            }
        } elseif ( is_home() || is_archive() || is_search() || is_404() ) {
            if ( is_author() ) {
                $themepfix = 'author';
            } elseif ( is_search() ) {
                $themepfix = 'search';
            } elseif ( is_post_type_archive( "team" ) || is_tax( "team_category" ) ) {
                $themepfix = 'team_archive';
            } else {
                $themepfix = 'blog';
            }
        }
        return $themepfix;
    }

}