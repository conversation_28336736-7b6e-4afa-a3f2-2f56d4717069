<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package etrade
 */

/**
 * Enqueue scripts and styles.
 */
if ( !function_exists( 'etrade_content_estimated_reading_time' ) ) {
    /**
     * Function that estimates reading time for a given $content.
     * @param string $content Content to calculate read time for.
     * @paramint $wpm Estimated words per minute of reader.
     * @returns int $time Esimated reading time.
     */
    function etrade_content_estimated_reading_time( $content = '', $wpm = 200 ) {
        $clean_content = strip_shortcodes( $content );
        $clean_content = strip_tags( $clean_content );
        $word_count = str_word_count( $clean_content );
        $time = ceil( $word_count / $wpm );
        $output = $time . esc_attr__( ' min read', 'etrade' );
        return $output;
    }
}

/**
 * Escapeing
 */
if ( !function_exists( 'awescapeing' ) ) {
    function awescapeing( $html ) {
        return $html;
    }
}

/**
 *  Convert Get Theme Option global to function
 */
if ( !function_exists( 'axil_get_opt' ) ) {
    function axil_get_opt() {
        global $axil_option;
        return $axil_option;
    }
}
/**
 * Get terms
 */
function axil_get_terms_gb( $term_type = null, $hide_empty = false ) {
    if ( !isset( $term_type ) ) {
        return;
    }
    $axil_custom_terms = array();
    $terms = get_terms( $term_type, array( 'hide_empty' => $hide_empty ) );
    array_push( $axil_custom_terms, esc_html__( '--- Select ---', 'etrade' ) );
    if ( is_array( $terms ) && !empty( $terms ) ) {
        foreach ( $terms as $single_term ) {
            if ( is_object( $single_term ) && isset( $single_term->name, $single_term->slug ) ) {
                $axil_custom_terms[$single_term->slug] = $single_term->name;
            }
        }
    }
    return $axil_custom_terms;
}

/**
 * Blog Pagination
 */
if ( !function_exists( 'axil_blog_pagination' ) ) {
    function axil_blog_pagination() {
        GLOBAL $wp_query;
        if ( $wp_query->post_count < $wp_query->found_posts ) {
            ?>
            <div class="post-pagination"> <?php
                the_posts_pagination( array(
                'prev_text' => '<i class="fal fa-arrow-left"></i>',
                'next_text' => '<i class="fal fa-arrow-right"></i>',
                'type'      => 'list',
                'show_all'  => false,
                'end_size'  => 1,
                'mid_size'  => 8,
            ) );?>
            </div>
            <?php
        }
    }
}

/**
 * Short Title
 */
if ( !function_exists( 'axil_short_title' ) ) {
    function axil_short_title( $title, $length = 30 ) {
        if ( strlen( $title ) > $length ) {
            return substr( $title, 0, $length ) . ' ...';
        } else {
            return $title;
        }
    }
}

/**
 * Get ACF data conditionally
 */
if ( !function_exists( 'axil_get_acf_data' ) ) {
    function axil_get_acf_data( $fields ) {
        return ( class_exists( 'ACF' ) && get_field_object( $fields ) ) ? get_field( $fields ) : false;
    }

}

/**
 * @param $url
 * @return string
 */
if ( !function_exists( 'axil_getEmbedUrl' ) ) {
    function axil_getEmbedUrl( $url ) {
        // function for generating an embed link
        $finalUrl = '';

        if ( strpos( $url, 'facebook.com/' ) !== false ) {
            // Facebook Video
            $finalUrl .= 'https://www.facebook.com/plugins/video.php?href=' . rawurlencode( $url ) . '&show_text=1&width=200';

        } else if ( strpos( $url, 'vimeo.com/' ) !== false ) {
            // Vimeo video
            $videoId = isset( explode( "vimeo.com/", $url )[1] ) ? explode( "vimeo.com/", $url )[1] : null;
            if ( strpos( $videoId, '&' ) !== false ) {
                $videoId = explode( "&", $videoId )[0];
            }
            $finalUrl .= 'https://player.vimeo.com/video/' . $videoId;

        } else if ( strpos( $url, 'youtube.com/' ) !== false ) {
            // Youtube video
            $videoId = isset( explode( "v=", $url )[1] ) ? explode( "v=", $url )[1] : null;
            if ( strpos( $videoId, '&' ) !== false ) {
                $videoId = explode( "&", $videoId )[0];
            }
            $finalUrl .= 'https://www.youtube.com/embed/' . $videoId;

        } else if ( strpos( $url, 'youtu.be/' ) !== false ) {
            // Youtube  video
            $videoId = isset( explode( "youtu.be/", $url )[1] ) ? explode( "youtu.be/", $url )[1] : null;
            if ( strpos( $videoId, '&' ) !== false ) {
                $videoId = explode( "&", $videoId )[0];
            }
            $finalUrl .= 'https://www.youtube.com/embed/' . $videoId;

        } else if ( strpos( $url, 'dailymotion.com/' ) !== false ) {
            // Dailymotion Video
            $videoId = isset( explode( "dailymotion.com/", $url )[1] ) ? explode( "dailymotion.com/", $url )[1] : null;
            if ( strpos( $videoId, '&' ) !== false ) {
                $videoId = explode( "&", $videoId )[0];
            }
            $finalUrl .= 'https://www.dailymotion.com/embed/' . $videoId;

        } else {
            $finalUrl .= $url;
        }

        return $finalUrl;
    }
}

/**
 * @param $prefix
 * @param $title
 * @param string $subtitle
 * @return array
 */
function axil_redux_add_fields( $prefix, $title, $subtitle = '' ) {
    return array(
        array(
            'id'       => $prefix . '_sec',
            'type'     => 'section',
            'title'    => $title,
            'subtitle' => $subtitle,
            'indent'   => true,
        ),
        array(
            'id'      => $prefix . '_activate',
            'type'    => 'switch',
            'title'   => esc_html__( 'Activate Ad', 'etrade' ),
            'on'      => esc_html__( 'Enabled', 'etrade' ),
            'off'     => esc_html__( 'Disabled', 'etrade' ),
            'default' => false,
        ),
        array(
            'id'       => $prefix . '_type',
            'type'     => 'button_set',
            'title'    => esc_html__( 'Ad Type', 'etrade' ),
            'options'  => array(
                'image' => esc_html__( 'Image Link', 'etrade' ),
                'code'  => esc_html__( 'Custom Code', 'etrade' ),
            ),
            'default'  => 'image',
            'required' => array( $prefix . '_activate', 'equals', true ),
        ),
        array(
            'id'       => $prefix . '_image',
            'type'     => 'media',
            'title'    => esc_html__( 'Image', 'etrade' ),
            'default'  => '',
            'required' => array( $prefix . '_type', 'equals', 'image' ),
        ),
        array(
            'id'       => $prefix . '_url',
            'type'     => 'text',
            'title'    => esc_html__( 'Link', 'etrade' ),
            'default'  => '',
            'required' => array( $prefix . '_type', 'equals', 'image' ),
        ),

        array(
            'id'       => $prefix . '_link_type',
            'type'     => 'button_set',
            'title'    => esc_html__( 'Open Advertisement Tab', 'etrade' ),
            'options'  => array(
                'blank' => esc_html__( 'Open in new tab', 'etrade' ),
                'same'  => esc_html__( 'Open in Same tab', 'etrade' ),
            ),
            'required' => array( $prefix . '_type', 'equals', 'image' ),
            'default'  => 'blank',

        ),
        array(
            'id'       => $prefix . '_code',
            'type'     => 'textarea',
            'title'    => esc_html__( 'Custom Code', 'etrade' ),
            'default'  => '',
            'subtitle' => esc_html__( 'Supports: Shortcode, Adsense, Text, HTML, Scripts', 'etrade' ),
            'required' => array( $prefix . '_type', 'equals', 'code' ),
        ),
    );
}

/***
 * pt_like_it
 */
add_action( 'wp_ajax_nopriv_pt_like_it', 'pt_like_it' );
add_action( 'wp_ajax_pt_like_it', 'pt_like_it' );
if ( !function_exists( 'pt_like_it' ) ) {
    function pt_like_it() {

        if ( !wp_verify_nonce( $_REQUEST['nonce'], 'pt_like_it_nonce' ) || !isset( $_REQUEST['nonce'] ) ) {
            exit( "No naughty business please" );
        }

        $likes = get_post_meta( $_REQUEST['post_id'], '_pt_likes', true );
        $likes = ( empty( $likes ) ) ? 0 : $likes;
        $new_likes = $likes + 1;

        update_post_meta( $_REQUEST['post_id'], '_pt_likes', $new_likes );

        if ( defined( 'DOING_AJAX' ) && DOING_AJAX ) {
            echo esc_html( $new_likes );
            die();
        } else {
            wp_redirect( get_permalink( $_REQUEST['post_id'] ) );
            exit();
        }
    }
}

/**
 * @param $tags
 * @param $context
 * @return array
 */
if ( !function_exists( 'etrade_kses_allowed_html' ) ) {
    function etrade_kses_allowed_html( $tags, $context ) {
        switch ( $context ) {
        case 'social':
            $tags = array(
                'a' => array( 'href' => array() ),
                'b' => array(),
            );
            return $tags;
        case 'allow_link':
            $tags = array(
                'a' => array(
                    'class'  => array(),
                    'href'   => array(),
                    'rel'    => array(),
                    'title'  => array(),
                    'target' => array(),
                ),
                'b' => array(),
            );
            return $tags;
        case 'allow_title':
            $tags = array(
                'a'    => array(
                    'class'  => array(),
                    'href'   => array(),
                    'rel'    => array(),
                    'title'  => array(),
                    'target' => array(),
                ),
                'span' => array(
                    'class' => array(),
                    'style' => array(),
                ),
                'b'    => array(),
            );
            return $tags;

        case 'alltext_allow':
            $tags = array(
                'a'          => array(
                    'class'  => array(),
                    'href'   => array(),
                    'rel'    => array(),
                    'title'  => array(),
                    'target' => array(),
                ),
                'abbr'       => array(
                    'title' => array(),
                ),
                'b'          => array(),
                'br'         => array(),
                'blockquote' => array(
                    'cite' => array(),
                ),
                'cite'       => array(
                    'title' => array(),
                ),
                'code'       => array(),
                'del'        => array(
                    'datetime' => array(),
                    'title'    => array(),
                ),
                'dd'         => array(),
                'div'        => array(
                    'class' => array(),
                    'title' => array(),
                    'style' => array(),
                    'id'    => array(),
                ),
                'dl'         => array(),
                'dt'         => array(),
                'em'         => array(),
                'h1'         => array(),
                'h2'         => array(),
                'h3'         => array(),
                'h4'         => array(),
                'h5'         => array(),
                'h6'         => array(),
                'i'          => array(
                    'class' => array(),
                ),
                'img'        => array(
                    'alt'    => array(),
                    'class'  => array(),
                    'height' => array(),
                    'src'    => array(),
                    'srcset' => array(),
                    'width'  => array(),
                ),
                'li'         => array(
                    'class' => array(),
                ),
                'ol'         => array(
                    'class' => array(),
                ),
                'p'          => array(
                    'class' => array(),
                ),
                'q'          => array(
                    'cite'  => array(),
                    'title' => array(),
                ),
                'span'       => array(
                    'class' => array(),
                    'title' => array(),
                    'style' => array(),
                ),
                'strike'     => array(),
                'strong'     => array(),
                'ul'         => array(
                    'class' => array(),
                ),
            );
            return $tags;
        default:
            return $tags;
        }
    }
    add_filter( 'wp_kses_allowed_html', 'etrade_kses_allowed_html', 10, 2 );
}

/**
 * Topbar Account
 */
add_action( 'axil_topbar_menu', 'axil_topbar_account' );

if ( !function_exists( 'etrade_tiny_account' ) ) {
    function etrade_tiny_account( $icon = false, $user = false ) {

        $login_url = '#';
        $register_url = '#';
        $profile_url = '#';

        /* Active woocommerce */
        if ( WOOC_WOO_ACTIVED ) {
            $myaccount_page_id = get_option( 'woocommerce_myaccount_page_id' );
            if ( $myaccount_page_id ) {
                $login_url = get_permalink( $myaccount_page_id );
                $register_url = wp_registration_url( $myaccount_page_id );
                $profile_url = $login_url;
            }
        } else {
            $login_url = wp_login_url();
            $register_url = wp_registration_url();
            $profile_url = admin_url( 'profile.php' );
        }
        $result = '<div class="singin-header-btn">';

        if ( !WOOC_CORE_USER_LOGGED && !$user ) { 
             if ( isset( $_GET['action'] ) ) {
                if ( in_array( $_GET['action'], array( 'lostpassword' ) ) ) { 
                    $result .= '<p>' . esc_attr__( 'Already a member?', 'etrade' ) . '</p>';
                $result .= '<a href="' . esc_url( $login_url ) . '" class="axil-btn btn-bg-secondary sign-up-btn">' . esc_attr__( 'Sign In', 'etrade' ) . '</a>';
                    }
               
            } else {

                $result .= '<div id="axil-already-member">';

                $result .= '<p>' . esc_attr__( 'Already a member?', 'etrade' ) . '</p>';
                $result .= ' <a id="axil-show-login-button" href="#" class="axil-btn btn-bg-secondary sign-up-btn">' . esc_attr__( 'Sign In', 'etrade' ) . '</a>';

                $result .= '</div>';

                $result .= '<div id="axil-not-member" class="inline slide-up fade-in">';
                $result .= '<p>' . esc_attr__( 'Not a member?', 'etrade' ) . '</p>';
                $result .= '<a id="axil-show-register-button" href="#" class="axil-btn btn-bg-secondary sign-up-btn">' . esc_attr__( ' Sign Up Now', 'etrade' ) . '</a>';
                $result .= '</div>';

            }

        } else {

            $result .= '<p>' . esc_attr__( ' Not a member?', 'etrade' ) . '</p>';
            $result .= '<a href="' . wp_logout_url( get_permalink() ) . '" class="sign-in-btn">' . esc_attr__( ' Sign Out', 'etrade' ) . '</a>';
        }

        $result .= '</div>';

        return apply_filters( 'etrade_tiny_account_ajax', $result );
    }
}

if ( !function_exists( 'etrade_topbar_account' ) ):
    function etrade_topbar_account() {
        echo etrade_tiny_account( true );
    }
endif;

/**
 * Topbar Account
 */
add_action( 'etrade_topbar_menu', 'etrade_topbar_account' );

if ( !function_exists( 'etrade_hes_shop_notification' ) ):
    function etrade_hes_shop_notification() {
        echo etrade_shop_notification( true );
    }
endif;

/**
 * notification
 */
add_action( 'do_etrade_hes_shop_notification', 'etrade_hes_shop_notification' );

function etrade_shop_notification() {
    // Create an array of arguments, which will tell WP_Query what information to load from the database
    $args = array(
        'post_type'   => 'notification',
        'post_status' => 'publish',
        'showposts'   => -1,

    );
    $result = '';
    $my_query = new WP_Query( $args );
    if ( $my_query->have_posts() ):

        $result .= '<div class="header-top-campaign">
	            <div class="container">
	                <div class="row justify-content-center">
	                    <div class="col-lg-5 col-md-10">
	                        <div class="header-campaign-activation axil-slick-arrow arrow-both-side header-campaign-arrow">';
        while ( $my_query->have_posts() ): $my_query->the_post();
            $url = get_the_permalink();
            $title = get_the_title();

                $result .= '<div class="slick-slide">
                            <div class="campaign-content">
                                <p>' . esc_attr( $title ) . ': <a href="' . esc_url( $url ) . '">' . esc_attr__( ' GET OFFER', 'etrade' ) . '</a></p>
                            </div>
                        </div>';

                    endwhile;
                    wp_reset_postdata();
                    $result .= '</div> 
	                   </div>
                    </div>
	            </div>
	        </div>';
        return apply_filters( 'etrade_shop_notification_ajax', $result );

    endif;
}

add_action( 'init', function () {
    remove_action( 'wp_footer', 'woocommerce_demo_store' );

} );

add_filter( 'elementor/frontend/print_google_fonts', '__return_false' );

add_action( 'elementor/frontend/after_register_styles', function () {
    foreach ( array( 'solid', 'regular', 'brands' ) as $style ) {
        wp_deregister_style( 'elementor-icons-fa-' . $style );
    }
}, 20 );

function axil_reset_pass_url() {
    $siteURL = get_option( 'siteurl' );
    return "{$siteURL}/my-account/lost-password/?action=lostpassword";
}
add_filter( 'lostpassword_url', 'axil_reset_pass_url', 11, 0 );

function axil_add_class_to_menu_item( $sorted_menu_objects, $args ) {
    $theme_location = 'primary'; // Name, ID, or Slug of the target menu location

    if ( isset( $_GET['sidebar'] ) ) {
        if ( $_GET['sidebar'] == 'right' ) {
            $class_to_add = 'is-no-active';
            $target_menu_title = 'Shop Left Sidebar';
        }
        if ( $_GET['sidebar'] == 'left' ) {
            $class_to_add = '';
            $target_menu_title = '';
        }
        if ( $_GET['sidebar'] == 'full' ) {
            $class_to_add = 'is-no-active';
            $target_menu_title = 'Shop Left Sidebar';
        }

        if ( $args->theme_location == $theme_location ) {
            foreach ( $sorted_menu_objects as $key => $menu_object ) {
                if ( $menu_object->title == $target_menu_title ) {
                    $menu_object->classes[] = $class_to_add;
                    break; // Optional.  Leave if you're only targeting one specific menu item
                }
            }
        }
    }

    return $sorted_menu_objects;
}
add_filter( 'wp_nav_menu_objects', 'axil_add_class_to_menu_item', 10, 2 );