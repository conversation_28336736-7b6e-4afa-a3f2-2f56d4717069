<?php
/**
 * Template part for displaying page banner style two
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options           = Helper::axil_get_options();
$banner_layout          = Helper::axil_banner_layout();
$banner_area            = $banner_layout['banner_area'];
$banner_img_id          = $banner_layout['banner_img_id'];
$banner_img             = $banner_layout['banner_img'];
$banner_title           = axil_get_acf_data("axil_custom_title");
$banner_sub_title       = axil_get_acf_data("axil_custom_sub_title");
$allowed_tags           = wp_kses_allowed_html( 'post' );
$page_breadcrumb        = Helper::axil_page_breadcrumb();
$page_breadcrumb_enable = $page_breadcrumb['breadcrumbs'];
$size                   = 'full'; 
$pimage                 = wp_get_attachment_image( $banner_img_id, $size );
?>
<?php if ("no" !== $banner_area && "0" !== $banner_area) {  ?>
 <div class="axil-breadcrumb-area">
     <div class="container">
         <div class="row align-items-center">
             <div class="col-lg-6 col-md-8">
                 <div class="inner"> 
                     <?php
                        if ("no" !== $page_breadcrumb_enable && "0" !== $page_breadcrumb_enable) {
                            axil_breadcrumbs();
                        }  ?> 
                        <?php
                        if($banner_title){ ?>
                            <h1 class="title"><?php echo wp_kses( $banner_title, $allowed_tags ); ?></h1>
                        <?php  } else { ?>
                            <h1 class="title"><?php wp_title(''); ?></h1>
                        <?php  }  ?>   
                        <?php
                        if($banner_sub_title){ ?> 
                            <p class="sub-title"><?php echo esc_html( $banner_sub_title ); ?></p>
                        <?php  } ?>   
                 </div>
             </div>
             <div class="col-lg-6 col-md-4">
                  <?php if( $pimage ){ ?>
                     <div class="inner">
                         <div class="bradcrumb-thumb">
                            <?php echo wp_kses( $pimage, $allowed_tags );?> 
                         </div>
                     </div> 
                <?php } ?>
             </div>
         </div>
     </div>
 </div>
<?php } ?>
