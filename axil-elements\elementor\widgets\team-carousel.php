<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Repeater;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Utils;
use Elementor\Group_Control_Typography;
use Elementor\Icons_Manager;

if (!defined('ABSPATH')) exit; // Exit if accessed directly
class axil_team_members extends Widget_Base
{

    public function get_name()
    {
        return 'axil-team-members';
    }
    public function get_title()
    {
        return esc_html__('Team Members', 'etrade-elements');
    }
    public function get_icon()
    {
        return 'eicon-banner';
    }
    public function get_categories()
    {
        return [ETRADE_ELEMENTS_THEME_PREFIX . '-widgets'];
    }
  

    protected function register_controls()
    {

        $this->start_controls_section(
            'title_section',
            [
                'label' => __('Section Title', 'etrade-elements'),
            ]
        );

        $this->add_control(
            'seation_title_on',
            [
                'label' => __('Section Title', 'etrade-elements'),
                'type' => Controls_Manager::SWITCHER,
                'label_on'    => __('On', 'etrade-elements'),
                'label_off'   => __('Off', 'etrade-elements'),
                'default'     => 'yes',
                'separator'     => 'after',

            ]
        );

        $this->add_responsive_control(
            'sec_title_tag',
            [
                'label' => __('Title HTML Tag', 'etrade-elements'),
                'type' => Controls_Manager::CHOOSE,
                'condition' => array('seation_title_on' => array('yes')),
                'options' => [
                    'h1'  => [
                        'title' => esc_html__('H1', 'etrade-elements'),
                        'icon' => 'eicon-editor-h1'
                    ],
                    'h2'  => [
                        'title' => esc_html__('H2', 'etrade-elements'),
                        'icon' => 'eicon-editor-h2'
                    ],
                    'h3'  => [
                        'title' => esc_html__('H3', 'etrade-elements'),
                        'icon' => 'eicon-editor-h3'
                    ],
                    'h4'  => [
                        'title' => esc_html__('H4', 'etrade-elements'),
                        'icon' => 'eicon-editor-h4'
                    ],
                    'h5'  => [
                        'title' => esc_html__('H5', 'etrade-elements'),
                        'icon' => 'eicon-editor-h5'
                    ],
                    'h6'  => [
                        'title' => esc_html__('H6', 'etrade-elements'),
                        'icon' => 'eicon-editor-h6'
                    ],
                    'div'  => [
                        'title' => esc_html__('div', 'etrade-elements'),
                        'icon' => 'eicon-font'
                    ]
                ],
                'default' => 'h2',
                'label_block' => true,

            ]
        );


        $this->add_control(
            'title_before',
            [
                'label' => __('Before Title', 'etrade-elements'),
                'type' => Controls_Manager::TEXTAREA,
                'placeholder' => __('Type your Description here.', 'etrade-elements'),
                'default' => 'Section sub title here',
                'condition' => array('seation_title_on' => array('yes')),
            ]
        );

        $this->add_control(
            'title',
            [
                'label' => __('Title', 'etrade-elements'),
                'type' => Controls_Manager::TEXTAREA,
                'placeholder' => __('Type your title here...', 'etrade-elements'),
                'default' => 'Section title here',
                'condition' => array('seation_title_on' => array('yes')),
            ]
        );


        $this->add_control(
            'beforetitlestyle',
            [
                'label'     => esc_html__('Before Color', 'etrade-elements'),
                'type'      => Controls_Manager::SELECT,
                'default'   => 'primary',
                'condition' => array('seation_title_on' => array('yes')),
                'options'   => [
                    'primary'       => esc_html__('Primary', 'etrade-elements'),
                    'secondary'     => esc_html__('Secondary', 'etrade-elements'),
                    'primary2'      => esc_html__('Primary 2', 'etrade-elements'),

                ],
            ]
        );

        $this->add_control(
            'icon',
            [
                'label' => __('Icons', 'etrade-elements'),
                'type' => Controls_Manager::ICONS,
                'condition' => array('seation_title_on' => array('yes')),
                'default' => [
                    'value' => 'fas fa-users',
                    'library' => 'solid',
                ],

            ]
        );
        $this->end_controls_section();

        $this->start_controls_section(
            'team_members',
            [
                'label' => esc_html__('Members', 'etrade-elements'),

            ]
        );
        $repeater = new Repeater();

        $repeater->add_control(
            'members_title',
            [
                'label' => esc_html__('Members\' Name', 'etrade-elements'),
                'type' => Controls_Manager::TEXT,
                'default' => esc_html__('Rosalina D. Willson', 'etrade-elements'),
                'placeholder' => esc_html__('Type Heading Text', 'etrade-elements'),
                'label_block' => true,
            ]
        );

        $repeater->add_control(
            'before_title',
            [
                'label' => esc_html__('Members\' Designation', 'etrade-elements'),
                'type' => Controls_Manager::TEXT,
                'default' => esc_html__('CEO', 'etrade-elements'),
                'placeholder' => esc_html__('Type Heading Text', 'etrade-elements'),
                'label_block' => true,
            ]
        );

        $repeater->add_control(
            'members_thumbnail',
            [
                'label' => esc_html__('Members\' Image', 'etrade-elements'),
                'type' => Controls_Manager::MEDIA,
                'default' => [
                    'url' => Utils::get_placeholder_image_src(),
                ],

            ]
        );

        $this->add_control(
            'members_list',
            [
                'label' => esc_html__('Members List', 'etrade-elements'),
                'type' => Controls_Manager::REPEATER,
                'fields' => $repeater->get_controls(),
                'default' => [
                    [
                        'members_title' => esc_html__('Team Member Title', 'etrade-elements'),
                        'before_title' => esc_html__('CEO', 'etrade-elements'),

                    ],
                    [
                        'members_title' => esc_html__('Team Member Title', 'etrade-elements'),
                        'before_title' => esc_html__('CEO', 'etrade-elements'),

                    ],
                    [
                        'members_title' => esc_html__('Team Member Title', 'etrade-elements'),
                        'before_title' => esc_html__('CEO', 'etrade-elements'),

                    ],
                    [
                        'members_title' => esc_html__('Team Member Title', 'etrade-elements'),
                        'before_title' => esc_html__('CEO', 'etrade-elements'),

                    ]
                ],
                'title_field' => '{{{ members_title }}}',
            ]
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'section_title_style_section',
            [
                'label' => __('Section Title', 'etrade-elements'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => __('Color', 'etrade-elements'),
                'type' => Controls_Manager::COLOR,
                'default' => '',
                'selectors' => array(
                    '{{WRAPPER}} .sec-title' => 'color: {{VALUE}}',
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_font_size',
                'label' => __('Typography', 'etrade-elements'),
                'selector' => '{{WRAPPER}} .sec-title',
            ]
        );

        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __('Margin', 'etrade-elements'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .sec-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ],
            ]
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'before_title_style_section',
            [
                'label' => __('Before Title', 'etrade-elements'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'before_title_color',
            [
                'label' => __('Color', 'etrade-elements'),
                'type' => Controls_Manager::COLOR,
                'default' => '',
                'selectors' => array(
                    '{{WRAPPER}} .title-highlighter' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .title-highlighter i' => 'background-color: {{VALUE}}',
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'before_title_font_size',
                'label' => __('Typography', 'etrade-elements'),
                'selector' => '{{WRAPPER}} .title-highlighter',
            ]
        );

        $this->add_responsive_control(
            'before_title_margin',
            [
                'label' => __('Margin', 'etrade-elements'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .title-highlighter' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ],
            ]
        );

        $this->end_controls_section();


        $this->start_controls_section(
            'members_name_style_section',
            [
                'label' => __('Members\' Name', 'etrade-elements'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'members_name_color',
            [
                'label' => __('Color', 'etrade-elements'),
                'type' => Controls_Manager::COLOR,
                'default' => '',

                'selectors' => array(
                    '{{WRAPPER}} .team-content .title' => 'color: {{VALUE}}',
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'members_name_font_size',
                'label' => __('Typography', 'etrade-elements'),

                'selector' => '{{WRAPPER}} .team-content .title',
            ]
        );

        $this->add_responsive_control(
            'members_name_margin',
            [
                'label' => __('Margin', 'etrade-elements'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],

                'selectors' => [
                    '{{WRAPPER}} .team-content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ],
            ]
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'members_designation_style_section',
            [
                'label' => __('Members\' Designation', 'etrade-elements'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'members_designation_color',
            [
                'label' => __('Color', 'etrade-elements'),
                'type' => Controls_Manager::COLOR,
                'default' => '',

                'selectors' => array(
                    '{{WRAPPER}} .team-content .subtitle' => 'color: {{VALUE}}',
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'members_designation_font_size',
                'label' => __('Typography', 'etrade-elements'),

                'selector' => '{{WRAPPER}} .team-content .subtitle',
            ]
        );

        $this->add_responsive_control(
            'members_designation_margin',
            [
                'label' => __('Margin', 'etrade-elements'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],

                'selectors' => [
                    '{{WRAPPER}} .team-content .subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ],
            ]
        );

        $this->end_controls_section();
    }

   private function slick_load_scripts(){
        wp_enqueue_style(  'slick' );
        wp_enqueue_style(  'slick-theme' );
        wp_enqueue_script( 'slick' );
    }
    protected function render()
    {
        $this->slick_load_scripts();
        $settings = $this->get_settings();
    ?>
        <div class="axil-team-area bg-wild-sand">
            <div class="team-left-fullwidth">
                <div class="container ml--xxl-0">
                    <?php if ($settings['seation_title_on']) { ?>
                        <div class="section-title-wrapper">
                            <?php if ($settings['sub_title_before']) { ?>
                                <span class="title-highlighter highlighter-<?php echo esc_attr($settings['beforetitlestyle']); ?> sub-title">
                                    <?php Icons_Manager::render_icon($settings['icon']); ?><?php echo wp_kses_post($settings['sub_title_before']); ?>
                                </span>
                            <?php  } ?>
                            <?php if ($settings['title']) { ?>
                                <<?php echo esc_html($settings['sec_title_tag']); ?> class="title sec-title">
                                    <?php echo wp_kses_post($settings['title']); ?></<?php echo esc_html($settings['sec_title_tag']); ?>>
                            <?php  } ?>
                        </div>
                    <?php } ?>
                    <div class="team-slide-activation slick-layout-wrapper--20 axil-slick-arrow  arrow-top-slide">
                        <?php foreach ($settings['members_list'] as $member) { ?>
                            <div class="slick-single-layout slick-slide">
                                <div class="axil-team-member">
                                    <div class="thumbnail"> <?php echo Group_Control_Image_Size::get_attachment_image_html($member, 'full', 'members_thumbnail'); ?></div>
                                    <div class="team-content">
                                        <?php if ($member['before_title']) { ?>
                                            <span class="subtitle"><?php echo esc_attr($member['before_title']); ?></span>
                                        <?php  } ?>
                                        <h5 class="title"><?php echo esc_attr($member['members_title']); ?></h5>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    <?php

    }
}
