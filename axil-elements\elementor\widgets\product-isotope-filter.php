<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Typography;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Axil_Isotope_Filter extends Widget_Base {

 public function get_name() {
        return 'wooc-isotope-filter';
    }    
    public function get_title() {
        return __( 'Product Isotope Filter', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-image-box';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
	private function wooc_cat_dropdown_1() {
		$terms = get_terms( array( 'taxonomy' => 'product_cat' ) );
		$category_dropdown = array( '0' => __( 'All Categories', 'etrade-elements' ) );

		foreach ( $terms as $category ) {
			$category_dropdown[$category->term_id] = $category->name . ' (' . $category->count .')';
		}

		return $category_dropdown;
	}
 
	 
    protected function register_controls() {        
        $args = array(
			'post_type'           => 'product',
			'posts_per_page'      => -1,
			'post_status'         => 'publish',
			'suppress_filters'    => false,
			'ignore_sticky_posts' => true,
		);
		$products = get_posts( $args );
		$products_dropdown = array();
		foreach ( $products as $product ) {
			$products_dropdown[$product->ID] = $product->post_title;
		}
        

       $this->start_controls_section(
        'sec_title_layout',
	        [
	            'label' => __( 'Section Title', 'etrade-elements' ),
	        ]
   		 );


		$this->add_control(
		    'section_title_display',
		    [
				'type' => Controls_Manager::SWITCHER,
				'label'       => esc_html__( 'Section Title Display', 'etrade-elements' ),
				'label_on'    => esc_html__( 'On', 'etrade-elements' ),
				'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
				'default'     => 'yes',
	        
		    ] 
		);   

		$this->add_control(
		    'sub_title',
		    [
		    	'type'    => Controls_Manager::TEXT,
				'label'       => esc_html__( 'Section Title before', 'etrade-elements' ),
				'default'     => 'Lorem Ipsum',
				'condition'   => array( 'section_title_display' => 'yes' ),			
		    ]
		);
		
		$this->add_control(
		    'beforetitlestyle',
		    [
		        'label' => esc_html__( 'Before Color', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT,
		        'default' => 'primary',
		        'condition'   => array( 'section_title_display' => 'yes' ),
		        'options' => [
		            'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
		            'secondary'   => esc_html__( 'Secondary', 'etrade-elements' ),                                          
		            'primary2'   => esc_html__( 'Primary 2', 'etrade-elements' ),                                          
		                                                      
		        ],
		    ] 
		);   

		$this->add_control(
		    'title',
		    [
		    	'type'    => Controls_Manager::TEXT,
				'label'       => esc_html__( 'Section Title', 'etrade-elements' ),
				'default'     => 'Lorem Ipsum',
				'condition'   => array( 'section_title_display' => 'yes' ),
                'label_block' => true, 		
		    ]
		);

		$this->add_control(
		    'section_filter_display',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Filter Display', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',
		        'separator'   => 'before',
		        
		    ] 
		);   
		
		$this->add_control(
		    'filter_all_display',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Filter "All" Tab Display', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',
				'condition'   => array( 'section_filter_display' => 'yes' ),	
		       
		        
		    ] 
		);   

		$this->add_control(
		    'all_tab_text',
		    [
		    	'type'    => Controls_Manager::TEXT,
				'label'       => esc_html__( 'All Tab Text', 'etrade-elements' ),
				'default'     => 'All',
				'condition'   => array( 'filter_all_display' => 'yes', 'section_filter_display' => 'yes' ),			
		    ]
		); 


 		$this->add_control(
		        'icon',
		        [
		            'label' => esc_html__( 'Icons', 'etrade-elements' ),
		            'type' => Controls_Manager::ICONS,
		            'condition'   => array( 'section_title_display' => 'yes' ),
		            'default' => [
		                'value' => 'far fa-shopping-basket',
		                'library' => 'solid',
		            ],
		                  
		        ]
		    );


 		$this->end_controls_section(); 



        $this->start_controls_section(
            'sec_general_layout',
            [
                'label' => __( 'Filter Query', 'etrade-elements' ),
            ]
        );
       

		$this->add_control(
		    'style',
		    [
		        'label' => __( 'Product Style', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT,
		        'default' => '1',
		        'options' => [
		            '1' => __( 'Style 1', 'etrade-elements' ),
					'2' => __( 'Style 2', 'etrade-elements' ),
					'3' => __( 'Style 3', 'etrade-elements' ),
					'4' => __( 'Style 4', 'etrade-elements' ),
                    '5' => __( 'Style 5', 'etrade-elements' ),
                    '6' => __( 'Style 6', 'etrade-elements' ),
									                  
		        ],
		    ] 
		);             
		$this->add_control(
		    'navtype',
		    [
		        'label' => __( 'Navigation Type', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT,
		        'default' => 'items',
		        'options' => [
		           'items' => __( 'Selected Items', 'etrade-elements' ),
					'cats'  => __( 'Selected Categories', 'etrade-elements' ),
					                  
		        ],
		    ] 
		);             
		$this->add_control(
		    'navitems',
		    [
		        'label' => __( 'Items to Show', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT2,
		        'default' => 'items',
		        'multiple'  => true,
		        'default'   => array( 'featured', 'new', 'popular'),
				'condition' => array( 'navtype' => 'items' ),
		        'options' => [
		          'featured' => __( 'Featured', 'etrade-elements' ),
					'new'      => __( 'New', 'etrade-elements' ),
					'popular'  => __( 'Popular', 'etrade-elements' ),
					'rating'   => __( 'Best Rated', 'etrade-elements' ),
					'sale'     => __( 'Sale', 'etrade-elements' ),
		        ],
		    ] 
		);            

		$this->add_control(
		    'navcats',
		    [
		        'label' => __( 'Categories to Show', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT2,
		        'default' => 'items',
		        'multiple'  => true,
		        'description' => __( 'If empty then all categories will be displayed', 'etrade-elements' ),
		        'options'     => $this->wooc_cat_dropdown_1(),
				'condition'   => array( 'navtype' => 'cats' ),
		        
		    ] 
		);             	
		$this->add_control(
		    'cat',
		    [
		        'label' => __( 'Category', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT2,
		        'multiple'  => true,
		      	'options'     => $this->wooc_cat_dropdown_1(),
				'default' => 'items',
				'condition'   => array( 'navtype' => 'items' ),
		        
		    ] 
		);             

 		$this->add_control(
        'number',
            [
                'label'   => __( 'Number of Products', 'etrade-elements' ),
                'type'    => Controls_Manager::NUMBER,
                'default'     => 4,
                'description' => __( 'Write -1 to show all', 'etrade-elements' ),                 
            ]

        );
   
		$this->add_control(
		    'out_stock_hide',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Hide Out-of-stock Products', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		); 	
 
        $this->add_control(
            'wishlist',
            [
                
                'type' => Controls_Manager::SWITCHER,
                'label'       => __( 'Wishlist Display', 'etrade-elements' ),
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
                
            ] 
        );      

         $this->add_control(
            'quickview',
            [
                
                'type' => Controls_Manager::SWITCHER,
                'label'       => __( 'Quick View Display', 'etrade-elements' ),
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
                
            ] 
        );      
         $this->add_control(
		    'rating_display',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Rating Display', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',
		       
		        
		    ] 
		); 	

		
		$this->add_control(
		    'display_attributes',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Attributes Display', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',
		         
		        
		    ] 
		); 	 

		$this->add_control(
		    'display_title_badge_check',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Title Badge Check', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',
		         
		        
		    ] 
		); 	  		
		
 
	  $this->end_controls_section();   

      $this->start_controls_section(
            'etrade_responsive',
                [
                'label' => __( 'Responsive Columns', 'etrade-elements' ),
                ]
            );

            $this->add_control(
                'col_xl',
                [
                    'label' => __( 'Desktops: > 1199px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '5',
                ] 
            );
            $this->add_control(
            'col_lg',
                [
                    'label' => __( 'Desktops: > 991px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '4',
                ] 
            );
            $this->add_control(
            'col_md',
                [
                    'label' => __( 'Tablets: > 767px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                             '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '3',
                ] 
            );

            $this->add_control(
            'col_sm',
                [
                    'label' => __( 'Phones: >575px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                             '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '2',
                ] 
            );         
            $this->add_control(
            'col_mobile',
                [
                    'label' => __( 'Small Phones: <576px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                             '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '2',
                ] 
            );
       $this->end_controls_section();

       
        $this->start_controls_section(
            'title_style_section',
            [
                'label' => __( 'Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );

         $this->add_control(
            'title_style_on',
            [
                'label' => __( 'Customize', 'etrade-elements' ),
                'type' => Controls_Manager::SWITCHER,
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => 'no',
               
            ]
        );   

 
          $this->add_control(
            'title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                'condition' => array( 'title_style_on' => array( 'yes' ) ),
                'selectors' => array(
                    '{{WRAPPER}} .woocue-sec-title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                 'condition' => array( 'title_style_on' => array( 'yes' ) ),
                'selector' => '{{WRAPPER}} .woocue-sec-title',
            ]
        );
       
        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 'condition' => array( 'title_style_on' => array( 'yes' ) ),
                'selectors' => [
                    '{{WRAPPER}} .woocue-sec-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
        
    $this->end_controls_section();

  $this->start_controls_section(
            'sub_title_style_section',
            [
                'label' => __( 'Sub Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );

         $this->add_control(
            'sub_title_style_on',
            [
                'label' => __( 'Customize', 'etrade-elements' ),
                'type' => Controls_Manager::SWITCHER,
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => 'no',
               
            ]
        );   

 
          $this->add_control(
            'sub_title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                'condition' => array( 'sub_title_style_on' => array( 'yes' ) ),
                'selectors' => array(
                    '{{WRAPPER}} .woocue-sub-title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'sub_title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                 'condition' => array( 'sub_title_style_on' => array( 'yes' ) ),
                'selector' => '{{WRAPPER}} .woocue-sub-title',
            ]
        );
       
        $this->add_responsive_control(
            'sub_title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 'condition' => array( 'sub_title_style_on' => array( 'yes' ) ),
                'selectors' => [
                    '{{WRAPPER}} .woocue-sub-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
        
    $this->end_controls_section();


        $this->start_controls_section(
            'sec_typography_type',
            [
                'label' => __( 'Product Typography', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,   
            ]
        );


		$this->add_group_control(
		Group_Control_Typography::get_type(),
			[
			    'name' => 'title_typo',
			    'label' => __( 'Typography', 'etrade-elements' ),  
			     'devices' => [ 'desktop', 'tablet', 'mobile' ],	
			   'selector' => '{{WRAPPER}} .woocue-title',
			]
		);

		$this->add_responsive_control(
			'title_typo_margin',
				[
				    'label' => __( 'Margin', 'etrade-elements' ),
				    'type' => Controls_Manager::DIMENSIONS,
				    'size_units' => [ 'px', '%', 'em' ],
				    'devices' => [ 'desktop', 'tablet', 'mobile' ],					    
				    'selectors' => [
				        '{{WRAPPER}}  .wooc-product-wrp .woocue-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				        
				    ],
				]
			);

       $this->end_controls_section();   

    }
    
	private function wooc_load_scripts(){ 
		wp_enqueue_script( 'isotope' );
		wp_enqueue_script( 'images-loaded' );
	}

	private function wooc_isotope_item_navigation( $settings ) {
		$navs = array(
			'featured' => __( 'Featured', 'etrade-elements' ),
			'new'      => __( 'New', 'etrade-elements' ),
			'popular'  => __( 'Popular', 'etrade-elements' ),
			'sale'     => __( 'Sale', 'etrade-elements' ),
			'rating'   => __( 'Best Rated', 'etrade-elements' ),
		);

		$navs = apply_filters( 'etrade_isotope_item_navigations', $navs );

		foreach ( $navs as $key => $value ) {
			if ( !in_array( $key , $settings['navitems'] ) ) {
				unset($navs[$key]);
			}
		}

		return $navs;
	}

	private function wooc_isotope_item_query( $settings ) {

		$result = array();

		// Post type
		$args = array(
			'post_type'           => 'product',
			'ignore_sticky_posts' => true,
			'post_status'         => 'publish',
			'suppress_filters'    => false,
			'posts_per_page'      => $settings['number'],
		);

		// Category
		if ( !empty( $settings['cat'] ) ) {
			$args['tax_query'][] = array(
				'taxonomy' => 'product_cat',
				'field'    => 'term_id',
				'terms'    => $settings['cat'],
			);
		}

		// Out-of-stock hide
		if ( $settings['out_stock_hide'] ) {
			$args['tax_query'][] = array(
				'taxonomy' => 'product_visibility',
				'field'    => 'slug',
				'terms'    => 'outofstock',
				'operator' => 'NOT IN',
			);
		}

		$args2 = array();
		
		if ( in_array( 'new' , $settings['navitems'] ) ) {
			$result['new'] = new \WP_Query( $args );
		}

		if ( in_array( 'featured' , $settings['navitems'] ) ) {
			$args2['tax_query'][] = array(
				'taxonomy' => 'product_visibility',
				'field'    => 'slug',
				'terms'    => 'featured',
			);
			$result['featured'] = new \WP_Query( $args + $args2 );
			$args2 = array();
		}

		if ( in_array( 'popular' , $settings['navitems'] ) ) {
			$args2['meta_key'] = 'total_sales';
			$args2['orderby']  = 'meta_value_num';
			$args2['order']    = 'DSC';
			$result['popular'] = new \WP_Query( $args + $args2 );
			$args2 = array();
		}

		if ( in_array( 'rating' , $settings['navitems'] ) ) {
			$args2['meta_key'] = '_wc_average_rating';
			$args2['orderby']  = 'meta_value_num';
			$args2['order']    = 'DSC';
			$result['rating']  = new \WP_Query( $args + $args2 );
			$args2 = array();
		}

		if ( in_array( 'sale' , $settings['navitems'] ) ) {
			$args2['meta_query'][] = array(
				'key'     => '_sale_price',
				'compare' => '!=',
				'value'   => ''
			);
			$result['sale'] = new \WP_Query( $args + $args2 );
			$args2 = array();
		}

		return $result;
	}

	private function wooc_isotope_cats_navigation( $settings ) {
		$category_dropdown = array();
		if ( $settings['navcats'] ) {
			$terms = get_terms( array( 'taxonomy' => 'product_cat', 'include' => $settings['navcats'], 'orderby'  => 'include' ) );
		}
		else {
			$terms = get_terms( array( 'taxonomy' => 'product_cat', 'parent' => 0 ) );
		}

		foreach ( $terms as $term ) {
			$category_dropdown[$term->slug] = $term->name;
		}

		return $category_dropdown;	
	}
    private function wooc_isotope_cats_item_query( $settings ) {
		// Post type
		$args = array(
			'post_type'           => 'product',
			'post_status'         => 'publish',
			'ignore_sticky_posts' => true,
			'posts_per_page'      => $settings['number'],
		);

		// Out-of-stock hide
		if ( $settings['out_stock_hide'] ) {
			$args['tax_query'][] = array(
				'taxonomy' => 'product_visibility',
				'field'    => 'slug',
				'terms'    => 'outofstock',
				'operator' => 'NOT IN',
			);
		}

		if ( $settings['navcats'] ) {
			$args['tax_query'][] = array(
				'taxonomy' => 'product_cat',
				'field'    => 'term_id',
				'terms'    => $settings['navcats'],
			);	
		}

		return new \WP_Query( $args );
	}
	protected function render() {
		$settings = $this->get_settings();		
		$this->wooc_load_scripts();
		if ( $settings['navtype'] == 'cats' ) {
			$settings['navs']  = $this->wooc_isotope_cats_navigation( $settings );
			$settings['query'] = $this->wooc_isotope_cats_item_query( $settings );
			$template = 'product-isotope-filter-2';
		
		}else {
			$settings['navs']    = $this->wooc_isotope_item_navigation( $settings );
			$settings['queries'] = $this->wooc_isotope_item_query( $settings );
			$template = 'product-isotope-filter-1';
		}
	
		return wooc_Elements_Helper::wooc_element_template( $template, $settings );
	}
}
