<?php
/**
 * Single Product Image
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/product-image.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.0.0
 */

defined( 'ABSPATH' ) || exit;

// Note: `wc_get_gallery_image_html` was added in WC 3.3.2 and did not exist prior. This check protects against theme overrides being used on older versions of WC.
if ( !function_exists( 'wc_get_gallery_image_html' ) ) {
    return;
}

global $product;

$post_thumbnail_id = $product->get_image_id();
$image = wp_get_attachment_image( $post_thumbnail_id, 'shop_single', true, array( "class" => "attachment-shop_single size-shop_single wp-post-image" ) );

$wrapper_classes = apply_filters( 'woocommerce_single_product_image_gallery_classes', array(
    'row ',
    ' row--10',
    'axil--' . ( has_post_thumbnail() ? 'with-images' : 'without-images' ),

    'images',

) );

$axil_options = Helper::axil_get_options();

$layout = Helper::axil_product_layout_style();

$attachment_ids = $product->get_gallery_image_ids();
if ( $attachment_ids && has_post_thumbnail() ) {
    $has_post_thumbnail = "wooc-has-post-thumbnail";
} else {
    $has_post_thumbnail = "wooc-none-post-thumbnail";
}
$html = "";
$popup_zoom = "";
 if ( $axil_options['wooc_popup_zoom'] ) {
    $popup_zoom = '<div class="product-quick-view position-view">
        <a href="#" class="popup-zoom" tabindex="0">
            <i class="far fa-search-plus"></i>
        </a>
    </div> ';
}
?>

<div class="<?php echo esc_attr( implode( ' ', array_map( 'sanitize_html_class', $wrapper_classes ) ) ); ?>">
	<?php
if ( $layout == '3' ) {
    if ( has_post_thumbnail() ) {
        $attachment_ids = $product->get_gallery_image_ids();
        $lightbox_src = wc_get_product_attachment_props( $post_thumbnail_id );

        echo '<div class="col-lg-6 mb--20 woocommerce-product-gallery__image single-product-main-image zoom-gallery zoom-gallery-cursor"><div class="single-product-thumbnail axil-product thumbnail-grid">';
         do_action( 'axil_woocommerce_after_product_thumbnails' ); 

		 echo '<div class="thumbnail"><a class="popup-zoom-single"  title="' . $lightbox_src['title'] . '"  href="' . $lightbox_src['url'] . '" >' . $image . '</a></div></div></div>';

        if ( $attachment_ids ) {
            foreach ( $attachment_ids as $attachment_id ) {
                $thumbnail_image = wp_get_attachment_image( $attachment_id, 'shop_single' );
                $lightbox_src = wc_get_product_attachment_props( $attachment_id );

                echo '<div class="col-lg-6 mb--20 zoom-gallery zoom-gallery-cursor"><div class="single-product-thumbnail axil-product thumbnail-grid"><div class="thumbnail"><a class="popup-zoom-single"  title="' . $lightbox_src['title'] . '" href="' . $lightbox_src['url'] . '" >' . $thumbnail_image . '</a></div></div></div>';

            }
        }

    } else {
        $html .= '<div class="col-lg-6 mb--20 woocommerce-product-gallery__image--placeholder"><div class="single-product-thumbnail axil-product thumbnail-grid"><div class="thumbnail">';
        $html .= sprintf( '<img src="%s" alt="%s" class="wp-post-image" />', esc_url( wc_placeholder_img_src() ), esc_html__( 'Awaiting product image', 'etrade' ) );
        $html .= '</div></div></div>';
    }

    echo apply_filters( 'woocommerce_single_product_image_thumbnail_html', $html, $post_thumbnail_id );

} elseif ( $layout == '4' ) {

    if ( has_post_thumbnail() ) {
        echo '<div class="col-lg-12 axil-product-lable single-product-thumbnail-wrap zoom-gallery">';
        echo '<div class="label-block">';
        do_action( 'wooc_sale_flash' );
           do_action( 'axil_woocommerce_after_product_thumbnails' ); 
        echo '</div>';
        echo $popup_zoom;

        echo '<div class="product-large-thumbnail-2 single-product-thumbnail axil-product slick-layout-wrapper--15 axil-slick-arrow arrow-both-side-3">';
        $attachment_ids = $product->get_gallery_image_ids();

        $lightbox_src = wc_get_product_attachment_props( $post_thumbnail_id );
        echo '<div class="woocommerce-product-gallery__image single-product-main-image">' . $image . '</div>';

        if ( $attachment_ids ) {
            foreach ( $attachment_ids as $attachment_id ) {
                $thumbnail_image = wp_get_attachment_image( $attachment_id, 'shop_single' );
                $lightbox_src = wc_get_product_attachment_props( $attachment_id );

                echo '<div class="thumbnail">' . $thumbnail_image . '</div>';

            }
        }
        echo "</div></div>";

    } else {
        $html .= '<div class="woocommerce-product-gallery__image--placeholder">';
        $html .= sprintf( '<img src="%s" alt="%s" class="wp-post-image" />', esc_url( wc_placeholder_img_src() ), esc_html__( 'Awaiting product image', 'etrade' ) );
        $html .= '</div>';
    }

    do_action( 'woocommerce_product_thumbnails' );

} elseif ( $layout == '6' ) {

    if ( has_post_thumbnail() ) {
        echo '<div class="col-lg-12 axil-product-lable single-product-thumbnail-wrap zoom-gallery">';
        echo '<div class="label-block">';
        do_action( 'wooc_sale_flash' );
        echo '</div>';
           do_action( 'axil_woocommerce_after_product_thumbnails' ); 
       echo $popup_zoom;
        echo '<div class="product-large-thumbnail-2 single-product-thumbnail axil-product slick-layout-wrapper--15 axil-slick-arrow arrow-both-side-3">';
        $attachment_ids = $product->get_gallery_image_ids();

        $lightbox_src = wc_get_product_attachment_props( $post_thumbnail_id );

        echo '<div class="woocommerce-product-gallery__image single-product-main-image"><a class="popup-zoom"  title="' . $lightbox_src['title'] . '"  href="' . $lightbox_src['url'] . '" >' . $image . '</a></div> ';

        if ( $attachment_ids ) {
            foreach ( $attachment_ids as $attachment_id ) {
                $thumbnail_image = wp_get_attachment_image( $attachment_id, 'shop_single' );
                $lightbox_src = wc_get_product_attachment_props( $attachment_id );

                echo '<a class="popup-zoom"  title="' . $lightbox_src['title'] . '" href="' . $lightbox_src['url'] . '" >' . $thumbnail_image . '</a>';

            }
        }
        echo "</div></div>";

    } else {
        $html .= '<div class="woocommerce-product-gallery__image--placeholder">';
        $html .= sprintf( '<img src="%s" alt="%s" class="wp-post-image" />', esc_url( wc_placeholder_img_src() ), esc_html__( 'Awaiting product image', 'etrade' ) );
        $html .= '</div>';
    }

    do_action( 'woocommerce_product_thumbnails' );

} elseif ( $layout == '5' || $layout == '7' ) {

    if ( has_post_thumbnail() ) {
        echo '<div class="col-lg-12 axil-product-lable single-product-thumbnail-wrap zoom-gallery">';
        do_action( 'axil_woocommerce_after_product_thumbnails' ); 
       echo $popup_zoom;

        echo '<div class="product-large-thumbnail-2 single-product-thumbnail axil-product slick-layout-wrapper--15 axil-slick-arrow arrow-both-side-3 thumbnail-badge">';
        $attachment_ids = $product->get_gallery_image_ids();
        $lightbox_src = wc_get_product_attachment_props( $post_thumbnail_id );

        echo '<div class="woocommerce-product-gallery__image single-product-main-image">' . $image . '</div> ';

        if ( $attachment_ids ) {
            foreach ( $attachment_ids as $attachment_id ) {
                $thumbnail_image = wp_get_attachment_image( $attachment_id, 'shop_single' );
                $lightbox_src = wc_get_product_attachment_props( $attachment_id );
                echo '<a class="popup-zoom"  title="' . $lightbox_src['title'] . '" href="' . $lightbox_src['url'] . '" >' . $thumbnail_image . '</a>';

            }
        }
        echo "</div></div>";

    } else {
        $html .= '<div class="woocommerce-product-gallery__image--placeholder">';
        $html .= sprintf( '<img src="%s" alt="%s" class="wp-post-image" />', esc_url( wc_placeholder_img_src() ), esc_html__( 'Awaiting product image', 'etrade' ) );
        $html .= '</div>';
    }
    echo apply_filters( 'woocommerce_single_product_image_thumbnail_html', $html, $post_thumbnail_id );
    do_action( 'woocommerce_product_thumbnails' );

    } elseif (  $layout == '2' || $layout == '1' ) {


    if ( has_post_thumbnail() ) {
        echo '<div class="col-lg-10 order-lg-2 axil-product-lable"><div class="single-product-thumbnail-wrap zoom-gallery">';
        echo '<div class="label-block">';

        do_action( 'wooc_sale_flash' );
        do_action( 'axil_woocommerce_after_product_thumbnails' ); 
        echo '</div>'; 
        
        echo $popup_zoom; 
        echo '<div class="single-product-thumbnail product-large-thumbnail axil-product thumbnail-badge">';
        $attachment_ids = $product->get_gallery_image_ids();
        $lightbox_src = wc_get_product_attachment_props( $post_thumbnail_id );

        echo '<div class="woocommerce-product-gallery__image single-product-main-image thumbnail">' . $image . '</div> ';
        if ( $attachment_ids ) {
            foreach ( $attachment_ids as $attachment_id ) {
                $thumbnail_image = wp_get_attachment_image( $attachment_id, 'shop_single' );
                $lightbox_src = wc_get_product_attachment_props( $attachment_id );
                echo '<div class="thumbnail">' . $thumbnail_image . '</div>';
            }
        }
        echo "</div></div></div>";
    } else {
        $html .= '<div class="woocommerce-product-gallery__image--placeholder thumbnail">';
        $html .= sprintf( '<img src="%s" alt="%s" class="wp-post-image" />', esc_url( wc_placeholder_img_src() ), esc_html__( 'Awaiting product image', 'etrade' ) );
        $html .= '</div>';
    }
    echo apply_filters( 'woocommerce_single_product_image_thumbnail_html', $html, $post_thumbnail_id );
    do_action( 'woocommerce_product_thumbnails' );

    } else {
 
$columns           = apply_filters( 'woocommerce_product_thumbnails_columns', 4 );
  do_action( 'axil_woocommerce_after_product_thumbnails' );
$post_thumbnail_id = $product->get_image_id();
$wrapper_classes   = apply_filters(
    'woocommerce_single_product_image_gallery_classes',
    array(
        'woocommerce-product-gallery',
        'woocommerce-product-gallery--' . ( $post_thumbnail_id ? 'with-images' : 'without-images' ),
        'woocommerce-product-gallery--columns-' . absint( $columns ),
        'images',
    )
);
?>
<div class="<?php echo esc_attr( implode( ' ', array_map( 'sanitize_html_class', $wrapper_classes ) ) ); ?>" data-columns="<?php echo esc_attr( $columns ); ?>" style="opacity: 0; transition: opacity .25s ease-in-out;">

    <figure class="woocommerce-product-gallery__wrapper">

        <?php
        if ( $post_thumbnail_id ) {

            $html = wc_get_gallery_image_html( $post_thumbnail_id, true );
        } else {
            $html  = '<div class="woocommerce-product-gallery__image--placeholder">';
            $html .= sprintf( '<img src="%s" alt="%s" class="wp-post-image" />', esc_url( wc_placeholder_img_src( 'woocommerce_single' ) ), esc_html__( 'Awaiting product image', 'woocommerce' ) );
            $html .= '</div>';
        }

        echo apply_filters( 'woocommerce_single_product_image_thumbnail_html', $html, $post_thumbnail_id ); // phpcs:disable WordPress.XSS.EscapeOutput.OutputNotEscaped

        do_action( 'woocommerce_product_thumbnails' );
        ?>
    </figure>
</div>

<?php }
?>
</div>