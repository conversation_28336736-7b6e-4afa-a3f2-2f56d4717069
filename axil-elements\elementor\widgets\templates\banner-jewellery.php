<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;
$allowed_tags = wp_kses_allowed_html( 'post' ); 
   
 if ('1' == $settings['axil_link_type']) {
        if ( !empty( $settings['url']['url'] ) ) {
            $attr  = 'href="' . $settings['url']['url'] . '"';
            $attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
            $attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
             
        }
        if ( !empty( $settings['btntext'] ) ) {
            $btn = '<a class="axil-btn btn-bg-white" ' . $attr . '><i class="fal fa-shopping-cart"></i>' . $settings['btntext'] . '</a>';
        }
        }else {
        $attr  = 'href="' . get_permalink($settings['axil_page_link']) . '"';
        $attr .= ' target="_self"';
        $attr .= ' rel="nofollow"';                        
        $btn = '<a class="axil-btn btn-bg-white" ' . $attr . '><i class="fal fa-shopping-cart"></i>' . $settings['btntext'] . '</a>';
    } 

?>   
<div class="axil-main-slider-area main-slider-style-7">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-sm-8">
                <div class="main-slider-content"> 
                    <?php  if($settings['sub_title']){ ?>
                    <span class="subtitle title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?>">
                        <?php Icons_Manager::render_icon( $settings['icon'] ); ?>
                        <?php echo esc_html( $settings['sub_title'] );?> 
                    </span>
                    <?php } ?>  
                    <?php if ( $settings['banner_title'] ) { ?>   
                        <h1 class="title"><?php echo wp_kses( $settings['banner_title'], $allowed_tags );?></h1>
                    <?php } ?>  
                     <?php if ( $settings['banner_description'] ) { ?>   
                        <p class="banner-description"> <?php echo wp_kses( $settings['banner_description'], $allowed_tags );?> </p>
                    <?php } ?>  
                    <?php if ( $settings['btntext'] ) { ?>   
                        <div class="shop-btn">
                            <?php echo wp_kses( $btn, $allowed_tags );?> 
                        </div>
                    <?php } ?>  
                </div>
            </div>
        </div>
    </div>
</div>  