<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;


$query 			= $settings['query']; 
$botton_border 	= $settings['botton_border'] ? 'on' : 'off';
$section_bottom_gap 	= $settings['islink'] ? '80' : '20';
$col_class  = "col-xl-{$settings['col_xl']} col-lg-{$settings['col_lg']} col-md-{$settings['col_md']} col-sm-{$settings['col_sm']} col-{$settings['col_mobile']}";

if ( !empty( $settings['cat'] ) ) {
	$shop_permalink = get_category_link( $settings['cat'] );
}
else {
	$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
}

$block_data = array(
	'layout'         				=> $settings['style'],	
	'rating_display' 				=> $settings['rating_display'] ? true : false,		
	'product_display_hover'         => $settings['product_display_hover'],
	'display_title_badge_check'     => $settings['display_title_badge_check'],
	'sale_price_only'        		=> $settings['sale_price_only'] ? true : false,
	'display_attributes'        	=> $settings['display_attributes'] ? true : false,
);
$btn = $attr = "";

 if ('2' == $settings['axil_link_type']) {
	
		$attr  = 'href="' . get_permalink($settings['axil_page_link']) . '"';
		$attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
		$attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
	
		$btn = '<a class="axil-btn btn-bg-lighter btn-load-more" ' . $attr . '>'.$settings['btntext'] .'</a>';

   } else {
	if ( $settings['url']['url'] ) {
		if ( !empty( $settings['url']['url'] ) ) {
			$attr  = 'href="' . $settings['url']['url'] . '"';
			$attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
			$attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
		}
		
		$btn = '<a class="axil-btn btn-bg-lighter btn-load-more" ' . $attr . '>'.$settings['btntext'] .'</a>';
		
	}
}

?>
<div class="product-area pb--<?php echo esc_attr( $section_bottom_gap );?> border-<?php echo esc_attr( $botton_border );?> style-<?php echo esc_attr( $settings['style'] );?>">
	<?php if ( $query->have_posts() ) :?>
		<div class="row row--15" >
			<?php
			while ( $query->have_posts() ) {
				$query->the_post();
				$id = get_the_ID();
				$product = wc_get_product( $id );?>
				<div class="<?php echo esc_attr( $col_class );?>">
					<?php 	wc_get_template( "custom/product-block/blocks.php" , compact( 'product', 'block_data' ) ); ?>
				</div>
			<?php } ?>
		</div>		
		<?php if ( $settings['islink']) { ?>
		<div class="row">
		    <div class="col-lg-12 text-center mt--20 mt_sm--0">
		          <?php echo wp_kses_post( $btn );?>
		    </div>
		</div>
		<?php	} ?>
	<?php else:?>
		<div><?php esc_html_e( 'No products available', 'etrade-elements' ); ?></div>
	<?php endif;?>
</div>
<?php wp_reset_postdata();?>