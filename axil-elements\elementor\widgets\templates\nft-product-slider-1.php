<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;
$query = $settings['query'];
$col_class  = "single-slide slick-slide";
if ( !empty( $settings['cat'] ) ) {
	$shop_permalink = get_category_link( $settings['cat'] );
}
else {
	$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
}

$block_data = array(
	'layout'         				=> '5',	
	'rating_display' 				=> $settings['rating_display'] ? true : false,		
	'product_display_hover'         => false,
	'product_badget_display'        => false,
);
$btn = $attr = "";

if ('2' == $settings['axil_link_type']) {
	
		$attr  = 'href="' . get_permalink($settings['axil_page_link']) . '"';
		$attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
		$attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
   } else {
	if ( !empty( $settings['url']['url'] ) ) {
		$attr  = 'href="' . $settings['url']['url'] . '"';
		$attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
		$attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
	}
}

if ( $settings['islink'] == 'yes' ) {
	$btn = '<a class="axil-btn btn-bg-white right-icon" ' . $attr . '>'.$settings['btntext'] .' <i class="fal fa-long-arrow-right"></i></a>';
}

$title_display  = $settings['section_title_display'] ? ' col-xl-6 col-md-6' : ' col-12';

?> 
<div class="axil-main-slider-area main-slider-style-3 style-<?php // echo esc_attr( $settings['style'] );?>">
	<div class="container">
		<div class="row align-items-center">
			<?php if ( $settings['section_title_display'] ): ?>	 
				<div class="col-xl-6 col-md-6">
					<div class="main-slider-content">
						<?php  if($settings['subtitle']){ ?>
						<span class="subtitle title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?>">
							<?php Icons_Manager::render_icon( $settings['icon'] ); ?><?php echo wp_kses_post( $settings['subtitle'] );?> 
						</span>
						<?php  } ?>  
						<h1 class="title sec-title"><?php echo wp_kses_post( $settings['title'] );?></h1> 
						<div class="shop-btn">
							<?php echo wp_kses_post( $btn );?> 
						</div>

					</div>
				</div>
			<?php endif; ?>

			<div class="<?php echo esc_attr( $title_display );?>">
				<div class="main-slider-large-thumb">
				<?php if ( $query->have_posts() ) :?>
					<div class="slider-thumb-activation-two axil-slick-dots">
						<?php
							while ( $query->have_posts() ) {
								$query->the_post();
								$id = get_the_ID();
								$product = wc_get_product( $id );
								?> 
								<div class="<?php echo esc_attr( $col_class );?>">
									<?php 	wc_get_template( "custom/product-block/blocks.php" , compact( 'product', 'block_data' ) ); ?>
								</div> 
								<?php } ?> 

							<?php else:?>
								<div><?php esc_html_e( 'No products available', 'etrade-elements' ); ?></div>
							<?php endif;?>
						</div>
						<?php wp_reset_postdata();?>					
					</div>
				</div>
			</div>
		</div>
	</div>