<?php
/**
 * Template part for displaying footer top layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

   
    $axil_options = Helper::axil_get_options();
    $subtitle = ( !empty( $axil_options['axil_ft_sub_title'] ) ) ? $axil_options['axil_ft_sub_title'] : "";
    $title = ( !empty( $axil_options['axil_ft_title'] ) ) ? $axil_options['axil_ft_title'] : "";
    $shortcode = ( !empty( $axil_options['axil_ft_shortcode'] ) ) ? $axil_options['axil_ft_shortcode'] : "";
    $axil_area_background = '';
    $banner_img = '';



    if ( $axil_options['axil_ft_area_background']['background-color'] != "" || $axil_options['axil_ft_area_background']['background-image'] != "" ) {
        $background_color = $axil_options['axil_ft_area_background']['background-color'] ? $axil_options['axil_ft_area_background']['background-color'] : '';
        $background_img = $axil_options['axil_ft_area_background']['background-image'] ? $axil_options['axil_ft_area_background']['background-image'] : '';
        $banner_img = "background-image:url({$background_img});background-color:{$background_color};";
        $axil_area_background = $background_img ? ' bg-color bg_image' : " bg_image bg_image--5";
    } else {
        $banner_img = '';
        $axil_area_background = " bg_image bg_image--5";
    }


?>
<div class="axil-newsletter-area axil-section-gap pt--0">
    <div class="container">
        <div class="row">
            <div class="col">
                <div style="<?php echo esc_attr( $banner_img ); ?>" class="etrade-newsletter-wrapper <?php echo esc_html( $axil_area_background ); ?>">
                    <div class="newsletter-content">
                        <span class="title-highlighter highlighter-primary2"><i class="fas fa-envelope-open"></i><?php echo esc_html( $subtitle ); ?></span>
                        <h2 class="title mb--40 mb_sm--30"><?php echo esc_html( $title ); ?></h3>
                        <?php if ( !empty( $shortcode ) ) {?>
                            <?php echo do_shortcode( $shortcode ); ?>
                        <?php }?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
 