/*-------------------------------------    
CSS INDEX
===================================
#. WooCommerce: General
#. Woocommerce: Widgets
#. Woocommerce: Owl Nav
#. Woocommerce: Top Bar
#. Woocommerce: Shop
#. Woocommerce: Single Product
#. Woocommerce: Cart
#. Woocommerce: Checkout
#. Woocommerce: Order received
#. Woocommerce: My Account
#. Woocommerce: Login/Register
#. Woocommerce: Widgets
#. Plugin: Variation Swatches
#. Plugin: Variation Gallery
#. Plugin: Yith Wishlist
#. Plugin: YITH Quickview
---------------------------------------*/
/*-------------------------------------
#. WooCommerce: General
---------------------------------------*/
/*-- Social_share --*/

/*-------------------------
    Admin Bar Styles  
---------------------------*/
/*----------------------
    Shop Styles  
----------------------*/

.wooc-overlay-cart {
    height: 100vh;
    width: 100vw;
    opacity: 0;
    background-color: black;
    position: fixed;
    top: 0;
    left: 0;
     z-index: 99; 
    display: none;
 
}

.axil-product.has-hover-image .thumbnail:hover img {
    opacity: 0!important;
    -webkit-transition: opacity 300ms ease 0.05s!important;
    -moz-transition: opacity 300ms ease 0.05s!important;
    -o-transition: opacity 300ms ease 0.05s!important;
    transition: opacity 300ms ease 0.05s!important;
}
.axil-product.has-hover-image .thumbnail img {
    opacity: 1!important;
    transform: translateZ(0) rotate(0);
    -webkit-transform: translateZ(0) rotate(0);
    -webkit-transition: opacity 300ms ease 0s!important;
    -moz-transition: opacity 300ms ease 0s!important;
    -o-transition: opacity 300ms ease 0s!important;
    transition: opacity 300ms ease 0s!important;
}
.axil-product.has-hover-image .product-hover-img {  
   border-radius: 6px;
}
.axil-product.product-style-two.has-hover-image .product-hover-img {  
    border-radius: 50%;
}
.axil-product.has-hover-image .product-hover-img {
  background-position: center center;
    width: 100%;
    height: 100%;
    opacity: 0;
    display: block;
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
    -webkit-transition: opacity 0ms ease 0.3s!important;
    -moz-transition: opacity 0ms ease 0.3s!important;
    -o-transition: opacity 0ms ease 0.3s!important;
    transition: opacity 0ms ease 0.3s!important;
}

.axil-product.has-hover-image .thumbnail:hover .product-hover-img {
    opacity: 1;
z-index: 1;
    -webkit-transition: opacity 0ms ease 0s!important;
    -moz-transition: opacity 0ms ease 0s!important;
    -o-transition: opacity 0ms ease 0s!important;
    transition: opacity 0ms ease 0s!important;
}









.overflow-hidden .elementor-widget-container{
  overflow: hidden;
}
.single-poster .inner .sub-title {   
    display: inline-block;    
}

.category-select {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: -10px;
}
.category-select .orderby ,
.select2-container.select2-container--default {
  color: var(--color-dark);
  width: auto;
  margin: 10px;
  padding-right: 43px;
  background: url(../images/arrow-icon2.png) 91% center no-repeat transparent;  
  font-weight: 500;
  font-size: 18px;
  border: 2px solid @accentColor;
  padding: 0 20px;
  position: relative;
  z-index: 99;

}
@media only screen and (max-width: 767px) {
  .category-select .orderby,
  .select2-container.select2-container--default  {
    width: 100%;
    background-position-x: 95%;
  }
}

.select2-container--default .select2-selection--single {
    background-color: transparent !important;
   
}

/* 
.axil-header  .mainmenu > li > a {
  line-height: 80px;
  height: 84px;   
} */

.axil-header .axil-mainmenu .mainmenu > li > a > img{
    max-height: 80px;
    line-height: 80px;
}

/* .axil-header .header-brand,
.axil-header .menu-item.logo {
    padding: 10px 0;
} */

.sidebar-inner .widget_archive ul li::marker {
   
     color: var(--color-body);
}

.axil-blog-area .axil-single-post  .axil-breadcrumb {   
  top: -30px;
}

body.admin-bar.header-sticky-active .axil-header .axil-sticky {
      top: 32px;
}


.comment-list .comment .time-spent .reply-edit{
  display: flex;
  margin-left: 31px;
  justify-content: center;
  align-items: center;
      margin-top: -7px;
}
.axil-product .product-hover-action .cart-action li.wishlist a.axiltheme-remove-from-wishlist{
    line-height: 35px;
}

.axil-product .product-hover-action .cart-action li.wishlist a.axiltheme-wishlist-icon:hover i{
  color: #fff;
}

.axil-product .product-hover-action .cart-action li.wishlist a.axiltheme-wishlist-icon{
    line-height: 35px;
}
/* body.single-product .axil-newsletter-area.axil-section-gap.pt--0{
    padding-top: 44px !important;
} */
body.woocommerce.woocommerce-page .axiltheme-archive-products  .axil-product  {
  margin-bottom: 40px;
}

body.woocommerce.woocommerce-page .axiltheme-loadmore-btn-area.pt--30{
  padding-top: 0 !important;
}

.quick-copyright{

}
.copyright-default .quick-link.quick-copyright li::after {
    content: inherit;
}

.error-area {
  height: 656px;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .error-area {
    height: auto;
    padding: 150px 0;
  }
}
@media only screen and (max-width: 767px) {
  .error-area {
    height: auto;
    padding: 100px 0;
  }
}

.error-area .inner img {
  margin-bottom: 40px;
}
.error-area .inner .title {
  margin-bottom: 20px;
}
.error-area .inner p {
  margin-bottom: 30px;
}

.error-area a.axil-button .hover-flip-item span::after {
  color: var(--color-primary) !important;
}

.error-area a.axil-button {
  line-height: 19px !important;
}

.star-rating {
  color: #5956E9;
  font-size: 17px;
  line-height: 1;  
  overflow: hidden;
  position: relative;
  height: 1em;
  width: 5.4em;
  font-family: 'star';
 
}

.star-rating:before {
  content: '\73\73\73\73\73';
  color: #d3ced2;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
}

.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}

.star-rating span:before {
  content: '\53\53\53\53\53';
  top: 0;
  position: absolute;
  left: 0;
}


.entry-tags {
  color: #8c8c8c;
  margin-top: 55px;
  background-color: #f9f9f9;
  padding: 30px;
  margin-bottom: 6rem;
  clear: both;
  border-radius: 6px;
}
.entry-tags::after,
.entry-tags::before {
  content: " ";
  display: block;
  clear: both;
}
.entry-tags span {
  font-size: 18px;
  font-weight: 600;
  color: var(--grey-dark-one);
  line-height: 1.3;
  padding-right: 5px;
}
.entry-tags a {
  color: #8c8c8c;
  -webkit-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
  padding-left: 5px;
}
.entry-tags a:hover {
  color: var(--primary-color);
}


.social-share {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  overflow: hidden;
}
.social-share li {
  margin-right: 1rem;
}
.social-share li a {
  font-size: 2rem;
  color: var(--grey-dark-one);
}
.social-share li a:hover {
  color: var(--primary-color);
}





.about-author img {
  max-width: 10.5rem;
  margin: 0 4rem 2rem 0;
  border-radius: 50%;
}
.about-author h5 {
  font-size: 1.8rem;
  line-height: 3rem;
  margin-bottom: 0.5rem;
}
.about-author .author-designation {
  font-size: 1.2rem;
  line-height: inherit;
  color: var(--grey-dark-three);
  font-weight: 400;
  margin-left: 1rem;
}
.about-author a img {
  max-width: 10.5rem;
  margin: 0 4rem 2rem 0;
}
@media (max-width: 767px) {
  .about-author a img {
    max-width: 6.5rem;
  }
}
.about-author p {
     font-size: var(--font-size-b3);
    line-height: var(--line-height-b3);
  color: var(--grey-dark-three);
  margin-bottom: 2rem;
}
.about-author .designation {
  margin-bottom: 1.5rem;
}
@media (max-width: 767px) {
  .about-author .media {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

#respond .comment-reply-title {
    font-size: 3.4rem;
    line-height: 3rem;
    margin-bottom: 2rem;
}

.axil-product.product-style-2  a .thumbnail {
    width: 276px;
    height: 276px;
    overflow: hidden;
    border-radius: 50%;
}

.axil-product.product-cat-block h2.woocommerce-loop-category__title {
    font-weight: 500;
    margin-bottom: 10px;
    transition: var(--transition);
    font-size: var(--h5);
}

.axil-product.product-cat-block  a .thumbnail img {
    border-radius: 6px;
    width: 100%;
}

.axil-slider.info-box.bg_image{

    background-image: radial-gradient(77.67% 226.43% at 30.03% 4.61%, #FFFFFF 0%, #FEEBED 100%);

}

.axil-header .header-items .shopping-items > li  a.cart-icon-area {
   
    padding: 20px 10px;
}

a.axiltheme-wishlist-icon img.ajax-loading {
	display: none;
}



.axil-checkout-notice .toggle-bar {    
    display: flex;
    align-items: center;
}
.axil-checkout-notice .axil-checkout-coupon {   
    margin-top: 30px;
}


.venobox img {
    width: 100%;
    border-radius: 6px;
}

.woocommerce .reviews-wrapper .star-rating {
    float: right;
    overflow: hidden;
    position: relative;
    height: 1em;
    line-height: 1;
    font-size: 16px;
    width: 5.4em;
    font-family: star;
}

.woocommerce .reviews-wrapper .star-rating span {
    overflow: hidden;
    float: left;
    top: 0;
    left: 0;
    position: absolute;
    padding-top: 1.5em;
}

.woocommerce .reviews-wrapper p.stars a {
    position: relative;
    height: 1em;
    width: 1em;
    text-indent: -999em;
    display: inline-block;
    text-decoration: none;
}
.woocommerce .reviews-wrapper .star-rating span::before {
    content: "\53\53\53\53\53";
    top: 0;
    position: absolute;
    left: 0;
    color: #FFDC60;
}

.woocommerce .reviews-wrapper p.stars.selected a:not(.active)::before {
    content: "\e020";
}
.woocommerce-product-rating {
	display: flex;
    align-items: center;
}
.woocommerce p.stars:hover a::before {
    content: "\e020";
}
.woocommerce p.stars a::before {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 1em;
    height: 1em;
    line-height: 1;
    font-family: WooCommerce;
    content: "\e021";
    text-indent: 0;
}
.woocommerce p.stars.selected a.active::before {
    content: "\e020";
}
.woocommerce p.stars a::before {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 1em;
    height: 1em;
    line-height: 1;
    font-family: WooCommerce;
    content: "\e021";
    text-indent: 0;
}

.axiltheme-star-rating {
	color: #5956E9;
	font-size: 13px;
	line-height: 1;
	margin: 10px 0 13px;
	overflow: hidden;
	position: relative;
	height: 1em;
	width: 5.4em;
	font-family: 'star';
	margin: 0px 6px 0 0px;
}

.axiltheme-star-rating:before {
	content: '\73\73\73\73\73';
	color: #d3ced2;
	float: left;
	top: 0;
	left: 0;
	position: absolute;
}

.axiltheme-star-rating span {
	overflow: hidden;
	float: left;
	top: 0;
	left: 0;
	position: absolute;
	padding-top: 1.5em;
}

.axiltheme-star-rating span:before {
	content: '\53\53\53\53\53';
	top: 0;
	position: absolute;
	left: 0;
}

.woocommerce span.onsale {
	
	display: inline-block;
	text-align: center;
	position: absolute;
	min-height: inherit;
	z-index: 10;
	min-width: inherit;
	border: 2px solid #fff;
	left: auto;
    right: -24px;
    top: 24px;
    background: #E76458;
    border: 2px solid #FFFFFF;
    height: 40px;
    line-height: 40px;
    padding: 8px 16px;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: -0.025em;
    color: #FFFFFF;
    border-radius: 500px;
    font-family: var(--font-secondary);


}

.yith-wcwl-add-button,
.yith-wcwl-wishlistexistsbrowse{
	display: none;
}
.product-action-wrapper.d-flex{
	flex: auto;
}
.color-variant li{
	margin: 8px 8px;
    cursor: pointer;
}

.woocommerce div.single-product-content form.cart .variations select {
    display: none!important;
}
 
.variation-radios input[type="radio"] ~ label::before {
	border: none;
	background: transparent !important;
	width: 28px;
	height: 28px;
	top: 0px;
	left: -1px;
	right: 0;
	margin: auto;
}

.variation-radios {
    display: flex;
    overflow: hidden;


}
.variation-radios input {
    position: absolute !important;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    width: 1px;
    border: 0;
    overflow: hidden;
}

.variation-radios label {
    background-color: #fff;   
    font-size: .75em;
    line-height: 1;
    text-align: center;
    padding: 0;
    margin-right: -1px;   
    transition: all 0.1s ease-in-out;
    font-weight: 400;
    display: inline-table;
 	background: transparent;    
    width: 16px;
    height: 16px;
    border-radius: 100%;
    border: 0 none !important;

}

.color-variant li span {
    padding: 5px;
    border: 1px solid transparent;
    display: block;
    border-radius: 100%;
    position: relative;
    z-index: 10;
}
.variation-radios label:hover {
    cursor: pointer;
}

.variation-radios input:checked + label span.color {
   border-color: #292930;
}
/*.variation-radios label:first-of-type {
    border-radius: 2px 0 0 2px;
}

.variation-radios label:last-of-type {
    border-radius: 0 2px 2px 0;
}*/
.woocommerce-product-details__short-description {
    margin-bottom: 1em;
}


/* -----------------------
Pagination Styles 
--------------------------*/
/* -------------------------------------------------------------------------------------------------------------*/

.nav-links .screen-reader-text {
  display: none;
}

.nav-links ul {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  list-style: none;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  margin: -3px;
  padding: 0;
}

.nav-links ul li {
  margin: 3px;
}

.nav-links ul li span {
  line-height: 42px;
  min-width: 42px;
  text-align: center;
  color: var(--color-heading);
  -webkit-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  display: block;
  padding: 0 15px;
  transition: all 0.5s;
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-small);
}

.nav-links ul li.active span {
  background: var(--color-primary);
  color: #ffffff;
  border-color: var(--color-primary);
}

.nav-links ul li a {
  line-height: 42px;
  min-width: 42px;
  text-align: center;
  color: var(--color-heading);
  -webkit-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  display: block;
  padding: 0 15px;
  transition: all 0.5s;
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-small);
}

.nav-links ul li a:hover {
  background: var(--color-primary);
  color: #ffffff;
  border-color: var(--color-primary);
}
/* -------------------------------------------------------------------------------------------------------------*/


/*-------------------------------------
#. Woocommerce: Order received
---------------------------------------*/
.woocommerce-order-received .woocommerce {
	color: #222222;
}

.woocommerce-order-received .woocommerce h2 {
	font-size: 20px;
}

.woocommerce-order-received .woocommerce .order_details li {
	font-size: 1em;
}

.woocommerce-order-received .woocommerce .woocommerce-thankyou-order-received {
	font-size: 20px;
	margin-bottom: 40px;
}

.woocommerce-order-received .woocommerce .title h3 {
	margin-bottom: 10px;
}

.woocommerce .woocommerce-customer-details address {
	border-radius: 0;
	padding: 20px;
	border: 1px solid #ccc;
}

.woocommerce .woocommerce-customer-details address br {
	margin-bottom: 10px;
}

.woocommerce .woocommerce-customer-details address p {
	margin-top: 10px;
}

.woocommerce-order-details {
	margin-top: 40px;
}



/*-------------------------------------
#. Woocommerce: Widgets
---------------------------------------*/
.sidebar-widget-area .widget.widget_shopping_cart a {
	color: #222222;
}

.sidebar-widget-area .widget.widget_shopping_cart .buttons a {
	color: #ffffff;
}

.sidebar-widget-area .widget.widget_shopping_cart .buttons a:hover {
	color: #ffffff;
}

.sidebar-widget-area .widget.widget_shopping_cart ul li:before {
	content: none;
}

.sidebar-widget-area .widget.widget_shopping_cart .mini_cart_item .quantity {
	color: #606060;
	border: none;
}

.sidebar-widget-area .widget.widget_shopping_cart .woocommerce-mini-cart__total {
	color: #222222;
	border: none;
	font-size: 16px;
}

.sidebar-widget-area .widget.widget_products .product_list_widget .woocue-content,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget .woocue-content,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget .woocue-content {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.sidebar-widget-area .widget.widget_products .product_list_widget .woocue-content .woocue-left,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget .woocue-content .woocue-left,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget .woocue-content .woocue-left {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
}

.sidebar-widget-area .widget.widget_products .product_list_widget .woocue-content .woocue-right,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget .woocue-content .woocue-right,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget .woocue-content .woocue-right {
	-webkit-box-flex: 1;
	-ms-flex: 1 1 auto;
	flex: 1 1 auto;
}

.sidebar-widget-area .widget.widget_products .product_list_widget li,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li {
	padding: 10px 0 0;
}

.sidebar-widget-area .widget.widget_products .product_list_widget li .woocue-left a,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li .woocue-left a,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li .woocue-left a {
	display: block;
	margin-right: 20px;
	position: relative;
	border-radius: 5px;
	overflow: hidden;
	font-weight: 400;
}

.sidebar-widget-area .widget.widget_products .product_list_widget li .woocue-left a img,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li .woocue-left a img,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li .woocue-left a img {
	-webkit-transform: scale(1);
	transform: scale(1);
	-webkit-transition: 0.3s ease-in-out;
	transition: 0.3s ease-in-out;
}

.sidebar-widget-area .widget.widget_products .product_list_widget li .woocue-left a .woocue-icon,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li .woocue-left a .woocue-icon,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li .woocue-left a .woocue-icon {
	height: 100%;
	width: 100%;
	position: absolute;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	left: 0;
	right: 0;
	z-index: 2;
	opacity: 0;
	visibility: hidden;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-transition: all 0.3s ease-out;
	transition: all 0.3s ease-out;
	background-color: rgba(34, 34, 34, 0.7);
}

.sidebar-widget-area .widget.widget_products .product_list_widget li .woocue-left a .woocue-icon i:before,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li .woocue-left a .woocue-icon i:before,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li .woocue-left a .woocue-icon i:before {
	color: #ffffff;
	margin-left: 0;
	font-size: 25px;
	line-height: 1;
}

.sidebar-widget-area .widget.widget_products .product_list_widget li .woocue-left a:hover .woocue-icon,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li .woocue-left a:hover .woocue-icon,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li .woocue-left a:hover .woocue-icon {
	opacity: 1;
	visibility: visible;
}

.sidebar-widget-area .widget.widget_products .product_list_widget li .woocue-left a:hover img,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li .woocue-left a:hover img,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li .woocue-left a:hover img {
	-webkit-transform: scale(1.1);
	transform: scale(1.1);
}

.sidebar-widget-area .widget.widget_products .product_list_widget li:before,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li:before,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li:before {
	content: none;
}

.sidebar-widget-area .widget.widget_products .product_list_widget li img,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget li img,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget li img {
	float: none;
	width: 85px;
	margin: 0;
	border-radius: 5px;
	position: relative;
}

.sidebar-widget-area .widget.widget_products .product_list_widget .product-title,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget .product-title,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget .product-title {
	display: block;
	color: #606060;
	vertical-align: top;
	font-weight: 400;
	-webkit-transition: all 0.3s ease-out;
	transition: all 0.3s ease-out;
}

.sidebar-widget-area .widget.widget_products .product_list_widget .product-title:hover,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget .product-title:hover,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget .product-title:hover {
	color: #5956E9;
}

.sidebar-widget-area .widget.widget_products .product_list_widget .axiltheme-star-rating,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget .axiltheme-star-rating,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget .axiltheme-star-rating {
	margin: 5px 0 0;
}

.sidebar-widget-area .widget.widget_products .product_list_widget .woocommerce-Price-amount,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget .woocommerce-Price-amount,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget .woocommerce-Price-amount {
	color: #222222;
	font-weight: 500;
	font-size: 18px;
	line-height: 1.3;
	margin: 10px 0 0;
	display: inline-block;
}

.sidebar-widget-area .widget.widget_products .product_list_widget del .woocommerce-Price-amount,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget del .woocommerce-Price-amount,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget del .woocommerce-Price-amount {
	color: #9c9c9c;
	font-weight: 400;
	padding-right: 5px;
	text-decoration: line-through;
}

.sidebar-widget-area .widget.widget_products .product_list_widget ins,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget ins,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget ins {
	background-color: transparent;
}

.sidebar-widget-area .widget.widget_products .product_list_widget .reviewer,
.sidebar-widget-area .widget.widget_recent_reviews .product_list_widget .reviewer,
.sidebar-widget-area .widget.widget_top_rated_products .product_list_widget .reviewer {
	display: inline-block;
	margin: 10px 0 0;
	color: #222222;
}

.sidebar-widget-area .widget.widget_product_categories li {
	margin-bottom: 10px;
	position: relative;
	list-style: none;
}

.sidebar-widget-area .widget.widget_product_categories li a {
	display: block;
	background-color: #f8f8fb;
	height: 50px;
	line-height: 50px;
	width: 100%;
	position: relative;
	text-transform: capitalize;
	font-size: 14px;
	font-weight: 500;
	padding-left: 20px;
	-webkit-transition: 0.3s ease-in-out;
	transition: 0.3s ease-in-out;
	border-radius: 12px;
}

.sidebar-widget-area .widget.widget_product_categories li a:hover {
	background-color: #E76458;
	color: #fff;
}

.sidebar-widget-area .widget.widget_product_categories li span.count {
	-webkit-transition: 0.3s ease-in-out;
	transition: 0.3s ease-in-out;
	position: absolute;
	right: 0;
	top: 0;
	height: 100%;
	width: 50px;
	background-color: #e6e6e6;
	text-align: center;
	color: #222222;
	line-height: 48px;
	height: 50px;
	border-radius: 0 12px 12px 0;
	display: inherit;
}

.sidebar-widget-area .widget.widget_product_categories li:hover span.count {
	background-color: #222222;
	color: #fff;
}

.sidebar-widget-area .widget.widget_product_categories .select2-container .select2-selection--single {
	height: 44px;
	border-radius: 10px;
	border-color: #f8f8fb;
	padding: 4px 0 6px 0;
}

.sidebar-widget-area .widget.widget_product_categories .select2-container .select2-selection--single .select2-selection__rendered {
	color: #606060;
	padding: 4px 25px 4px 14px;
}

.sidebar-widget-area .widget.widget_product_categories .select2-container .select2-selection--single .select2-selection__rendered .select2-selection__placeholder {
	color: #606060;
	font-size: 15px;
	line-height: 1.5;
}

.sidebar-widget-area .widget.widget_product_categories .select2-container .select2-selection--single .select2-selection__arrow {
	height: inherit;
	top: -1px;
	right: 5px;
}

.sidebar-widget-area .widget.woocommerce-widget-layered-nav .select2-container--default .select2-selection--multiple .select2-selection__choice {
	line-height: 1;
	color: #222222;
	background: #d5d5d5;
	border: none;
	-webkit-box-shadow: none;
	box-shadow: none;
	outline: none;
	padding: 7px 10px;
	font-size: 14px;
}

.sidebar-widget-area .widget.woocommerce-widget-layered-nav .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
	margin-right: 7px;
	padding: 0;
}

.sidebar-widget-area .widget.woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-dropdown__submit {
	border-radius: 2px;
	font-weight: 500;
}

/*--------------------------------------
#. Plugin: Variation Swatches
---------------------------------------*/
.woocwpvs .woocwpvs-terms-wrapper {
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	vertical-align: middle;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-color-term {
	height: 17px;
	width: 17px;
	-webkit-box-shadow: none !important;
	box-shadow: none !important;
	padding: 0 !important;
	margin: 0 10px 0 0 !important;
	border-radius: 4px !important;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-color-term:last-child {
	margin: 0 !important;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-color-term .woocwpvs-term-span-color {
	border-radius: 50% !important;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-color-term .woocwpvs-term-span-color:after {
	content: none !important;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-color-term.selected .woocwpvs-term-span-color,
.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-color-term:hover .woocwpvs-term-span-color {
	-webkit-box-shadow: 0 5px 5px 0 rgba(34, 34, 34, 0.4);
	box-shadow: 0 5px 5px 0 rgba(34, 34, 34, 0.4);
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-size-term {
	margin: 0 20px 10px 0 !important;
	-webkit-box-shadow: none !important;
	box-shadow: none !important;
	padding: 0 !important;
	height: inherit !important;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-size-term .woocwpvs-term-span-size {
	border: 1px solid #dcdcdc;
	color: #222222 !important;
	border-radius: 4px !important;
	padding: 10px 15px !important;
	line-height: 1;
	-webkit-transition: 0.3s ease-in-out;
	transition: 0.3s ease-in-out;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-size-term:hover .woocwpvs-term-span-size,
.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-size-term.selected .woocwpvs-term-span-size {
	background-color: #5956E9;
	border-color: #5956E9;
	color: #ffffff !important;
}

.woocwpvg-has-product-thumbnail .woocwpvg-thumbnail-position-bottom .woocwpvg-slider-wrapper {
	margin-bottom: 15px;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-term:not(.woocwpvs-radio-term),
.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-term:not(.woocwpvs-radio-term):hover,
.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-term:not(.woocwpvs-radio-term).selected:hover,
.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-term:not(.woocwpvs-radio-term).selected {
	-webkit-box-shadow: none !important;
	box-shadow: none !important;
}

.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-size-term:hover span,
.woocwpvs .woocwpvs-terms-wrapper .woocwpvs-size-term.selected:hover span {
	color: #fff !important;
}

.wooc-product-block .woocwpvs-variation-terms-wrapper .woocwpvs-color-term {
	height: 10px;
	width: 10px;
}

/*--------------------------------------
#. Plugin: Variation Gallery
---------------------------------------*/
.woocwpvg-wrapper .woocwpvg-slider-wrapper .woocwpvg-slider .woocwpvg-gallery-image {
	text-align: left;
}

.woocwpvg-wrapper .woocwpvg-slider-wrapper .woocwpvg-trigger {
	border-radius: 2px;
	width: 45px;
	height: 45px;
}

.woocwpvg-wrapper .woocwpvg-slider-wrapper .woocwpvg-trigger .dashicons-search {
	width: inherit;
	height: inherit;
}

.woocwpvg-wrapper .woocwpvg-slider-wrapper .woocwpvg-trigger .dashicons-search:before {
	content: "\f0b2";
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
	font-size: 18px;
	line-height: 45px;
	width: inherit;
	height: inherit;
	color: #606060;
	-webkit-transition: all 0.5s ease-out;
	transition: all 0.5s ease-out;
}

.woocwpvg-wrapper .woocwpvg-slider-wrapper .woocwpvg-trigger .dashicons-search:hover:before {
	color: #5956E9;
}



/*-------------------------------------
#. Plugin: Yith Wishlist
---------------------------------------*/
.woocommerce-wishlist .hidden-title-form {
	padding: 20px 0;
}

.hidden-title-form input[type="text"],
.hidden-title-form input[type="email"],
.hidden-title-form input[type="url"],
.hidden-title-form input[type="password"],
.hidden-title-form input[type="search"],
.hidden-title-form input[type="number"],
.hidden-title-form input[type="tel"],
.hidden-title-form input[type="range"],
.hidden-title-form input[type="date"],
.hidden-title-form input[type="month"],
.hidden-title-form input[type="week"],
.hidden-title-form input[type="time"],
.hidden-title-form input[type="datetime"],
.hidden-title-form input[type="datetime-local"],
.hidden-title-form input[type="color"],
.hidden-title-form textarea {
	border: 2px solid #f8f8fb;
	border-radius: 8px;
	padding: 4px 4px 4px 9px;
}

.hidden-title-form button,
.hidden-title-form input[type="button"],
.hidden-title-form input[type="reset"],
.hidden-title-form input[type="submit"] {
	background-color: #222222;
	border: medium none;
	color: #fff;
	border-radius: 8px;
	padding: 5px 20px;
}

.yith_wcwl_wishlist_footer .yith-wcwl-share>ul {
	padding: 0;
	margin: 0;
}

.yith_wcwl_wishlist_footer .yith-wcwl-share>ul li {
	display: inline-block;
	vertical-align: middle;
}

.yith_wcwl_wishlist_footer .yith-wcwl-share>ul li a {
	margin-left: 10px;
	color: #222222;
	font-size: 15px;
	line-height: 1;
	background-color: #f8f8fb;
	padding: 18px 20px;
	border-radius: 12px;
	display: block;
}

.yith_wcwl_wishlist_footer .yith-wcwl-share>ul li a:hover {
	color: #fff;
	background-color: #5956E9;
}

.yith_wcwl_wishlist_footer .yith-wcwl-share>ul li:first-child a {
	margin: 0;
}

.yith_wcwl_wishlist_footer .yith-wcwl-share-title {
	color: #222222;
	font-size: 20px;
	line-height: 1.4;
	padding: 0;
	margin-bottom: 20px;
	font-weight: 600;
	padding-left: 45px;
	position: relative;
}

.yith_wcwl_wishlist_footer .yith-wcwl-share-title:after {
	width: 18px;
	height: 4px;
	content: '';
	position: absolute;
	left: 0px;
	border-radius: 2px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	background-color: #5956E9;
}

.yith_wcwl_wishlist_footer .yith-wcwl-share-title:before {
	width: 4px;
	height: 4px;
	content: '';
	position: absolute;
	left: 22px;
	border-radius: 2px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	background-color: #5956E9;
}

.woocommerce .shop_table.wishlist_table tr td.product-remove {
	padding: 30px 12px 30px 0;
}

.woocommerce .shop_table.wishlist_table tr td.product-remove a.remove {
	margin: 0 !important;
}

.woocommerce .shop_table.wishlist_table .product-thumbnail img {
	width: 90px;
	border-radius: 8px;
	margin-right: 28px;
}

.woocommerce .shop_table.wishlist_table .product-add-to-cart {
	text-align: right;
}

.woocommerce .shop_table.wishlist_table .product-add-to-cart a {	

  background: var(--color-primary);
  border-radius: 500px;
  font-size: 18px;
  line-height: var(--line-height-b2);
  font-weight: 500;
  display: inline-block;
  padding: 16px 40px;
  color: #ffffff;
  transition: 0.3s;
  border: 2px solid transparent;
}


@media only screen and (max-width: 767px) {
 .woocommerce .shop_table.wishlist_table .product-add-to-cart a {
    padding: 10px 30px;
  }
}

.woocommerce .shop_table.wishlist_table .product-add-to-cart a.add_to_cart_button {
  background: transparent;
  border: 2px solid #656973;
  color: var(--color-dark);
}
.woocommerce .shop_table.wishlist_table .product-add-to-cart a.add_to_cart_button:hover {
  border-color: var(--color-primary);
  background-color: var(--color-primary);
  color: #fff;
}



.woocommerce .shop_table.wishlist_table .product-price del {
	color: #5956E9;
	font-weight: 400;
	padding-right: 5px;
	opacity: 0.5;
	display: inline-block;
}

.woocommerce .shop_table.wishlist_table .product-price ins {
	background-color: transparent;
	display: inline-block;
}

.woocommerce .shop_table.wishlist_table tfoot td {
	border: none;
}

.woocommerce .shop_table.wishlist_table tfoot td .yith-wcwl-share {
	margin: 20px 0 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.woocommerce .shop_table.wishlist_table tfoot td .yith-wcwl-share .yith-wcwl-share-title {
	margin: 0 20px 0 0;
}

.woocommerce .shop_table.wishlist_table tfoot td .yith-wcwl-share ul {
	margin: 0;
}

.woocommerce .shop_table.wishlist_table tfoot td .yith-wcwl-share ul a {
	margin: 0 1px;
}

.woocommerce form .form-row .password-input {
	width: 100%;
}



.wishlist-title-container{
	margin-bottom: 30px;
}



.wishlist-title  h2 {
    margin-bottom: 20px;
    font-weight: 500;
    display: inline-block;
    font-size: 32px;

}


.wishlist_table  thead {
    background-color: var(--color-lighter);
}
.wishlist_table  thead th:first-child {
    border-radius: 20px 0 0 20px;
}
.wishlist_table  thead th {
    font-size: 20px;
    text-transform: capitalize;
    border: none;
    color: var(--color-heading);
    padding: 18px 15px;
}
.wishlist_table tbody td {
    border-top: none;
    border-bottom: 2px solid var(--color-lighter);
    vertical-align: middle;
    padding: 20px 15px;
    font-size: 20px;
    font-weight: 500;
    color: var(--color-body);
    min-width: 150px;
}

.wishlist_table tbody.wishlist-items-wrapper .product-remove a.remove_from_wishlist {
    display: block;
    text-align: center;
    height: 32px;
    width: 32px;
    line-height: 30px;
    background-color: var(--color-lighter);
    border: 2px solid var(--color-white);
    border-radius: 50%;
   
    color: var(--color-black);
    transition: var(--transition);
    font-size: 18px;
}
.wishlist_table tbody.wishlist-items-wrapper .product-remove a.remove_from_wishlist:hover {
    border-color: var(--color-primary);
}

/*--------------------------------------
#. Plugin: YITH Quickview
---------------------------------------*/
#yith-quick-view-content {
	padding: 10px;
}

#yith-quick-view-content div.images {
	float: none;
	width: inherit;
}

.woo-categories-shop-top {
	margin-bottom: 40px;
	border-bottom: 2px solid #f8f8fb;
	padding-bottom: 30px;
	position: relative;
}

/* Header: Product categories menu */
.wooc-shop-categories {
	padding: 0 0 0 20px;
	margin: 0;
	position: relative;
}

.wooc-shop-categories li {
	display: inline-block;
	vertical-align: middle;
	font-family: 'Josefin Sans', sans-serif;
}

.wooc-shop-categories li span {
	color: #e9f1f8;
	font-weight: 700;
}

.wooc-shop-categories li:first-child a {
	margin-left: 0;
}

.wooc-shop-categories li a {
	font-size: 15px;
	font-weight: 500;
	line-height: 2;
	margin: 0 15px;
	color: #606060;
}

.wooc-shop-categories li a:hover {
	color: #5956E9;
}

.wooc-shop-categories li.current-cat a {
	color: #5956E9;
}

.wooc-shop-categories .wooc-shop-sub-categories {
	width: 100%;
	padding-top: 16px;
	overflow: hidden;
}

.wooc-shop-categories .wooc-shop-sub-categories li a {
	margin: 0 28px 0 0;
}

.wooc-shop-categories .wooc-shop-sub-categories li.current-cat a,
.wooc-shop-categories .wooc-shop-sub-categories li.active a {
	padding-bottom: 3px;
}

.wooc-shop-categories .wooc-shop-sub-categories li span {
	display: none;
}

/* Header: Product categories menu - Back button */
.wooc-shop-categories li.wooc-category-back-button.current-cat a {
	color: inherit;
	padding-bottom: 0;
	border-bottom: 0 none;
	opacity: 0.5;
	cursor: default;
}

.wooc-shop-categories li.wooc-category-back-button a i {
	vertical-align: middle;
}

/*--------------------------------------
#. Wooc : Banner
---------------------------------------*/
/*
.wooc-product-banner.layout-1{
	.product-image-content span.bg-shape {   
	    left: 0;

	    @media @tablet {
	    	 left: -21px;
	    } 
	   @media @tabletSmill {
	    	 left: 0px;
	    }

	    @media @mobile {
			right: 0;
			margin: auto;
			bottom: 0;
			 left: 0;
		}
	}
	.slider-big-img img {
		display: inline-block;
		margin-left: -30px;
		@media @desktop {
			margin-left: 0;
			width: 80%;

		}
	}

	.slider-big-img {
		text-align: left;
		padding-left: 50px;
		@media @desktop {
			padding-left: 0;
		}
		@media @mobile {
			text-align: center;
		}

	}

	.nav-item-image{
		margin-right: 15px;
	}
	.wooc-price{
		font-weight: 600;
		font-size: 16px;
		line-height: 1.3;	
		.wooc-sale-price{		
			 color: @primaryColor;	  	
		}	
		.wooc-reg-price {
			color: @metaColor;
			padding-left: 5px;
			font-size: 15px;
		}
		
	}
	.product-banner-nav-img .media {
		.ptitle{
			margin-top: 22px;
			margin-bottom: 8px;
			padding-right: 15px;
			line-height: 1.3;
			font-size: 18px;
		}
	}
}*/
/*
.wooc-product-banner.layout-2{
	.product-image-content span.bg-shape {   
	    left: 0;

	    @media @tablet {
	    	 left: -21px;
	    } 
	   @media @tabletSmill {
	    	 left: 0px;
	    }

	    @media @mobile {
			right: 0;
			margin: auto;
			bottom: 0;
			 left: 0;
		}
	}
	.slider-big-img img {
		display: inline-block;
		margin-left: -30px;
		@media @desktop {
			margin-left: 0;
			width: 80%;

		}
	}

	.slider-big-img {
		text-align: left;
		padding-left: 50px;
		@media @desktop {
			padding-left: 0;
		}
		@media @mobile {
			text-align: center;
		}

	}

	.nav-item-image{
		margin-right: 15px;
	}
	.wooc-price{
		font-weight: 600;
		font-size: 16px;
		line-height: 1.3;	
		.wooc-sale-price{		
			 color: @primaryColor;	  	
		}	
		.wooc-reg-price {
			color: @metaColor;
			padding-left: 5px;
			font-size: 15px;
		}
		
	}
	.product-banner-nav-img .media {
		.ptitle{
			margin-top: 22px;
			margin-bottom: 8px;
			padding-right: 15px;
			line-height: 1.3;
			font-size: 18px;
			color: @secondaryColor;
		}
	}
}

*/
.wooc-product-banner.layout-1 .product-image-content span.bg-shape,
.wooc-product-banner.layout-2 .product-image-content span.bg-shape,
.wooc-product-banner.layout-3 .product-image-content span.bg-shape {
	left: 0;
}

@media all and (max-width: 991.98px) {

	.wooc-product-banner.layout-1 .product-image-content span.bg-shape,
	.wooc-product-banner.layout-2 .product-image-content span.bg-shape,
	.wooc-product-banner.layout-3 .product-image-content span.bg-shape {
		left: -21px;
	}
}

@media all and (max-width: 825.98px) {

	.wooc-product-banner.layout-1 .product-image-content span.bg-shape,
	.wooc-product-banner.layout-2 .product-image-content span.bg-shape,
	.wooc-product-banner.layout-3 .product-image-content span.bg-shape {
		left: 0px;
	}
}

@media all and (max-width: 767.98px) {

	.wooc-product-banner.layout-1 .product-image-content span.bg-shape,
	.wooc-product-banner.layout-2 .product-image-content span.bg-shape,
	.wooc-product-banner.layout-3 .product-image-content span.bg-shape {
		right: 0;
		margin: auto;
		bottom: 0;
		left: 0;
	}
}

.wooc-product-banner.layout-1 .slider-big-img img,
.wooc-product-banner.layout-2 .slider-big-img img,
.wooc-product-banner.layout-3 .slider-big-img img {
	display: inline-block;
	margin-left: -30px;
}

@media all and (max-width: 1199.98px) {

	.wooc-product-banner.layout-1 .slider-big-img img,
	.wooc-product-banner.layout-2 .slider-big-img img,
	.wooc-product-banner.layout-3 .slider-big-img img {
		margin-left: 0;
		width: 80%;
	}
}

.wooc-product-banner.layout-1 .slider-big-img,
.wooc-product-banner.layout-2 .slider-big-img,
.wooc-product-banner.layout-3 .slider-big-img {
	text-align: left;
	padding-left: 50px;
}

@media all and (max-width: 1199.98px) {

	.wooc-product-banner.layout-1 .slider-big-img,
	.wooc-product-banner.layout-2 .slider-big-img,
	.wooc-product-banner.layout-3 .slider-big-img {
		padding-left: 0;
	}
}

@media all and (max-width: 767.98px) {

	.wooc-product-banner.layout-1 .slider-big-img,
	.wooc-product-banner.layout-2 .slider-big-img,
	.wooc-product-banner.layout-3 .slider-big-img {
		text-align: center;
	}
}

.wooc-product-banner.layout-1 .nav-item-image,
.wooc-product-banner.layout-2 .nav-item-image,
.wooc-product-banner.layout-3 .nav-item-image {
	margin-right: 15px;
}

.wooc-product-banner.layout-1 .wooc-price,
.wooc-product-banner.layout-2 .wooc-price,
.wooc-product-banner.layout-3 .wooc-price {
	font-weight: 600;
	font-size: 18px;
	line-height: 1.3;
}

.wooc-product-banner.layout-1 .wooc-price .wooc-sale-price,
.wooc-product-banner.layout-2 .wooc-price .wooc-sale-price,
.wooc-product-banner.layout-3 .wooc-price .wooc-sale-price {
	color: #222222;
}

.wooc-product-banner.layout-1 .wooc-price .wooc-reg-price,
.wooc-product-banner.layout-2 .wooc-price .wooc-reg-price,
.wooc-product-banner.layout-3 .wooc-price .wooc-reg-price {
	color: #b3bed3;
	padding-left: 5px;
	font-size: 15px;
}

.wooc-product-banner.layout-1 .product-banner-nav-img .media .ptitle,
.wooc-product-banner.layout-2 .product-banner-nav-img .media .ptitle,
.wooc-product-banner.layout-3 .product-banner-nav-img .media .ptitle {
	margin-top: 26px;
	margin-bottom: 8px;
	padding-right: 15px;
	line-height: 1.3;
	font-size: 16px;
	color: #222222;
	font-weight: 500;
}

/*.wooc-product-banner.layout-2{
	.slider-big-img{
		margin-right: 15px;
	}
	.slider-big-img img {
		display: inline-block;
		border-radius: 30px;

	}
	.product-banner-nav-img .media img {
		margin-left: 0;
		position: relative;
		border-radius: 12px;
		margin-right: 15px;
	}
	.nav-item .item-bg:after {
		content: "";
		background-color: #fff;
		width: 100%;
		position: absolute;
		z-index: -1;
		border-radius: 16px;
		min-height: 104px;
		bottom: 0;
	}
}*/
.wooc-product-banner .product-banner-left {
	position: relative;
	z-index: 9;
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-banner-left {
		padding-left: 0;
	}
}

.wooc-product-banner .product-banner-left p {
	font-weight: 500;
	color: #606060;
	margin-bottom: 18px;
	position: relative;
	padding-left: 15px;
	display: inline-block;
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-banner-left p {
		display: inline-block;
		clear: both;
		font-size: 14px;
	}
}

.wooc-product-banner .product-banner-left p:after {
	position: absolute;
	content: "";
	height: 40px;
	width: 40px;
	background-color: #fff;
	left: 0;
	bottom: -5px;
	border-radius: 50%;
	z-index: -1;
}

@media all and (max-width: 991.98px) {
	.wooc-product-banner .product-banner-left .wooc-button-pli {
		padding: 16px 20px;
	}
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-banner-left .wooc-button-pli {
		font-size: 14px;
		padding: 14px 20px;
	}
}

@media all and (max-width: 991.98px) {
	.wooc-product-banner .product-banner-left .wooc-button-light {
		padding: 16px 20px;
	}
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-banner-left .wooc-button-light {
		font-size: 14px;
		padding: 14px 20px;
	}
}

.wooc-product-banner .product-banner-left .banner-title {
	font-size: 64px;
	line-height: 1.2;
	font-weight: 600;
	position: relative;
	margin-bottom: 40px;
}

@media all and (max-width: 1640.98px) {
	.wooc-product-banner .product-banner-left .banner-title {
		font-size: 64px;
		line-height: 1.2;
	}
}

@media all and (max-width: 1399.98px) {
	.wooc-product-banner .product-banner-left .banner-title {
		font-size: 60px;
		line-height: 1.2;
	}
}

@media all and (max-width: 1199.98px) {
	.wooc-product-banner .product-banner-left .banner-title {
		font-size: 42px;
		line-height: 1.2;
	}
}

@media all and (max-width: 1024.98px) {
	.wooc-product-banner .product-banner-left .banner-title {
		font-size: 42px;
		line-height: 1.2;
	}
}

@media all and (max-width: 991.98px) {
	.wooc-product-banner .product-banner-left .banner-title {
		font-size: 38px;
		line-height: 1.2;
	}
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-banner-left .banner-title {
		font-size: 38px;
	}
}

@media all and (max-width: 479.98px) {
	.wooc-product-banner .product-banner-left .banner-title {
		font-size: 36px;
	}
}

.wooc-product-banner .product-banner-left .banner-title span {
	color: #5956E9;
	width: 4px;
	height: 4px;
	background-color: #5956E9;
	position: absolute;
	bottom: 13px;
	line-height: 1;
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-banner-left .banner-title span {
		bottom: 9px;
	}
}

.wooc-product-banner .product-banner-left .banner-link-set .wooc-button-pli {
	margin-right: 10px;
}

@media all and (max-width: 1199.98px) {
	.wooc-product-banner .product-banner-left .banner-link-set .wooc-button-pli {
		margin-right: 4px;
		padding: 18px 20px;
	}
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-banner-left .banner-link-set .wooc-button-pli {
		margin-right: 4px;
		padding: 16px 18px;
	}
}

@media all and (max-width: 1199.98px) {
	.wooc-product-banner .product-banner-left .banner-link-set .wooc-button-light {
		margin-right: 4px;
		padding: 18px 20px;
	}
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-banner-left .banner-link-set .wooc-button-light {
		padding: 16px 18px;
	}
}

.wooc-product-banner .product-banner-nav-img .media img {
	position: relative;
}

.wooc-product-banner .product-banner-nav-img .media h4 {
	margin-bottom: 0;
	font-size: 16px;
	font-weight: 600;
}

.wooc-product-banner .product-banner-nav-img .media p {
	margin-bottom: 0;
}

.wooc-product-banner .nav-item {
	margin-bottom: 15px;
	position: relative;
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .nav-item {
		margin-bottom: 10px;
		margin-top: 5px;
	}
}

.wooc-product-banner .nav-item .item-bg:after {
	content: "";
	background-color: #fff5e8;
	width: 100%;
	position: absolute;
	z-index: -2;
	border-radius: 16px;
	min-height: 104px;
	bottom: -8px;
}

.slider-big-img {
	text-align: center;
}

.slider-big-img img {
	display: inline-block;
}

.bg-shape-color {
	background-color: #ffffff;
}

.bg-shape-color:before,
.bg-shape-color:after {
	content: '';
	width: 446px;
	height: 446px;
	position: absolute;
	bottom: 0;
	background-color: #ffffff;
	opacity: 0.4;
	z-index: -1;
	left: 0;
	right: 0;
	margin: auto;
	border-radius: 50%;
	-webkit-transform: scale(0);
	transform: scale(0);
}

@media all and (max-width: 1199.98px) {

	.bg-shape-color:before,
	.bg-shape-color:after {
		width: 300px;
		height: 300px;
	}
}

.slick-slide.slick-current.slick-active .bg-shape-color:before,
.slick-slide.slick-current.slick-active .bg-shape-color:after {
	-webkit-animation-name: scaling;
	animation-name: scaling;
	-webkit-animation-duration: 4s;
	animation-duration: 4s;
	-webkit-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	-webkit-animation-timing-function: linear;
	animation-timing-function: linear;
	-webkit-animation-delay: 1.3s;
	animation-delay: 1.3s;
}

@-webkit-keyframes scaling {
	0% {
		-webkit-transform: scale(0.5);
		transform: scale(0.5);
	}

	90% {
		-webkit-transform: scale(1.7);
		transform: scale(1.7);
	}

	100% {
		opacity: 0;
	}
}

@keyframes scaling {
	0% {
		-webkit-transform: scale(0.5);
		transform: scale(0.5);
	}

	90% {
		-webkit-transform: scale(1.7);
		transform: scale(1.7);
	}

	100% {
		opacity: 0;
	}
}

.bg-shape-color1 {
	background-color: #fff5e8;
}

.bg-shape-color1:before,
.bg-shape-color1:after {
	background-color: #fff5e8;
}

.bg-shape-color2 {
	background-color: #f5f1ff;
}

.bg-shape-color2:before,
.bg-shape-color2:after {
	background-color: #f5f1ff;
}

.bg-shape-color3 {
	background-color: #ecfeff;
}

.bg-shape-color3:before,
.bg-shape-color3:after {
	background-color: #ecfeff;
}

.bg-shape-color4 {
	background-color: #e9fff3;
}

.bg-shape-color4:before,
.bg-shape-color4:after {
	background-color: #e9fff3;
}

.bg-shape-color5 {
	background-color: #ecfeff;
}

.bg-shape-color5:before,
.bg-shape-color5:after {
	background-color: #ecfeff;
}

.wooc-product-banner .product-image-content span.bg-shape {
	bottom: 50px;
	width: 446px;
	height: 446px;
	position: absolute;
	display: inline-block;
	border-radius: 50%;
	opacity: 0;
	z-index: -1;
	-webkit-transform: scale(0);
	transform: scale(0);
}

@media all and (max-width: 1199.98px) {
	.wooc-product-banner .product-image-content span.bg-shape {
		width: 300px;
		height: 300px;
	}
}

@media all and (max-width: 991.98px) {
	.wooc-product-banner .product-image-content span.bg-shape {
		width: 400px;
		height: 400px;
	}
}

@media all and (max-width: 825.98px) {
	.wooc-product-banner .product-image-content span.bg-shape {
		width: 300px;
		height: 300px;
	}
}

@media all and (max-width: 767.98px) {
	.wooc-product-banner .product-image-content span.bg-shape {
		width: 360px;
		height: 360px;
	}
}

@media all and (max-width: 575.98px) {
	.wooc-product-banner .product-image-content span.bg-shape {
		width: 300px;
		height: 300px;
	}
}

.wooc-product-banner .slick-slide.slick-current.slick-active.product-image-content span.bg-shape {
	opacity: 1;
	-webkit-transform: scale(1);
	transform: scale(1);
	-webkit-animation-duration: 1s;
	animation-duration: 1s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	-webkit-animation-name: czoomIn;
	animation-name: czoomIn;
	-webkit-animation-delay: 1s;
	animation-delay: 1s;
}

@-webkit-keyframes czoomIn {
	from {
		opacity: 0;
		-webkit-transform: scale3d(0.3, 0.3, 0.3);
		transform: scale3d(0.3, 0.3, 0.3);
	}

	50% {
		opacity: 1;
	}
}

@keyframes czoomIn {
	from {
		opacity: 0;
		-webkit-transform: scale3d(0.3, 0.3, 0.3);
		transform: scale3d(0.3, 0.3, 0.3);
	}

	50% {
		opacity: 1;
	}
}

/* Search */
#wooc-shop-search {
	width: 100%;
	height: 100%;
	overflow: hidden;
	-webkit-transition: opacity 0.2s ease;
	transition: opacity 0.2s ease;
}

#wooc-shop-search.fade-in {
	opacity: 1;
}

#wooc-shop-search-close {
	position: absolute;
	top: 44%;
	right: 23px;
	z-index: 10;
	font-size: 24px;
	line-height: 1;
	color: #aaa;
	width: 23px;
	height: 23px;
	margin-top: -12px;
	background: #fff;
	width: 42px;
	height: 42px;
	text-align: center;
	border-radius: 14px;
	line-height: 1.7;
}

#wooc-shop-search-close:hover {
	color: #282828;
}

#wooc-shop-search .wooc-shop-search-inner {
	position: relative;
}

#wooc-shop-search .wooc-shop-search-input-wrap {
	position: relative;
	overflow: hidden;
	background-color: #f2f7fc;
	padding: 30px;
	border-radius: 14px;
}

#wooc-shop-search-input {
	display: block;
	font-size: 16px;
	line-height: normal;
	font-weight: 600;
	width: 100%;
	padding: 0 23px 0 0;
	border: 0 none;
	background: none;
}

#wooc-shop-search-input:focus {
	border: none;
	background-color: transparent;
}

#wooc-shop-search-form:focus {
	border: none;
	background-color: transparent;
}

#wooc-shop-search input::-ms-clear {
	/* IE: Remove "X" button */
	width: 0;
	height: 0;
}

/* Search: Notice */
#wooc-shop-search-notice {
	line-height: 1;
	color: #aaa;
	height: 0;
	white-space: nowrap;
	text-align: left;
	opacity: 0;
	-webkit-transition: all 0.2s ease;
	transition: all 0.2s ease;
	cursor: default;
}

#wooc-shop-search-notice.show {
	height: 32px;
	opacity: 1;
}

#wooc-shop-search-notice span {
	display: block;
	padding-top: 16px;
}

/* Results bar */
.wooc-shop-results-bar {
	width: auto;
	margin-bottom: 17px;
	padding: 0;
	overflow: hidden;
}

.wooc-shop-header.centered~#wooc-shop-products .wooc-shop-results-bar {
	text-align: center;
}

.wooc-shop-results-bar.has-filters.is-category .wooc-shop-search-taxonomy-reset,
.wooc-shop-results-bar.is-category {
	display: none;
}

.wooc-shop-results-bar.has-filters.is-category {
	display: block;
}

.wooc-shop-results-bar ul {
	margin: 0 -4px;
}

.wooc-shop-results-bar ul li {
	display: inline-block;
	margin: 0 4px 8px;
}

.wooc-shop-results-bar a {
	position: relative;
	display: inline-block;
	color: inherit;
	line-height: 1.4;
	max-width: 100%;
	white-space: nowrap;
	text-overflow: ellipsis;
	vertical-align: top;
	overflow: hidden;
	padding: 5px 14px 5px 37px;
	border: 1px solid;
	border-radius: 30px;
}

.wooc-shop-results-bar a:hover {
	text-decoration: line-through;
}

.wooc-shop-results-bar a:before {
	display: block;
	position: absolute;
	top: 6px;
	left: 12px;
	font-family: 'wooc-font';
	font-size: 15px;
	font-style: normal;
	font-weight: normal;
	line-height: 1;
	color: inherit;
	content: "\e117";
}

.wooc-shop-results-bar a#wooc-shop-search-taxonomy-reset {
	max-width: 350px;
}

.wooc-shop-results-bar a span {
	color: #282828;
}

@media all and (max-width: 568px) {
	.wooc-shop-results-bar.is-category {
		display: block;
	}

	.wooc-shop-results-bar.has-filters.is-category .wooc-shop-search-taxonomy-reset {
		display: inline-block;
	}
}

#wooc-shop-widgets-ul li.widget.woocommerce.widget_layered_nav_filters {
	width: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

#wooc-shop-widgets-ul li.widget.woocommerce.widget_layered_nav_filters .wooc-widget-title {
	margin-bottom: 0;
	margin-right: 20px;
}

.woocommerce .widget_layered_nav_filters ul li a::before {
	color: #5956E9;
}

.header-style-4 .woocommerce-account .woocommerce .woocommerce-MyAccount-navigation {
	margin-bottom: 0;
}

.woocommerce .woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item--chosen a::before {
	color: #5956E9;
}

.woocueproduct-isotope .wooc-shop-view {
	text-align: center;
	margin-top: 60px;
}

/* order review */



.woocue-layout-2 .woocueisotope-tab.woocue-navs {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: end;
	-ms-flex-align: end;
	align-items: flex-end;
}

.woocue-layout-2 .woocueisotope-tab.woocue-navs .wooc-slider-title-block-1 .woocue-sec-title {
	margin-bottom: 20px;
}

.woocue-layout-2 .woocueisotope-tab.woocue-navs .wooc-slider-title-block-1 .woocue-sub-title {
	text-align: left;
}

.woocue-layout-4 .woocueisotope-tab.woocue-navs {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.woocue-layout-4 .woocueisotope-tab.woocue-navs .wooc-slider-title-block-1 .woocue-sec-title {
	margin-bottom: 20px;
}

.woocue-layout-4 .woocueisotope-tab.woocue-navs .wooc-slider-title-block-1 .woocue-sub-title {
	text-align: left;
}


.clear {
	clear: both;
	display: block;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute {
	font-size: 0;
	padding-top: 7px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute:first-child {
	padding-top: 0;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute a {
	display: inline-block;
	color: inherit;
	margin-right: 7px;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	-webkit-tap-highlight-color: transparent;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute a:last-child {
	margin-right: 0;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute a i {
	display: block;
	width: 12px;
	height: 12px;
	border-radius: 4px;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute a i.wooc-pa-color-white {
	border: 1px solid #f8f8fb;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute a .wooc-pa-image-thumbnail-wrap {
	position: relative;
	display: block;
	width: 12px;
	height: 12px;
	overflow: hidden;
	border-radius: 4px;
	border: 1px solid #f8f8fb;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute a img {
	position: absolute;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute.wooc-shop-loop-attribute-label a {
	margin-right: 6px;
}

.wooc-thumb-wrapper .wooc-shop-loop-attribute a span {
	display: block;
	font-size: 14px;
	line-height: 17px;
	width: 18px;
	height: 18px;
	padding-left: 1px;
	border: 1px solid;
	border-radius: 50px;
	text-align: center;
}

.wooc-product-layout .wooc-thumb-wrapper .wooc-shop-loop-attributes {

}

.wooc-product-layout:hover .wooc-thumb-wrapper .wooc-shop-loop-attributes {
	
}

/* Summary: Grouped */
.product.thumbnails-vertical.product-type-grouped .summary {
	padding-right: 0;
	padding-left: 79px;
}

.group_table {
	width: 100%;
	line-height: 1.4;
	margin-bottom: 15px;
}

.woocommerce div.product form.cart table.woocommerce-grouped-product-list tr {
	vertical-align: middle;
	border-bottom: 1px solid #f8f8fb;
	padding: 5px 10px 5px 0;
}

.woocommerce div.product form.cart .group_table tr:first-child {
	border-top: 1px solid #f8f8fb;
}

/* Grouped: Thumbnail */
@media all and (min-width: 1080px) {
	.woocommerce-grouped-product-list-item__thumbnail {
		width: 70px;
	}
}

.woocommerce-grouped-product-list-item__thumbnail img {
	width: 100%;
	max-width: 50px;
	min-width: 20px;
	border-radius: 6px;
}

.woocommerce div.product form.cart table.woocommerce-grouped-product-list td a.add_to_cart_button {
	vertical-align: middle;
	color: #fff;
}

.woocommerce div.product form.cart table.woocommerce-grouped-product-list td a.add_to_cart_button:hover {
	color: #fff;
}

.woocommerce div.product form.cart table.woocommerce-grouped-product-list td a {
	vertical-align: middle;
	color: #222222;
}

.woocommerce div.product form.cart table.woocommerce-grouped-product-list td a:hover {
	color: #5956E9;
}

.container-fluid.no-gutters {
	padding-right: 0;
	padding-left: 0;
}

/*.woocommerce div.product form.cart .reset_variations {
	display: none !important;
}
*/
.woocommerce div.product .single-product-top-3 .woocommerce-variation-price .price {
	margin: 0;
}


.wooc-sticky-right {
	padding-left: 30px;
}

.woocommerce .widget_layered_nav ul li a,
.woocommerce .widget_layered_nav ul li span {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.woocommerce .widget_layered_nav ul li a i,
.woocommerce .widget_layered_nav ul li span i {
	width: 16px;
	height: 16px;
	display: inline-block;
	border-radius: 2px;
	margin-right: 10px;
	    border-radius: 50%;
}

.woocommerce .widget_layered_nav ul li a img,
.woocommerce .widget_layered_nav ul li span img {
	width: 24px;
	border-radius: 100%;
	height: 24px;
	margin-right: 10px;
}

.nice-select .option {
	font-size: 14px;
}



.woocommerce-products-header {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.woocommerce-products-header {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
}

.woocommerce-products-header__title.page-title {
	padding: 10px;
	-webkit-box-flex: 50%;
	-ms-flex: 50%;
	flex: 50%;
	position: relative;
	padding-left: 45px;
}

.woocommerce-products-header__title.page-title:after {
	width: 18px;
	height: 4px;
	content: '';
	position: absolute;
	left: 0px;
	border-radius: 2px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	background-color: #E76458;
}

.woocommerce-products-header__title.page-title:before {
	width: 4px;
	height: 4px;
	content: '';
	position: absolute;
	left: 22px;
	border-radius: 2px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	background-color: #E76458;
}

.woocommerce-breadcrumb {
	padding: 10px;
	-webkit-box-flex: 50%;
	-ms-flex: 50%;
	flex: 50%;
	text-align: right;
}

@media (max-width: 991px) {

	.woocommerce-products-header__title.page-title,
	.woocommerce-breadcrumb {
		-webkit-box-flex: 100%;
		-ms-flex: 100%;
		flex: 100%;
	}
}

.woocommerce-order-received .woocommerce table.shop_table thead tr th {
	color: #222222;
}

.woocommerce-order-received .woocommerce table.shop_table.order_details .product-name a,
.woocommerce-order-received .woocommerce table.shop_table.order_details .download-product a {
	color: #606060;
}

.woocommerce-order-received .woocommerce .woocommerce-customer-details address {
	border-radius: 12px;
	padding: 20px;
	border: 1px solid #f8f8fb;
}

.woocommerce-order-received .woocommerce-order-received .woocommerce .order_details li {
	font-size: 16px;
	font-weight: 400;
}

.woocommerce-order-received .woocommerce ul.order_details li strong {
	display: block;
	font-size: 16px;
	text-transform: none;
	line-height: 2.5;
}

.woocommerce-order-received .woocommerce table.shop_table tbody th,
.woocommerce-order-received .woocommerce table.shop_table tfoot td,
.woocommerce-order-received .woocommerce table.shop_table tfoot th {
	font-weight: 700;
	border-top: 1px solid #f8f8fb;
}

.woocommerce-order-received .woocommerce table.shop_table th {
	font-weight: 700;
	padding: 12px 14px;
	line-height: 1.5em;
}

.sidebar-widget-area .widget.widget_shopping_cart .buttons a {
	color: #ffffff;
	padding: 11px;
	font-size: 14px;
	font-weight: 500;
}

.woocommerce .wooc-shop-sidebar .widget_shopping_cart_content a.remove {
	color: #fff !important;
	background: #5956E9;
	font-size: 16px;
}

.woocommerce .wooc-shop-sidebar .widget_shopping_cart_content a.remove:hover {
	background-color: #222222 !important;
}

.wooc-shop-sidebar .woocommerce.widget_shopping_cart .cart_list li a.remove,
.wooc-shop-sidebar .woocommerce.widget_shopping_cart .cart_list li a.remove {
	top: 18px;
}



.wooc-shop-sidebar .woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-list li {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.wooc-shop-sidebar .select2-container--default.select2-container--focus .select2-selection--multiple {
	border: none;
}

.wooc-shop-sidebar .select2-container--default .select2-selection--multiple {
	padding: 2px 15px 0px 7px;
	color: #646464;
	background-color: #f6f6f6;
	border-radius: 8px;
	border: none;
	height: 42px;
}

.wooc-shop-sidebar .select2-container--default .select2-selection--multiple:placeholder {
	color: #606060;
	opacity: 1;
}

.wooc-shop-sidebar .select2-container--default .select2-selection--single {
	background-color: #fff;
	border: 1px solid #f8f8fb;
	border-radius: 8px;
	padding: 5px 0 8px 0;
	height: 40px;
}

.wooc-shop-sidebar .select2.select2-container.select2-container--default {
	margin-bottom: 10px;
}

.wooc-shop-sidebar .select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 28px;
	position: absolute;
	top: 6px;
	right: 4px;
	width: 20px;
}

.wooc-shop-sidebar .woocommerce .woocommerce-widget-layered-nav-dropdown__submit {
	margin-top: 4px;
	border-radius: 8px !important;
	line-height: 1;
	padding: 10px 20px;
}

.wooc-shop-sidebar .woocommerce .woocommerce-widget-layered-nav-dropdown__submit:hover {
	background-color: #222222;
}

.yith-wcqv-main .wooc-nav {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.yith-wcqv-main .container {
	max-width: 100%;
	padding: 0;
}

.yith-wcqv-main .product_title {
	margin-top: 20px;
}

.woocommerce.woocommerce-wishlist table.shop_table {
	margin-bottom: 0;
}

.sidebar-widget-area .widget.widget_product_categories .select2-container {
	margin-bottom: 10px;
}

.sidebar-widget-area .widget.woocommerce-widget-layered-nav .select2-selection--multiple {
	height: 44px;
	border-radius: 10px;
	border-color: #e9f1f8;
	padding: 4px 0 6px 5px;
	margin-bottom: 6px;
}

.sidebar-widget-area .widget.woocommerce-widget-layered-nav .select2-selection--multiple li {
	margin: 0;
}

.sidebar-widget-area .widget.woocommerce-widget-layered-nav .select2-container--default .select2-selection--single {
	height: 44px;
	border-radius: 10px;
	border-color: #e9f1f8;
	padding-top: 6px;
}

.sidebar-widget-area .widget.woocommerce-widget-layered-nav .select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 26px;
	position: absolute;
	top: 9px;
	right: 7px;
	width: 20px;
}

.sidebar-widget-area .widget.woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-dropdown__submit {
	border-radius: 2px;
	font-weight: 500;
	border-radius: 10px;
	line-height: 1;
}

.woocommerce div.product.has-post-thumbnail .single-product-top-3 .woocue-top-item .woocue-top-left span.onsale {
	left: 162px;
	right: inherit;
}

.woocommerce div.product .single-product-top-3 .single-woocwc-wrapper .product-social {
	padding-left: 20px;
	margin-top: 15px;
}

.woocue-price .mark,
.woocue-price ins {
	background-color: transparent;
}

.woocue-price .added_to_cart.wc-forward {
	position: absolute;
	right: -8px;
	top: 1px;
	font-size: 14px;
}

.header-middlebar.icon-true .btn-account .dropdown-toggle {
	width: 48px;
	height: 44px;
	text-align: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	background-color: #222222;
	color: #fff;
	border: none;
	border-radius: 14px;
}

.header-middlebar.icon-true .btn-account .dropdown-toggle:hover {
	background-color: #5956E9;
}

.header-middlebar.icon-true .btn-account .dropdown-toggle:after {
	content: none;
}

.header-middlebar.icon-true .btn-account .dropdown-toggle i:before {
	margin-right: 0;
	color: #fff;
}

.site .header-icon-area.icon-true .wooc-button-grey-icon {
	background-color: #5956E9;
	border-radius: 14px;
	color: #fff;
}

.site .header-icon-area.icon-true .wooc-button-grey-icon i:before {
	font-size: 16px;
	color: #fff;
}

.wooc-elementor-widget h3 {
	font-size: 22px;
	line-height: 1.3;
	color: #222222;
	padding-bottom: 18px;
	margin-bottom: 30px;
	position: relative;
}

.wooc-elementor-widget h3:after {
	position: absolute;
	content: "";
	height: 2px;
	width: 100%;
	background-color: #e9f1f8;
	left: 0;
	bottom: 0;
}

.wooc-elementor-widget .tagcloud a {
	color: #444444;
	padding: 8px 14px;
	display: inline-block;
	font-size: 13px !important;
	margin-bottom: 6px;
	margin-right: 6px;
	-webkit-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
	border: none;
	border-radius: 10px;
	line-height: 1.5;
	font-weight: 500;
	background: #fff5e8;
}

.wooc-elementor-widget .tagcloud a:hover {
	background-color: #222222;
	border-color: #222222;
	color: #fff;
	text-decoration: none;
}

@media all and (max-width: 479.99px) {
	.wooc-product-slider .woocue-items {
		max-width: 100%;
	}
}

.wooc-product-slider .wooc-product-block {
	margin-bottom: 0;
}

.wooc-product-slider.woocue-style-1 .owl-custom-nav .owl-nav button.owl-prev,
.wooc-product-slider.woocue-style-1 .owl-custom-nav .owl-nav button.owl-next {
	top: 130px;
}

/*body.wooc_product_layout
_1 .pagination-area {
	margin-top: 60px;
}

body.wooc_product_layout
_1 .axiltheme-loadmore-btn-area {
	margin-top: 60px;
}*/

.wooc-slick-slider .slick-prev,
.wooc-slick-slider .slick-next {
	height: 36px;
	width: 36px;
	border-radius: 100%;
	background: #f8f8fb;
	-webkit-box-shadow: none;
	box-shadow: none;
	margin: 0;
	outline: none;
	font-size: 16px;
	line-height: 1;
	font-weight: 700;
	color: #222222;
	min-height: inherit;
	-webkit-transition: all 0.5s ease-out;
	transition: all 0.5s ease-out;
	opacity: 0;
	visibility: hidden;
	z-index: 1;
	border: 1px solid #ffffff;
}

.wooc-slick-slider .slick-prev:hover,
.wooc-slick-slider .slick-next:hover {
	-webkit-box-shadow: none;
	box-shadow: none;
	background: #5956E9;
	color: #ffffff;
	border: 1px solid #5956E9;
}

.wooc-slick-slider .slick-prev:before,
.wooc-slick-slider .slick-next:before {
	content: none;
}

.wooc-slick-slider .slick-prev {
	left: 0;
}

.wooc-slick-slider .slick-next {
	right: 0;
}

.wooc-slick-slider:hover .slick-prev,
.wooc-slick-slider:hover .slick-next {
	opacity: 1;
	visibility: visible;
}

.wooc-slick-slider:hover .slick-prev {
	left: 10px;
}

.wooc-slick-slider:hover .slick-next {
	right: 10px;
}

 

.axil-product.product-style-two .wooc-shop-loop-attribute-color {	
	justify-content: center;
	margin-top: 15px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {	
	display: none !important;
}
.select2-container--default .select2-selection--single {
	
	border: none !important;	
	margin-top: 15px !important;
}


.single-header{
	position: absolute;
	width: 100%;
	z-index: 99;
	padding: 40px 0;
}

.single-header-right ul{
	list-style: none;
	display: flex;
	align-items: center;
	justify-content: flex-end;

}
.single-header-right ul li{
	margin-left: 30px; 

}

.inner-mb-40 .axil-product-layout1{
	margin-bottom: 40px;
}

.product-area {
    border-bottom: none;
}


.axil-signin-form {
  margin-bottom: 30px;
  position: relative; }

  .login-username,
  .login-password{ 
position: relative;
  }


  	.login-username label,
  .login-password label{
    margin-bottom: 6px;
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
    color: var(--color-dark); 
}

   .login-username input,
  .login-password input{

  
    border: 0 none;
    border-radius: 16px;
    height: 50px;
    font-size: 18px;
    padding: 0 20px;
    background-color: #fff;
    border: 1px solid var(--color-body); }
  .login-username input:focus,
  .login-password input:focus {
      border-color: var(--color-primary);
      box-shadow: none; }

  .form-group textarea {
    min-height: 160px;
    border: 0 none;
    border-radius: 16px;
    resize: none;
    padding: 15px;
    font-size: 18px;
    background-color: #fff;
    border: 1px solid var(--color-body); }
    .form-group textarea:focus {
      border-color: var(--color-primary); }

input[type="submit"] {
  width: auto;
  padding: 0 30px;
  border-radius: 500px;
  display: inline-block;
  font-weight: 500;
  transition: 0.3s;
  height: 60px;
  background: var(--color-primary);
  color: var(--color-white);
  font-weight: var(--p-medium);
  font-size: 18px;
  line-height: var(--line-height-b3);
  border: 2px solid var(--color-primary); 
}
  input[type="submit"]:hover {
    background: transparent;
    color: var(--color-primary);
   
}

	.login-username label,
	.login-password label{
	position: absolute;
	top: -11px;
	left: 20px;
	pointer-events: none;
	z-index: 9;
	background: #fff;
	padding: 0 10px; }

	.axil-signin-form {
	width: 100%;
	padding: 20px 0;
	margin: 0 0 0 0;
}

.header-style-6 .mainmenu-nav ul.mainmenu>li.megamenu-wrapper {
    position: relative;
}

.header-style-6 .mainmenu-nav ul.mainmenu>li.megamenu-wrapper .megamenu-sub-menu {
    left: 0;
    transform: none;
    z-index: 99;
}

.axil-header .logo a img.light-logo {
    display: none;
}

.popup-mobilemenu-area .inner .mobile-menu-top .logo a img.light-logo {
    display: none;
}

.mainmenu-nav ul.mainmenu > li.megamenu-wrapper {
  position: static;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu {
  box-sizing: border-box;
  position: absolute;
  background: #fff;
  width: 100%;
  left: 0;
  right: 0;
  opacity: 0;
  visibility: hidden;
  min-width: 1230px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 0 0 10px 10px;
  box-shadow: var(--shadow-primary);
  z-index: 99;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item {
  display: flex;
  margin: 0;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav {
  min-width: 279px;
  width: auto;
  border-right: 1px solid #CED0D4;
  padding: 30px;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav .vertical-nav-item {
  margin: 10px 0;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav .vertical-nav-item a {
  color: var(--color-midgray);
  font-size: 16px;
  line-height: 24px;
  display: block;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav .vertical-nav-item a.hover-flip-item-wrapper span::before {
  color: var(--color-midgray);
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav .vertical-nav-item a.hover-flip-item-wrapper span::after {
  color: var(--color-primary);
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav .vertical-nav-item.active a {
  color: var(--color-primary);
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav .vertical-nav-item.active a.hover-flip-item-wrapper span::before {
  color: var(--color-primary);
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav .vertical-nav-item:first-child {
  margin-top: 0;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav .vertical-nav-item:last-child {
  margin-bottom: 0;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper .megamenu-sub-menu .megamenu-item .axil-vertical-nav-content {
  width: 83.589%;
  padding: 30px;
}
.mainmenu-nav ul.mainmenu > li.megamenu-wrapper:hover .megamenu-sub-menu {
  opacity: 1;
  visibility: visible;
}


/* ------------------------
    Custom Animation 01 
----------------------------*/
@-webkit-keyframes headerSlideDown {
  0% {
    margin-top: -100px;
  }
  to {
    margin-top: 0;
  }
}
@keyframes headerSlideDown {
  0% {
    margin-top: -100px;
  }
  to {
    margin-top: 0;
  }
}
/*------------------------
      slidefadeinup
  --------------------------*/
@-webkit-keyframes slideFadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 20%, 0);
    transform: translate3d(0, 20%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes slideFadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 20%, 0);
    transform: translate3d(0, 20%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.slideFadeInUp {
  -webkit-animation-name: slideFadeInUp;
  animation-name: slideFadeInUp;
}


.axil-header-bottom .header-department .department-megamenu {	
	background: #fff;
}

.cart-dropdown .cart-item .item-img .close-btn {   
    line-height: 2.5;
}


.axil-search .icon {	
	z-index: 9;
}

.axil-product .thumbnail .wooc-slick-slider a img {
	border-radius: 6px;
	width: inherit; 
	max-width: inherit; 
	height: inherit;
}


.axil-header .quick-link li a.nav-link{
	padding: 0;
}

.nav-link {
    display: block;
     padding: 0; 
}

.category-select .widget{
	margin-right: 20px;
}
.category-select{
	margin: 0 !important;
}

.overflow-hidden .elementor-image{
	overflow: hidden !important;
    border-radius: 6px;

}
.axil-product .product-content .product-price-variant div.price {
    margin: 4px;
    color: var(--color-heading);
    font-weight: 500;
    font-size: 20px;
}
.axil-order-summery .summery-table tbody th {
    border-color: rgba(101, 105, 115, 0.2);
    font-size: 18px;
    font-weight: var(--s-medium);
    color: #292930;
    padding: 18px 15px 18px 0;
    min-width: 180px;
}

.axil-order-summery .summery-table tbody tr:last-child th {
    border-bottom: 1px solid rgba(101, 105, 115, 0.2);
}
.axil-product-cart-wrap button{
    width: inherit;
}


.ui-slider {
    position: relative;
    text-align: left;
}

.ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 0.9em;
    height: 0.9em;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.25);
    cursor: pointer;
    background: #e7e7e7;
    background: -webkit-gradient(linear,left top,left bottom,from(#FEFEFE),to(#e7e7e7));
    background: -webkit-linear-gradient(#FEFEFE,#e7e7e7);
    background: -moz-linear-gradient(center top,#FEFEFE 0%,#e7e7e7 100%);
    background: -moz-gradient(center top,#FEFEFE 0%,#e7e7e7 100%);
    outline: none;
    top: -.3em;
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.65) inset;
    -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.65) inset;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.65) inset;
}

.ui-slider .ui-slider-handle:last-child {
    margin-left: -1em;
}

.ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size:.7em;
    display: block;
    border: 0;
    background: none repeat scroll 0 0 #FF6B6B;  
   
    border-radius: 1em;
}

.price_slider_wrapper .ui-widget-content {
    -webkit-border-radius: 1em;
    -moz-border-radius: 1em;
    border-radius: 1em;
    background: #1e1e1e;
    background: -webkit-gradient(linear,left top,left bottom,from(#1e1e1e),to(#6a6a6a));
    background: -webkit-linear-gradient(#1e1e1e,#6a6a6a);
    background: -moz-linear-gradient(center top,#1e1e1e 0%,#6a6a6a 100%);
    background: -moz-gradient(center top,#1e1e1e 0%,#6a6a6a 100%);
}

.ui-slider-horizontal {
    height:.5em;
}

.ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%;
}

.ui-slider-horizontal .ui-slider-range-min {
    left: -1px;
}

.ui-slider-horizontal .ui-slider-range-max {
    right: -1px;
}
.axil-product .product-hover-action .cart-action li.add-to-cart a.added_to_cart.wc-forward {
    background: transparent;
    height: inherit;
    line-height: inherit;
    padding: 8px 0 0 0;   
    border-radius: 500px;
    font-weight: 700;
    font-size: 14px;
    border: 1px solid transparent;
    transition: 0.3s;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-align: center;
    color: var(--color-primary);
}


.axil-shop-sidebar .product-categories ul li.current {
	position: relative;
	padding: 0 0 0  28px !important;
}
.axil-shop-sidebar .product-categories ul li.current::before {
    content: "";
    height: 16px;
    width: 16px;
    line-height: 15px;
    text-align: center;
    border: 1px solid var(--color-body);
    border-radius: 50%;
    position: absolute;
    top: 5px;
    left: 0;
    transition: var(--transition);
        background-color: var(--color-primary);
    border-color: var(--color-primary);
    content: "\f00c";
    font-family: var(--font-awesome);
    font-size: 8px;
    font-weight: 700;
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.axil-shop-sidebar .product-categories ul li.active {
	position: relative;
	padding: 0 0 0  28px !important;
}
.axil-shop-sidebar .product-categories ul li.active::before {
    content: "";
    height: 16px;
    width: 16px;
    line-height: 15px;
    text-align: center;
    border: 1px solid var(--color-body);
    border-radius: 50%;
    position: absolute;
    top: 5px;
    left: 0;
    transition: var(--transition);
        background-color: var(--color-primary);
    border-color: var(--color-primary);

    content: "\f00c";
    font-family: var(--font-awesome);
    font-size: 8px;
    font-weight: 700;
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}


.axil-shop-sidebar .product-categories ul li.current-cat a[aria-label="Remove filter"]::before,
.axil-shop-sidebar .product-categories ul li.chosen a[aria-label="Remove filter"]::before{
    content: "\f00d" !important;
    background: #E76458;
     border-color: #E76458;

}

.axil-shop-top .filter-results{
	margin-bottom: 0 !important;
}


.price_slider_amount .button {
		margin-bottom: 20px;
		margin-top: 20px;

		background: var(--color-primary);
		border-radius: 500px;
		font-size: 18px;
		line-height: var(--line-height-b2);
		font-weight: 500;
		display: inline-block;
		padding: 16px 40px;
		color: #ffffff;
		transition: 0.3s;
		border: 2px solid transparent;
		width: inherit;
}

.ui-slider .ui-slider-range {
  box-shadow: none ;
}


.price_slider_amount .price_label {
    width: 100%;
    word-spacing: 10px;
    padding: 0 !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    line-height: 1 !important;
    margin-top: 0 !important;
        color: var(--color-heading);
}

.axiltheme-loadmore-btn-area .axil-btn.btn-outline.btn-load-more span.axiltheme-loadmore-btn-icon{
	display: none;
}
.axiltheme-loadmore-btn-area .axil-btn.btn-outline.btn-load-more{
	width: inherit;
}

.cart-dropdown .cart-item .item-img .close-btn{
	text-align: center;
}

.shop-right-sidebar .category-select.justify-content-end{
	justify-content: flex-start !important; 
}


.axil-product.product-style-four .wooc-shop-loop-attribute-color {
    justify-content: center;
    align-items: center;
}


@media only screen and (max-width: 575px){
	.axil-footer-widget .social-share {
	   margin-bottom: 30px;
	}

	.axil-product .product-content .color-variant-wrapper {
		margin-top: 10px;
		transition: var(--transition);
		transition-delay: 0.2s;
	}
	.axil-product .product-content .color-variant-wrapper {
		height: inherit;
	}
}

.axil-mainmenu.mainmenu-bottom .header-department .department-nav-menu .department-megamenu-wrap .elementor-widget-container h5{
	font-size: 20px;
    color: var(--color-black);
    margin-bottom: 12px;
}
.axil-mainmenu.mainmenu-bottom .header-department .department-nav-menu .department-megamenu-wrap .elementor-widget-container ul{
	padding-left: 0;
}
.axil-mainmenu.mainmenu-bottom .header-department .department-nav-menu .department-megamenu-wrap .elementor-widget-container ul li a{
     font-size: 14px;
    font-weight: 500;
    color: var(--color-body);
    padding: 4px 0;
    border: none;
}

.slick-slide {
   
    cursor: inherit;
}

.axil-mainmenu.mainmenu-bottom .header-department .department-nav-menu > ul > li .department-megamenu {
   padding-left: 0;
}

.poster-style-two .poster-content.content-top-right{
	left: inherit;
}


.axil-product-lable .label-block.label-right {
	right: 0;
	left: auto;
	position: absolute;
	top: 24px;
	z-index: 2;

}

.axil-product-lable .label-block.label-right {
    background: #E76458;
    border: 2px solid #FFFFFF;
    height: 40px;
    line-height: 40px;
    padding: 8px 16px;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: -0.025em;
    color: #FFFFFF;
    border-radius: 500px;
    font-family: var(--font-secondary);
}

.product_meta{
padding-top: 30px;
}

.color-variant li img{
	width: 20px;
	border-radius: 100%;
}

.single-product-4 .reviews-wrapper .row {  
    display: block;   
    margin-right: 0;
    margin-left: 0;
}

.single-product-4 .reviews-wrapper .row .col-lg-6 {
   
    flex: 100%;
    max-width: 100%;
}


.single-product-3 .reviews-wrapper .row {  
    display: block;   
    margin-right: 0;
    margin-left: 0;
}

.single-product-3 .reviews-wrapper .row .col-lg-6 {
   
    flex: 100%;
    max-width: 100%;
}