<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package etrade
 */
trait BannerTrait {

    public static function axil_shop_banner_layout() {
        $axil_options = Helper::axil_get_options();
        $size = 'full';

        $banner_area = $axil_options['axil_shop_banner_enable'];
        $banner_img = $axil_options['axil_shop_banner_img']['url'];
        $banner_img_id = $axil_options['axil_shop_banner_img']['id'];
        $banner_title = $axil_options['axil_shop_banner_title'];
        $banner_subtitle = $axil_options['axil_shop_bannr_subtitle'];


        if ( !empty( $banner_img_id ) ) {
            $thumbnail_url = wp_get_attachment_image_src( $banner_img_id, $size );
            $banner_img_id = $thumbnail_url[0];

        } else {
            $banner_img = $axil_options['axil_shop_banner_img']['url'];
        }

        /**
         * Load Value
         */

        $banner_layout = array(
            'banner_area'     => $banner_area,
            'banner_img'      => $banner_img,
            'banner_img_id'   => $banner_img_id,
            'banner_title'    => $banner_title,
            'banner_subtitle' => $banner_subtitle,
        );

        return $banner_layout;

    }

    /**
     * @return array
     * Banner Layout
     */
    public static function axil_banner_layout() {
        $axil_options = Helper::axil_get_options();
        $size = 'full';

        /**
         * Get Page Options value
         */
        $banner_area = axil_get_acf_data( 'axil_title_wrapper_show' );

        $banner_img = axil_get_acf_data( 'etrade_select_banner_img' );
        /**
         * Set Condition
         */

        $banner_area = ( !empty( $banner_area ) ) ? $banner_area : $axil_options['axil_banner_enable'];

        if ( isset( $axil_options['axil_select_banner_img']['id'] ) ) {
            $axil_select_banner_img_id = $axil_options['axil_select_banner_img']['id'];
        } else {
            $axil_select_banner_img_id = '';
        }

        $banner_img = ( !empty( $banner_img ) ) ? $banner_img : $axil_select_banner_img_id;
        $banner_img_id = ( !empty( $banner_img ) ) ? $banner_img : $axil_select_banner_img_id;

        if ( !empty( $banner_img ) ) {
            $thumbnail_url = wp_get_attachment_image_src( $banner_img, $size );
            $banner_img = $thumbnail_url[0];

        } else {
            $banner_img = $axil_options['axil_select_banner_img']['url'];
        }
        /**
         * Load Value
         */

        $banner_layout = array(
            'banner_area'   => $banner_area,
            'banner_img'    => $banner_img,
            'banner_img_id' => $banner_img_id,
        );

        return $banner_layout;

    }

    /**
     * @return array
     * Banner Layout
     */
    public static function axil_page_breadcrumb() {
        $axil_options = Helper::axil_get_options();
        /**
         * Get Page Options value
         */
        $breadcrumbs = axil_get_acf_data( 'axil_breadcrumbs_enable' );

        /**
         * Set Condition
         */
        $breadcrumbs = ( !empty( $breadcrumbs ) ) ? $breadcrumbs : $axil_options['axil_breadcrumb_enable'];

        /**
         * Load Value
         */
        $breadcrumbs_enable = array(
            'breadcrumbs' => $breadcrumbs,
        );
        return $breadcrumbs_enable;

    }

}
