<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base; 
use Elementor\Controls_Manager; 
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;
use <PERSON>ementor\Repeater;
if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class axil_best_deals extends Widget_Base {

 public function get_name() {
        return 'axil-best-deals';
    }    
    public function get_title() {
        return esc_html__( 'Best Deals', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-image-box';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }  
    public function axil_get_img($img)
     {
         $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
         return $img;
    } 
 

  public function get_product_name ( $post_type = 'product' ){
        $options = array();
        $options = ['0' => esc_html__( 'None', 'etrade-elements' )];
        $axil_post = array( 'posts_per_page' => -1, 'post_type'=> $post_type );
        $axil_post_terms = get_posts( $axil_post );
        if ( ! empty( $axil_post_terms ) && ! is_wp_error( $axil_post_terms ) ){
            foreach ( $axil_post_terms as $term ) {
                $options[ $term->ID ] = $term->post_title;
            }
            return $options;
        }
    }
    private function axil_get_all_pages()
        {

            $page_list = get_posts(array(
                'post_type' => 'page',
                'orderby' => 'date',
                'order' => 'DESC',
                'posts_per_page' => -1,
            ));

            $pages = array();

            if (!empty($page_list) && !is_wp_error($page_list)) {
                foreach ($page_list as $page) {
                    $pages[$page->ID] = $page->post_title;
                }
            }

            return $pages;
        }


 

    protected function register_controls() {
              
        $this->start_controls_section(
            'best_layout',
            [
                'label' => esc_html__( 'Layout', 'etrade-elements' ),
            ]
        );
         $this->add_control(
	        'style',
	        [
	            'label' => esc_html__( 'Layout', 'etrade-elements' ),
	            'type' => Controls_Manager::SELECT,
	            'default' => '1',
	            'options' => [
	                '1' => esc_html__( 'Style 1', 'etrade-elements' ),
					'2' => esc_html__( 'Style 2', 'etrade-elements' ),			                 
					'3' => esc_html__( 'Style 3', 'etrade-elements' ),			                 
					 		                 
						                 
	            ],
	        ] 
	    );         

       $this->end_controls_section();  

		$this->start_controls_section(
			    'best_layout_by_content',
			    [
			        'label' => esc_html__( 'Info/Image/link', 'etrade-elements' ),
			        'condition'   => array( 'style' =>  array( '1', '2' )),
			    ]
			);

			$this->add_control(
			    'title',
			    [
			        'label' => esc_html__( 'Title', 'etrade-elements' ),
			        'type' => Controls_Manager::TEXTAREA,
			        'default' => esc_html__( 'Decorative Plant For Home', 'etrade-elements' ),
			        'placeholder' => esc_html__( 'Title', 'etrade-elements' ),
			    ]
			);
			$this->add_control(
			    'subtitle',
			    [
			        'label' => esc_html__( 'Sub Title', 'etrade-elements' ),
			        'type' => Controls_Manager::TEXT,
			        'default' => esc_html__( 'Starting From', 'etrade-elements' ),
			        'placeholder' => esc_html__( 'Starting From', 'etrade-elements' ),
			    ]
			);
				
			$this->add_control(
			    'current-price',
			    [
			        'label' => esc_html__( 'Price', 'etrade-elements' ),
			        'type' => Controls_Manager::TEXT,
			        'default' => esc_html__( '$35.00', 'etrade-elements' ),
			         
			    ]
			);
				

			$this->add_control(
			    'image',
			    [
			        'label' => esc_html__('Image','etrade-elements'),
			        'type'=>Controls_Manager::MEDIA,			        
			        'default' => [
			            'url' =>  $this->axil_get_img( 'collection_5.jpg' ),
			        ],

			        'dynamic' => [
			            'active' => true,
			        ],
			            
			    ]
			);
			$this->add_group_control(
			    Group_Control_Image_Size::get_type(),
			    [
			        'name' => 'image_size',
			        'default'  => 'full',
			        'separator' => 'none',	
			        	         
			    ]
			);		


			$this->add_control(
			    'link_text',
			    [
			        'label' => esc_html__('Link Text','etrade-elements'),
			        'type' => Controls_Manager::TEXT,
			        'default' => 'View All Items',
			          'separator'     => 'before', 
			        'title' => esc_html__('Enter button text','etrade-elements'),
			        'condition'   => array( 'style' =>  array( '1' )), 
			    ]
			);

			  $this->add_control(
			    'link_type',
			    [
			        'label' => esc_html__('Link Type','etrade-elements'),
			        'type' => Controls_Manager::SELECT,
			        'options' => [
			            '1' => 'Custom Link',
			            '2' => 'Internal Page',
			        ],
			        'default' => '1',
			    ]
			);
			  $this->add_control(
			    'link',
			    [
			        'label' => esc_html__('Link link','etrade-elements'),
			        'type' => Controls_Manager::URL,
			        'dynamic' => [
			            'active' => true,
			        ],
			        'placeholder' => esc_html__('https://your-link.com','etrade-elements'),
			        'show_external' => true,
			        'default' => [
			            'url' => '#',
			            'is_external' => true,
			            'nofollow' => true,
			        ],
			        'condition' => [
			            'link_type' => '1'
			        ]
			    ]
			);
			  $this->add_control(
			    'page_link',
			    [
			        'label' => esc_html__('Select Link Page','etrade-elements'),
			        'type' => Controls_Manager::SELECT2,
			        'label_block' => true,
			        'options' =>  $this-> axil_get_all_pages(),
			        'condition' => [
			            'link_type' => '2'
			        ]
			    ]
			);


		$this->end_controls_section();





 		$this->start_controls_section(
            'best_bry_content',
            [
                'label' => esc_html__( 'Info/Image/link', 'etrade-elements' ),
                'condition'   => array( 'style' =>  array( '3')),
            ]
        );

		 $repeater = new Repeater();

        $repeater->add_control(
		    'title',
		    [
		        'label' => esc_html__( 'Title', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXTAREA,
		        'default' => esc_html__( 'Decorative Plant For Home', 'etrade-elements' ),
		        'placeholder' => esc_html__( 'Title', 'etrade-elements' ),
		    ]
		);
	      $repeater->add_control(
		    'subtitle',
		    [
		        'label' => esc_html__( 'Sub Title', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXT,
		        'default' => esc_html__( 'Starting From', 'etrade-elements' ),
		        'placeholder' => esc_html__( 'Starting From', 'etrade-elements' ),
		    ]
		);
			
		 $repeater->add_control(
			    'current-price',
			    [
			        'label' => esc_html__( 'Price', 'etrade-elements' ),
			        'type' => Controls_Manager::TEXT,
			        'default' => esc_html__( '$35.00', 'etrade-elements' ),
			         
			    ]
			);
			
 
    	      $repeater->add_control(
		    'image',
		    [
		        'label' => esc_html__('Image','etrade-elements'),
		        'type'=>Controls_Manager::MEDIA,			        
		        'default' => [
		            'url' =>  $this->axil_get_img( 'collection_5.jpg' ),
		        ],

		        'dynamic' => [
		            'active' => true,
		        ],
		            
		    ]
		);
	   $repeater->add_group_control(
            Group_Control_Image_Size::get_type(),
            [
                'name' => 'image_size',
                'default' => 'full',
                'separator' => 'none',
                   
            ]
        );

     $repeater->add_control(
            'link_text',
            [
                'label' => esc_html__('Link Text','etrade-elements'),
                'type' => Controls_Manager::TEXT,
                'default' => 'View All Items',
                  'separator'     => 'before', 
                'title' => esc_html__('Enter button text','etrade-elements'),
                'condition'   => array( 'style' =>  array( '1' )), 
            ]
        );

            $repeater->add_control(
            'link_type',
            [
                'label' => esc_html__('Link Type','etrade-elements'),
                'type' => Controls_Manager::SELECT,
                'options' => [
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ],
                'default' => '1',
            ]
        );
             $repeater->add_control(
            'link',
            [
                'label' => esc_html__('Link link','etrade-elements'),
                'type' => Controls_Manager::URL,
                'dynamic' => [
                    'active' => true,
                ],
                'placeholder' => esc_html__('https://your-link.com','etrade-elements'),
                'show_external' => true,
                'default' => [
                    'url' => '#',
                    'is_external' => true,
                    'nofollow' => true,
                ],
                'condition' => [
                    'link_type' => '1'
                ]
            ]
        );
            $repeater->add_control(
            'page_link',
            [
                'label' => esc_html__('Select Link Page','etrade-elements'),
                'type' => Controls_Manager::SELECT2,
                'label_block' => true,
                'options' =>  $this-> axil_get_all_pages(),
                'condition' => [
                    'link_type' => '2'
                ]
            ]
        );

        $this->add_control(
            'deals_list',
            [
                'label' => esc_html__( 'Deals List', 'etrade-elements' ),
                'type' => Controls_Manager::REPEATER,
                'fields' => $repeater->get_controls(),
                
                'default' => [
                    [
                        'title' => esc_html__( 'Ladies Short Sleeve Dress', 'etrade-elements' ),
                        'subtitle' => esc_html__( 'Starting From', 'etrade-elements' ),
                        'current-price' => esc_html__( '$30.00', 'etrade-elements' ),
                        'image' => [
                             'url' => $this->axil_get_img( 'collection_5.png' ),
                            ],
                        
                    ], 
                    [
                        'title' => esc_html__( 'Oil Soap Wood Home Cleaner', 'etrade-elements' ),
                        'subtitle' => esc_html__( 'Starting From', 'etrade-elements' ),
                        'current-price' => esc_html__( '$11.70', 'etrade-elements' ),
                        'image' => [
                             'url' => $this->axil_get_img( 'collection_6.png' ),
                            ],
                        
                    ],
                    [
                        'title' => esc_html__( 'Large Pendant Light Ceiling', 'etrade-elements' ),
                        'subtitle' => esc_html__( 'Starting From', 'etrade-elements' ),
                        'current-price' => esc_html__( '$15.22', 'etrade-elements' ),
                        'image' => [
                             'url' => $this->axil_get_img( 'collection_7.png' ),
                            ],
                        
                    ],
                    [
                        'title' => esc_html__( 'Iphone New Model', 'etrade-elements' ),
                        'subtitle' => esc_html__( 'Starting From', 'etrade-elements' ),
                        'current-price' => esc_html__( '$499.22', 'etrade-elements' ),
                        'image' => [
                             'url' => $this->axil_get_img( 'collection_8.png' ),
                            ],
                        
                    ],
                     
                ],
                'title_field' => '{{{ title }}}',
            ]
        ); 

		$this->end_controls_section();

        $this->start_controls_section(
            'sec_style_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,   
            ]
        );

		$this->add_control(
            'title_color',
            [
                'label' => esc_html__( 'Title Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,    
				'selectors' => array( 
					'{{WRAPPER}} .title' => 'color: {{VALUE}} !important',
					
				),                                  
                
            ]
        );

		$this->add_control(
            'subtitle_color',
            [
                'label' => esc_html__( 'Subtitle Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,   
				'selectors' => array( 
					'{{WRAPPER}} .price-warp .price-text' => 'color: {{VALUE}}'					
					),                                
             
            ]
        );		
		
       $this->end_controls_section();   

        $this->start_controls_section(
            'sec_typography_type',
            [
                'label' => esc_html__( 'Typography', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,   
            ]
        );


		$this->add_group_control(
		Group_Control_Typography::get_type(),
			[
			    'name' => 'title_typo',
			    'label' => esc_html__( 'Title', 'etrade-elements' ),  
			     'devices' => [ 'desktop', 'tablet', 'mobile' ],	
			   'selector' => '{{WRAPPER}} .title',
			]
		);

		$this->add_responsive_control(
			'title_typo_margin',
				[
				    'label' => esc_html__( 'Title Margin', 'etrade-elements' ),
				    'type' => Controls_Manager::DIMENSIONS,
				    'size_units' => [ 'px', '%', 'em' ],
				    'devices' => [ 'desktop', 'tablet', 'mobile' ],					    
				    'selectors' => [
				        '{{WRAPPER}} .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				        
				    ],
				]
			);


		$this->add_group_control(
		Group_Control_Typography::get_type(),
			[
			    'name' => 'subtitle_typo',
			    'label' => esc_html__( 'Subtitle Typography', 'etrade-elements' ),  
			     'devices' => [ 'desktop', 'tablet', 'mobile' ],	
			   'label'    => esc_html__( 'Subtitle', 'etrade-elements' ),
				'selector' => '{{WRAPPER}} .info-subtitle',
			]
		);

		$this->add_responsive_control(
			'subtitle_typo_margin',
				[
				    'label' => esc_html__( 'Subtitle Margin', 'etrade-elements' ),
				    'type' => Controls_Manager::DIMENSIONS,
				    'size_units' => [ 'px', '%', 'em' ],
				    'devices' => [ 'desktop', 'tablet', 'mobile' ],					    
				    'selectors' => [
				        '{{WRAPPER}} .single-poster .inner  .info-subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				        '{{WRAPPER}} .single-poster .inner .sub-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				        
				    ],
				]
			);

		 $this->end_controls_section();
 
         

  		$this->start_controls_section(
            'plus_icon_style_section',
            [
                'label' => esc_html__( 'Plus Icon', 'etrade-elements' ),
                  'condition'   => array( 'style' =>  array( '1' )), 
                                
            ]
        	);  
	         $this->add_control(
			    'plus_icon_display',
			    [
			         'type' => Controls_Manager::SWITCHER,
					'label'       => esc_html__( 'Plus Icon Display', 'etrade-elements' ),
					'label_on'    => esc_html__( 'On', 'etrade-elements' ),
					'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
					'default'     => 'yes',
					 'separator'     => 'before',
		        
			    ] 
			);   

   			$this->add_control(
			    'popup_icon_active',
			    [
			         'type' => Controls_Manager::SWITCHER,
					'label'       => esc_html__( 'Active Display', 'etrade-elements' ),
					'label_on'    => esc_html__( 'On', 'etrade-elements' ),
					'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
					'default'     => 'yes',
					 'separator'  => 'before',
					 'condition'   => array( 'plus_icon_display' => 'yes' ),
		        
			    ] 
			);   

	          $this->add_control(   
                'product_ids',
                [
                    'label'         => __( 'Select Product will display', 'etrade-elements' ),
                    'type'          => Controls_Manager::SELECT,
                    'options'       => $this->get_product_name(),                  
                    'label_block'   => true,
                    'multiple'      => true,
                    'separator'     => 'before',
                    'condition'   => array( 'plus_icon_display' => 'yes' ),
                    
                ] 
            );
    
            $this->add_control(
                'rating_display',
                [
                    
                    'type'          => Controls_Manager::SWITCHER,
                    'label'         => esc_html__( 'Rating Display', 'etrade-elements' ),
                    'label_on'      => esc_html__( 'On', 'etrade-elements' ),
                    'label_off'     => esc_html__( 'Off', 'etrade-elements' ),
                    'default'       => 'yes',
                    'condition'   => array( 'plus_icon_display' => 'yes' ),
                    
                    
                ] 
            );  
            $this->add_control(
			'sale_price_only',
			[

				'type' => Controls_Manager::SWITCHER,
				'label'       => __('Display only sale price', 'etrade-elements'),
				'label_on'    => __('On', 'etrade-elements'),
				'label_off'   => __('Off', 'etrade-elements'),
				'default'     => 'yes',

			]
		);
			$this->add_control(
				'pos_x_type',
				[
					'type' => Controls_Manager::CHOOSE,
					'label' => esc_html__( 'Horizontal Position', 'etrade-elements' ),
					'condition'   => array( 'plus_icon_display' => 'yes' ),
					'options' => [
						'left' => [
							'title' => esc_html__( 'Left', 'etrade-elements' ),
							'icon' => 'eicon-h-align-left',
						], 
						'right' => [
							'title' => esc_html__( 'Right', 'etrade-elements' ),
							'icon' => 'eicon-h-align-right',
						],
					],
					'default' => 'left',
				]
			);

			$this->add_responsive_control(
				'pos_x',
				[
					'type' => Controls_Manager::SLIDER,
					'label' => esc_html__( 'Spacing', 'etrade-elements' ),
					'condition'   => array( 'plus_icon_display' => 'yes' ),
					'size_units' => array( 'px', '%' ),
					'range' => array(
						'px' => array(
							'min' => -500,
							'max' => 500,
						),
						'%' => array(
							'min' => -100,
							'max' => 100,
						),
					),
					'default' => array(
						'unit' => '%',
						'size' => 40,
					),

					'selectors' => [
						'{{WRAPPER}} .product-collection.product-collection-two .collection-content .plus-btn.axil-pos-left'  => 'left: {{SIZE}}{{UNIT}};',
						'{{WRAPPER}} .product-collection.product-collection-two .collection-content .plus-btn.axil-pos-right' => 'right: {{SIZE}}{{UNIT}};',
					],
				]
			); 

			$this->add_control(
				'pos_y_type',
				[
					'type' => Controls_Manager::CHOOSE,
					'label' => esc_html__( 'Vertical Position', 'etrade-elements' ),
					'condition'   => array( 'plus_icon_display' => 'yes' ),
					'options' => [
						'top' => [
							'title' => esc_html__( 'Left', 'etrade-elements' ),
							'icon' => 'eicon-v-align-top',
						], 
						'bottom' => [
							'title' => esc_html__( 'Right', 'etrade-elements' ),
							'icon' => 'eicon-v-align-bottom',
						],
					],
					'default' => 'top',
				]
			);

			$this->add_responsive_control(
				'pos_y',
				[
					'type' => Controls_Manager::SLIDER,
					'label' => esc_html__( 'Spacing', 'etrade-elements' ),
					'size_units' => array( 'px', '%' ),
					'condition'   => array( 'plus_icon_display' => 'yes' ),
					'range' => array(
						'px' => array(
							'min' => -500,
							'max' => 500,
						),
						'%' => array(
							'min' => -100,
							'max' => 100,
						),
					),
					'default' => array(
						'unit' => '%',
						'size' => 30,
					),
					'selectors' => [
						'{{WRAPPER}} .product-collection.product-collection-two .collection-content .plus-btn.axil-pos-top'    => 'top: {{SIZE}}{{UNIT}};',
						'{{WRAPPER}} .product-collection.product-collection-two .collection-content .plus-btn.axil-pos-bottom' => 'bottom: {{SIZE}}{{UNIT}};',
					],
				]
			);  
 		$this->end_controls_section();


    }

	protected function render() {
		$settings = $this->get_settings();  
		$template   = 'best-deals-' . str_replace("style", "", $settings['style']);                 
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
  
	
	}
}

