<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package etrade
 */
?>

<?php
$axil_options = Helper::axil_get_options();
// Footer Top
$footer_top_layout = Helper::axil_footer_top_layout();
$footer_top_area = $footer_top_layout['footer_top_area'];

if ( "no" !== $footer_top_area && "0" !== $footer_top_area && "" !== $footer_top_area && !is_404() ) {
    get_template_part( 'template-parts/footer/footer-top', 1 );
}
/**
 * Get Page Options value
 */
$service_policy_area = axil_get_acf_data( 'axil_show_service_policy' );
/**
 * Set Condition
 */

$service_policy_area = ( empty( $service_policy_area ) ) ? $axil_options['axil_service_policy_section_enable'] : $service_policy_area;
if ( "no" !== $service_policy_area && "0" !== $service_policy_area && !is_404() ) {
    get_template_part( 'template-parts/footer/service-policy' );
}
?>

            <div class="hover_overlay_content"></div>
        </div>
    </div>
</div>

<?php
// Footer
$footer_layout = Helper::axil_footer_layout();
$footer_area = $footer_layout['footer_area'];
$footer_style = $footer_layout['footer_style'];

if ( $footer_area ) {
    get_template_part( 'template-parts/footer/footer', $footer_style );
}
if ( $axil_options['axil_scroll_to_top_enable'] ) {?>
   
      <a href="#top" class="back-to-top" id="backto-top"><i class="fal fa-arrow-up"></i></a>
    
<?php }
wp_footer();
?>
</body>
</html>
