<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base; 
use Elementor\Repeater;
use Elementor\Controls_Manager; 
use Elementor\Group_Control_Image_Size;
use <PERSON>ementor\Utils;
 

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Wooc_Product_Grid extends Widget_Base {

 public function get_name() {
        return 'wooc-product-grid';
    }    
    public function get_title() {
        return esc_html__( 'Product Grid', 'etrade-elements' );
    }
    public function get_icon() {
        return 'eicon-products';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }

	private function wooc_cat_dropdown_1() {
		$terms = get_terms( array( 'taxonomy' => 'product_cat' ) );
		$category_dropdown = array( '0' => esc_html__( 'All Categories', 'etrade-elements' ) );

		foreach ( $terms as $category ) {
			$category_dropdown[$category->term_id] = $category->name;
		}

		return $category_dropdown;
	}

	public function get_product_name ( $post_type = 'product' ){
        $options = array();
        $options = ['0' => esc_html__( 'None', 'etrade-elements' )];
        $axil_post = array( 'posts_per_page' => -1, 'post_type'=> $post_type );
        $axil_post_terms = get_posts( $axil_post );
        if ( ! empty( $axil_post_terms ) && ! is_wp_error( $axil_post_terms ) ){
            foreach ( $axil_post_terms as $term ) {
                $options[ $term->ID ] = $term->post_title;
            }
            return $options;
        }
    }

	private function wooc_cat_dropdown_2() {
		$terms = get_terms( array( 'taxonomy' => 'product_cat', 'parent' => 0, 'hide_empty' => false ) );
		$category_dropdown = array();
		foreach ( $terms as $category ) {
			$category_dropdown[$category->term_id] = $category->name;
		}

		return $category_dropdown;
	}

 	private function axil_get_all_pages()
	    {

	        $page_list = get_posts(array(
	            'post_type' => 'page',
	            'orderby' => 'date',
	            'order' => 'DESC',
	            'posts_per_page' => -1,
	        ));

	        $pages = array();

	        if (!empty($page_list) && !is_wp_error($page_list)) {
	            foreach ($page_list as $page) {
	                $pages[$page->ID] = $page->post_title;
	            }
	        }

	        return $pages;
	    }


	private function wooc_build_query( $settings ) {

		if ( !$settings['custom_id'] ) {

			// Post type
			$number = $settings['number'];

		$p_ids = array(); 
			if ( !empty($settings['posts_not_in'])){
			    foreach ( $settings['posts_not_in'] as $p_idsn ) {
			        $p_ids[] = $p_idsn;
			    }
			} 


			$args = array(
				'post_type'      => 'product',
				'posts_per_page' => $number ? $number : 3,
				'ignore_sticky_posts' => true,
				'post_status'         => 'publish',
				'suppress_filters'    => false, 
				'post__not_in'   	  => $p_ids
			);

			$args['tax_query'] = array();

			// Category
			if ( !empty( $settings['cat'] ) ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_cat',
					'field'    => 'term_id',
					'terms'    => $settings['cat'],
				);
			}

			// Featured only
			if ( $settings['featured_only'] ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_visibility',
					'field'    => 'slug',
					'terms'    => 'featured',
				);
			}

			// Out-of-stock hide
			if ( $settings['out_stock_hide'] ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_visibility',
					'field'    => 'slug',
					'terms'    => 'outofstock',
					'operator' => 'NOT IN',
				);
			}

			// Order
			$args['orderby'] = $settings['orderby'];
			switch ( $settings['orderby'] ) {

				case 'title':
				case 'menu_order':
				$args['order']    = 'ASC';
				break;

				case 'bestseller':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = 'total_sales';
				break;

				case 'rating':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_wc_average_rating';
				break;

				case 'price_l':
				$args['orderby']  = 'meta_value_num';
				$args['order']    = 'ASC';
				$args['meta_key'] = '_price';
				break;

				case 'price_h':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_price';
				break;
			}
		}

		else {

			$posts = $settings['product_ids'];
			$args = array(
				'post_type'      => 'product',
				'ignore_sticky_posts' => true,
				'nopaging'       => true,
				'post__in'       => $posts,
				'orderby'        => 'post__in',
			);
		}

		return new \WP_Query( $args );
	}


    protected function register_controls() {
        
        $args = array(
			'post_type'           => 'product',
			'posts_per_page'      => -1,
			'post_status'         => 'publish',
			'suppress_filters'    => false,
			'ignore_sticky_posts' => true,
		);
		$products = get_posts( $args );
		$products_dropdown = array();

		foreach ( $products as $product ) {
			$products_dropdown[$product->ID] = $product->post_title;
		}

 		$this->start_controls_section(
            'sec_general',
            [
                'label' => esc_html__( 'General options', 'etrade-elements' ),                
            ]
        );
		$this->add_control(
		    'style',
		    [
		        'label' => esc_html__( 'Product Loop Style', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT,
		        'default' => '1',
		        'options' => [
		            '1'   => esc_html__( 'Style One', 'etrade-elements' ),
		            '2'   => esc_html__( 'Style Two', 'etrade-elements' ),
		            '3'   => esc_html__( 'Style Three', 'etrade-elements' ),                           
		            '4'   => esc_html__( 'Style four', 'etrade-elements' ),                           
		            '5'   => esc_html__( 'Style Five', 'etrade-elements' ),                           
		            '6'   => esc_html__( 'Style Six', 'etrade-elements' ),                           
		            '7'   => esc_html__( 'Style Seven', 'etrade-elements' ),                           
		            '8'   => esc_html__( 'Style Eight', 'etrade-elements' ),                           
		           
		        ],
		    ] 
		);

		$this->add_control(
		    'botton_border',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Bottom Border', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		);   


		$this->add_responsive_control(
			'bottom_space',
			[
				'type' => Controls_Manager::SLIDER,
				'label' => esc_html__( 'Bottom Spacing', 'etrade-elements' ),
				'size_units' => array( 'px', '%' ),
				'condition'   => array( 'botton_border' => 'yes' ),
				'range' => array(
					'px' => array(
						'min' => 0,
						'max' => 120,
					),
					 
				),
				'default' => array(
					'unit' => 'px',
					'size' => 40,
				),
				'selectors' => [
					'{{WRAPPER}} .product-area.border-on'    => 'padding-bottom: {{SIZE}}{{UNIT}};',
					
				],
			]
		); 
		$this->add_control(
		    'custom_id',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Custom Product ID', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		);   

 		$this->add_control(      
            'cat',
                [
                'label' => __( 'Categories', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT2,
               	'options'     => $this->wooc_cat_dropdown_1(),            
                'label_block'   => true,                
                'default'     => '0',
                'separator'     => 'before',
                 'multiple'  => true,
                 'condition'   => array( 'custom_id' => '' ),
                ] 
            );
		$this->add_control(      
		    'posts_not_in',
		        [
		        'label' => __( 'Select The Posts that will not display', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT2,
		        'options'       => $this->get_product_name(),                  
		        'label_block'   => true,
		        'multiple'      => true,
		        'separator'     => 'before',
		        'condition'   => array( 'custom_id' => ''  ),
		        ] 
		    );
		$this->add_control(      
		    'product_ids',
		        [
		        'label' => __( 'Product Seperated by commas', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT2,
		        'options'       => $this->get_product_name(),                  
		        'label_block'   => true,
		        'multiple'      => true,
		        'separator'     => 'before',
		        'condition'   => array( 'custom_id' => 'yes' ),
		        ] 
		    );
		$this->add_control(
		    'product_display_hover',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Display Hover Image', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		); 		
		$this->add_control(
		    'display_title_badge_check',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Title Badge Check', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		); 		
         	$this->add_control(
		    'rating_display',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Rating Display', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',
		        
		        
		    ] 
		); 	
 	
	 
 		$this->end_controls_section();			
        $this->start_controls_section(
            'sec_filter',
            [
                'label' => esc_html__( 'Product Filtering', 'etrade-elements' ),
                
            ]
        );       

	 	$this->add_control(
	        'number',
	            [
	                'label'   => esc_html__( 'Number of items', 'etrade-elements' ),
	                'type'    => Controls_Manager::NUMBER,
	                'default'     => 8,    
	                'condition'   => array( 'custom_id' => '' ),           
	                
	            ]

	        );
	 		
		$this->add_control(
		    'sale_price_only',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Display only sale price', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',		      
		        
		    ] 
		);	
		$this->add_control(
		    'display_attributes',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Display Color Attributes', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',		      
		        
		    ] 
		);   	
		$this->add_control(      
            'orderby',
                [
                'label' => esc_html__( 'Orderby', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT2,
	               'options'     => array(
						'date'        => esc_html__( 'Date (Recents comes first)', 'etrade-elements' ),
						'title'       => esc_html__( 'Title', 'etrade-elements' ),
						'bestseller'  => esc_html__( 'Bestseller', 'etrade-elements' ),
						'rating'      => esc_html__( 'Rating(High-Low)', 'etrade-elements' ),
						'price_l'     => esc_html__( 'Price(Low-High)', 'etrade-elements' ),
						'price_h'     => esc_html__( 'Price(High-Low)', 'etrade-elements' ),
						'rand'        => esc_html__( 'Random(Changes on every page load)', 'etrade-elements' ),
						'menu_order'  => esc_html__( 'Custom Order (Available via Order field inside Page Attributes box)', 'etrade-elements' ),
					),    
                'label_block'   => true,    
                'condition'   => array( 'custom_id' => '' ),            
                'default'     => 'date',
                'separator'     => 'before',
                ] 
            );

		$this->add_control(
		    'out_stock_hide',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Hide Out-of-stock Products', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        'condition'   => array( 'custom_id' => '' ),
		        
		    ] 
		); 		

		$this->add_control(
		    'featured_only',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Display only Featured Products', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        'condition'   => array( 'custom_id' => '' ),
		        
		    ] 
		); 		
				



       $this->end_controls_section();   


		
		$this->start_controls_section(
		'sec_linking',
		    [
		        'label' => __( 'Link and Button', 'etrade-elements' ),  
		         	
		                  
		    ]
		);    

		$this->add_control(
		    'islink',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Show Button', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'no',
		        
		    ] 
		); 		              


		$this->add_control(
		    'btntext',
		    [
		        'label'   => __( 'Button Text', 'etrade-elements' ),
		        'type'    => Controls_Manager::TEXT,
		        'default' => 'View All Products',
		        'condition'   => array( 'islink' => 'yes' ),
		    ]
		);


		 $this->add_control(
            'axil_link_type',
            [
                'label' => esc_html__( 'See All Link Type', 'etrade-elements'),
                'type' => Controls_Manager::SELECT,
                'options' => [
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ],
                'default' => '1',
                'condition'   => array( 'islink' => 'yes' ),
                'label_block' => true
            ]
        );


     $this->add_control(
            'axil_page_link',
            [
                'label' => esc_html__('Select See All Page', 'etrade-elements'),
                'type' => Controls_Manager::SELECT2,
                'label_block' => true,
                'options' => $this-> axil_get_all_pages(),
                'condition' => [
                    'axil_link_type' => '2',
                    'islink' => 'yes',
                ]
            ]
        );

		$this->add_control(
		    'url',
		    [
		        'label'   => __( 'Detail URL', 'etrade-elements' ),
		        'type'    => Controls_Manager::URL,
		        'placeholder' => 'https://your-link.com',	
		        'condition' => [
                    'axil_link_type' => '1',
                    'islink' => 'yes',
                ]	        
		    ]
		);   
 
		$this->end_controls_section();
  
      $this->start_controls_section(
            'etrade_responsive',
                [
                'label' => __( 'Responsive Columns', 'etrade-elements' ),
                ]
            );

            $this->add_control(
                'col_xl',
                [
                    'label' => __( 'Desktops: > 1199px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '3',
                ] 
            );
            $this->add_control(
            'col_lg',
                [
                    'label' => __( 'Desktops: > 991px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                            '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '4',
                ] 
            );
            $this->add_control(
            'col_md',
                [
                    'label' => __( 'Tablets: > 767px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '2',
                ] 
            );

            $this->add_control(
            'col_sm',
                [
                    'label' => __( 'Phones: >575px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '2',
                ] 
            );         
            $this->add_control(
            'col_mobile',
                [
                    'label' => __( 'Small Phones: <576px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '12',
                ] 
            );
       $this->end_controls_section();

    }
    
	protected function render() {
		$settings = $this->get_settings();				
		$template = 'shop-grid';	
		$settings['query'] = $this->wooc_build_query( $settings );	
		return wooc_Elements_Helper::wooc_element_template( $template, $settings );
	}
}