.fw-option-type-icon .option-type-icon-list {
	margin-top: 1em;
	padding-left: 1px;
	padding-bottom: 1px;
	overflow: auto;
	max-height: 10.1em;
}

.fw-option-type-icon .option-type-icon-list:first-child {
	margin-top: 0;
}

.fw-option-type-icon .option-type-icon-list:after {
	content: '';
	display: block;
	clear: both;
}

.fw-option-type-icon .option-type-icon-list i {
	float: left;
	border: 1px solid #e1e1e1;
	margin: 0 0 -1px -1px;
	padding: 0.5em 0;
	background-color: #ffffff;
	text-align: center;
	min-width: 2em;
}

body.rtl .fw-option-type-icon .option-type-icon-list i {
	float: right;
}

.fw-option-type-icon .option-type-icon-list i:before {
	display: inline-block;
}

.fw-option-type-icon .option-type-icon-list i:hover {
	background: #0074a2;
	color: #fff;
	cursor: pointer;
}
.fw-option-type-icon .option-type-icon-list i.active:hover,
.fw-option-type-icon .option-type-icon-list i.active{
	background: #64bd1f;
	color: #fff;
}

.fw-option-type-icon .fw-backend-option {
	border-bottom: none;
	padding-bottom: 0;
}

.fw-option-type-icon .fw-options-tabs-wrapper .fw-options-tabs-contents {
	margin-top: 0 !important;
}


/* Let's get this party started */

.fw-option-type-icon .option-type-icon-list::-webkit-scrollbar {
	width: 5px;
}


/* Handle */

.fw-option-type-icon .option-type-icon-list::-webkit-scrollbar-thumb {
	-webkit-border-radius: 2px;
	border-radius: 2px;
	background: rgba(92,92,92,0.8);
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5);
}

.fw-option-type-icon .option-type-icon-list::-webkit-scrollbar-thumb:window-inactive {
	background: rgba(92,92,92,0.4);
}
