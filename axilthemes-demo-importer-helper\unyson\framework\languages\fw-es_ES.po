# 
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Unyson\n"
"POT-Creation-Date: 2016-02-02 15:48+0300\n"
"PO-Revision-Date: 2016-03-26 22:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Spain) (http://www.transifex.com/themefuse/unyson/language/es_ES/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_ES\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.5.4\n"
"X-Poedit-Basepath: .\n"
"X-Poedit-KeywordsList: _:1;gettext:1;dgettext:2;ngettext:1,2;dngettext:2,3;__:1;_e:1;_c:1;_n:1,2;_n_noop:1,2;_nc:1,2;__ngettext:1,2;__ngettext_noop:1,2;_x:1,2c;_ex:1,2c;_nx:1,2,4c;_nx_noop:1,2,3c;_n_js:1,2;_nx_js:1,2,3c;esc_attr__:1;esc_html__:1;esc_attr_e:1;esc_html_e:1;esc_attr_x:1,2c;esc_html_x:1,2c;comments_number_link:2,3;t:1;st:1;trans:1;transChoice:1,2\n"
"X-Poedit-SearchPath-0: ..\n"
"X-Poedit-SourceCharset: UTF-8\n"

#: ../framework/manifest.php:5
msgid "Unyson"
msgstr "Unyson"

#: ../framework/helpers/class-fw-wp-filesystem.php:43
msgid "Cannot connect to Filesystem directly"
msgstr "No se puede conectar al sistema de archivos directamente"

#: ../framework/helpers/class-fw-wp-filesystem.php:271
#, php-format
msgid "Cannot create directory \"%s\". It must be inside \"%s\""
msgstr "No se puede crear el directorio \"%s\". Debe estar dentro de \"%s\""

#: ../framework/helpers/class-fw-wp-filesystem.php:273
msgid "\" or \""
msgstr "\" o \""

#: ../framework/helpers/class-fw-flash-messages.php:95
#, php-format
msgid "Invalid flash message type: %s"
msgstr "Tipo de mensaje flash no válido: %s"

#: ../framework/helpers/class-fw-wp-list-table.php:185
msgid "No items found."
msgstr "No se encuentran items"

#: ../framework/helpers/class-fw-wp-list-table.php:309
msgid "Bulk Actions"
msgstr "Acciones en lote"

#: ../framework/helpers/class-fw-wp-list-table.php:319
msgid "Apply"
msgstr "Aplicar"

#: ../framework/helpers/class-fw-wp-list-table.php:403
msgid "All dates"
msgstr "Todas las fechas"

#: ../framework/helpers/class-fw-wp-list-table.php:416
#, php-format
msgid "%1$s %2$d"
msgstr "%1$s %2$d"

#: ../framework/helpers/class-fw-wp-list-table.php:432
msgid "List View"
msgstr "Ver como lista"

#: ../framework/helpers/class-fw-wp-list-table.php:433
msgid "Excerpt View"
msgstr "Ver como extracto"

#: ../framework/helpers/class-fw-wp-list-table.php:459
#, php-format
msgid "%s pending"
msgstr "pendiente %s"

#: ../framework/helpers/class-fw-wp-list-table.php:714
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:33
msgid "Select All"
msgstr "Seleccionar todo"

#: ../framework/helpers/general.php:1150
msgid "year"
msgstr "año"

#: ../framework/helpers/general.php:1151
msgid "years"
msgstr "años"

#: ../framework/helpers/general.php:1153
msgid "month"
msgstr "mes"

#: ../framework/helpers/general.php:1154
msgid "months"
msgstr "meses"

#: ../framework/helpers/general.php:1156
msgid "week"
msgstr "semana"

#: ../framework/helpers/general.php:1157
msgid "weeks"
msgstr "semanas"

#: ../framework/helpers/general.php:1159
msgid "day"
msgstr "día"

#: ../framework/helpers/general.php:1160
msgid "days"
msgstr "días"

#: ../framework/helpers/general.php:1162
msgid "hour"
msgstr "hora"

#: ../framework/helpers/general.php:1163
msgid "hours"
msgstr "horas"

#: ../framework/helpers/general.php:1165
msgid "minute"
msgstr "minuto"

#: ../framework/helpers/general.php:1166
msgid "minutes"
msgstr "minutos"

#: ../framework/helpers/general.php:1168
msgid "second"
msgstr "segundo"

#: ../framework/helpers/general.php:1169
msgid "seconds"
msgstr "segundos"

#: ../framework/helpers/general.php:1558
msgid "Maximum stack depth exceeded"
msgstr "Profundidad máxima de grupo excedida"

#: ../framework/helpers/general.php:1561
msgid "Underflow or the modes mismatch"
msgstr "Flujo inferior o modos no coinciden"

#: ../framework/helpers/general.php:1564
msgid "Unexpected control character found"
msgstr "Control de carácter inesperado encontrado"

#: ../framework/helpers/general.php:1567
msgid "Syntax error, malformed JSON"
msgstr "Error de sintaxis, JSON malformado"

#: ../framework/helpers/general.php:1570
msgid "Malformed UTF-8 characters, possibly incorrectly encoded"
msgstr "Caracteres UTF-8 malformados, posiblemente codificación incorrecta"

#: ../framework/helpers/general.php:1573
#: ../framework/extensions/backups/class-fw-extension-backups.php:550
msgid "Unknown error"
msgstr "Error desconocido"

#: ../framework/helpers/class-fw-form.php:80
#, php-format
msgid "Form with id \"%s\" was already defined"
msgstr "Formulario id \"%s\"ya se encuentra definido"

#: ../framework/helpers/class-fw-form.php:168
msgid "Nonce verification failed"
msgstr "Mientras la verificación ha fallado"

#: ../framework/helpers/class-fw-form.php:331
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/views/form.php:13
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:66
msgid "Submit"
msgstr "Enviar"

#: ../framework/extensions/update/class-fw-extension-update.php:285
#: ../framework/extensions/update/class-fw-extension-update.php:602
msgid "Cannot delete: "
msgstr "No se puede eliminar: "

#: ../framework/extensions/update/class-fw-extension-update.php:292
msgid "Cannot create: "
msgstr "No se puede crear"

#: ../framework/extensions/update/class-fw-extension-update.php:370
msgid "Cannot remove old temporary directory: "
msgstr "No se puede remover el directorio temporal"

#: ../framework/extensions/update/class-fw-extension-update.php:376
#: ../framework/extensions/update/class-fw-extension-update.php:572
msgid "Cannot create directory: "
msgstr "No se puede crear el directorio: "

#: ../framework/extensions/update/class-fw-extension-update.php:381
#, php-format
msgid "Downloading the %s..."
msgstr "Descargando... %s"

#: ../framework/extensions/update/class-fw-extension-update.php:386
#, php-format
msgid "Cannot download the %s."
msgstr "No se puede descargar archivo zip %s"

#: ../framework/extensions/update/class-fw-extension-update.php:396
#, php-format
msgid "Installing the %s..."
msgstr "Instalando %s"

#: ../framework/extensions/update/class-fw-extension-update.php:402
#: ../framework/extensions/update/class-fw-extension-update.php:431
#: ../framework/extensions/update/class-fw-extension-update.php:531
#: ../framework/extensions/update/class-fw-extension-update.php:552
#: ../framework/extensions/update/class-fw-extension-update.php:582
msgid "Cannot access directory: "
msgstr "No se puede acceder al directorio"

#: ../framework/extensions/update/class-fw-extension-update.php:421
msgid "Cannot remove: "
msgstr "No se puede remover"

#: ../framework/extensions/update/class-fw-extension-update.php:456
#: ../framework/extensions/update/class-fw-extension-update.php:522
#: ../framework/extensions/update/class-fw-extension-update.php:628
#: ../framework/extensions/update/class-fw-extension-update.php:652
#, php-format
msgid "Cannot move \"%s\" to \"%s\""
msgstr "No se puede mover \"%s\" a \"%s\""

#: ../framework/extensions/update/class-fw-extension-update.php:472
#, php-format
msgid "Cannot merge \"%s\" with \"%s\""
msgstr "No se puede unir \"%s\" con \"%s\""

#: ../framework/extensions/update/class-fw-extension-update.php:485
#, php-format
msgid "The %s has been successfully updated."
msgstr "Se ha actualizado correctamente %s"

#: ../framework/extensions/update/class-fw-extension-update.php:492
#, php-format
msgid "Cannot remove temporary directory \"%s\"."
msgstr "No se puede remover el directorio temporal \"%s\"."

#: ../framework/extensions/update/class-fw-extension-update.php:672
#: ../framework/extensions/update/class-fw-extension-update.php:740
#: ../framework/extensions/update/class-fw-extension-update.php:808
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:930
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1427
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1837
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2054
msgid "Invalid nonce."
msgstr "Mientras tanto no es válido"

#: ../framework/extensions/update/class-fw-extension-update.php:683
msgid "Framework Update"
msgstr "Actualizar Framework"

#: ../framework/extensions/update/class-fw-extension-update.php:699
msgid "Failed to get framework latest version."
msgstr "Ha fallado obtener la última versión del framework."

#: ../framework/extensions/update/class-fw-extension-update.php:716
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:360
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:374
#: ../framework/extensions/update/views/updates-list.php:10
#: ../framework/core/class-fw-manifest.php:353
msgid "Framework"
msgstr "Framework"

#: ../framework/extensions/update/class-fw-extension-update.php:751
msgid "Theme Update"
msgstr "Actualizar Tema"

#: ../framework/extensions/update/class-fw-extension-update.php:767
msgid "Failed to get theme latest version."
msgstr "Ha fallado obtener la última versión del tema."

#: ../framework/extensions/update/class-fw-extension-update.php:784
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:393
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:407
msgid "Theme"
msgstr "Tema"

#: ../framework/extensions/update/class-fw-extension-update.php:817
msgid "Please check the extensions you want to update."
msgstr "Por favor marque las extensiones que quiere actualizar."

#: ../framework/extensions/update/class-fw-extension-update.php:841
msgid "Extensions Update"
msgstr "Actualizar Extensiones"

#: ../framework/extensions/update/class-fw-extension-update.php:879
msgid "No extensions updates found."
msgstr "No se encuentran actualizaciones disponibles."

#: ../framework/extensions/update/class-fw-extension-update.php:886
#, php-format
msgid "Extension \"%s\" does not exist or is disabled."
msgstr "Extensión \"%s\" no existe o está deshabilitada."

#: ../framework/extensions/update/class-fw-extension-update.php:893
#, php-format
msgid "No update found for the \"%s\" extension."
msgstr "No existen actualizaciones para la extensión \"%s\"."

#: ../framework/extensions/update/class-fw-extension-update.php:915
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:426
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:440
#, php-format
msgid "%s extension"
msgstr "extensión \"%s\" "

#: ../framework/extensions/update/manifest.php:5
#: ../framework/extensions/builder/includes/option-types/builder/includes/fullscreen.php:82
msgid "Update"
msgstr "Actualizar"

#: ../framework/extensions/update/manifest.php:6
msgid "Keep you framework, extensions and theme up to date."
msgstr "Mantenga su framework y extensiones actualizadas"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:91
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:232
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2439
msgid "Github error:"
msgstr "Github error: "

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:100
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:241
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2446
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (Response code: %d)"
msgstr "Ha fallado el acceso a publicaciones del repositorio Github \"%s\". (Código de respuesta: %d)"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:108
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:249
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2462
#, php-format
msgid "Failed to access Github repository \"%s\" releases."
msgstr "Ha fallado el acceso a publicaciones del repositorio Github \"%s\"."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:122
#, php-format
msgid "No releases found in repository \"%s\"."
msgstr "No hay publicaciones encontradas en repositorio \"%s\"."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:143
#, php-format
msgid ""
"%s manifest has invalid \"github_update\" parameter. Please use "
"\"user/repo\" format."
msgstr "Manifiesto %s es un parámetro inválido para \"github_update\". Utilice el formato \"user/repo\""

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:171
#, php-format
msgid "Failed to fetch %s latest version from github \"%s\"."
msgstr "Ha fallado recuperar la última versión %s de Github \"%s\"."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:264
#, php-format
msgid "%s github repository \"%s\" does not have the \"%s\" release."
msgstr "Github %s repositorio \"%s\" no tiene publicación \"%s\"."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:284
#, php-format
msgid "Cannot download %s zip."
msgstr "No se puede descargar archivo zip %s"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:297
#, php-format
msgid "Cannot save %s zip."
msgstr "No se puede guardar archivo zip %s"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:316
#, php-format
msgid "Cannot remove %s zip."
msgstr "No se puede remover archivo zip %s"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:325
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2566
msgid "Cannot access the unzipped directory files."
msgstr "No se puede acceder al directorio de archivos extraídos."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:341
#, php-format
msgid "The unzipped %s directory not found."
msgstr "No se encuentra el directorio de archivos extraídos %s"

#: ../framework/extensions/update/views/updates-list.php:12
#, php-format
msgid "You have the latest version of %s."
msgstr "Usted tiene la última versión de %s"

#: ../framework/extensions/update/views/updates-list.php:25
msgid "Update Framework"
msgstr "Actualizar Framework"

#: ../framework/extensions/update/views/updates-list.php:37
msgid "Your theme is up to date."
msgstr "Su tema se encuentra actualizado"

#: ../framework/extensions/update/views/updates-list.php:50
msgid "Update Theme"
msgstr "Actualizar Tema"

#: ../framework/extensions/update/views/updates-list.php:60
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:844
#, php-format
msgid "%s Extensions"
msgstr "Extensiones %s"

#: ../framework/extensions/update/views/updates-list.php:62
#, php-format
msgid "You have the latest version of %s Extensions."
msgstr "Usted tiene la última versión de sus extensiones %s"

#: ../framework/extensions/update/views/updates-list.php:80
#: ../framework/extensions/update/views/updates-list.php:95
#: ../framework/extensions/update/views/updates-list.php:100
msgid "Update Extensions"
msgstr "Actualizar Extensiones"

#: ../framework/extensions/update/views/updates-list.php:99
msgid "New extensions updates available."
msgstr "Nuevas actualizaciones de extensiones disponibles"

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:16
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:14
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:16
msgid "Go to updates page"
msgstr "Ir a la página de actualizaciones"

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:19
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:17
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:19
msgid "Return to Updates page"
msgstr "Volver a la página de actualizaciones"

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:84
#, php-format
msgid "You have version %s installed. Update to %s."
msgstr "Usted tiene la versión %s instalada. Actualice %s"

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:126
msgid "No Extensions for update."
msgstr "Actualizar Extensiones"

#: ../framework/extensions/portfolio/manifest.php:7
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:260
#: ../framework/core/components/extensions/manager/available-extensions.php:72
msgid "Portfolio"
msgstr "Portafolios"

#: ../framework/extensions/portfolio/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:73
msgid ""
"This extension will add a fully fledged portfolio module that will let you "
"display your projects using the built in portfolio pages."
msgstr "Esta extensión añade un completo módulo de proyectos que le permitirá mostrar sus proyectos utilizando las páginas de portafolios predefinidas."

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:115
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Project"
msgstr "Proyecto"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:116
msgid "Projects"
msgstr "Proyectos"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:123
#: ../framework/extensions/learning/class-fw-extension-learning.php:63
#: ../framework/extensions/learning/class-fw-extension-learning.php:127
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:58
#: ../framework/extensions/events/class-fw-extension-events.php:76
#: ../framework/extensions/media/extensions/slider/posts.php:8
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:32
msgid "Add New"
msgstr "Añadir Nuevo"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:124
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:198
#: ../framework/extensions/learning/class-fw-extension-learning.php:64
#: ../framework/extensions/learning/class-fw-extension-learning.php:128
#: ../framework/extensions/events/class-fw-extension-events.php:77
#: ../framework/extensions/events/class-fw-extension-events.php:131
#, php-format
msgid "Add New %s"
msgstr "Añadir nuevo %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:125
#: ../framework/extensions/learning/class-fw-extension-learning.php:129
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:89
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:76
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:88
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:86
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:68
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:34
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:50
#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:110
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:45
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:37
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:73
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:81
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:57
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:50
#: ../framework/extensions/events/class-fw-extension-events.php:78
#: ../framework/includes/option-types/popup/class-fw-option-type-popup.php:158
#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:151
#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:177
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:161
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:187
msgid "Edit"
msgstr "Editar"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:126
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:196
#: ../framework/extensions/learning/class-fw-extension-learning.php:65
#: ../framework/extensions/learning/class-fw-extension-learning.php:66
#: ../framework/extensions/learning/class-fw-extension-learning.php:130
#: ../framework/extensions/learning/class-fw-extension-learning.php:192
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:59
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:60
#: ../framework/extensions/events/class-fw-extension-events.php:79
#: ../framework/extensions/events/class-fw-extension-events.php:129
#, php-format
msgid "Edit %s"
msgstr "Editar %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:127
#: ../framework/extensions/learning/class-fw-extension-learning.php:67
#: ../framework/extensions/learning/class-fw-extension-learning.php:131
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:61
#: ../framework/extensions/events/class-fw-extension-events.php:80
#, php-format
msgid "New %s"
msgstr "Nuevo %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:128
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:193
#: ../framework/extensions/learning/class-fw-extension-learning.php:68
#: ../framework/extensions/learning/class-fw-extension-learning.php:132
#: ../framework/extensions/learning/class-fw-extension-learning.php:189
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:62
#: ../framework/extensions/events/class-fw-extension-events.php:81
#: ../framework/extensions/events/class-fw-extension-events.php:126
#, php-format
msgid "All %s"
msgstr "Todos los %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:129
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:130
#: ../framework/extensions/learning/class-fw-extension-learning.php:69
#: ../framework/extensions/learning/class-fw-extension-learning.php:70
#: ../framework/extensions/learning/class-fw-extension-learning.php:133
#: ../framework/extensions/learning/class-fw-extension-learning.php:134
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:63
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:64
#: ../framework/extensions/events/class-fw-extension-events.php:82
#: ../framework/extensions/events/class-fw-extension-events.php:83
#, php-format
msgid "View %s"
msgstr "Ver %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:131
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:192
#: ../framework/extensions/learning/class-fw-extension-learning.php:71
#: ../framework/extensions/learning/class-fw-extension-learning.php:135
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:65
#: ../framework/extensions/events/class-fw-extension-events.php:84
#: ../framework/extensions/events/class-fw-extension-events.php:125
#, php-format
msgid "Search %s"
msgstr "Buscar %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:132
#: ../framework/extensions/learning/class-fw-extension-learning.php:72
#: ../framework/extensions/learning/class-fw-extension-learning.php:136
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:66
#: ../framework/extensions/events/class-fw-extension-events.php:85
#, php-format
msgid "No %s Found"
msgstr "No se encuentran %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:133
#: ../framework/extensions/learning/class-fw-extension-learning.php:73
#: ../framework/extensions/learning/class-fw-extension-learning.php:137
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:67
#: ../framework/extensions/events/class-fw-extension-events.php:86
#, php-format
msgid "No %s Found In Trash"
msgstr "No se encuentran %s en la papelera"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:136
msgid "Create a portfolio item"
msgstr "Crear un item en portafolio"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:183
#: ../framework/extensions/events/class-fw-extension-events.php:116
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:268
msgid "Category"
msgstr "Categoría"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:184
#: ../framework/extensions/events/class-fw-extension-events.php:117
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:168
msgid "Categories"
msgstr "Categorías"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:194
#: ../framework/extensions/learning/class-fw-extension-learning.php:190
#: ../framework/extensions/events/class-fw-extension-events.php:127
#, php-format
msgid "Parent %s"
msgstr "Principal %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:195
#: ../framework/extensions/learning/class-fw-extension-learning.php:191
#: ../framework/extensions/events/class-fw-extension-events.php:128
#, php-format
msgid "Parent %s:"
msgstr "Principal %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:197
#: ../framework/extensions/learning/class-fw-extension-learning.php:193
#: ../framework/extensions/events/class-fw-extension-events.php:130
#: ../framework/core/components/extensions/manager/views/extension.php:234
#: ../framework/core/components/extensions/manager/views/extension.php:272
#, php-format
msgid "Update %s"
msgstr "Actualizar %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:199
#: ../framework/extensions/learning/class-fw-extension-learning.php:195
#: ../framework/extensions/events/class-fw-extension-events.php:132
#, php-format
msgid "New %s Name"
msgstr "Nuevo nombre de %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:200
#: ../framework/extensions/learning/class-fw-extension-learning.php:196
#: ../framework/extensions/events/class-fw-extension-events.php:133
#, php-format
msgid "%s"
msgstr "%s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Gallery"
msgstr "Galería"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:240
msgid "Set project gallery"
msgstr "Establecer galería de proyecto"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:241
msgid "Edit project gallery"
msgstr "Editar galería de proyecto"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:271
msgid "Project Cover Image"
msgstr "Imagen de portada del proyecto"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:287
msgid "Edit this item"
msgstr "Editar Item"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:348
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:361
#: ../framework/extensions/learning/class-fw-extension-learning.php:320
#: ../framework/extensions/learning/class-fw-extension-learning.php:333
msgid "View all categories"
msgstr "Ver todas las categorías"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:381
msgid "Cover Image"
msgstr "Imagen de portada"

#: ../framework/extensions/seo/settings-options.php:17
#: ../framework/extensions/social/settings-options.php:11
msgid "General"
msgstr "General"

#: ../framework/extensions/seo/settings-options.php:21
#: ../framework/extensions/social/settings-options.php:15
msgid "General Settings"
msgstr "Configuraciones generales"

#: ../framework/extensions/seo/class-fw-extension-seo.php:89
msgid "Site name"
msgstr "Nombre del sitio"

#: ../framework/extensions/seo/class-fw-extension-seo.php:95
msgid "Site description"
msgstr "Descripción del sitio"

#: ../framework/extensions/seo/class-fw-extension-seo.php:101
msgid "Current time"
msgstr "Hora actual"

#: ../framework/extensions/seo/class-fw-extension-seo.php:107
msgid "Current date"
msgstr "Fecha actual"

#: ../framework/extensions/seo/class-fw-extension-seo.php:113
msgid "Current month"
msgstr "Mes actual"

#: ../framework/extensions/seo/class-fw-extension-seo.php:119
msgid "Current year"
msgstr "Año actual"

#: ../framework/extensions/seo/class-fw-extension-seo.php:125
msgid "Date of the post/page"
msgstr "Fecha del post/página"

#: ../framework/extensions/seo/class-fw-extension-seo.php:131
msgid "Title of the post/page/term"
msgstr "Título del post/página/término"

#: ../framework/extensions/seo/class-fw-extension-seo.php:137
msgid "Excerpt of the current post, of auto-generate if it is not set"
msgstr "Resumen del post actual, si no se ha generado es automático"

#: ../framework/extensions/seo/class-fw-extension-seo.php:143
msgid "Excerpt of the current post, without auto-generation"
msgstr "Resumen del post actual, sin autogenerar"

#: ../framework/extensions/seo/class-fw-extension-seo.php:149
msgid "Post tags, separated by coma"
msgstr "Etiquetas de post, separadas por coma"

#: ../framework/extensions/seo/class-fw-extension-seo.php:155
msgid "Post categories, separated by coma"
msgstr "Categorías de post, separadas por coma"

#: ../framework/extensions/seo/class-fw-extension-seo.php:161
msgid "Category/tag/term description"
msgstr "Descripción de categoría/etiqueta/término"

#: ../framework/extensions/seo/class-fw-extension-seo.php:167
msgid "Term title"
msgstr "Título de término"

#: ../framework/extensions/seo/class-fw-extension-seo.php:173
msgid "Post modified time"
msgstr "Hora de modificación del post"

#: ../framework/extensions/seo/class-fw-extension-seo.php:179
msgid "Post/page id"
msgstr "Post/página id "

#: ../framework/extensions/seo/class-fw-extension-seo.php:185
msgid "Post/page author \"nicename\""
msgstr "Nombre de autor del Post/página"

#: ../framework/extensions/seo/class-fw-extension-seo.php:191
msgid "Post/page author id"
msgstr "ID de autor del post/página"

#: ../framework/extensions/seo/class-fw-extension-seo.php:197
msgid "Search phrase in search page"
msgstr "Buscar frase en búsqueda de página"

#: ../framework/extensions/seo/class-fw-extension-seo.php:203
#: ../framework/extensions/seo/class-fw-extension-seo.php:209
msgid "Page number"
msgstr "Número de página"

#: ../framework/extensions/seo/class-fw-extension-seo.php:215
msgid "Attachment caption"
msgstr "Leyenda (subtítulo) de adjunto"

#: ../framework/extensions/seo/class-fw-extension-seo.php:435
#: ../framework/extensions/seo/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:120
msgid "SEO"
msgstr "SEO"

#: ../framework/extensions/seo/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:121
msgid ""
"This extension will enable you to have a fully optimized WordPress website "
"by adding optimized meta titles, keywords and descriptions."
msgstr "Esta extensión añade un completo módulo para optimizar su sitio web en Wordpress, añadiendo títulos (<title>,<meta>) optimizados, palabras claves y descripciones."

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:22
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:310
msgid "Titles & Meta"
msgstr "Títulos & Meta"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:26
#: ../framework/extensions/breadcrumbs/settings-options.php:6
#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:9
msgid "Homepage"
msgstr "Página principal"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:30
msgid "Homepage Title"
msgstr "Título de página principal"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:31
msgid "Set homepage title format"
msgstr "Establecer formato de título para página de inicio"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:36
msgid "Homepage Description"
msgstr "Descripción de la página de inicio "

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:37
msgid "Set homepage description"
msgstr "Establecer descripción para página de inicio"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:44
msgid "Homepage Meta Keywords"
msgstr "Palabras claves para página de inicio"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:45
msgid "Set homepage meta keywords"
msgstr "Establecer palabras claves para página de inicio"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:54
msgid "Custom Posts"
msgstr "Post personalizados (Custom posts)"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:59
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:111
#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:120
#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:7
#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:15
#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:12
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:15
#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:56
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:480
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:62
msgid "Title"
msgstr "Título"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:60
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:112
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:287
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:315
msgid "Set title format"
msgstr "Establecer formato de título"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:63
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:74
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:87
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:115
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:126
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:139
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:247
msgid "Here are some tags examples:"
msgstr "Aquí hay algunos ejemplos de etiquetas: "

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:70
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:122
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:320
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:66
msgid "Description"
msgstr "scripción"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:71
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:123
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:293
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:321
msgid "Set description format"
msgstr "Establecer formato de descripción"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:83
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:135
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:328
msgid "Meta Keywords"
msgstr "Palabras claves (Meta Keywords)"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:84
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:136
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:329
msgid "Set meta keywords"
msgstr "Establecer palabras claves"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:96
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:148
msgid "Meta Robots"
msgstr "Robots (Meta Robots)"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:97
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:149
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:188
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:227
msgid "noindex, follow"
msgstr "noindex, follow"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:105
msgid "Taxonomies"
msgstr "Taxonomías"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:158
msgid "Other"
msgstr "Otro (a)"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:165
msgid "Author Page Title"
msgstr "Título de la página de autor"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:166
msgid "Set author page title format"
msgstr "Establecer formato de la página de autor"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:171
msgid "Author Page Description"
msgstr "Descripción de la página de autor"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:172
msgid "Set author page description"
msgstr "Establecer descripción de la página de autor"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:179
msgid "Author Meta Keywords"
msgstr "Palabras claves para autor"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:180
msgid "Set author page meta keywords"
msgstr "Establecer palabras claves para autor"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:187
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:226
msgid "Metarobots"
msgstr "Metarobots"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:193
msgid "Disable Author Archives"
msgstr "Deshabilitar archivo de autor"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:194
msgid "Disable Author archives SEO settings"
msgstr "Deshabilitar configuración SEO para archivo de autor"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:204
msgid "Date Achieves Title"
msgstr "Título de la fecha de alcance"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:205
msgid "Set date achieves title format"
msgstr "Establecer título de la fecha de alcance"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:210
msgid "Date Achieves Description"
msgstr "Descripción de la fecha de alcance"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:211
msgid "Set date achieves description"
msgstr "Establecer descripción de la fecha de alcance"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:218
msgid "Date achieves Meta Keywords"
msgstr "Establecer palabras claves para la fecha de alcance"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:219
msgid "Set date achieves meta keywords"
msgstr "Establecer alcance de fecha para palabras claves"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:232
msgid "Disable Date Archives"
msgstr "Deshabilitar la fecha para archivo"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:233
msgid "Disable date archives SEO settings"
msgstr "Deshabilitar configuraciones SEO para archivo de fechas"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:243
msgid "Search Page Title"
msgstr "Título de la página de búsqueda"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:244
msgid "Set search page title format"
msgstr "Formato del título de la página de búsqueda"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:259
msgid "404 Page Title"
msgstr "Título de página 404"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:260
msgid "Set 404 page title format"
msgstr "Establecer formato del título de página 404"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:262
#: ../framework/extensions/breadcrumbs/settings-options.php:8
msgid "404 Not Found"
msgstr "404 no se encuentra"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:286
msgid "SEO Title"
msgstr "Título SEO"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:292
msgid "SEO Description"
msgstr "Descipción SEO"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:314
msgid "Page Title"
msgstr "Título de página"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:356
msgid "Use Meta Keywords"
msgstr "Utilizar palabras claves"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:357
msgid "Allow the use of meta keywords in posts and taxonomies"
msgstr "Habilitar el uso de palabras claves en posts y taxonomías"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:33
msgid "Google Webmasters"
msgstr "Google Webmasters"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:34
msgid "Insert Google Webmasters verification code"
msgstr "Insertar código de verificación de Google Webmasters"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:41
msgid "Bing Webmasters"
msgstr "Bing Webmasters"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:42
msgid "Insert Bing Webmasters verification code"
msgstr "Insertar código de verificación de Bing Webmasters"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:80
msgid "Webmasters"
msgstr "Webmasters"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:89
#, php-format
msgid "Webmaster %s already exists"
msgstr "Webmaster %s ya existe"

#: ../framework/extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:634
msgid "Google"
msgstr "Google"

#: ../framework/extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:638
msgid "Bing"
msgstr "Bing"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:19
msgid "Check if you want to exclude this page"
msgstr "Marque si quiere excluir esta página"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:40
msgid "Check if you want to exclude this category"
msgstr "Marque si quiere excluir esta categoría"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:57
msgid "Sitemap"
msgstr "Mapa del sitio"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:68
msgid "View Sitemap"
msgstr "Ver mapa del sitio"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:69
msgid "Press button to view sitemap file"
msgstr "Presione este botón para ver archivo sitemap"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:71
msgid "XML Sitemap"
msgstr "XML Sitemap"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:81
msgid "Search Engines"
msgstr "Motores de búsqueda"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:83
msgid "After adding content the extension will automatically ping to:"
msgstr "Después de añadir contenido la extensión realiza ping automático a:"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:93
msgid "Exclude Pages"
msgstr "Excluir páginas"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:95
msgid "Please check the pages you do not want to include in sitemap"
msgstr "Por favor marque las páginas que quiere excluir en su sitemap"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:106
msgid "Exclude Categories"
msgstr "Excluir categorías"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:108
msgid "Please check the categories you do not want to include in sitemap"
msgstr "Por favor marque las categorías que quiere excluir de su sitemap"

#: ../framework/extensions/mailer/manifest.php:5
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:187
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:188
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:103
#: ../framework/core/components/extensions/manager/available-extensions.php:240
msgid "Mailer"
msgstr "Mailer"

#: ../framework/extensions/mailer/manifest.php:6
#: ../framework/core/components/extensions/manager/available-extensions.php:241
msgid ""
"This extension will let you set some global email options and it is used by "
"other extensions (like Forms) to send emails."
msgstr "Esta extensión le permite establecer opciones globales para email y puede ser utilizada por otras extensiones (cómo Forms) para enviar emails."

#: ../framework/extensions/mailer/class-fw-extension-mailer.php:48
msgid "Invalid send method"
msgstr "Método de envío inválido"

#: ../framework/extensions/mailer/class-fw-extension-mailer.php:81
msgid "The message has been successfully sent!"
msgstr "El mensaje se ha enviado con éxito!"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:27
msgid "Invalid email configuration"
msgstr "Configuración de email inválida"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:145
#: ../framework/extensions/mailer/includes/class-mailer-sender.php:161
msgid "Email sent"
msgstr "Email enviado"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:146
msgid "Could not send via smtp"
msgstr "No se puede enviar vía smtp"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:162
msgid "Could not send via wp_mail"
msgstr "No se puede enviar vía wp_mail"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:25
msgid "Server Address"
msgstr "Dirección del servidor"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:26
msgid "Enter your email server"
msgstr "Ingrese su email del servidor"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:31
msgid "Username"
msgstr "Usuario"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:32
msgid "Enter your username"
msgstr "ingrese su nombre de usuario"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:37
msgid "Password"
msgstr "Contraseña"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:38
msgid "Enter your password"
msgstr "ingrese su contraseña"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:43
msgid "Secure Connection"
msgstr "Conección segura"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:54
msgid "Custom Port"
msgstr "Puerto personalizado"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:55
msgid "Optional - SMTP port number to use."
msgstr "Opcional - puerto SMTP a utilizar."

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:56
msgid "Leave blank for default (SMTP - 25, SMTPS - 465)"
msgstr "Deje en blanco, por defecto (SMTP - 25, SMTPS - 465)"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:82
msgid "Username cannot be empty"
msgstr "Usuario no puede estar vacío"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:89
msgid "Password cannot be empty"
msgstr "Contraseña no puede estar vacía"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:96
msgid "Invalid host"
msgstr "Host inválido"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:176
#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-wpmail.php:56
msgid "Could not send the email"
msgstr "No se puede enviar el email"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:76
msgid "Send Method"
msgstr "Método de envío"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:77
msgid "Select the send form method"
msgstr "Seleccione el método de envío del formulario"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:94
msgid "From Name"
msgstr "Nombre remitente"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:95
msgid "The name you'll see in the From filed in your email client."
msgstr "El nombre que verá en el campo de correo electrónico de su email."

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:102
msgid "From Address"
msgstr "Dirección remitente"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:103
msgid "The form will look like was sent from this email address."
msgstr "El formulario se verá enviado desde esta dirección de email."

#: ../framework/extensions/learning/class-fw-extension-learning.php:56
msgid "Lesson"
msgstr "Clase"

#: ../framework/extensions/learning/class-fw-extension-learning.php:57
#: ../framework/extensions/learning/views/content-course.php:14
msgid "Lessons"
msgstr "Clases"

#: ../framework/extensions/learning/class-fw-extension-learning.php:78
msgid "Create a lesson"
msgstr "Crear una clase"

#: ../framework/extensions/learning/class-fw-extension-learning.php:120
#: ../framework/extensions/learning/hooks.php:53
msgid "Course"
msgstr "Curso"

#: ../framework/extensions/learning/class-fw-extension-learning.php:121
#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:108
msgid "Courses"
msgstr "Cursos"

#: ../framework/extensions/learning/class-fw-extension-learning.php:142
msgid "Create a course"
msgstr "Crear curso"

#: ../framework/extensions/learning/class-fw-extension-learning.php:181
#: ../framework/extensions/learning/class-fw-extension-learning.php:222
msgid "Course Category"
msgstr "Categoría del curso"

#: ../framework/extensions/learning/class-fw-extension-learning.php:182
#: ../framework/extensions/learning/class-fw-extension-learning.php:223
msgid "Course Categories"
msgstr "Categorías de curso"

#: ../framework/extensions/learning/class-fw-extension-learning.php:188
msgid "Search categories"
msgstr "Buscar categorías"

#: ../framework/extensions/learning/class-fw-extension-learning.php:194
msgid "Add New category"
msgstr "Añadir categoría"

#: ../framework/extensions/learning/class-fw-extension-learning.php:285
msgid "View all courses"
msgstr "Ver todos los cursos"

#: ../framework/extensions/learning/class-fw-extension-learning.php:511
msgid "No courses available"
msgstr "No hay cursos disponibles"

#: ../framework/extensions/learning/class-fw-extension-learning.php:513
msgid "Without Course"
msgstr "Sin curso"

#: ../framework/extensions/learning/class-fw-extension-learning.php:520
msgid "Select Course"
msgstr "Seleccionar Curso"

#: ../framework/extensions/learning/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:216
msgid "Learning"
msgstr "Aprendizaje"

#: ../framework/extensions/learning/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:217
msgid ""
"This extension adds a Learning module to your theme. Using this extension "
"you can add courses, lessons and tests for your users to take."
msgstr "Esta extensión añade un módulo de aprendizaje a su tema. utilizando esta extensión usted podrá agregar cursos, clases y exámenes para sus usuarios."

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:118
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:145
msgid "Quiz"
msgstr "Examen"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:259
msgid "Quiz Elements"
msgstr "Elementos del examen"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:272
msgid "Quiz settings"
msgstr "Configuraciones de examen"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:276
msgid "Quiz Passmark Points"
msgstr "Nota de aprobación del examen"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:278
msgid "The points number at which the test will be passed."
msgstr "Número de puntos con que el examen es aprobado."

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:286
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:296
msgid "Lesson Quiz"
msgstr "Examen de clase"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:535
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:544
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:552
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:559
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:568
msgid "Invalid Quiz"
msgstr "Examen inválido"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:21
#, php-format
msgid "You require %d points in oder to pass the test"
msgstr "Usted requiere %d puntos para aprobar este examen"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:27
msgid "Sorry, you did not pass the test"
msgstr "Lo sentimos, usted no ha aprobado el examen"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:29
msgid "Congratulation, you passed the test"
msgstr "Felicidades, usted ha aprobado el examen"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:43
#, php-format
msgid "You answered correctly %s questions from %s"
msgstr "Usted ha respondido de manera correcta a %s preguntas de %s"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:69
#: ../framework/extensions/learning/views/content-lesson.php:18
msgid "Back to"
msgstr "Volver a"

#: ../framework/extensions/learning/extensions/learning-quiz/views/start-quiz.php:11
msgid "Start Quiz"
msgstr "Iniciar Examen"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:20
msgid "Correct answers"
msgstr "Respuestas Correctas"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:21
msgid "Add correct answer variants"
msgstr "Añadir variantes de respuestas correctas"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:24
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:23
msgid "Set Correct Answer"
msgstr "Establcer respuesta correcta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:34
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:31
msgid "Wrong answers"
msgstr "Respuestas incorrectas"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:35
msgid "Add wrong answer variants"
msgstr "Añadir respuesta incorrecta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:38
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:35
msgid "Set Wrong Answer"
msgstr "Establecer respuesta incorrecta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:42
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:55
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:53
msgid "Creates a"
msgstr "Crear una"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:59
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:91
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:82
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:180
msgid "Multiple Choice"
msgstr "Elección múltiple"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
msgid "item"
msgstr "item"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:87
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:73
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:86
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:84
#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:76
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:75
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:80
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:80
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:48
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:75
msgid "Label"
msgstr "Etiqueta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:88
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:75
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:87
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:85
msgid "Add/Edit Question"
msgstr "Añadir/Editar Pregunta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:90
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:77
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:90
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:87
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:58
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:51
msgid "Delete"
msgstr "Eliminar"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:92
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:90
msgid "More"
msgstr "Más"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:93
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:91
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:57
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:57
msgid "Close"
msgstr "Cerrar"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:94
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:78
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:91
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:56
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:52
msgid "Edit Label"
msgstr "Editar etiqueta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:96
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:80
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:93
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:93
msgid "The question label is empty"
msgstr "La etiqueta de pregunta se encuentra vacía"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:97
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:81
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:94
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:94
msgid "Invalid mark point number"
msgstr "Número de marca inválido"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:98
msgid "There needs to be at least one correct answer"
msgstr "Es necesario que haya al menos una respuesta correcta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:18
msgid "Correct answer"
msgstr "Respuesta correcta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:19
msgid "The question answer will be true or false"
msgstr "La respuesta a la pregunta debe ser verdadero o falso"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:22
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:20
msgid "True"
msgstr "Verdadero"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:26
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:24
msgid "False"
msgstr "Falso"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:45
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:74
msgid "True/False"
msgstr "Verdadero/Falso"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:19
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:21
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:97
msgid "Text before gap"
msgstr "Texto antes de relleno"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:28
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:30
msgid "Gap"
msgstr "Rellenar"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:37
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:39
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:98
msgid "Text after gap"
msgstr "Texto después de rellenar"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:58
msgid "Gap Fill"
msgstr "Completar relleno"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:89
msgid "Gap _____ Fill"
msgstr "Completar _____ Relleno"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:96
#, php-format
msgid "At least one of the fields ( %s or %s ) has to ve filled with text"
msgstr "Al menos uno de los campos (%s o %s) debe ser rellenado con texto"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:19
msgid "Correct Answer"
msgstr "Respuesta correcta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:20
msgid "Write the correct answer text"
msgstr "Escriba la respuesta correcta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:32
msgid "Add wrong answers variants"
msgstr "Agregar variantes de respuestas incorrectas"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:82
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:180
msgid "Single Choice"
msgstr "Elección única"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:95
msgid "Correct answer cannot be empty"
msgstr "Respuesta correcta no puede estar vacía"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:96
msgid "There are not any wrong answers set"
msgstr "No hay respuestas incorrectas establecidas"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:51
msgid "Question"
msgstr "Pregunta"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:53
msgid "Type the question..."
msgstr "Escriba la pregunta..."

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:58
msgid "Points"
msgstr "Puntos"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:18
msgid "Get list of courses"
msgstr "Obtener lista de cursos"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:20
msgid "Lesson Courses"
msgstr "Clase de cursos"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:126
msgid "Number of courses"
msgstr "Número de cursos"

#: ../framework/extensions/analytics/settings-options.php:11
msgid "Google Analytics"
msgstr "Google Analytics"

#: ../framework/extensions/analytics/settings-options.php:12
msgid "Enter your Google Analytics code (Ex: UA-XXXXX-X)"
msgstr "ingrese su código de seguimiento Google Analytics (Ej: UA-XXXXX-X))"

#: ../framework/extensions/analytics/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:192
msgid "Analytics"
msgstr "Analytics"

#: ../framework/extensions/analytics/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:193
msgid ""
"Enables the possibility to add the Google Analytics tracking code that will "
"let you get all the analytics about visitors, page views and more."
msgstr "Añade la posibilidad de agregar el código de seguimiento de Google Analytics para obtener analítica de sus visitas, páginas vistas y más"

#: ../framework/extensions/blog/class-fw-extension-blog.php:36
#: ../framework/extensions/blog/class-fw-extension-blog.php:37
#: ../framework/extensions/breadcrumbs/settings-options.php:7
#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:10
msgid "Blog"
msgstr "Blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:38
msgid "Add blog post"
msgstr "Añadir publicación al blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:39
msgid "Add new blog post"
msgstr "Añadir nueva publicación al blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:40
msgid "All blog posts"
msgstr "Todas las publicaciones en el blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:41
msgid "Edit blog post"
msgstr "Editar publicación"

#: ../framework/extensions/blog/class-fw-extension-blog.php:42
#: ../framework/extensions/blog/class-fw-extension-blog.php:43
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:49
msgid "Blog Post"
msgstr "Publicación en blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:44
msgid "New blog post"
msgstr "Añadir nueva publicación"

#: ../framework/extensions/blog/class-fw-extension-blog.php:45
msgid "No blog posts found"
msgstr "No se encuentran publicaciones"

#: ../framework/extensions/blog/class-fw-extension-blog.php:46
msgid "No blog posts found in trash"
msgstr "No se encuentran publicaciones en la papelera"

#: ../framework/extensions/blog/class-fw-extension-blog.php:47
msgid "Search blog posts"
msgstr "Buscar publicaciones en blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:48
msgid "View blog post"
msgstr "Ver publicación en blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:67
#: ../framework/extensions/blog/class-fw-extension-blog.php:87
#: ../framework/extensions/blog/manifest.php:7
#: ../framework/extensions/blog/manifest.php:8
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:50
msgid "Blog Posts"
msgstr "Publicaciones en blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:76
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:74
msgid "Blog Categories"
msgstr "Categorías del blog"

#: ../framework/extensions/styling/class-fw-extension-styling.php:60
#: ../framework/extensions/styling/class-fw-extension-styling.php:61
#: ../framework/extensions/styling/class-fw-extension-styling.php:78
#: ../framework/extensions/styling/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:48
msgid "Styling"
msgstr "Estilizar"

#: ../framework/extensions/styling/class-fw-extension-styling.php:104
#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:278
#: ../framework/core/components/backend.php:357
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2277
msgid "Save"
msgstr "Guardar"

#: ../framework/extensions/styling/class-fw-extension-styling.php:118
msgid "You have no permission to change Styling options"
msgstr "Usted no tiene permisos para cambiar opciones de estilo"

#: ../framework/extensions/styling/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:49
msgid ""
"This extension lets you control the website visual style. Starting from "
"predefined styles to changing specific fonts and colors across the website."
msgstr "Esta extensión le permite mantener el control visual de su sitio web. Comenzando por estilos predefinidos hasta cambiar fuentes y colores a través del sitio web."

#: ../framework/extensions/styling/extensions/switch-style-panel/manifest.php:7
msgid "Switch Style Panel"
msgstr "Cambiar Panel de Estilos"

#: ../framework/extensions/styling/extensions/switch-style-panel/manifest.php:8
msgid ""
"Show on the front-end a panel that allows the user to make the switch "
"between predefined styles."
msgstr "Mostrar en el front-end un panel que permite al usuario realizar cambios entre estilos predefinidos."

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:9
msgid "Frontend Style Switcher"
msgstr "Cambiar Estilos Frontend"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:10
msgid "Enable frontend style switcher"
msgstr "Activar cambiar estilos en frontend"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:13
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:274
#: ../framework/extensions/shortcodes/shortcodes/map/options.php:45
#: ../framework/extensions/shortcodes/shortcodes/button/options.php:24
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:42
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:34
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:48
#: ../framework/includes/option-types/simple.php:454
#: ../framework/includes/option-types/switch/class-fw-option-type-switch.php:151
msgid "Yes"
msgstr "Si"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:17
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:278
#: ../framework/extensions/shortcodes/shortcodes/map/options.php:49
#: ../framework/extensions/shortcodes/shortcodes/button/options.php:28
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:46
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:38
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:52
#: ../framework/includes/option-types/switch/class-fw-option-type-switch.php:147
msgid "No"
msgstr "No"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:24
msgid "The text that will be displayed at the top of the panel."
msgstr "El texto que se muestra arriba del panel."

#: ../framework/extensions/styling/includes/option-types/style/views/settings.php:119
msgid "Background"
msgstr "Fondo"

#: ../framework/extensions/styling/includes/option-types/style/views/predefined.php:26
msgid "Predefined Styles"
msgstr "Estilos Predefinidos"

#: ../framework/extensions/styling/includes/option-types/style/views/preview.php:41
msgid "This is a simplified preview, not changes are reflected."
msgstr "Esta es una vista simplificada, los cambios no son reflejados."

#: ../framework/extensions/feedback/class-fw-extension-feedback.php:64
#: ../framework/core/components/extensions/manager/available-extensions.php:144
msgid "Feedback"
msgstr "Feedback"

#: ../framework/extensions/feedback/class-fw-extension-feedback.php:111
msgid "Reviews"
msgstr "Opiniones"

#: ../framework/extensions/feedback/settings-options.php:10
#: ../framework/extensions/shortcodes/extensions/page-builder/settings-options.php:11
msgid "Activate for"
msgstr "Activo para"

#: ../framework/extensions/feedback/settings-options.php:16
msgid "Select the options you want the Feedback extension to be activated for"
msgstr "Seleccione las opciones en que será activada la extensión Feedback"

#: ../framework/extensions/feedback/manifest.php:7
msgid "FeedBack"
msgstr "Feedback"

#: ../framework/extensions/feedback/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:145
msgid ""
"Adds the possibility to leave feedback (comments, reviews and rating) about "
"your products, articles, etc. This replaces the default comments system."
msgstr "Añade la posibilidad de dejar un feedback (comentarios, opiniones y valoraciones) acerca de sus productos, artículos, etc. Esta reemplaza el sistema de comentarios por defecto."

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:84
msgid "Rating:"
msgstr "Valoración: "

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:195
msgid "Feedback Stars"
msgstr "Estrellas feedback"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:221
#: ../framework/extensions/feedback/extensions/feedback-stars/views/rate.php:12
msgid "Rating"
msgstr "Valoración"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "ERROR"
msgstr "ERROR"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "please rate the post."
msgstr "por favor valore esta publicación."

#: ../framework/extensions/feedback/extensions/feedback-stars/manifest.php:7
msgid "FeedBack Stars"
msgstr "Estrellas Feedback"

#: ../framework/extensions/feedback/extensions/feedback-stars/manifest.php:8
msgid "Allows visitors to appreciate a post using star rating"
msgstr "Permite a sus visitantes valorar el post por medio de estrellas de valroación"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
msgid "Pingback:"
msgstr "Pingback: "

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
msgid "(Edit)"
msgstr "(Editar)"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:37
msgid "Post author"
msgstr "Autor de publicación"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:57
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
#, php-format
msgid "%1$s at %2$s"
msgstr "%1$s en %2$s"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:63
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:53
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:56
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:122
msgid "Your comment is awaiting moderation."
msgstr "Su comentario se encuentra en espera de moderación."

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:24
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:40
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:119
msgid "says"
msgstr "dice"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/view-rates.php:24
#, php-format
msgid "Based on %s Votes"
msgstr "Basado en %s Votos"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:6
msgid "Rating System"
msgstr "Sistema de valoración"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:9
msgid "Enter the number of stars you want in the rating system"
msgstr "Ingrese el número de estrellas que quiere en el sistema de valoración"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:11
msgid "5 stars"
msgstr "5 estrellas"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:12
msgid "7 stars"
msgstr "7 estrellas"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:13
msgid "10 stars"
msgstr "10 estrellas"

#: ../framework/extensions/feedback/views/reviews.php:32
#: ../framework/extensions/feedback/views/reviews.php:53
msgid "Comment navigation"
msgstr "Explorar comentario"

#: ../framework/extensions/feedback/views/reviews.php:35
#: ../framework/extensions/feedback/views/reviews.php:56
msgid "&larr; Older Comments"
msgstr "&larr; Comentarios Previos"

#: ../framework/extensions/feedback/views/reviews.php:36
#: ../framework/extensions/feedback/views/reviews.php:57
msgid "Newer Comments &rarr;"
msgstr "Comentarios Nuevos &rarr;"

#: ../framework/extensions/feedback/views/reviews.php:62
msgid "Comments are closed."
msgstr "Comentarios cerrados."

#: ../framework/extensions/shortcodes/extensions/page-builder/settings-options.php:18
msgid ""
"Select the posts you want the Page Builder extension to be activated for"
msgstr "Seleccione las publicaciones en que quiera activar el Constructor de Página"

#: ../framework/extensions/shortcodes/extensions/page-builder/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:84
msgid "Page Builder"
msgstr "Constructor de Página"

#: ../framework/extensions/shortcodes/extensions/page-builder/manifest.php:9
msgid ""
"Lets you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr "Permite construir de manera sencilla innumerables páginas con la ayuda del constructor visual \"arrastre y suelte\" que viene con un grupo de shortcodes listos para utilizar."

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:51
msgid ""
"There must not be more than one page Editor integrated with the wp post "
"editor per page"
msgstr "No debe haber más de un editor de páginas integrado con el editor de publicación por página."

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:96
msgid "Visual Page Builder"
msgstr "Constructor visual de páginas"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:97
msgid "Default Editor"
msgstr "Editor por Defecto"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:126
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:117
#: ../framework/extensions/shortcodes/shortcodes/section/config.php:5
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:22
msgid "Layout Elements"
msgstr "Elementos de Diseño"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:99
#, php-format
msgid "No Page Builder tab specified for shortcode: %s"
msgstr "No hay pestaña especificada en el constructor de página para el shortcode: %s"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:111
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:75
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:83
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:57
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:238
#: ../framework/core/components/extensions/manager/views/extension.php:141
#: ../framework/core/components/extensions/manager/views/extension.php:345
msgid "Remove"
msgstr "Remover"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:112
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:74
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:82
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:56
msgid "Duplicate"
msgstr "Duplicar"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:12
msgid "Testimonials"
msgstr "Testimonios"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:9
msgid "Add some Testimonials"
msgstr "Añadir algunos testimonios"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/table/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/map/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/button/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:10
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:10
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:106
msgid "Content Elements"
msgstr "Elementos de contenido"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:8
msgid "Option Testimonials Title"
msgstr "Opción título de testimonios"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:13
msgid "Add/Edit Testimonial"
msgstr "Agregar/Editar Testimonio"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:14
msgid "Here you can add, remove and edit your Testimonials."
msgstr "Aquí usted añadir, remover y editar sus testimonios."

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:19
msgid "Quote"
msgstr "Cita (frase)"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:20
msgid "Enter the testimonial here"
msgstr "Ingrese el testimonio aquí"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:25
#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:6
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:102
msgid "Image"
msgstr "Imagen"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:26
#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:9
msgid ""
"Either upload a new, or choose an existing image from your media library"
msgstr "Ya sea subir una nueva o escoger una existente en su biblioteca multimedia"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:30
msgid "Name"
msgstr "Nombre"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:31
msgid "Enter the Name of the Person to quote"
msgstr "Ingrese el nombre de la persona a citar"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:35
msgid "Position"
msgstr "Cargo"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:36
msgid "Can be used for a job description"
msgstr "Utilice para describir cargo en empresa"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:40
msgid "Website Name"
msgstr "Nombre del sitio web"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:41
msgid "Linktext for the above Link"
msgstr "Texto sobre el link"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:45
msgid "Website Link"
msgstr "Link a sitio web"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:46
msgid "Link to the Persons website"
msgstr "Link a sitio web personal"

#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:8
msgid "Accordion"
msgstr "Acordeón"

#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:9
msgid "Add an Accordion"
msgstr "Añadir Acordeón"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:8
msgid "Tabs"
msgstr "Pestañas"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:9
msgid "Add/Edit Tabs"
msgstr "Agregar/Editar Pestañas"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:10
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:10
msgid "Create your tabs"
msgstr "Crear sus pestañas"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:19
#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:24
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/text-block/options.php:10
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:19
msgid "Content"
msgstr "Contenido"

#: ../framework/extensions/shortcodes/shortcodes/table/config.php:8
msgid "Table"
msgstr "Tabla"

#: ../framework/extensions/shortcodes/shortcodes/table/config.php:9
msgid "Add a Table"
msgstr "Agregar una tabla"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:64
msgid "table-builder option type must be inside the table shortcode"
msgstr "la opción constructor de tablas debe estar dentro de un shortcode tabla"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:171
msgid "Table Styling"
msgstr "Estilo de tabla"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:172
msgid "Choose the table styling options"
msgstr "Escoger las opciones de estilo de tabla"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:174
msgid "Use the table as a pricing table"
msgstr "Utilizar tabla como tabla de precios"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:175
msgid "Use the table to display tabular data"
msgstr "Utilizar tabla para mostrar tabla tabular"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:197
msgid "Default row"
msgstr "Fila por defecto"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:198
msgid "Heading row"
msgstr "Título de fila"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:199
msgid "Pricing row"
msgstr "Precio de fila"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:200
msgid "Button row"
msgstr "Botón de fila"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:201
msgid "Row switch"
msgstr "Cambio de fila"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:211
msgid "Default column"
msgstr "Columna por defecto"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:212
msgid "Description column"
msgstr "Descripción de columna"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:213
msgid "Highlight column"
msgstr "Resaltar columna"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:214
msgid "Center text column"
msgstr "Centrar texto de columna"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:251
msgid "per month"
msgstr "por mes"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:262
#: ../framework/extensions/shortcodes/shortcodes/button/config.php:8
msgid "Button"
msgstr "Botón"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:263
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:39
#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:148
#: ../framework/includes/option-types/addable-option/class-fw-option-type-addable-option.php:20
#: ../framework/includes/option-types/addable-popup/class-fw-option-type-addable-popup.php:153
#: ../framework/includes/option-types/addable-box/class-fw-option-type-addable-box.php:186
msgid "Add"
msgstr "Añadir"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-head-template.php:27
msgid "Add Column"
msgstr "Añadir Columna"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/view.php:161
msgid "Add Row"
msgstr "Añadir fila"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:24
#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:25
msgid "Custom"
msgstr "Personalizar"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:27
msgid "Locations"
msgstr "Ubicaciones"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:28
msgid "Add/Edit Location"
msgstr "Agregar/Editar ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:31
msgid "Note: Please set location"
msgstr "Nota: por favor establezca ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:35
#: ../framework/extensions/events/class-fw-extension-events.php:187
msgid "Location"
msgstr "Ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:39
msgid "Location Title"
msgstr "Título de ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:40
msgid "Set location title"
msgstr "Establecer título de ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:44
msgid "Location Description"
msgstr "Descripción de ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:45
msgid "Set location description"
msgstr "Establecer descripción de ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:49
msgid "Location Url"
msgstr "URL de ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:50
msgid "Set page url (Ex: http://example.com)"
msgstr "Establecer url de página (Ej: http://ejemplo.com)"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:53
msgid "Location Image"
msgstr "Imagen de ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:54
msgid "Add location image"
msgstr "Añadir imagen de ubicación"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:113
msgid "No location provider specified for map shortcode"
msgstr "No hay proveedor de ubicación especificado en el shortcode mapa"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:115
msgid "Map Placeholder"
msgstr "Marcador (Placeholder) en mapa"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:122
#, php-format
msgid "Unknown location provider \"%s\" specified for map shortcode"
msgstr "Proveedor de ubicación desconocido \"%s\" especificado en shortcode mapa"

#: ../framework/extensions/shortcodes/shortcodes/map/config.php:8
msgid "Map"
msgstr "Mapa"

#: ../framework/extensions/shortcodes/shortcodes/map/config.php:9
msgid "Add a Map"
msgstr "Agregar un mapa"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:13
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:214
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:473
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:11
msgid "Population Method"
msgstr "Método de agrupación"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:14
msgid "Select map population method (Ex: events, custom)"
msgstr "Seleccione método de agrupación (Ej: eventos, personalizado)"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:24
msgid "Map Type"
msgstr "Tipo de mapa"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:25
msgid "Select map type"
msgstr "Seleccione tipo de mapa"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:27
msgid "Roadmap"
msgstr "Mapa vial"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:28
msgid "Terrain"
msgstr "Terreno"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:29
msgid "Satellite"
msgstr "télite"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:30
msgid "Hybrid"
msgstr "Híbrido"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:34
msgid "Map Height"
msgstr "Alto de mapa"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:35
msgid "Set map height (Ex: 300)"
msgstr "Establecer alto de mapa (Ej: 300)"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:41
msgid "Disable zoom on scroll"
msgstr "Desactivar acercamiento al hacer scroll"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:42
msgid "Prevent the map from zooming when scrolling until clicking on the map"
msgstr "Previene el acercamiento cuando hace scroll hasta que haga click en el mapa"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:76
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:65
msgid "Column"
msgstr "Columna"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:120
#, php-format
msgid "Add a %s column"
msgstr "Agregar una %s columna"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:12
msgid "Columns"
msgstr "Columnas"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:47
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:47
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:51
msgid "No Templates Saved"
msgstr "No hay plantillas guardadas"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:50
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:50
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:54
msgid "Load Template"
msgstr "Cargar plantilla"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:91
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:91
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:94
msgid "Template Name"
msgstr "Nombre de plantilla"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:92
msgid "Save Column"
msgstr "Guardar columna"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:93
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:93
msgid "Save as Template"
msgstr "Guardar como plantilla"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:174
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:174
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:206
msgid "No Title"
msgstr "Sin título"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:6
msgid "Special Heading"
msgstr "Título de encabezado especial"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:7
msgid "Add a Special Heading"
msgstr "Agregar un título de encabezado especial"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:8
msgid "Heading Title"
msgstr "Título de encabezado"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:9
msgid "Write the heading title content"
msgstr "Escriba el contenido del encabezado"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:13
msgid "Heading Subtitle"
msgstr "Subtítulo de encabezado"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:14
msgid "Write the heading subtitle content"
msgstr "Escriba el contenido del subtítulo de encabezado"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:18
msgid "Heading Size"
msgstr "Tamaño de encabezado"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:30
msgid "Centered"
msgstr "Centrado"

#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:8
msgid "Team Member"
msgstr "Miembro de equipo"

#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:9
msgid "Add a Team Member"
msgstr "Agregar mienbro de equipo"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:7
msgid "Team Member Image"
msgstr "Imagen de miembro"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:12
msgid "Team Member Name"
msgstr "Nombre de miembro"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:13
msgid "Name of the person"
msgstr "Nombre de la persona"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:18
msgid "Team Member Job Title"
msgstr "Cargo de miembro"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:19
msgid "Job title of the person."
msgstr "Cargo de la persona"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:24
msgid "Team Member Description"
msgstr "Descripción de miembro"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:25
msgid "Enter a few words that describe the person"
msgstr "Ingrese algunas palabras para describir a la persona"

#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:6
#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:8
#: ../framework/extensions/megamenu/class-fw-extension-megamenu.php:115
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:188
msgid "Icon"
msgstr "Icono"

#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:7
msgid "Add an Icon"
msgstr "Agregar Icono"

#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:13
msgid "Icon title"
msgstr "Título de icono"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:6
msgid "Icon Box"
msgstr "Icono con caja"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:7
msgid "Add an Icon Box"
msgstr "Agregar una caja con icono"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:8
msgid "Box Style"
msgstr "Estilo de Caja"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:10
msgid "Icon above title"
msgstr "Icono sobre el título"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:11
msgid "Icon in line with title"
msgstr "Icono en línea con el título"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:16
msgid "Choose an Icon"
msgstr "Escoger un Icono"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:20
msgid "Title of the Box"
msgstr "Título de la caja"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:25
msgid "Enter the desired content"
msgstr "Ingrese el contenido deseado"

#: ../framework/extensions/shortcodes/shortcodes/button/config.php:9
msgid "Add a Button"
msgstr "Agregar botón"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:7
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:17
msgid "Button Label"
msgstr "Etiqueta de botón"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:18
msgid "This is the text that appears on your button"
msgstr "Este es el texto que aparece en su botón"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:23
msgid "Button Link"
msgstr "Link de botón"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:14
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:24
msgid "Where should your button link to"
msgstr "Lugar dónde direcciona el link de botón"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:20
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:38
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:30
msgid "Open Link in New Window"
msgstr "Abrir en nueva ventana"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:21
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:39
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:31
msgid "Select here if you want to open the linked page in a new window"
msgstr "Seleccione aquí si usted quiere abrir link en una nueva ventana"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:32
msgid "Button Color"
msgstr "Color de botón"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:33
msgid "Choose a color for your button"
msgstr "Escoger color de botón"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:36
msgid "Default"
msgstr "Por defecto"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:37
msgid "Black"
msgstr "Negro"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:38
msgid "Blue"
msgstr "Azul"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:39
msgid "Green"
msgstr "Verde"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:40
msgid "Red"
msgstr "Rojo"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:6
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:108
msgid "Video"
msgstr "Video"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:7
msgid "Add a Video"
msgstr "Agregar video"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:8
#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:10
msgid "Media Elements"
msgstr "Elementos multimedia"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:8
msgid "Insert Video URL"
msgstr "Inserte URL del video"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:9
#: ../framework/extensions/shortcodes/shortcodes/section/options.php:24
msgid "Insert Video URL to embed this video"
msgstr "Inserte URL para adjuntar este video"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:13
msgid "Video Width"
msgstr "Ancho del video"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:14
msgid "Enter a value for the width"
msgstr "Ingrese el valor del ancho"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:19
msgid "Video Height"
msgstr "Alto del video"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:20
msgid "Enter a value for the height"
msgstr "Ingrese el valor para el alto"

#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:8
#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:16
msgid "Calendar"
msgstr "Calendario"

#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:9
msgid "Add a Calendar"
msgstr "Agregar Calendario"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:14
msgid "Select calendar population method (Ex: events, custom)"
msgstr "Seleccione método de agrupación del calendario (Ej: eventos, personalizado)"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:23
msgid "Calendar Type"
msgstr "Tipo de calendario"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:24
msgid "Select calendar type"
msgstr "Seleccione tipo de calendario"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:27
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:23
msgid "Daily"
msgstr "Diario"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:28
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:22
msgid "Weekly"
msgstr "Semanal"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:29
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:21
msgid "Monthly"
msgstr "Mensual"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:33
msgid "Start Week On"
msgstr "La semana empieza el "

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:34
msgid "Select first day of week"
msgstr "Seleccione primer día de la semana"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:37
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:110
msgid "Monday"
msgstr "Lunes"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:38
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:109
msgid "Sunday"
msgstr "Domingo"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:29
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:56
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:82
#: ../framework/extensions/events/class-fw-extension-events.php:69
#: ../framework/extensions/events/class-fw-extension-events.php:74
#: ../framework/extensions/events/manifest.php:7
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:77
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:185
#: ../framework/core/components/extensions/manager/available-extensions.php:180
msgid "Events"
msgstr "Eventos"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:30
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:59
msgid "Add/Edit Date & Time"
msgstr "Agregar/Editar fecha y evento"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:33
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:63
msgid "Note: Please set start & end event datetime"
msgstr "Nota: Por favor seleccione fecha de inicio y fin del evento"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:37
msgid "Event Title"
msgstr "Título del evento"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:38
msgid "Enter the event title"
msgstr "Ingrese el título del evento"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:42
msgid "Event URL"
msgstr "URL del evento"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:43
msgid "Enter the event URL (Ex: http://your-domain.com/event)"
msgstr "Ingrese la URL del evento (Ej: http://ejemplo.com/evento)"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:47
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:58
msgid "Date & Time"
msgstr "Fecha y hora"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:48
msgid "Enter the event date & time"
msgstr "Ingrese la fecha y hora del evento"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:129
msgid "No events provider specified for calendar shortcode"
msgstr "No existen eventos especificados para el shortcode calendario"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:138
#, php-format
msgid "Unknown events provider \"%s\" specified for calendar shortcode"
msgstr "Eventos desconocidos especificados \"%s\" para el shortcode calendario"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:55
#: ../framework/extensions/events/class-fw-extension-events.php:68
#: ../framework/extensions/events/class-fw-extension-events.php:75
msgid "Event"
msgstr "Evento"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:57
#: ../framework/extensions/shortcodes/shortcodes/calendar/views/view.php:23
msgid "Today"
msgstr "Hoy"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:66
#, php-format
msgid "Calendar: View %s not found"
msgstr "Calendario: No se encuentra %s"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:67
#, php-format
msgid "Calendar: Wrong date format %s. Should be either \"now\" or \"yyyy-mm-dd\""
msgstr "Calendario: Formato de fecha incorrecto %s. Debe ser \"ahora\" o \"aaaa-mm-dd\""

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:69
msgid "Calendar: Event URL is not set"
msgstr "Calendario: URL de evento no establecida"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:70
#, php-format
msgid ""
"Calendar: Wrong navigation direction %s. Can be only \"next\" or \"prev\" or"
" \"today\""
msgstr "Calendario: dirección de explroación incorrecta %s. Debe ser solo \"Siguiente\", \"Anterior\" u \"Hoy\""

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:72
msgid ""
"Calendar: Time split parameter should divide 60 without decimals. Something "
"like 10, 15, 30"
msgstr "Calendario: El parámetro de división debe dividir 60 sin decimales. Algo como 10, 15, 30"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:74
msgid "No events in this day."
msgstr "No hay eventos este día."

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:77
#, php-format
msgid "week %s of %s"
msgstr "semana %s de %s"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:79
msgid "Week "
msgstr "Semana"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:80
msgid "All day"
msgstr "Todo el día"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:81
msgid "Time"
msgstr "Hora"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:83
msgid "Ends before timeline"
msgstr "Termina antes de cronograma"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:84
msgid "Starts after timeline"
msgstr "Inicia después de cronograma"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:85
msgid "January"
msgstr "Enero"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:86
msgid "February"
msgstr "Febrero"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:87
msgid "March"
msgstr "Marzo"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:88
msgid "April"
msgstr "Abril"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:89
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:101
msgid "May"
msgstr "Mayo"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:90
msgid "June"
msgstr "Junio"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:91
msgid "July"
msgstr "Julio"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:92
msgid "August"
msgstr "Agosto"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:93
msgid "September"
msgstr "Septiembre"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:94
msgid "October"
msgstr "Octubre"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:95
msgid "November"
msgstr "Noviembre"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:96
msgid "December"
msgstr "Diciembre"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:97
msgid "Jan"
msgstr "Ene"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:98
msgid "Feb"
msgstr "Feb"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:99
msgid "Mar"
msgstr "Mar"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:100
msgid "Apr"
msgstr "Abr"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:102
msgid "Jun"
msgstr "Jun"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:103
msgid "Jul"
msgstr "Jul"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:104
msgid "Aug"
msgstr "Ago"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:105
msgid "Sep"
msgstr "Sep"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:106
msgid "Oct"
msgstr "Oct"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:107
msgid "Nov"
msgstr "Nov"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:108
msgid "Dec"
msgstr "Dic"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:111
msgid "Tuesday"
msgstr "Martes"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:112
msgid "Wednesday"
msgstr "Miércoles"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:113
msgid "Thursday"
msgstr "Jueves"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:114
msgid "Friday"
msgstr "Viernes"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:115
msgid "Saturday"
msgstr "Sábado"

#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:7
msgid "Add an Image"
msgstr "Agregar Imagen"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:8
msgid "Choose Image"
msgstr "Escoger imagen"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:16
msgid "Width"
msgstr "Ancho"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:17
msgid "Set image width"
msgstr "Establecer ancho de imagen"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:22
#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:24
msgid "Height"
msgstr "Alto"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:23
msgid "Set image height"
msgstr "Establecer alto de imagen"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:33
msgid "Image Link"
msgstr "Link de imagen"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:34
msgid "Where should your image link to?"
msgstr "Dónde direccionará su imagen?"

#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:8
msgid "Notification"
msgstr "Notificación"

#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:9
msgid "Add a Notification Box"
msgstr "Agregar caja de notificación"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:7
msgid "Message"
msgstr "Mensaje"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:8
msgid "Notification message"
msgstr "Notificación de mensaje"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:10
msgid "Message!"
msgstr "Mensaje!"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:13
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:444
msgid "Type"
msgstr "Tipo"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:14
msgid "Notification type"
msgstr "Tipo de notificación"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:17
msgid "Congratulations"
msgstr "Felicitación"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:18
msgid "Information"
msgstr "Información"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:19
msgid "Alert"
msgstr "Alerta"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:20
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:456
msgid "Error"
msgstr "Error"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:9
msgid "Congratulations!"
msgstr "Felicitaciones!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:13
msgid "Information!"
msgstr "Información!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:17
msgid "Alert!"
msgstr "Alerta!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:21
msgid "Error!"
msgstr "Error!"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:8
msgid "Widget Area"
msgstr "Área de widget"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:9
msgid "Add a Widget Area"
msgstr "Agregar área de widget"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/options.php:5
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:5
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:7
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:40
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:91
msgid "Sidebar"
msgstr "Barra lateral (Sidebar)"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:8
msgid "Call To Action"
msgstr "Llamar a la acción"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:9
msgid "Add a Call to Action"
msgstr "Agregar llamar a la acción"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:9
msgid "This can be left blank"
msgstr "Esto se puede dejar en blanco"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:14
msgid "Enter some content for this Info Box"
msgstr "Ingrese algún contenido para esta caja de información"

#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:8
msgid "Text Block"
msgstr "Bloque de texto"

#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:9
msgid "Add a Text Block"
msgstr "Agregar bloque de texto"

#: ../framework/extensions/shortcodes/shortcodes/text-block/options.php:11
msgid "Enter some content for this texblock"
msgstr "Ingrese algún contenido para este bloque de texto"

#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:6
msgid "Divider"
msgstr "Separador"

#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:7
msgid "Add a Divider"
msgstr "Agregar separador"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:13
msgid "Ruler Type"
msgstr "Tipo de regla"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:14
msgid "Here you can set the styling and size of the HR element"
msgstr "Aquí usted puede establecer el estilo y alto de su elemento HR (separador)"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:16
msgid "Line"
msgstr "Línea"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:17
msgid "Whitespace"
msgstr "Espacio en blanco"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:25
msgid ""
"How much whitespace do you need? Enter a pixel value. Positive value will "
"increase the whitespace, negative value will reduce it. eg: '50', '-25', "
"'200'"
msgstr "Cuánto espacio necesita? ingrese un valor en pixeles. Valor positivo incrementa el espacio, valor negativo disminuye espacio. Ej: '50', '-25', '200'"

#: ../framework/extensions/shortcodes/shortcodes/section/config.php:6
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:23
msgid "Section"
msgstr "Sección"

#: ../framework/extensions/shortcodes/shortcodes/section/config.php:7
msgid "Add a Section"
msgstr "Agregar sección"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:7
msgid "Full Width"
msgstr "Ancho fluido "

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:11
msgid "Background Color"
msgstr "Color de fondo"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:12
msgid "Please select the background color"
msgstr "Seleccione color de fondo"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:16
msgid "Background Image"
msgstr "Imagen de fondo"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:17
msgid "Please select the background image"
msgstr "Seleccione imagen de fondo"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:23
msgid "Background Video"
msgstr "Video de fondo"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:12
msgid "Sections"
msgstr "Secciones"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:92
msgid "Save Section"
msgstr "Guardar sección"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:24
msgid "Creates a section"
msgstr "Crear una sección"

#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:9
msgid "Add some Tabs"
msgstr "Agregar algunas pestañas"

#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:9
msgid "Add/Edit Tab"
msgstr "Agregar/Editar Pestaña"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcode.php:194
#, php-format
msgid "No default view (views/view.php) found for shortcode: %s"
msgstr "No hay vista por defecto encontrada (views/view.php) para el shortcode: %s"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:203
#, php-format
msgid "Shortcode \"%s\" from %s was already defined at %s"
msgstr "Shortcode \"%s\" de %s ya ha se ha definido en %s"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:264
#, php-format
msgid "Class file found for shortcode %s but no class %s found"
msgstr "Archivo Class encontrado para shortcode %s pero no se ha definido la class %s"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:269
#, php-format
msgid "The class %s must extend from FW_Shortcode"
msgstr "La class %s debe ser extendida desde FW_Shortcode"

#: ../framework/extensions/builder/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:204
msgid "Builder"
msgstr "Constructor"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:306
msgid "Full Screen"
msgstr "Pantalla Completa"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:307
msgid "Exit Full Screen"
msgstr "Salir de pantalla completa"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:334
msgid "Undo"
msgstr "Deshacer"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:335
msgid "Redo"
msgstr "Rehacer"

#: ../framework/extensions/builder/includes/option-types/builder/includes/fullscreen.php:81
msgid "Preview Changes"
msgstr "Previsualizar Cambios"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/class-fw-ext-builder-templates.php:119
msgid "Templates"
msgstr "Plantillas"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:12
msgid "Full Templates"
msgstr "Plantillas Completas"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:61
msgid "Save Full Template"
msgstr "Guardar Plantilla Completa"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:95
msgid "Save Builder Template"
msgstr "Guardar Plantilla Constructor"

#: ../framework/extensions/social/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:252
msgid "Social"
msgstr "Social"

#: ../framework/extensions/social/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:253
msgid ""
"Use this extension to configure all your social related APIs. Other "
"extensions will use the Social extension to connect to your social accounts."
msgstr "Utilice esta extensión para configurar sus APIs de redes sociales. Otras extensiones pueden ser utilizadas para conectar con sus cuentas sociales"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:62
#: ../framework/core/components/backend.php:584
msgid "Facebook"
msgstr "Facebook"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:66
#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:33
msgid "API Settings"
msgstr "Configuración API"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:72
msgid "App ID/API Key:"
msgstr "App ID/API Key:"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:73
msgid "Enter Facebook App ID / API Key."
msgstr "Ingrese su App ID/API Key de Facebook:"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:78
msgid "App Secret:"
msgstr "APP secreto:"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:79
msgid "Enter Facebook App Secret."
msgstr "Ingrese su secreto de APP Facebok."

#: ../framework/extensions/social/extensions/social-facebook/manifest.php:7
#: ../framework/extensions/social/extensions/social-facebook/manifest.php:8
msgid "Social Facebook"
msgstr "Facebook Social"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:29
#: ../framework/core/components/backend.php:592
msgid "Twitter"
msgstr "Twitter"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:39
msgid "Consumer Key"
msgstr "Clave de cliente"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:40
msgid "Enter Twitter Consumer Key."
msgstr "Ingrese su clave de cliente de Twitter."

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:45
msgid "Consumer Secret"
msgstr "Secret Cliente"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:46
msgid "Enter Twitter App Secret."
msgstr "ingrese su APP secreto de Twitter"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:51
msgid "Access Token"
msgstr "Token de acceso"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:52
msgid "Enter Twitter Access Token."
msgstr "Ingrese su Token de acceso de Twitter."

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:57
msgid "Access Token Secret"
msgstr "Token de acceso secreto"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:58
msgid "Enter Twitter Access Token Secret."
msgstr "Ingrese el token secreto de acceso ."

#: ../framework/extensions/social/extensions/social-twitter/manifest.php:7
#: ../framework/extensions/social/extensions/social-twitter/manifest.php:8
msgid "Social Twitter"
msgstr "Twitter Social"

#: ../framework/extensions/forms/class-fw-extension-forms.php:112
#: ../framework/extensions/forms/class-fw-extension-forms.php:123
#: ../framework/extensions/forms/class-fw-extension-forms.php:131
#: ../framework/extensions/forms/class-fw-extension-forms.php:142
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:102
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:114
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:124
msgid "Unable to process the form"
msgstr "Incapaz de procesar este formulario"

#: ../framework/extensions/forms/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:228
msgid "Forms"
msgstr "Formulario"

#: ../framework/extensions/forms/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:229
msgid ""
"This extension adds the possibility to create a contact form. Use the drag &"
" drop form builder to create any contact form you'll ever want or need."
msgstr "Esta extensión añade la posibilidad de crear un formulario de contacto. Utilice el constructor \"arrastrar y soltar\" para crear el formulario de contacto que quiera o necesite."

#: ../framework/extensions/forms/extensions/contact-forms/manifest.php:5
msgid "Contact Forms"
msgstr "Formularios de Contacto"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:134
msgid "Invalid destination email (please contact the site administrator)"
msgstr "Dirección de envío inválida (Contacte al administrador del sitio)"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:158
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:77
msgid "Message sent!"
msgstr "Mensaje enviado!"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:164
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:85
msgid "Oops something went wrong."
msgstr "Oops ha ocurrido algo mal."

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:190
msgid "Please configure the {mailer_link} extension."
msgstr "Por favor configure la extensión {mailer_link}."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:8
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:107
msgid "Contact form"
msgstr "Formulario de contacto"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:9
msgid "Build contact forms"
msgstr "Construir formularios de contacto"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:15
msgid "Form Fields"
msgstr "Campos de formularios"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:39
#: ../framework/core/components/extensions/manager/views/extension.php:82
#: ../framework/core/components/extensions/manager/views/extension-page-header.php:31
msgid "Settings"
msgstr "Configuraciones"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:42
msgid "Options"
msgstr "Opciones"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:53
msgid "Subject Message"
msgstr "Asunto del mensaje"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:54
msgid "This text will be used as subject message for the email"
msgstr "Este texto es utilizado como asunto del mensaje en el email"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:55
msgid "New message"
msgstr "Nuevo mensaje"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:64
msgid "Submit Button"
msgstr "Botón Enviar"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:65
msgid "This text will appear in submit button"
msgstr "Este texto aparece en el botón enviar"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:66
msgid "Send"
msgstr "Enviar"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:75
msgid "Success Message"
msgstr "Mensaje de éxito"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:76
msgid "This text will be displayed when the form will successfully send"
msgstr "Este texto se muestra cuando el formulario se ha enviado exitosamente"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:83
msgid "Failure Message"
msgstr "Mensaje de fallo"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:84
msgid "This text will be displayed when the form will fail to be sent"
msgstr "Este texto se muestra cuando el formulario no e ha enviado"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:94
msgid "Email To"
msgstr "Email a"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:95
msgid "We recommend you to use an email that you verify often"
msgstr "Recomendamos el uso de un correo electrónico que verifique frecuentemente"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:96
msgid "The form will be sent to this email address."
msgstr "El formulario será enviado a esta dirección de email."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:52
msgid "Contact Form"
msgstr "Formulario de Contacto"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:54
msgid "Configure Mailer"
msgstr "Configurar Mailer"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:108
msgid "Add a Contact Form"
msgstr "Agregar Formulario de Contacto"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:9
msgid "Note that the type can't be changed later."
msgstr "Tenga en cuenta que el tipo no se puede cambiar más adelante."

#: ../framework/extensions/forms/views/backend/submit-box-add.php:11
msgid ""
"You will need to create a new form in order to have a different form type."
msgstr "Usted tendrá que crear un nuevo formulario con el fin de tener un tipo diferente de formulario."

#: ../framework/extensions/forms/views/backend/submit-box-add.php:20
#: ../framework/extensions/forms/views/backend/submit-box-edit.php:16
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:14
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:31
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-error.php:8
msgid "Delete Permanently"
msgstr "Eliminar de forma permanente"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:22
#: ../framework/extensions/forms/views/backend/submit-box-edit.php:18
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:16
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:33
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-error.php:10
msgid "Move to Trash"
msgstr "Mover a la papelera"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:33
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:35
msgid "Create"
msgstr "Crear"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:24
msgid "Add a Recaptcha field"
msgstr "Agregar campo Recaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:26
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:78
msgid "Recaptcha"
msgstr "Recaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:59
msgid "Set site key"
msgstr "Establecer clave de sitio"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:60
msgid "Set secret key"
msgstr "Establecer clave secreta"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:76
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:81
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:81
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:76
msgid "Enter field label (it will be displayed on the web site)"
msgstr "Ingrese el valor de etiqueta (Este es mostrado en el sitio web)"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:133
msgid "Security Code"
msgstr "Código de seguridad"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:147
msgid "Could not validate the form"
msgstr "No es posible validar el formulario"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:148
msgid "Please fill the recaptcha"
msgstr "Por favor complete el recaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:37
msgid "Site key"
msgstr "Clave de sitio"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:38
msgid "Your website key. More on how to configure ReCaptcha"
msgstr "Su clave de sitio. Mas en cómo configurar ReCaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:42
msgid "Secret key"
msgstr "Clave secreta"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:43
msgid "Your secret key. More on how to configure ReCaptcha"
msgstr "Su clave secreta. Mas en cómo configurar ReCaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:18
msgid "Add a Paragraph Text"
msgstr "Agregar párrafo de texto"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:20
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:48
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:77
msgid "Paragraph Text"
msgstr "Párrafo de texto"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:49
msgid "Toggle mandatory field"
msgstr "Activar campo obligatorio"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:83
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:83
msgid "Mandatory Field"
msgstr "Campo Obligatorio"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:84
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:84
msgid "Make this field mandatory?"
msgstr "Hacer este campo obligatorio?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:98
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:98
msgid "Placeholder"
msgstr "Marcador (Placeholder)"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:99
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:99
msgid "This text will be used as field placeholder"
msgstr "Este texto es utilizado como marca en el campo."

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:105
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:107
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:107
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:105
msgid "Default Value"
msgstr "Valor por defecto"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:106
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:108
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:108
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:106
msgid "This text will be used as field default value"
msgstr "Este texto es utilizado como valor por defecto"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:126
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:128
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:126
msgid "Restrictions"
msgstr "Restricciones"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:127
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:127
msgid "Set characters or words restrictions for this field"
msgstr "Establecer caracteres o palabras restringidas en este campo"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:131
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:131
msgid "Characters"
msgstr "Caracteres"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:132
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:132
msgid "Words"
msgstr "Palabras"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:140
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:154
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:142
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:156
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:140
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:154
msgid "Min"
msgstr "Min"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:141
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:155
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:143
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:157
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:141
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:155
msgid "Minim value"
msgstr "Valor Mínimo"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:146
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:160
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:148
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:162
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:146
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:160
msgid "Max"
msgstr "Max"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:147
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:161
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:149
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:163
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:147
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:161
msgid "Maxim value"
msgstr "Valor Máximo"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:178
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:180
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:121
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:137
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:137
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:114
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:121
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:178
msgid "Instructions for Users"
msgstr "Instrucciones para usuarios"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:179
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:181
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:122
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:138
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:138
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:115
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:122
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:179
msgid "The users will see these instructions in the tooltip near the field"
msgstr "Los usuarios verán estas instrucciones en el tooltip junto al campo"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:331
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:368
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:230
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:244
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:244
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:210
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:217
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:332
msgid "The {label} field is required"
msgstr "El campo {label} es requerido"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:336
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:337
#, php-format
msgid "The {label} field must contain minimum %d character"
msgstr "El campo {label} debe contener mínimo %d caracter"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:341
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:342
#, php-format
msgid "The {label} field must contain minimum %d characters"
msgstr "El campo {label} debe contener mínimo %d caracteres"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:346
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:347
#, php-format
msgid "The {label} field must contain maximum %d character"
msgstr "El campo {label} debe contener máximo %d caracter"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:351
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:352
#, php-format
msgid "The {label} field must contain maximum %d characters"
msgstr "El campo {label} debe contener máximo %d caracteres"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:356
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:357
#, php-format
msgid "The {label} field must contain minimum %d word"
msgstr "El campo {label} debe contener mínimo %d palabra"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:361
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:362
#, php-format
msgid "The {label} field must contain minimum %d words"
msgstr "El campo {label} debe contener mínimo %d palabras"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:366
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:367
#, php-format
msgid "The {label} field must contain maximum %d word"
msgstr "El campo {label} debe contener máximo %d palabra"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:371
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:372
#, php-format
msgid "The {label} field must contain maximum %d words"
msgstr "El campo {label} debe contener máximo %d palabras"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:20
msgid "Add a Number field"
msgstr "Agregar campo numérico"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:79
msgid "Number"
msgstr "Número"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:129
msgid "Set digits or values restrictions of this field"
msgstr "Establecer dígitos o valores restringidos en este campo"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:133
msgid "Digits"
msgstr "Digitos"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:134
msgid "Value"
msgstr "Valores"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:363
msgid "The {label} field must be a valid number"
msgstr "El campo {label} debe ser un número válido"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:373
#, php-format
msgid "The {label} field must have minimum %d digit"
msgstr "El campo {label} debe contener mínimo %d dígito"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:378
#, php-format
msgid "The {label} field must have minimum %d digits"
msgstr "El campo {label} debe contener mínimo %d dígitos"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:383
#, php-format
msgid "The {label} field must have maximum %d digit"
msgstr "El campo {label} debe contener máximo %d dígito"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:388
#, php-format
msgid "The {label} field must have maximum %d digits"
msgstr "El campo {label} debe contener máximo %d dígitos"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:393
#, php-format
msgid "The {label} field minimum value must be %s"
msgstr "El campo {label} debe ser valor mínimo de %s"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:398
#, php-format
msgid "The {label} field maximum value must be %s"
msgstr "El campo {label} debe ser valor máximo de %s"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:18
msgid "Add a Dropdown"
msgstr "Agregar Lista (Dropdown)"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:79
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:164
msgid "Dropdown"
msgstr "Lista (Dropdown)"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:103
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:103
msgid "Choices"
msgstr "Opciones"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:104
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:104
msgid "Add choice"
msgstr "Agregar opción"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:110
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:113
msgid "Randomize"
msgstr "Aleatorio"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:111
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:114
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:114
msgid "Do you want choices to be displayed in random order?"
msgstr "Quiere que las opciones sean mostradas en orden aleatorio?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:235
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:249
msgid "{label}: Submitted data contains not existing choice"
msgstr "{label}: Datos enviados contienen opción inexistente"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:38
msgid "Edit Title"
msgstr "Editar título"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:39
msgid "Edit Subtitle"
msgstr "Editar Subtítulo"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:57
msgid "The title will be displayed on contact form header"
msgstr "Este título es mostrado en el encabezado de formulario"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:62
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:7
msgid "Subtitle"
msgstr "Subtítulo"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:63
msgid "The form header subtitle text"
msgstr "Subtítulo en encabezado de formulario"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:18
msgid "Add a Single Choice field"
msgstr "Agregar un campo de elección única"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:56
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:56
msgid "{x} More"
msgstr "{x} Más"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:113
msgid "Randomize?"
msgstr "Aleatorio?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:124
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:124
msgid "Field Layout"
msgstr "Diseño de campo"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:125
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:125
msgid "Select choice display layout"
msgstr "Seleccione opción de diseño"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:127
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:127
msgid "One column"
msgstr "Una columna"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:128
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:128
msgid "Two columns"
msgstr "Dos columnas"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:129
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:129
msgid "Three columns"
msgstr "Tres columnas"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:130
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:130
msgid "Side by side"
msgstr "Lado a lado"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:18
msgid "Add a Multiple Choices field"
msgstr "Agregar campo de elecciones múltiples"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:249
msgid "{label}: Submitted data contains not existing choices"
msgstr "{label}: Datos enviados contienen opciones inexistentes"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:18
msgid "Add an Email field"
msgstr "Agregar campo de Email"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:79
msgid "Email"
msgstr "Email"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:215
msgid "The {label} field must contain a valid email"
msgstr "Campo {label} debe contener una dirección de email válida"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:18
msgid "Add a Website field"
msgstr "Agregar campo sitio web"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:79
msgid "Website"
msgstr "Sitio Web"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:222
msgid "The {label} field must be a valid website name"
msgstr "Campo {label} debe tener un sitio web válido"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:18
msgid "Add a Single Line Text"
msgstr "Agregar campo de texto de una línea"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:20
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:47
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:77
msgid "Single Line Text"
msgstr "Campo de Texto de una línea"

#: ../framework/extensions/breadcrumbs/settings-options.php:17
msgid "Text for Homepage"
msgstr "Texto para página de inicio"

#: ../framework/extensions/breadcrumbs/settings-options.php:18
msgid "The homepage anchor will have this text"
msgstr "La etiqueta de página de inicio tendrá este texto"

#: ../framework/extensions/breadcrumbs/settings-options.php:23
msgid "Text for Blog Page"
msgstr "Texto para página de blog"

#: ../framework/extensions/breadcrumbs/settings-options.php:24
msgid ""
"The blog page anchor will have this text. In case homepage will be set as "
"blog page, will be taken the homepage text"
msgstr "La etiqueta de página de blog tendrá este texto. En caso de que la página de inicio esté configurada como página de blog (entradas), tomará el texto de página de inicio"

#: ../framework/extensions/breadcrumbs/settings-options.php:29
msgid "Text for 404 Page"
msgstr "Texto para página 404"

#: ../framework/extensions/breadcrumbs/settings-options.php:30
msgid "The 404 anchor will have this text"
msgstr "La etiqueta de página de error 404 tendrá este texto"

#: ../framework/extensions/breadcrumbs/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:108
msgid "Breadcrumbs"
msgstr "Migas de pan (Breadcrumbs)"

#: ../framework/extensions/breadcrumbs/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:109
msgid ""
"Creates a simplified navigation menu for the pages that can be placed "
"anywhere in the theme. This will make navigating the website much easier."
msgstr "Crea un menu de navegación simplificado para páginas que puede ser ubicado en cualquier lugar de un tema. Esto conlleva a una navegación mas sencilla."

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:11
msgid "404 Not found"
msgstr "404 No se encuentra"

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:124
msgid "Searching for:"
msgstr "Búsqueda para: "

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:248
#: ../framework/includes/option-types/multi-select/class-fw-option-type-multi-select.php:472
msgid "No title"
msgstr "(sin título)"

#: ../framework/extensions/events/class-fw-extension-events.php:89
msgid "Create a event item"
msgstr "Crear un item evento"

#: ../framework/extensions/events/class-fw-extension-events.php:186
msgid "Date"
msgstr "Fecha"

#: ../framework/extensions/events/class-fw-extension-events.php:204
msgid "Event Options"
msgstr "Opciones de eventp"

#: ../framework/extensions/events/class-fw-extension-events.php:310
msgid "Multi Interval Event"
msgstr "Evento Multi Intervalo"

#: ../framework/extensions/events/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:181
msgid ""
"This extension adds a fully fledged Events module to your theme. It comes "
"with built in pages that contain a calendar where events can be added."
msgstr "Esta extensión añade un completo módulo para gestionar Eventos en su tema. Viene con páginas elaboradas que contienen calendario en donde cada evento es agregado."

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:81
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:189
msgid "Event Categories"
msgstr "Categorías de Evento"

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:82
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:190
msgid "Select an event category"
msgstr "Seleccione una categoría de evento"

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:84
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:192
msgid "All Events"
msgstr "Todos Los Eventos"

#: ../framework/extensions/events/extensions/events-tags/manifest.php:7
msgid "Event-search-tags"
msgstr "buscar-etiquetas-evento"

#: ../framework/extensions/events/extensions/events-tags/manifest.php:8
msgid "Connect extension event with shortcodes map & calendar"
msgstr "Conectar extensión con shortcode mapa y calendario"

#: ../framework/extensions/events/views/content.php:16
msgid "Google Calendar"
msgstr "Calendario Google"

#: ../framework/extensions/events/views/content.php:17
msgid "Ical Export"
msgstr "Exportar Ical"

#: ../framework/extensions/events/views/content.php:20
msgid "Start"
msgstr "Comienza"

#: ../framework/extensions/events/views/content.php:21
msgid "End"
msgstr "Finaliza"

#: ../framework/extensions/events/views/content.php:25
msgid "Speakers"
msgstr "Presentantes"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:37
msgid "Event Location"
msgstr "Ubicación de evento"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:39
msgid "Where does the event take place?"
msgstr "Dónde tendrá lugar el evento?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:43
msgid "All Day Event?"
msgstr "Evento todo el día?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:44
msgid "Is your event an all day event?"
msgstr "Su evento dura todo el día?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:67
msgid "Start & End of Event"
msgstr "Comienzo y Fin de Evento"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:68
msgid "Set start and end events datetime"
msgstr "Establecer fecha comienzo y fin de evento"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:97
msgid "Associated User"
msgstr "Usuario asociado"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:99
msgid "Link this event to a specific user"
msgstr "Asociar este evento a usuario específico"

#: ../framework/extensions/sidebars/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:132
msgid "Sidebars"
msgstr "Barras Laterales (Sidebars)"

#: ../framework/extensions/sidebars/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:133
msgid ""
"Brings a new layer of customization freedom to your website by letting you "
"add more than one sidebar to a page, or different sidebars on different "
"pages."
msgstr "Aporta una nueva capa de personalización libre para su sitio web permitiendo agregar mas de una barra lateral (Sidebar) a su página o diferentes barras laterales en diferentes páginas."

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:131
msgid "No matches found"
msgstr "No hay resultados coincidentes"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:139
msgid "Do you realy want to change without saving?"
msgstr "Realmente quiere realizar cambios sin guardar?"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:141
msgid "Missing ID. Check that you provided all mandatory data."
msgstr "ID Incorrecto. Revise que ha ingresado todos los datos requeridos"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:142
#: ../framework/extensions/sidebars/views/backend-main-view.php:27
msgid "Created"
msgstr "Creado"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:143
msgid "(For Grouped Pages)"
msgstr "(Para Páginas Agrupadas)"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:144
msgid "(For Specific Pages)"
msgstr "(Para Páginas Específicas)"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:145
msgid "No sidebar name specified"
msgstr "Nombre de barra lateral no especificado"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:146
msgid "Sidebar Name"
msgstr "Nombre de barra lateral"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:147
msgid "New Sidebar"
msgstr "Nueva barra lateral"

#: ../framework/extensions/sidebars/views/frontend-no-widgets.php:5
msgid "Widgets Page"
msgstr "Página de widgets"

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:5
msgid "For specific"
msgstr "Para específica"

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:23
msgid "Type to search ..."
msgstr "Escriba aquí para buscar ..."

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:30
msgid "Search for a specific page you want to set a sidebar for"
msgstr "Búsqueda para una página específica para la cual quiere establecer una barra lateral"

#: ../framework/extensions/sidebars/views/backend-tab-grouped.php:6
msgid "For group"
msgstr "Para un grupo"

#: ../framework/extensions/sidebars/views/backend-tab-grouped.php:25
msgid "Select group of pages you want to set a sidebar for."
msgstr "Seleccione un grupo de páginas para las cuales quiere establecer su barra lateral."

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:18
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:60
msgid "Choose the position for your sidebar(s)"
msgstr "Escoja la posición de su(s) barra(s) lateral(es)"

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:43
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:40
msgid "Add Sidebar"
msgstr "Agregar Barra Lateral"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:4
msgid "Sidebars for"
msgstr "Barras Laterales para"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Specific Page"
msgstr "Para página específica"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Grouped Page"
msgstr "Para grupo de páginas"

#: ../framework/extensions/sidebars/views/backend-main-view.php:11
#: ../framework/extensions/sidebars/views/backend-main-view.php:15
msgid "Manage Sidebars"
msgstr "Gestionar Barras Laterales"

#: ../framework/extensions/sidebars/views/backend-main-view.php:18
msgid ""
"Use this section to create and/or set different sidebar(s) for different "
"page(s)"
msgstr "Utilice esta sección para crear y/o establecer diferentes barras laterales para diferentes páginas"

#: ../framework/extensions/sidebars/views/backend-main-view.php:24
msgid "For Grouped Pages"
msgstr "Para grupo de páginas"

#: ../framework/extensions/sidebars/views/backend-main-view.php:25
msgid "For Specific Pages"
msgstr "Para página específica"

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:32
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:121
msgid "Select sidebar you wish to replace."
msgstr "Seleccione barra lateral que quiere reemplazar"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:241
msgid "No sidebar name specified."
msgstr "Nombre de barra lateral no especificado"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:270
msgid "Dynamic sidebar doesn't exixt"
msgstr "Barra lateral dinámica no existe"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:277
msgid ""
"The placeholder can't be deleted because it is used in one of sidebars "
"below."
msgstr "El marcador no puede ser eliminado porque es utilizado en una de las barras laterales de abajo."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:280
msgid ""
"Please replace it first so that you will not have visual gaps in your "
"layout."
msgstr "Por favor primero reemplace y de este modo usted no tendrá deficiencias visuales en su diseño."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:293
msgid "Successfully removed"
msgstr "Eliminado exitosamente"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:543
msgid "Default for all pages"
msgstr "Por defecto para todas las páginas"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:613
msgid " (no title)"
msgstr "(sin título)"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:780
msgid "Error: Type or sub_type error"
msgstr "Error: Error en tipo o subtipo"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:838
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:869
msgid "Error: Sidebars not set"
msgstr "Error: Barras laterales no establecidas"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:865
msgid "Error: Position doesn't exists. Please check config file."
msgstr "Error: Posición inexistente. Por favor revise el archivo de configuración."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:55
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:253
msgid "Page"
msgstr "Página"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:56
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:153
msgid "Pages"
msgstr "Páginas"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:61
msgid "Portfolio Project"
msgstr "Proyecto en Portafolios"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:62
msgid "Portfolio Projects"
msgstr "Proyectos en Portafolios"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:73
msgid "Blog Category"
msgstr "Categoría del Blog"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:79
msgid "Portfolio Category"
msgstr "Categoría de Portafolios"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:85
msgid "Home Page"
msgstr "Página de Inicio"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:90
msgid "Search Page"
msgstr "Página de Búsqueda"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:95
msgid "404 Page"
msgstr "Página 404"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:100
msgid "Author Page"
msgstr "Página de Autor"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:105
msgid "Archive Page"
msgstr "Página de Archivo"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:149
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:308
msgid "All Pages"
msgstr "Todas las Páginas"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:184
msgid "Others"
msgstr "Otras"

#: ../framework/extensions/megamenu/manifest.php:7
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:64
#: ../framework/core/components/extensions/manager/available-extensions.php:60
msgid "Mega Menu"
msgstr "Mega Menú"

#: ../framework/extensions/megamenu/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:61
msgid ""
"The Mega Menu extension adds a user-friendly drop down menu that will let "
"you easily create highly customized menu configurations."
msgstr "La extensión Mega Menú añade un menú desplegable fácil de utilizar que le permitirá crear de manera fácil configuraciones altamente personalizadas de menú."

#: ../framework/extensions/megamenu/class-fw-extension-megamenu.php:58
msgid "Select Icon"
msgstr "Seleccionar Icono"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:44
#, php-format
msgid "%s (Invalid)"
msgstr "%s (No válido)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:48
#, php-format
msgid "%s (Pending)"
msgstr "%s (Pendiente)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:61
msgid "sub item"
msgstr "sub item"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:97
msgid "Edit Menu Item"
msgstr "Editar Item de Menú"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:108
msgid "URL"
msgstr "URL"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:117
msgid "Navigation Label"
msgstr "Etiqueta de navegación"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:125
msgid "Title Attribute"
msgstr "Atributo de título"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:134
msgid "Open link in a new window/tab"
msgstr "Abrir link en nueva ventana/pestaña"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:141
msgid "CSS Classes (optional)"
msgstr "Clases CSS (Opcional)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:149
msgid "Link Relationship (XFN)"
msgstr "Relación del enlace (XFN)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:157
msgid "Mega Menu Column Title"
msgstr "Título de Columna Mega Menú"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:158
msgid "Item Title"
msgstr "Título de Item"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:166
msgid "Hide"
msgstr "Ocultar"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:172
msgid "This column should start a new row"
msgstr "Esta columna debe comenzar en una nueva fila"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:178
msgid "Description (HTML)"
msgstr "Descripción (HTML)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:181
msgid ""
"The description will be displayed in the menu if the current theme supports "
"it."
msgstr "La descripción es mostrada en el menú si el actual tema tiene soporte para esta."

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:190
msgid "Add Icon"
msgstr "Agregar Icono"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:191
msgid "Edit Icon"
msgstr "Editar Icono"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:205
msgid "Use as Mega Menu"
msgstr "Utilizar como Mega Menú"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:211
msgid "Move"
msgstr "Mover"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:212
msgid "Up one"
msgstr "Uno arriba"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:213
msgid "Down one"
msgstr "Uno abajo"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:216
msgid "To the top"
msgstr "Al principio"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:225
#, php-format
msgid "Original: %s"
msgstr "Original: %s"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:239
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:60
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2282
msgid "Cancel"
msgstr "Cancelar"

#: ../framework/extensions/backups/class-fw-extension-backups.php:299
#: ../framework/extensions/backups/class-fw-extension-backups.php:366
msgid "File not specified"
msgstr "Archivo no especificado"

#: ../framework/extensions/backups/class-fw-extension-backups.php:399
#: ../framework/extensions/backups/class-fw-extension-backups.php:400
#: ../framework/core/components/extensions/manager/available-extensions.php:156
msgid "Backup"
msgstr "Respaldar"

#: ../framework/extensions/backups/class-fw-extension-backups.php:554
#: ../framework/extensions/backups/class-fw-extension-backups.php:568
msgid "Access Denied"
msgstr "Acceso Denegado"

#: ../framework/extensions/backups/class-fw-extension-backups.php:561
msgid "Archive not found"
msgstr "Archivo no encontrado"

#: ../framework/extensions/backups/class-fw-extension-backups.php:575
#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:202
msgid "Failed to open file"
msgstr "Ha fallado abrir el archivo"

#: ../framework/extensions/backups/helpers.php:37
msgid "Invalid JSON response"
msgstr "Respuesta JSON no válida"

#: ../framework/extensions/backups/helpers.php:44
msgid ""
"HTTP Loopback Connections are not enabled on this server. If you need to "
"contact your web host, tell them that when PHP tries to connect back to the "
"site at the URL `{url}` and it gets the error `{error}`. There may be a "
"problem with the server configuration (eg local DNS problems, mod_security, "
"etc) preventing connections from working properly."
msgstr "Conexiones de bucle invertido HTTP no están activadas en este servidor. \nSi necesita ponerse en contacto con su proveedor de alojamiento web, dígales que cuando PHP intenta conectarse de nuevo al sitio en la URL `{url}` este obtiene el error `{error}`. Puede haber un problema con la configuración del servidor (Ej: problemas en DNS local, mod_security, etc) previniendo que las conecciones funcionen apropiadamente."

#: ../framework/extensions/backups/helpers.php:123
#: ../framework/extensions/backups/helpers.php:145
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:363
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:431
#, php-format
msgid "Failed to create dir: %s"
msgstr "Falla al crear directorio: %s"

#: ../framework/extensions/backups/helpers.php:152
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:137
#, php-format
msgid "Failed to copy: %s"
msgstr "Falla al copiar: %s"

#: ../framework/extensions/backups/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:168
msgid "Backup & Demo Content"
msgstr "Respaldo & Contenido Demostrativo"

#: ../framework/extensions/backups/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:169
msgid ""
"This extension lets you create an automated backup schedule, import demo "
"content or even create a demo content archive for migration purposes."
msgstr "Esta extensión le permite crear respaldos programados automáticamente, importar contenido demostrativo o incluso crear un archivo de contenido demostrativo para propósitos de migración."

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:97
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:98
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:385
msgid "Demo Content Install"
msgstr "Instalar Contenido Demostrativo"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:282
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:329
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:362
msgid "Forbidden"
msgstr "Prohibido"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:342
msgid "Invalid demo"
msgstr "Demostración Inválida"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:349
msgid "A content install is currently running"
msgstr "Un contenido de instalación se está ejecutando actualmente"

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:28
#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:39
#: ../framework/extensions/backups/views/page.php:17
#: ../framework/extensions/backups/views/page.php:28
msgid "Important"
msgstr "Importante"

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:30
#: ../framework/extensions/backups/views/page.php:19
#, php-format
msgid "You need to activate %s."
msgstr "Usted necesita activar %s."

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:31
#: ../framework/extensions/backups/views/page.php:20
msgid "zip extension"
msgstr "extensión zip"

#: ../framework/extensions/backups/views/page.php:70
msgid "Archives"
msgstr "Archivos"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:76
msgid "Full Backup"
msgstr "Respaldo Total"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:85
msgid "Content Backup"
msgstr "Respaldo de Contenido"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:88
msgid ""
"Warning! \n"
"You are about to delete a backup, it will be lost forever. \n"
"Are you sure?"
msgstr "Advertencia!  \nUsted está eliminando un respaldo, este se perderá completamente. \nEstá seguro (a)?"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:16
msgid "Interval"
msgstr "Intervalo"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:20
#: ../framework/core/components/extensions/manager/views/extension.php:180
msgid "Disabled"
msgstr "Deshabilitado"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:25
msgid "Select how often do you want to backup your website."
msgstr "Seleccione la frecuencia con qué quiere hacer copias de seguridad de su sitio web."

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:32
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:45
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:58
msgid "Age Limit"
msgstr "Límite de Antigüedad"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:33
msgid "Age limit of backups in months"
msgstr "Límite de Antigüedad de respaldo en meses"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:46
msgid "Age limit of backups in weeks"
msgstr "Límite de Antigüedad de respaldo en semanas"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:59
msgid "Age limit of backups in days"
msgstr "Límite de Antigüedad de respaldo en días"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:186
msgid "Backup Schedule"
msgstr "Respaldo Programado"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:288
msgid "Once Weekly"
msgstr "Una vez a la semana"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:292
msgid "Once a month"
msgstr "Una vez al mes"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:139
msgid "undefined"
msgstr "indefinido"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:279
msgid "Task type not registered"
msgstr "Tipo de tarea no registrada"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:286
msgid "Execution stopped (next step did not started)"
msgstr "Ejecución detenida (siguiente paso no inicia)"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:306
msgid "Timed out"
msgstr "Se agotó el tiempo"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:366
msgid "Invalid execution end time"
msgstr "Tiempo del fin de ejecución no válido"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:537
msgid "Invalid execution result"
msgstr "Resultado de la ejecución no válido"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:639
msgid "Invalid token"
msgstr "Token inválido"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:658
msgid "Invalid tasks hash"
msgstr "Hash de tareas inválido"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-restore.php:9
msgid "Image Sizes Restore"
msgstr "Restablecer Tamaños de Imagen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:12
msgid "Archive Unzip"
msgstr "Descomprimir Archivo"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:39
msgid "Zip file not specified"
msgstr "Archivo zip no especificado"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:47
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:50
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:49
#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:114
msgid "Destination dir not specified"
msgstr "Directorio de destino no especificado"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:56
msgid "Destination dir is not empty"
msgstr "Directorio de destino no está vacío"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:63
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:59
#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:50
msgid "Zip extension missing"
msgstr "Falta la extensión zip"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:71
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:70
#, php-format
msgid "Cannot open zip (Error code: %s)"
msgstr "No es posible abrir zip (Código de error: %s)"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:38
msgid "Database export"
msgstr "Exportar Base de datos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:71
msgid "Database table disappeared"
msgstr "Tabla de la base desaparecida"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:91
msgid "Cannot create file"
msgstr "No es posible crear archivo"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:97
msgid "Cannot reopen file"
msgstr "No es posible re-abrir archivo"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:141
msgid "Cannot export CREATE TABLE sql"
msgstr "No es posible exportar sql CREATE TABLE"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:211
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:267
msgid "Cannot get next database table"
msgstr "No es posible obtener la siguiente tabla de la base de datos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:9
msgid "Files Restore"
msgstr "Restaurar Archivos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:32
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:41
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:45
msgid "Source dir not specified"
msgstr "Directorio de origen no especificado"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:39
msgid "Invalid source dir"
msgstr "Directorio de origen no válido"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:46
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:25
msgid "Source dirs not specified"
msgstr "Directorios de origen no especificados"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:116
msgid "No filesystem access, credentials required"
msgstr "Sin acceso al sistema de archivos, se requieren credenciales"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:120
msgid "No filesystem access, invalid credentials"
msgstr "Sin acceso al sistema de archivos, credenciales inválidas"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:126
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:135
msgid "Filesystem init failed"
msgstr "Inicio del sistema de archivos incorrecto"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:192
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:317
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:341
#, php-format
msgid "Cannot convert Filesystem path: %s"
msgstr "No se puede convertir ruta del sistema de archivo: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:197
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:322
#, php-format
msgid "Failed to list dir: %s"
msgstr "Falla al listar directorio: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:227
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:281
#, php-format
msgid "Failed to remove dir: %s"
msgstr "Falla al remover directorio: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:234
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:288
#, php-format
msgid "Failed to remove file: %s"
msgstr "Falla al remover archivo: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:376
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:444
#, php-format
msgid "Failed to copy file: %s"
msgstr "Falla al copiar archivo: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:12
msgid "Archive Zip"
msgstr "Archivo Zip"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:97
msgid "Cannot close the zip file"
msgstr "No es posible cerrar archivo zip"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:103
msgid "Cannot move zip in destination dir"
msgstr "No es posible mover zip a directorio de destino"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:9
msgid "Files Export"
msgstr "Exportar Archivos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:33
msgid "Destination not specified"
msgstr "Destino no especificado"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:74
#, php-format
msgid "Source dir %s is empty"
msgstr "Directorio de origen %s está vacío"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:116
msgid "Failed to get dir chmod"
msgstr "Falla al obtener chmod del directorio"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:123
msgid "Failed to create destination dir"
msgstr "Falla al crear directorio de destino"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:214
#, php-format
msgid "Failed to restore dir listing from: %s"
msgstr "Falla al restablecer la lista del directorio: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:12
msgid "Database restore"
msgstr "Restaurar Base de Datos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:63
msgid "Database file not found"
msgstr "Archivo de base de datos no encontrado"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:111
#, php-format
msgid "Cannot drop temporary table: %s"
msgstr "No es posible colocar la tabla temporal: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:129
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:285
msgid "Cannot open db file"
msgstr "No es posible abrir archivo de base de datos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:138
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:294
msgid "Cannot move cursor in db file"
msgstr "No es posible mover cursor en archivo de base de datos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:154
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:377
#, php-format
msgid "Failed to decode line %d from db file."
msgstr "Falla al decodificar línea %d del archivo de base de datos."

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:186
#, php-format
msgid "Cannot read line %d from db file"
msgstr "No es posible leer línea %d del archivo de base de datos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:195
msgid "Required params not found"
msgstr "Parámetros requeridos no encontrados"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:232
msgid "Cannot do full db restore because backup is missing some tables"
msgstr "No es posible realizar la restauración total de base de datos porque al respaldo le faltan algunas tablas"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:396
#, php-format
msgid "Failed to drop tmp table %s"
msgstr "Falla al colocar tabla tmp %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:435
#, php-format
msgid "Failed to create tmp table %s"
msgstr "Falla al crear tabla tmp %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:447
#, php-format
msgid "Tried to insert data in table that was not imported %s"
msgstr "Intento de insertar datos en tabla que no se importó %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:606
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:628
#, php-format
msgid "Failed insert row from line %d"
msgstr "Falló insertar fila de línea %d"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:642
#, php-format
msgid "Invalid json type %s in db file"
msgstr "Tipo de json %s no válido en archivo de base de datos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:649
msgid "Cannot read line from db file"
msgstr "No es posible leer línea de archivo de base de datos"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:688
msgid "Failed to restore options keeping step"
msgstr "Falla al restaurar la etapa opciones de mantenimiento"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:723
#, php-format
msgid "Failed to keep option: %s"
msgstr "Falla al mantener la opción: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:774
msgid "Tables drop failed"
msgstr "Colocar tablas ha fallado"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:787
msgid "Tables rename failed."
msgstr "Renombrar tablas ha fallado"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:799
#, php-format
msgid "Invalid sub task %s"
msgstr "Sub tarea %s no válida"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:12
msgid "Directory Cleanup"
msgstr "Limpieza de Directorio"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:21
msgid "Dir not specified"
msgstr "Directorio no especificado"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:30
msgid "Cannot remove directory"
msgstr "No es posible remover el directorio"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:37
msgid "Cannot create directory"
msgstr "No es posible crear el directorio"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:55
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:70
#, php-format
msgid "Cannot create file: %s"
msgstr "No es posible crear archivo: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:9
msgid "Image Sizes Remove"
msgstr "Remover tamaños de imagen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:23
msgid "Uploads dir not specified"
msgstr "Directorio de subidas no especificado"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:18
msgid "Download"
msgstr "Descargar"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:119
msgid "Invalid destination dir"
msgstr "Directorio de destino no válido"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:132
#, php-format
msgid "Invalid type: %s"
msgstr "Tipo no válido: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:141
#, php-format
msgid "Args not specified for type: %s"
msgstr "Argumentos no especificados para tipo: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:9
msgid "Local Download"
msgstr "Descarga Local"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:24
msgid "Source not specified"
msgstr "Origen no especificado"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:29
msgid "Invalid source"
msgstr "Origen no válido"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:38
msgid "Invalid source type"
msgstr "Tipo de origen no válido"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:59
#, php-format
msgid "Cannot open zip: %s"
msgstr "No es posible abrir zip: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:70
msgid "Unhandled type"
msgstr "Tipo no controlado"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:13
msgid "Downloading..."
msgstr "Descargando..."

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:16
msgid "Download finished. Doing unzip..."
msgstr "Descarga finalizada. Realizando descompresión..."

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:19
#, php-format
msgid "Downloading... %s of %s"
msgstr "Descargando... %s de %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:24
#, php-format
msgid "Downloading... %s"
msgstr "Descargando... %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:67
msgid "Url not specified"
msgstr "Url no especificada"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:72
msgid "Invalid url"
msgstr "Url no válida"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:79
msgid "File id not specified"
msgstr "Id de archivo no especificado"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:84
msgid "Invalid file id"
msgstr "Id de archivo no válido"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:105
#, php-format
msgid "Zip open failed (code %d). Please try again"
msgstr "Falla al abrir zip (código %d). Por favor intente nuevamente"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:112
msgid "Zip extract failed"
msgstr "Extracción zip ha fallado"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:119
msgid "Failed to close the zip after extract"
msgstr "Falla al cerrar el zip después de extraer"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:152
#, php-format
msgid "Request failed. Error code: %d"
msgstr "Solicitud ha fallado. Código de error: %d"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:163
msgid "Invalid byte position"
msgstr "Posición de byte inválida"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:168
msgid "Empty response body"
msgstr "Estructura de la respuesta vacía"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:188
msgid "File ended without content"
msgstr "Termino de archivo sin contenido"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:213
msgid "Failed to write data to file"
msgstr "Falla al escribir datos al archivo"

#: ../framework/extensions/translation/settings-options.php:11
msgid "Default Language"
msgstr "Idioma Predeterminado"

#: ../framework/extensions/translation/settings-options.php:12
msgid "This is the default language of your website."
msgstr "Este es el idioma predeterminado de su sitio web."

#: ../framework/extensions/translation/settings-options.php:19
msgid "Translate to"
msgstr "Traducir a "

#: ../framework/extensions/translation/settings-options.php:20
msgid "Choose the languages you want your website translated to."
msgstr "Escoja el idioma al cual quiere que su sitio sea traducido."

#: ../framework/extensions/translation/settings-options.php:27
msgid "Convert data"
msgstr "Convertir datos"

#: ../framework/extensions/translation/settings-options.php:28
msgid ""
"Set to default language the posts, pages categories or tags that don't have "
"a language set ?"
msgstr "Establecer idioma predeterminado para publicaciones, páginas, categorías o etiquetas que no tengan idioma establecido?"

#: ../framework/extensions/translation/manifest.php:7
#: ../framework/extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:336
#: ../framework/core/components/extensions/manager/available-extensions.php:264
msgid "Translations"
msgstr "Traducciones"

#: ../framework/extensions/translation/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:265
msgid ""
"This extension lets you translate your website in any language or even add "
"multiple languages for your users to change at their will from the front-"
"end."
msgstr "Esta extensión le permite traducir su sitio web a cualquier idioma o incluso añadir múltiples idiomas para que sus usuarios cambien a su elección desde el front-end."

#: ../framework/extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:114
msgid "All Languages"
msgstr "Todos los idiomas"

#: ../framework/extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:332
msgid "The term translation does already exists.ACTION +++ "
msgstr "La traducción del término ya existe. ACCIÓN +++"

#: ../framework/extensions/translation/extensions/translate-terms/manifest.php:7
msgid "Translate Terms"
msgstr "Traducir términos"

#: ../framework/extensions/translation/extensions/translate-terms/manifest.php:8
msgid "This extension translate terms"
msgstr "Esta extensión traduce términos"

#: ../framework/extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:341
msgid "Language of this post"
msgstr "Idioma de esta publicación"

#: ../framework/extensions/translation/extensions/translate-posts/manifest.php:7
msgid "Translate Posts"
msgstr "Traducir Publicaciones"

#: ../framework/extensions/translation/extensions/translate-posts/manifest.php:8
msgid "This extension translate posts"
msgstr "Esta extensión traduce publicaciones"

#: ../framework/extensions/translation/extensions/translate-widgets/manifest.php:7
msgid "Translate Widgets"
msgstr "Traducir Widgets"

#: ../framework/extensions/translation/extensions/translate-widgets/manifest.php:8
msgid "This extension translate Widgets"
msgstr "Esta extensión traduce Widgets"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:10
msgid "Language Switcher"
msgstr "Alternar Idioma"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:11
msgid "A Language Switcher Widget"
msgstr "Un widget alternador de idioma"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:42
msgid "New title"
msgstr "Nuevo título"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:45
#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:49
msgid "Title:"
msgstr "Título:"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:14
msgid "A calendar of your site&#8217;s Posts."
msgstr "Un calendario de publicaciones en su sitio."

#: ../framework/extensions/translation/extensions/translate-menus/manifest.php:7
msgid "Translate Menus"
msgstr "Traducir menus"

#: ../framework/extensions/translation/extensions/translate-menus/manifest.php:8
msgid "This extension translate menus"
msgstr "Esta extensión traduce menus"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:211
msgid "Slider Design"
msgstr "Diseñor del Deslizador"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:213
msgid "Number of Images"
msgstr "Número de imágenes"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:231
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:234
#, php-format
msgid "%s updated."
msgstr "%s actualizado."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:232
msgid "Custom field updated."
msgstr "Campo personalizado actualizado."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:233
msgid "Custom field deleted."
msgstr "Campo personalizado eliminado."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:235
#, php-format
msgid "%s restored to revision from %s"
msgstr "%s se restablece a la revisión del %s"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:237
#, php-format
msgid "%s published."
msgstr "%s publicado."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:238
msgid "Page saved."
msgstr "Página guardada."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:239
#, php-format
msgid "%s submitted."
msgstr "%s se ha enviado."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:240
#, php-format
msgid "%s scheduled for: %s."
msgstr "%s programada para: %s."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:242
#, php-format
msgid "%s draft updated."
msgstr "%s borrador actualizado."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:272
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:52
msgid "Publish"
msgstr "Publicar"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:342
msgid ""
"This slider was created correctly, but the code implementation was delete "
"from source code or blocked from filter.Delete this post or recovery slider "
"implementation"
msgstr "Este deslizador se ha creado correctamente, pero el código de implementación fue eliminado del código fuente o bloqueado desde el filtro. Elimine esta publicación o recupere la implementación del deslizador."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:351
msgid ""
"This slider was created correctly, but the multimedia_types from config.php "
"file was deleted, please set multimedia_types for this slider type."
msgstr "Este deslizador se ha creado correctamente, pero los tipos_multimedia del archivo config.php fueron eliminados, por favor establezca tipos_multimedia (multimedia_types) para este tipo de deslizador."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:361
msgid "Slider Configuration"
msgstr "Configuración del Deslizador"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:375
msgid "Slider Title"
msgstr "Título de deslizador"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:377
msgid "Choose a title for your slider only for internal use: Ex: \"Homepage\"."
msgstr "Escoja un título para su deslizador solo para uso interno: Ej: \"Página de inicio\"."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:433
msgid "Slider Settings"
msgstr "Opciones del Deslizador"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:461
msgid ""
"You don't have slider extensions, please create at least one extension for "
"properly work"
msgstr "Usted no tiene extensiones para deslizador, por favor cree al menos una extensión para su correcto funcionamiento."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:474
msgid "Choose the population method for your slider"
msgstr "Escoja el método para agrupación de su deslizador"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:542
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:94
msgid "(no title)"
msgstr "(sin título)"

#: ../framework/extensions/media/extensions/slider/posts.php:6
#: ../framework/extensions/media/extensions/slider/posts.php:12
#: ../framework/extensions/media/extensions/slider/posts.php:18
#: ../framework/extensions/media/extensions/slider/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:12
msgid "Sliders"
msgstr "Deslizadores"

#: ../framework/extensions/media/extensions/slider/posts.php:7
#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:8
msgid "Slider"
msgstr "Deslizador"

#: ../framework/extensions/media/extensions/slider/posts.php:9
msgid "Add New Slider"
msgstr "Agregar Nuevo Deslizador"

#: ../framework/extensions/media/extensions/slider/posts.php:10
msgid "Edit Slider"
msgstr "Editar Deslizador"

#: ../framework/extensions/media/extensions/slider/posts.php:11
msgid "New Slider"
msgstr "Nuevo Deslizador"

#: ../framework/extensions/media/extensions/slider/posts.php:13
msgid "View Slider"
msgstr "Ver Deslizador"

#: ../framework/extensions/media/extensions/slider/posts.php:14
msgid "Search Sliders"
msgstr "Buscar Deslizadores"

#: ../framework/extensions/media/extensions/slider/posts.php:15
msgid "No Sliders found"
msgstr "No se encuentran deslizadores"

#: ../framework/extensions/media/extensions/slider/posts.php:16
msgid "No Sliders found in Trash"
msgstr "No se encuentran deslizadores en la papelera"

#: ../framework/extensions/media/extensions/slider/manifest.php:6
msgid ""
"Adds the Sliders extension to your website. You'll be able to create "
"different built in jQuery sliders for your homepage and all the other "
"website pages."
msgstr "Añade la extensión deslizadores (Sliders) a su sitio web. Usted podrá crear diferentes deslizadores jQuery integrados para su página de inicio y otras páginas del sitio web."

#: ../framework/extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:6
msgid "Nivo Slider"
msgstr "Deslizador Nivo"

#: ../framework/extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:6
msgid "Owl Slider"
msgstr "Deslizador Owl"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/manifest.php:6
msgid "Bx-Slider"
msgstr "Deslizador Bx"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:4
msgid "Population Method Categories opt 1"
msgstr "Categorías del método de agrupación opc 1"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:5
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:11
msgid "Option description"
msgstr "Descripción de opción"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:10
msgid "Population Method Categories opt 2"
msgstr "Categorías del método de agrupación opc 2"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:5
msgid "Type of Transition"
msgstr "Tipo de transición"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:6
msgid "Type of transition between slides"
msgstr "Tipo de transición entre diapositivas"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:9
msgid "Horizontal"
msgstr "Horizontal"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:10
msgid "Vertical"
msgstr "Vertical"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:11
msgid "Fade"
msgstr "Desvanecer"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:9
msgid "Choose a subtitle for your slide."
msgstr "Escoja un subtítulo para su diapositiva."

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:9
msgid "Add a Slider"
msgstr "Añadir un slider"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:9
msgid "Select Slider"
msgstr "Seleccione Deslizador"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:14
msgid "Set width"
msgstr "Establecer ancho"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:19
msgid "Set height"
msgstr "Establecer alto"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:34
msgid "No Sliders Available"
msgstr "No hay deslizadores disponibles"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:47
msgid "create a new Slider"
msgstr "crear un nuevo deslizador"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:49
msgid ""
"No Sliders created yet. Please go to the {br}Sliders page and "
"{add_slider_link}."
msgstr "No hay deslizadores creados aún. Por favor vaya a la {br} Página Deslizadores y {add_slider_link}."

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:5
msgid ""
"Note that the type and population can't be changed later. You'll need to "
"create a new slider to have a different slider type or population method."
msgstr "Note que el tipo y agrupación no podrá ser modificado después. Usted necesitará crear un nuevo deslizador para tener un tipo distinto de deslizador o método de agrupación."

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:31
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:48
msgid "Schedule"
msgstr "Programado"

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:40
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:57
msgid "Submit for Review"
msgstr "Enviar para revisión"

#: ../framework/extensions/media/extensions/population-method/class-fw-extension-population-method.php:36
#, php-format
msgid "Specified population method does not exists: %s"
msgstr "Método de agrupación especificado no existe: %s"

#: ../framework/extensions/media/extensions/population-method/class-fw-extension-population-method.php:55
#, php-format
msgid "Population method %s does not exist"
msgstr "Método de agrupación %s no existe"

#: ../framework/extensions/media/extensions/population-method/manifest.php:3
msgid "Population Methods"
msgstr "Métodos de Agrupación"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:21
msgid "Automatically, fetch images from categories"
msgstr "Automáticamente, extrae imágenes desde categorías"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:29
#, php-format
msgid "%s extension needs configured categories in post types"
msgstr "extensión %s necesita ser categorías configuradas en tipos de entradas (post types)"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:34
msgid "Categories Population Method"
msgstr "Categorías del Método de Agrupación"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:44
msgid "Choose Category"
msgstr "Escoger Categoría"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:53
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:53
msgid "Number of Images in the slider"
msgstr "Número de imágenes en el deslizador"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:107
msgid "Select Specific Categories"
msgstr "Seleccionar Categorías Específicas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:4
msgid "Population Method - Categories"
msgstr "Método de Agrupación - Categorías"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:21
msgid "Automatically, fetch images from tags"
msgstr "Automáticamente, extrae imágenes desde etiquetas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:29
#, php-format
msgid "%s extension needs configured tags in post types"
msgstr "Extensión %s necesita ser etiquetas configuradas en tipos de entradas (post types)"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:34
msgid "Tags Population Method"
msgstr "Método de Agrupación de Etiquetas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:44
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:44
msgid "Choose Tag"
msgstr "Escoger Etiqueta"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:107
msgid "Select Specific tags"
msgstr "Seleccionar etiquetas específicas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:4
msgid "Population Method - Tags"
msgstr "Método de Agrupación - Etiquetas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:21
msgid "Automatically, fetch images from posts"
msgstr "Automáticamente, extrae imágenes desde entradas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:29
#, php-format
msgid "%s extension needs configured post categories in post types"
msgstr "Extensión %s necesita ser entradas configuradas en tipos de entradas (post types)"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:34
msgid "Posts Population Method"
msgstr "Método de Agrupación de Entradas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:101
msgid "Select Specific posts"
msgstr "Seleccionar entradas específicas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:4
msgid "Population Method - Posts"
msgstr "Método de Argupación - Entradas"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:22
msgid "Manually, I'll upload the images myself"
msgstr "Manualmente, subiré las imágenes por mi cuenta"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:33
msgid "Click to edit / Drag to reorder"
msgstr "Click para editar / Arrastrar para reordenar"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:54
msgid "Choose"
msgstr "Escoger"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:4
msgid "Population Method - Custom"
msgstr "Método de Agrupación - Personalizado"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:57
msgid "Add Slide"
msgstr "Agregar Diapositiva"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:59
#: ../framework/views/backend-settings-form.php:47
msgid "Save Changes"
msgstr "Guardar Cambios"

#: ../framework/core/Fw.php:73
msgid "Framework requirements not met:"
msgstr "No se cumplen los requisitos del Framework: "

#: ../framework/core/class-fw-manifest.php:293
msgid "minimum required version is"
msgstr "Versión mínima requerida es "

#: ../framework/core/class-fw-manifest.php:296
msgid "maximum required version is"
msgstr "Versión máxima requerida es "

#: ../framework/core/class-fw-manifest.php:301
msgid "and"
msgstr "y"

#: ../framework/core/class-fw-manifest.php:308
#, php-format
msgid "Current WordPress version is %s, %s"
msgstr "Versión actual de Wordpress es %s, %s"

#: ../framework/core/class-fw-manifest.php:314
#, php-format
msgid "Current Framework version is %s, %s"
msgstr "Versión actual del Framework es %s, %s"

#: ../framework/core/class-fw-manifest.php:323
#, php-format
msgid "Current version of the %s extension is %s, %s"
msgstr "Versión actual de la extensión %s es %s, %s"

#: ../framework/core/class-fw-manifest.php:329
#, php-format
msgid "%s extension is required"
msgstr "Extensión %s es requerida"

#: ../framework/core/class-fw-manifest.php:334
#, php-format
msgid "%s extension is required (%s)"
msgstr "Extensión %s es requerida (%s)"

#: ../framework/core/extends/class-fw-option-type.php:283
#, php-format
msgid "Option type %s has no default value"
msgstr "Tipo de opción %s no tiene un valor por defecto"

#: ../framework/core/components/backend.php:355
msgid "Done"
msgstr "Realizado"

#: ../framework/core/components/backend.php:356
msgid "Ah, Sorry"
msgstr "Ah, Disculpas"

#: ../framework/core/components/backend.php:358
#: ../framework/includes/option-types/color-picker/class-fw-option-type-color-picker.php:39
#: ../framework/includes/option-types/color-picker/class-fw-option-type-color-picker.php:40
#: ../framework/includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:41
#: ../framework/includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:42
msgid "Reset"
msgstr "Restablecer"

#: ../framework/core/components/backend.php:541
#: ../framework/core/components/backend.php:542
#: ../framework/core/components/backend.php:650
msgid "Theme Settings"
msgstr "Configuración del Tema"

#: ../framework/core/components/backend.php:577
msgid "leave a review"
msgstr "dejar una opinión"

#: ../framework/core/components/backend.php:588
msgid ""
"Unyson WordPress Framework is the fastest and easiest way to develop a "
"premium theme. I highly recommend it"
msgstr "Framework Unyson Wordpress es el más rápido y fácil camino para desarrollar un tema premium. Recomiendo altamente éste"

#: ../framework/core/components/backend.php:594
msgid ""
"If you like Unyson, {wp_review_link}, share on {facebook_share_link} or "
"{twitter_share_link}."
msgstr "Si a usted le gusta Unyson, {wp_review_link}, comparta en {facebook_share_link} o {twitter_share_link}"

#: ../framework/core/components/backend.php:1303
msgid "You have no permissions to change settings options"
msgstr "Usted no tiene autorización para cambiar opciones de configuración"

#: ../framework/core/components/backend.php:1316
msgid "The options were successfully reset"
msgstr "Las opciones se han restablecido exitosamente"

#: ../framework/core/components/backend.php:1327
msgid "The options were successfully saved"
msgstr "Las opciones se han guardado exitosamente"

#: ../framework/core/components/backend.php:1440
msgid "Unknown collected group"
msgstr "Grupo recogido desconocido"

#: ../framework/core/components/backend.php:1779
#, php-format
msgid "Undefined option type: %s"
msgstr "Tipo de opción no definido: %s"

#: ../framework/core/components/backend.php:1826
#, php-format
msgid "Undefined container type: %s"
msgstr "Tipo de contenedor no definido: %s"

#: ../framework/core/components/extensions.php:447
#: ../framework/core/components/extensions.php:525
#, php-format
msgid "Extension %s is invalid."
msgstr "Extensión %s no es válida"

#: ../framework/core/components/theme.php:206
msgid "Theme requirements not met:"
msgstr "No se cumplen los requisitos del Tema: "

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:351
msgid "Cannot remove the old extensions backup dir"
msgstr "No es posible remover el directorio antiguo del respaldo de extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:357
msgid "Cannot create the extensions backup dir"
msgstr "No es posible crear el directorio del respaldo de extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:362
msgid "Cannot backup the extensions"
msgstr "No es posible respaldar extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:408
msgid "Cannot clear the extensions directory"
msgstr "No es posible vaciar el directorio de extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:413
msgid "Cannot recreate the extensions directory"
msgstr "No es posible recrear el directorio de extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:419
msgid "Cannot recover the extensions"
msgstr "No es posible recuperar extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:645
#, php-format
msgid "Cannot activate hidden standalone extension %s"
msgstr "No es posible activar extensión oculta autonóma %s "

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:874
msgid "You are not allowed to install extensions."
msgstr "Usted no está autorizado para instalar extensiones."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:891
msgid "All supported extensions are already installed."
msgstr "Todas las extensiones soportadas ya se encuentran instaladas."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:967
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2384
#, php-format
msgid "Cannot remove temporary directory: %s"
msgstr "No se puede remover el directorio temporal %s."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1037
msgid "You have no permissions to install extensions"
msgstr "Usted no tiene permisos para instalar extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1044
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1517
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1901
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2116
msgid "No extensions provided"
msgstr "No hay extensiones proveídas"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1053
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1527
msgid "WP Filesystem is not initialized"
msgstr "Sistema de archivos WP no está inicializado"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1079
#, php-format
msgid "Extension \"%s\" is already installed."
msgstr "Extensión \"%s\" ya se encuentra instalada."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1094
#, php-format
msgid "Extension \"%s\" is not available for install."
msgstr "Extensión \"%s\" no está disponible para instalación."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1122
#, php-format
msgid "Parent extension \"%s\" not available."
msgstr "Extensión principal \"%s\" no disponible"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1156
#, php-format
msgid "Downloading the \"%s\" extension..."
msgstr "Descargando la extensión \"%s\" ..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1200
#, php-format
msgid "Installing the \"%s\" extension..."
msgstr "Instalando la extensión \"%s\" ..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1238
#, php-format
msgid "The %s extension has been successfully installed."
msgstr "La extensión %s se ha instalado correctamente."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1399
msgid "You are not allowed to delete extensions."
msgstr "Usted no tiene autorización para eliminar extensiones."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1510
msgid "You have no permissions to uninstall extensions"
msgstr "Usted no tiene autorización para desinstalar extensiones."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1578
#, php-format
msgid "Deleting the \"%s\" extension..."
msgstr "Eliminando la extensión \"%s\" ..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1590
#, php-format
msgid "Cannot delete the \"%s\" extension."
msgstr "No es posible eliminar la extensión \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1602
#, php-format
msgid "The %s extension has been successfully deleted."
msgstr "La extensión %s se ha eliminado exitosamente."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1703
msgid "Extension not specified."
msgstr "Extensión no especificada."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1708
#, php-format
msgid "Extension \"%s\" is not installed."
msgstr "Extensión \"%s\" no está instalada."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1776
#, php-format
msgid "Extension \"%s\" does not exist or is not active."
msgstr "Extensión \"%s\" no existe o no está activa."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1785
#, php-format
msgid "%s extension does not have settings."
msgstr "Extensión %s no tiene configuraciones."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1806
msgid "Extension has no Install Instructions"
msgstr "Extensión no tiene instrucciones de instalación"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1830
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2047
msgid "Invalid request method."
msgstr "Método de solicitud inválido."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1842
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2059
msgid "No extension specified."
msgstr "Extensión no especificada."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1894
msgid "You have no permissions to activate extensions"
msgstr "Usted no tiene permisos para activar extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1914
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2132
#, php-format
msgid "Extension \"%s\" does not exist."
msgstr "Extensión \"%s\" no existe."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2109
msgid "You have no permissions to deactivate extensions"
msgstr "Usted no tiene permisos para desactivar extensiones"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2297
msgid "You are not allowed to save extensions settings."
msgstr "Usted no tiene autorización para guardar configuraciones de extensiones."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2304
msgid "Invalid extension."
msgstr "Extensión inválida."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2309
msgid "Extension does not have settings options."
msgstr "Extensión no tiene opciones de configuración."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2341
msgid "Extensions settings successfully saved."
msgstr "Configuraciones de extensiones guardadas exitosamente."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2368
#, php-format
msgid "Extension \"%s\" has no download sources."
msgstr "Extensión \"%s\" no tiene origen de descarga."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2392
#, php-format
msgid "Cannot create temporary directory: %s"
msgstr "No se puede crear el directorio temporal: %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2403
#, php-format
msgid "\"%s\" extension github source \"user_repo\" parameter is required"
msgstr "Recurso Github de extensión \"%s\" requiere el parámetro \"user_repo\""

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2454
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (%s)"
msgstr "Falló acceso al repositorio de publicaciones Github \"%s\". (%s)"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2477
#, php-format
msgid "\"%s\" extension github repository \"%s\" has no releases."
msgstr "Repositorio de extensión \"%s\" no tiene publicaciones \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2510
#, php-format
msgid "Cannot download the \"%s\" extension zip. (Response code: %d)"
msgstr "No es posible descargar zip de extensión \"%s\". (Código de respuesta: %d)"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2517
#, php-format
msgid "Cannot download the \"%s\" extension zip. %s"
msgstr "No es posible descargar zip de extensión \"%s\". %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2525
#, php-format
msgid "Cannot download the \"%s\" extension zip."
msgstr "No es posible descargar zip de extensión \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2538
#, php-format
msgid "Cannot save the \"%s\" extension zip."
msgstr "No es posible guardar zip de extensión \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2557
#, php-format
msgid "Cannot remove the \"%s\" extension downloaded zip."
msgstr "No es posible remover zip descargado de extensión \"%s\""

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2582
#, php-format
msgid "The unzipped \"%s\" extension directory not found."
msgstr "Directorio descomprimido de extensión \"%s\" no encontrado."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2589
#, php-format
msgid "Unknown \"%s\" extension download source \"%s\""
msgstr "Desconocido \"%s\" origen de descarga extensión \"%s\""

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2615
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2634
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2697
#, php-format
msgid "Cannot read directory \"%s\"."
msgstr "No es posible leer el directorio \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2649
#, php-format
msgid "Cannot delete \"%s\"."
msgstr "No es posible eliminar \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2660
#, php-format
msgid "Cannot create the \"%s\" directory."
msgstr "No es posible crear el directorio \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2678
#, php-format
msgid "Cannot move \"%s\" to \"%s\"."
msgstr "No es posible mover \"%s\" a \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3103
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3150
#, php-format
msgid "Cannot activate the %s extension because it is not installed. %s"
msgstr "No es posible activar la extensión %s porque esta no está instalada. %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3107
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3154
msgid "Install"
msgstr "Instalar"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3197
msgid "Install theme compatible extensions"
msgstr "Instalar extensiones compatibles con el tema"

#: ../framework/core/components/extensions/manager/available-extensions.php:13
msgid ""
"Adds a sliders module to your website from where you'll be able to create "
"different built in jQuery sliders for your homepage and rest of the pages."
msgstr "Añade módulo deslizadores (Sliders) a su sitio web, donde usted podrá crear diferentes deslizadores jQuery integrados para su página de inicio y otras páginas del sitio web."

#: ../framework/core/components/extensions/manager/available-extensions.php:24
msgid "Media"
msgstr "Medios"

#: ../framework/core/components/extensions/manager/available-extensions.php:36
msgid "Population method"
msgstr "Método de agrupación"

#: ../framework/core/components/extensions/manager/available-extensions.php:85
msgid ""
"Let's you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr "Permite construir de manera sencilla innumerables páginas con la ayuda del constructor visual \"arrastre y suelte\" que viene con un grupo de shortcodes listos para utilizar."

#: ../framework/core/components/extensions/manager/available-extensions.php:96
msgid "Shortcodes"
msgstr "Shortcodes"

#: ../framework/core/components/extensions/manager/available-extensions.php:157
msgid ""
"This extension lets you set up daily, weekly or monthly backup schedule. You"
" can choose between a full backup or a data base only backup."
msgstr "Esta extensión le permite establecer tareas de respaldo diario, semanal o mensual. Usted puede escoger entre un respaldo total o solo un respaldo de base de datos."

#: ../framework/core/components/extensions/manager/views/extension.php:89
#: ../framework/core/components/extensions/manager/views/extension-page-header.php:25
msgid "Install Instructions"
msgstr "Instrucciones de instalación"

#: ../framework/core/components/extensions/manager/views/extension.php:102
msgid "Compatible"
msgstr "Compatible"

#: ../framework/core/components/extensions/manager/views/extension.php:102
msgid "with your current theme"
msgstr "con su actual tema"

#: ../framework/core/components/extensions/manager/views/extension.php:174
#, php-format
msgid "Parent extension \"%s\" is disabled"
msgstr "Extensión principal \"%s\" está deshabilitada."

#: ../framework/core/components/extensions/manager/views/extension.php:199
#, php-format
msgid "You need to update WordPress to %s: %s"
msgstr "Usted necesita actualizar Wordpress a %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:201
msgid "Update WordPress"
msgstr "Actualizar Wordpress"

#: ../framework/core/components/extensions/manager/views/extension.php:205
#, php-format
msgid "WordPress needs to be updated to %s"
msgstr "Wordpress necesita ser actualizado a %s"

#: ../framework/core/components/extensions/manager/views/extension.php:215
#, php-format
msgid "Maximum supported WordPress version is %s"
msgstr "Versión máxima de Wordpress soportada es %s"

#: ../framework/core/components/extensions/manager/views/extension.php:230
#, php-format
msgid "You need to update %s to %s: %s"
msgstr "Usted necesita actualizar %s a %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:239
#, php-format
msgid "%s needs to be updated to %s"
msgstr "%s necesita actualizarse a %s"

#: ../framework/core/components/extensions/manager/views/extension.php:250
#, php-format
msgid "Maximum supported %s version is %s"
msgstr "Versión máxima soportada %s y es %s"

#: ../framework/core/components/extensions/manager/views/extension.php:268
#, php-format
msgid "You need to update the %s extension to %s: %s"
msgstr "Usted necesita actualizar la extensión %s a %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:277
#, php-format
msgid "The %s extension needs to be updated to %s"
msgstr "La extensión %s necesita actualizarse a %s"

#: ../framework/core/components/extensions/manager/views/extension.php:288
#, php-format
msgid "Maximum supported %s extension version is %s"
msgstr "Versión máxima soportada de extensión %s y es %s"

#: ../framework/core/components/extensions/manager/views/extension.php:303
#, php-format
msgid "The %s extension is disabled"
msgstr "La extensión %s está deshabilitada"

#: ../framework/core/components/extensions/manager/views/extension.php:304
#, php-format
msgid "Activate %s"
msgstr "Activar %s"

#: ../framework/core/components/extensions/manager/views/extension.php:313
#, php-format
msgid "The %s extension is not installed: %s"
msgstr "Extensión \"%s\" no está instalada: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:316
#, php-format
msgid "Install %s"
msgstr "Instalar %s"

#: ../framework/core/components/extensions/manager/views/extension.php:321
#, php-format
msgid "The %s extension is not installed"
msgstr "Extensión \"%s\" no está instalada"

#: ../framework/core/components/extensions/manager/views/extension.php:342
msgid "View Requirements"
msgstr "Ver requerimientos"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:40
#, php-format
msgid "%s Settings"
msgstr "%s Configuraciones"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:43
#, php-format
msgid "%s Install Instructions"
msgstr "%s Instrucciones de instalación"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:46
msgid "Unknown tab:"
msgstr "Pestaña desconocida: "

#: ../framework/core/components/extensions/manager/views/delete-form.php:42
#: ../framework/core/components/extensions/manager/views/install-form.php:51
msgid "No, Return me to the extension list"
msgstr "No, Regrese a a la lista de extensiones"

#: ../framework/core/components/extensions/manager/views/delete-form.php:45
msgid "Click to view entire list of directories which will be deleted"
msgstr "Click para ver lista completa de directorios serán eliminados"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:17
msgid "Active Extensions"
msgstr "Extensiones Activas"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:33
msgid "No extensions activated yet"
msgstr "No hay extensiones activas aún"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:33
msgid "Check the available extensions below"
msgstr "Revisar abajo extensiones disponibles"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:59
msgid "Available Extensions"
msgstr "Extensiones Disponibles"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:184
msgid "Show other extensions"
msgstr "Mostrar otras extensiones"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:185
msgid "Hide other extensions"
msgstr "Ocultar otras extensiones"

#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:14
#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:14
msgid "Go to extensions page"
msgstr "Ir a la página de extensiones"

#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:17
#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:17
msgid "Return to Extensions page"
msgstr "Volver a la página de extensiones"

#: ../framework/views/backend-settings-form.php:48
msgid "Reset Options"
msgstr "Restablecer Opciones"

#: ../framework/views/backend-settings-form.php:62
msgid "by"
msgstr "por"

#: ../framework/views/backend-settings-form.php:155
msgid ""
"Click OK to reset.\n"
"All settings will be lost and replaced with default settings!"
msgstr "Click OK para restablecer.\nTodas las configuraciones de perderán y reemplazadas a las configuraciones predeterminadas!"

#: ../framework/views/backend-settings-form.php:202
msgid "Resetting"
msgstr "Restableciendo"

#: ../framework/views/backend-settings-form.php:204
msgid "We are currently resetting your settings."
msgstr "Estamos actualmente restableciendo sus configuraciones."

#: ../framework/views/backend-settings-form.php:206
#: ../framework/views/backend-settings-form.php:212
msgid "This may take a few moments."
msgstr "Esto podría tomar unos momentos."

#: ../framework/views/backend-settings-form.php:208
msgid "Saving"
msgstr "Guardando"

#: ../framework/views/backend-settings-form.php:210
msgid "We are currently saving your settings."
msgstr "Estamos actualmente guardando sus configuraciones."

#: ../framework/includes/option-types/class-fw-option-type-undefined.php:25
msgid "UNDEFINED OPTION TYPE"
msgstr "TIPO DE OPCIÓN NO DEFINIDA"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:22
msgid "25%"
msgstr "25%"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:23
msgid "50%"
msgstr "50%"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:24
msgid "100%"
msgstr "100%"

#: ../framework/includes/option-types/map/views/view.php:12
msgid "Specify location"
msgstr "Especificar ubicación"

#: ../framework/includes/option-types/map/views/view.php:27
msgid "Location Venue"
msgstr "Lugar de ubicación"

#: ../framework/includes/option-types/map/views/view.php:42
msgid "Address"
msgstr "Dirección"

#: ../framework/includes/option-types/map/views/view.php:57
msgid "City"
msgstr "Ciudad"

#: ../framework/includes/option-types/map/views/view.php:72
msgid "Country"
msgstr "País"

#: ../framework/includes/option-types/map/views/view.php:87
msgid "State"
msgstr "Región o Estado"

#: ../framework/includes/option-types/map/views/view.php:103
msgid "Zip Code"
msgstr "Código Zip"

#: ../framework/includes/option-types/map/views/view.php:138
msgid "Cannot find the location?"
msgstr "No encuentra la ubicación?"

#: ../framework/includes/option-types/map/views/view.php:150
msgid "Reset location"
msgstr "Restablecer ubicación"

#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:150
msgid "Add Image"
msgstr "Agregar Imagen"

#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:176
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:186
msgid "Upload"
msgstr "Subir"

#: ../framework/includes/option-types/typography-v2/view.php:39
msgid "Font face"
msgstr "Tipo de fuente"

#: ../framework/includes/option-types/typography-v2/view.php:48
msgid "Normal"
msgstr "Normal"

#: ../framework/includes/option-types/typography-v2/view.php:49
msgid "Italic"
msgstr "Itálica"

#: ../framework/includes/option-types/typography-v2/view.php:50
msgid "Oblique"
msgstr "Oblicua"

#: ../framework/includes/option-types/typography-v2/view.php:59
#: ../framework/includes/option-types/typography-v2/view.php:118
msgid "Style"
msgstr "Estilo"

#: ../framework/includes/option-types/typography-v2/view.php:85
msgid "Weight"
msgstr "Espesor"

#: ../framework/includes/option-types/typography-v2/view.php:101
msgid "Script"
msgstr "Script"

#: ../framework/includes/option-types/typography-v2/view.php:128
msgid "Size"
msgstr "Tamaño"

#: ../framework/includes/option-types/typography-v2/view.php:139
msgid "Line height"
msgstr "Alto de línea"

#: ../framework/includes/option-types/typography-v2/view.php:150
msgid "Letter spacing"
msgstr "Espacio entre letras"

#: ../framework/includes/option-types/typography-v2/view.php:173
msgid "Color"
msgstr "Color"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:156
msgid "Unknown Set"
msgstr "Configruación Desconocida"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:186
msgid "Web Application Icons"
msgstr "Iconos Aplicaciones Web"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:187
msgid "Hand Icons"
msgstr "Iconos Manos"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:188
msgid "Transportation Icons"
msgstr "Iconos Transportes"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:189
msgid "Gender Icons"
msgstr "Iconos Géneros"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:190
msgid "File Type Icons"
msgstr "Iconos Tipos de Archivos"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:193
msgid "Payment Icons"
msgstr "Iconos de Pago"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:195
msgid "Currency Icons"
msgstr "Iconos de Monedas"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:196
msgid "Text Editor Icons"
msgstr "Iconos Editor de Texto"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:197
msgid "Directional Icons"
msgstr "Iconos Direcciones"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:198
msgid "Video Player Icons"
msgstr "Iconos Reproductor de Video"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:199
msgid "Brand Icons"
msgstr "Iconos de Marcas"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:200
msgid "Medical Icons"
msgstr "Iconos Médicos"

#: ../framework/includes/option-types/icon/view.php:39
msgid "All Categories"
msgstr "Todas las Categorías"

#: ../framework/includes/option-types/datetime-range/view.php:41
#: ../framework/includes/option-types/gradient/view.php:46
msgid "to"
msgstr "a"

#: ../framework/includes/option-types/multi-picker/class-fw-option-type-multi-picker.php:179
#, php-format
msgid "No 'picker' key set for multi-picker option: %s"
msgstr "No establecida clave 'picker' (selector) para opción multi-selección: %s"

#: ../framework/includes/option-types/background-image/view.php:37
msgid "Predefined images"
msgstr "Imágenes predefinidas"

#: ../framework/includes/option-types/background-image/view.php:38
msgid "Custom image"
msgstr "Imagen personalizada"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:160
msgid "Add Images"
msgstr "Agregar imágenes"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:188
msgid "1 File"
msgstr "1 Archivo"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:189
#, php-format
msgid "%u Files"
msgstr "%u Archivos"
