<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

class WooC_Functions {

    protected static $instance = null;

    public function __construct() {

        /* Theme supports for WooCommerce */
        add_action( 'after_setup_theme', array( $this, 'theme_support' ) );

        /* Body class */
        add_filter( 'body_class', array( $this, 'body_classes' ) );

        /* Disable default styles */
        add_filter( 'woocommerce_enqueue_styles', array( $this, 'disable_styles' ) );

        /* Title */
        add_filter( 'axiltheme_page_title', array( $this, 'page_title' ) );
 
        /* Header cart count number */
        add_filter( 'woocommerce_add_to_cart_fragments', array( $this, 'axil_header_cart_count' ) );

        add_filter( 'woocommerce_layered_nav_count', '__return_false' );

        /* Breadcrumb */
        remove_action( 'woocommerce_before_main_content', 'woocommerce_breadcrumb', 20 );
        add_action( 'woocommerce_archive_description', 'woocommerce_breadcrumb', 10 );

        /* Replace default placeholder image */
        add_filter( 'woocommerce_placeholder_img_src', array( $this, 'placeholder_img_src' ) );

        /* Modify responsive smallscreen size */
        add_filter( 'woocommerce_style_smallscreen_breakpoint', array( $this, 'smallscreen_breakpoint' ) );

        /* Shop hide default page title */
        add_filter( 'woocommerce_show_page_title', '__return_false' );

        /* Star rating html */
        //add_filter( 'woocommerce_product_get_rating_html',             array( $this, 'star_rating_html' ), 10, 3 );

        /* Shop/Archive Wrapper */
        remove_action( 'woocommerce_before_main_content', 'woocommerce_output_content_wrapper', 10 );
        remove_action( 'woocommerce_sidebar', 'woocommerce_get_sidebar', 10 );
        remove_action( 'woocommerce_after_main_content', 'woocommerce_output_content_wrapper_end', 10 );

        remove_action( 'woocommerce_before_main_content', 'wrapper_start', 10 );
        remove_action( 'woocommerce_after_main_content', 'wrapper_end', 10 );

        add_action( 'woocommerce_before_main_content', array( $this, 'wrapper_start' ), 10 );
        add_action( 'woocommerce_after_main_content', array( $this, 'wrapper_end' ), 10 );

        remove_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count', 20 );
        remove_action( 'woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 30 );
        add_action( 'woocommerce_before_shop_loop', array( $this, 'shop_topbar' ), 20 );

        add_action( 'etrade_woocommerce_result_count', array( $this, 'etrade_woocommerce_result_count' ), 20 );

        /* Shop loop */
        add_filter( 'loop_shop_per_page', array( $this, 'loop_shop_per_page' ) );
        add_filter( 'loop_shop_columns', array( $this, 'loop_shop_columns' ) );
        add_filter( 'woocommerce_sale_flash', array( $this, 'sale_flash' ), 10, 3 );

        remove_action( 'woocommerce_before_shop_loop_item', 'woocommerce_template_loop_product_link_open', 10 );
        remove_action( 'woocommerce_before_shop_loop_item_title', 'woocommerce_show_product_loop_sale_flash', 10 );
        remove_action( 'woocommerce_before_shop_loop_item_title', 'woocommerce_template_loop_product_thumbnail', 10 );
        remove_action( 'woocommerce_shop_loop_item_title', 'woocommerce_template_loop_product_title', 10 );
        remove_action( 'woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 5 );
        remove_action( 'woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_price', 10 );
        remove_action( 'woocommerce_after_shop_loop', 'woocommerce_pagination', 10 );
        remove_action( 'woocommerce_after_shop_loop_item', 'woocommerce_template_loop_product_link_close', 5 );
        remove_action( 'woocommerce_after_shop_loop_item', 'woocommerce_template_loop_add_to_cart', 10 );

        add_action( 'woocommerce_after_shop_loop', array( $this, 'pagination' ), 10 );

        // Sharing
        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_sharing', 50 );

        // Review
        remove_action( 'woocommerce_review_before_comment_meta', 'woocommerce_review_display_rating', 10 );

        // Hide product data tabs
        add_filter( 'woocommerce_product_tabs', array( $this, 'hide_product_data_tab' ) );
        add_filter( 'woocommerce_product_review_comment_form_args', array( $this, 'product_review_form' ) );

        // Hide some tab headings
        add_filter( 'woocommerce_product_description_heading', '__return_false' );
        add_filter( 'woocommerce_product_additional_information_heading', '__return_false' );

        // Review avatar size
        add_filter( 'woocommerce_review_gravatar_size', array( $this, 'review_gravatar_size' ) );

        // Single Product Layout

        /* Cart */
        remove_action( 'woocommerce_cart_collaterals', 'woocommerce_cross_sell_display' );
        remove_action( 'woocommerce_cart_collaterals', 'woocommerce_cart_totals', 10 );
        add_action( 'woocommerce_cart_collaterals', 'woocommerce_cart_totals' );

        /* Checkout */
        remove_action( 'woocommerce_checkout_order_review', 'woocommerce_checkout_payment', 20 );
        add_action( 'woocommerce_checkout_after_order_review', 'woocommerce_checkout_payment' );

        /* Yith Quickview */
        if ( function_exists( 'YITH_WCQV_Frontend' ) ) {

            add_filter( 'yith_add_quick_view_button_html', '__return_false' );
        }

        /* Yith Compare */
        if ( class_exists( 'YITH_Woocompare' ) ) {
            global $yith_woocompare;
            remove_action( 'woocommerce_after_shop_loop_item', array( $yith_woocompare->obj, 'add_compare_link' ), 20 );
            remove_action( 'woocommerce_single_product_summary', array( $yith_woocompare->obj, 'add_compare_link' ), 35 );
            add_filter( 'yith_woocompare_compare_added_label', '__return_empty_string' );
            add_action( 'woocommerce_thumbnail_shop_loop_item', array( $this, 'wooc_print_compare_icon' ), 20 );

        }

        /* Yith Wishlist */
        if ( function_exists( 'YITH_WCWL_Frontend' ) && class_exists( 'YITH_WCWL_Ajax_Handler' ) ) {
            $wishlist_init = YITH_WCWL_Frontend();
            remove_action( 'wp_head', array( $wishlist_init, 'add_button' ) );
            add_action( 'wp_ajax_etrade_add_to_wishlist', array( $this, 'add_to_wishlist' ) );
            add_action( 'wp_ajax_nopriv_etrade_add_to_wishlist', array( $this, 'add_to_wishlist' ) );
        }

        remove_action( 'woocommerce_widget_shopping_cart_buttons', 'woocommerce_widget_shopping_cart_button_view_cart', 10 );
        remove_action( 'woocommerce_widget_shopping_cart_buttons', 'woocommerce_widget_shopping_cart_proceed_to_checkout', 20 );

        add_action( 'woocommerce_widget_shopping_cart_buttons', array( $this, 'etrade_woocommerce_widget_shopping_cart_button_view_cart' ), 10 );
        add_action( 'woocommerce_widget_shopping_cart_buttons', array( $this, 'etrade_woocommerce_widget_shopping_cart_proceed_to_checkout' ), 20 );

    }


    public static function axil_social_sharing() {
          $axil_options = Helper::axil_get_options();
          
        $url   = urlencode( get_permalink() );
        $title = urlencode( get_the_title() );


        $sharer = array(
            'facebook' => array(
                'url'  => "http://www.facebook.com/sharer.php?u=$url",
                'icon' => 'fa-facebook',
            ),
            'twitter'  => array(
                'url'  => "https://twitter.com/intent/tweet?source=$url&text=$title:$url",
                'icon' => 'fa-twitter'
            ),
            'linkedin' => array(
                'url'  => "http://www.linkedin.com/shareArticle?mini=true&url=$url&title=$title",
                'icon' => 'fa-linkedin'
            ),
            'pinterest'=> array(
                'url'  => "http://pinterest.com/pin/create/button/?url=$url&description=$title",
                'icon' => 'fa-pinterest'
            ),
            'tumblr'   => array(
                'url'  => "http://www.tumblr.com/share?v=3&u=$url &quote=$title",
                'icon' => 'fa-tumblr'
            ),
            'reddit'   => array(
                'url'  => "http://www.reddit.com/submit?url=$url&title=$title",
                'icon' => 'fa-reddit'
            ),
            'vk'       => array(
                'url'  => "http://vkontakte.ru/share.php?url=$url",
                'icon' => 'fa-vk'
            ),
        );

        foreach ( $axil_options['wc_share'] as $key => $value ) {
            if ( !$value ) {
                unset( $sharer[$key] );
            }
        }
        return $sharer;
    }

    public static function wooc_print_compare_icon(){
        $axil_options = Helper::axil_get_options();
 

        if ( !class_exists( 'YITH_Woocompare' ) ) {
            return false;
        }

        if ( is_shop() && !$axil_options['display_shop_compare'] ) {
            return false;
        }

        if ( is_product() && !$axil_options['wc_product_compare_icon'] ) {
            return false;
        }

        global $product;
        global $yith_woocompare;
        $id  = $product->get_id();
        $url = method_exists( $yith_woocompare->obj, 'add_product_url' ) ? $yith_woocompare->obj->add_product_url( $id ) : '';

        $html = '';
        $html .= '<i class="fal fa-repeat-alt"></i>';
         
        ?>

        <a href="<?php echo esc_url( $url );?>" class="compare button" data-product_id="<?php echo esc_attr( $id );?>" title="<?php esc_attr_e( 'Add To Compare', 'etrade' ); ?>"><?php echo wp_kses_post( $html ); ?></a>
        <?php
    }

    public static function instance() {
        if ( null == self::$instance ) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * Output the view cart button.
     */
    public function etrade_woocommerce_widget_shopping_cart_button_view_cart() {
        echo '<a href="' . esc_url( wc_get_cart_url() ) . '" class="axil-btn btn-bg-primary viewcart-btn">' . esc_html__( 'View cart', 'etrade' ) . '</a>';
    }

    /**
     * Output the proceed to checkout button.
     */
    public function etrade_woocommerce_widget_shopping_cart_proceed_to_checkout() {
        echo '<a href="' . esc_url( wc_get_checkout_url() ) . '" class="axil-btn btn-bg-secondary checkout-btn">' . esc_html__( 'Checkout', 'etrade' ) . '</a>';
    }

    public static function wooc_woocommerce_version_check( $version = '3.0' ) {
        if ( class_exists( 'WooCommerce' ) ) {
            global $woocommerce;
            if ( version_compare( $woocommerce->version, $version, ">=" ) ) {
                return true;
            }
        }
        return false;
    }
    public function theme_support() {
        add_theme_support( 'etrade', array(
            'gallery_thumbnail_image_width' => 150,
        ) );

        add_theme_support( 'wc-product-gallery-zoom' );
        add_theme_support( 'wc-product-gallery-lightbox' );
        add_theme_support( 'wc-product-gallery-slider' );

        add_post_type_support( 'product', 'page-attributes' );
    }

    public function body_classes( $classes ) {
        $axil_options = Helper::axil_get_options();

        if ( isset( $_GET['sidebar'] ) ) {
            if ( $_GET['sidebar'] == 'left' ) {
                $classes[] = 'shop-left-sidebar';
            }
            if ( $_GET['sidebar'] == 'right' ) {
                $classes[] = 'shop-right-sidebar';
            }
            if ( $_GET['sidebar'] == 'full' ) {
                $classes[] = 'shop-full-width';
            }
        } else {
            $classes[] = 'shop-' . $axil_options['shop_layout'];
        }

        $layout = Helper::axil_product_layout_style();
        $classes[] = 'single-product-layout-' . $layout;

        return $classes;
    }

    
    public function disable_styles( $enqueue_styles ) {
        if ( !is_cart() && !is_checkout() ) {
            unset( $enqueue_styles['woocommerce-layout'] ); // Remove the layout
        }
        unset( $enqueue_styles['woocommerce-smallscreen'] ); // Remove the smallscreen optimisation
        return $enqueue_styles;
    }

    public function page_title( $title ) {
        if ( is_woocommerce() ) {
            $title = woocommerce_page_title( false );
        }

        return $title;
    }

    public function axil_header_cart_count( $fragments ) {
        $cartCount = WC()->cart->get_cart_contents_count();
        $className = $count = '';
        if ( $cartCount > 0 ) {
            $className = 'cart-count';
            $count = $cartCount;
        }

        $number = '<span class="' . $className . ' header-cart-num">' . $count . '</span>';
        $fragments['span.header-cart-num'] = $number;
        return $fragments;
    }

    public function placeholder_img_src( $src ) {
        $default = WC()->plugin_url() . '/assets/images/placeholder.png';

        if ( $src == $default ) {
            $src = Helper::get_img( 'wc-placeholder.jpg' );
        }

        return $src;
    }

    public function pagination() {

        $axil_options = Helper::axil_get_options();

        if ( isset( $_GET['pagination'] ) ) {
            if ( $_GET['pagination'] == 'numb' ) {
                $axil_options['wooc_pagination'] = 'numbered';
            }
        }

        if ( $axil_options['wooc_pagination'] == 'load-more' ) {
            LoadMore::instance()->init( 'loadmore' );
        } else if ( $axil_options['wooc_pagination'] == 'infinity-scroll' ) {
            LoadMore::instance()->init( 'infiscroll' );
        } else {
            get_template_part( 'template-parts/pagination' );
        }

    }

    public function smallscreen_breakpoint() {
        return '767px';
    }

    public function star_rating_html( $html, $rating, $count ) {
        $html = 0 < $rating ? '<div class="product-rating"><div class="star-rating"><span class="review-link" style="width:' . (  ( $rating / 5 ) * 100 ) . '%"></span></div>' : '';
        return $html;
    }

    public function etrade_woocommerce_result_count() {
        if ( !wc_get_loop_prop( 'is_paginated' ) || !woocommerce_products_will_display() ) {
            return;
        }
        $args = array(
            'total'    => wc_get_loop_prop( 'total' ),
            'per_page' => wc_get_loop_prop( 'per_page' ),
            'current'  => wc_get_loop_prop( 'current_page' ),
        );

        wc_get_template( 'loop/result-count.php', $args );
    }

    public function wrapper_start() {
        self::get_custom_template_part( 'shop-header' );
    }

    public function wrapper_end() {
        self::get_custom_template_part( 'shop-footer' );
    }

    public function shop_topbar() {
        $axil_options = Helper::axil_get_options();
        self::get_custom_template_part( 'shop-top-default' );

    }

    public function loop_shop_per_page() {
        $axil_options = Helper::axil_get_options();

        if ( isset( $_GET['sidebar'] ) ) {
            if ( $_GET['sidebar'] == 'left' ) {

                return $axil_options['wooc_num_product'];
            }
            if ( $_GET['sidebar'] == 'right' ) {
                return $axil_options['wooc_num_product'];
            }
            if ( $_GET['sidebar'] == 'full' ) {

                return '12';
            }
        } else {
            return $axil_options['wooc_num_product'];
        }

    }

    public function loop_shop_columns() {
        $axil_options = Helper::axil_get_options();
        if ( $axil_options['shop_layout'] == 'full-width' ) {
            return 4;
        }
        return 3;
    }

    public static function wc_get_loop_price( $product, $sale_price_only = false ) {

        if ( $sale_price_only ) {
            $price_html = wc_price( wc_get_price_to_display( $product ) ) . $product->get_price_suffix();
        } else {
            $price_html = $product->get_price_html();
        }?>

	<div class="product-price-variant">
		<?php if ( $product->get_price_html() ): ?>
		    <div class="price"><?php echo wp_kses( $price_html, 'alltext_allow' ); ?></div>
		<?php endif;?>
	</div>

<?php
return true;
    }

 
    public function sale_flash( $args, $post, $product ) {

        $axil_options = Helper::axil_get_options();
        if ( $axil_options['wooc_sale_label'] == 'percentage' ) {

            if ( $product->get_type() === 'variable' ) {
                // Get product variation prices
                $product_variation_prices = $product->get_variation_prices();

                $highest_sale_percent = 0;

                foreach ( $product_variation_prices['regular_price'] as $key => $regular_price ) {
                    // Get sale price
                    $sale_price = $product_variation_prices['sale_price'][$key];

                    // Is product variation on sale?
                    if ( $sale_price < $regular_price ) {
                        $sale_percent = round(  (  ( $regular_price - $sale_price ) / $regular_price ) * 100 );

                        // Is current sale percent highest?
                        if ( $sale_percent > $highest_sale_percent ) {
                            $highest_sale_percent = $sale_percent;
                        }
                    }
                }
                // Return variation sale percent
                $poff = esc_html( 'OFF', 'etrade' );
                return sprintf( '<div class="product-badget">%s%% %s</div>', $highest_sale_percent, $poff );

            } else {
                $regular_price = $product->get_regular_price();
                $sale_percent = 0;

                // Make sure calculated
                if ( intval( $regular_price ) > 0 ) {
                    $sale_percent = round(  (  ( $regular_price - $product->get_sale_price() ) / $regular_price ) * 100 );
                }

                $poff = esc_html( 'OFF', 'etrade' );
                return sprintf( '<div class="product-badget">%s%% %s</div>', $sale_percent, $poff );

            }

        } else {
 
                $price = $product->get_regular_price();
                $sale = $product->get_sale_price();

                // Fix NAN in grouped product
                if ( !$price ) {
                    return $args;
                } 

                $discount = (  ( $price - $sale ) / $price ) * 100;
                $discount = round( $discount );
                $poff = esc_html( 'OFF', 'etrade' );
                $text = isset( $axil_options['wooc_sale_label_txt'] ) ? $axil_options['wooc_sale_label_txt'] : esc_html__( 'Sale!', 'etrade' );

               return sprintf( '<div class="product-badget">%s</div>', $text );


            
        }
    }
 
    public function hide_product_data_tab( $tabs ) {
        $axil_options = Helper::axil_get_options();
        if ( empty( $axil_options['wooc_description'] ) ) {
            unset( $tabs['description'] );
        }
        if ( empty( $axil_options['wooc_reviews'] ) ) {
            unset( $tabs['reviews'] );
        }
        if ( empty( $axil_options['wooc_additional_info'] ) ) {
            unset( $tabs['additional_information'] );
        }
        return $tabs;
    }

    public function review_gravatar_size() {
        return '60';
    }

    public function product_review_form( $comment_form ) {
        $commenter = wp_get_current_commenter();

        $comment_form['fields'] = array(
            'author' => '<div class="row"><div class="col-sm-6"><div class="comment-form-author form-group"><label>Name <span class="require">*</span></label><input id="author" name="author" type="text" class="form-control" value="' . esc_attr( $commenter['comment_author'] ) . '"  required /></div></div>',
            'email'  => '<div class="comment-form-email col-sm-6"><div class="form-group"><label>Email <span class="require">*</span> </label><input id="email" class="form-control" name="email" type="email" value="' . esc_attr( $commenter['comment_author_email'] ) . '"   required /></div></div></div>',
        );

        $comment_form['comment_field'] = '';

        if ( get_option( 'woocommerce_enable_review_rating' ) === 'yes' ) {
            $comment_form['comment_field'] = '<div class="rating-wrapper d-flex-center mb--40 comment-form-rating"><label for="rating">' . esc_html__( 'Your Rating', 'etrade' ) . '</label>
			<select name="rating" id="rating" required>
			<option value="">' . esc_html__( 'Rate&hellip;', 'etrade' ) . '</option>
			<option value="5">' . esc_html__( 'Perfect', 'etrade' ) . '</option>
			<option value="4">' . esc_html__( 'Good', 'etrade' ) . '</option>
			<option value="3">' . esc_html__( 'Average', 'etrade' ) . '</option>
			<option value="2">' . esc_html__( 'Not that bad', 'etrade' ) . '</option>
			<option value="1">' . esc_html__( 'Very Poor', 'etrade' ) . '</option>
			</select></div>';
        }

        $comment_form['comment_field'] .= '<div class="form-group comment-form-comment"><label>Other Notes (optional)</label><textarea id="comment" name="comment" class="form-control" placeholder="' . esc_html__( 'Your Comment', 'etrade' ) . '"  required></textarea></div>';

        return $comment_form;
    }

    public static function get_template_part( $template, $args = array() ) {
        extract( $args );

        $template = '/' . $template . '.php';

        if ( file_exists( get_stylesheet_directory() . $template ) ) {
            $file = get_stylesheet_directory() . $template;
        } else {
            $file = get_template_directory() . $template;
        }

        require $file;
    }

    public static function get_custom_template_part( $template, $args = array() ) {
        $template = 'woocommerce/custom/template-parts/' . $template;
        self::get_template_part( $template, $args );
    }

    public static function product_slider( $products, $title, $before_heading, $type = '' ) {
        $filename = '/woocommerce/custom/template-parts/product-slider.php';

        $child_file = get_stylesheet_directory() . $filename;
        $parent_file = get_template_directory() . $filename;

        if ( file_exists( $child_file ) ) {
            $file = $child_file;
        } else {
            $file = $parent_file;
        }

        include $file;
    }
    public static function product_grid( $products, $title, $before_heading, $type = '' ) {
        $filename = '/woocommerce/custom/template-parts/product-grid.php';

        $child_file = get_stylesheet_directory() . $filename;
        $parent_file = get_template_directory() . $filename;

        if ( file_exists( $child_file ) ) {
            $file = $child_file;
        } else {
            $file = $parent_file;
        }

        include $file;
    }

    public static function wooc_print_add_to_cart_icon( $icon = true, $text = true, $toggle = true ) {
        global $product;
        $quantity = 1;
        $class = implode( ' ', array_filter( array(
            'cart-btn button',
            $toggle ? 'wooc-toltp' : "non-wooc-toltp",
            'product_type_' . $product->get_type(),
            $product->is_purchasable() && $product->is_in_stock() ? 'add_to_cart_button' : '',
            $product->supports( 'ajax_add_to_cart' ) ? 'ajax_add_to_cart' : '',
        ) ) );

        $html = '';

        if ( $icon ) {
            $html .= '<i class="fal fa-shopping-cart"></i>';
        }

        if ( $text ) {
            $html .= '<span>' . $product->add_to_cart_text() . '</span>';
        }

        echo sprintf( '<a rel="nofollow" data-tips="%s" title="%s" href="%s" data-quantity="%s" data-product_id="%s" data-product_sku="%s" class="%s">' . $html . '</a>',
            esc_attr( $product->add_to_cart_text() ),
            esc_attr( $product->add_to_cart_text() ),
            esc_url( $product->add_to_cart_url() ),
            esc_attr( isset( $quantity ) ? $quantity : 1 ),
            esc_attr( $product->get_id() ),
            esc_attr( $product->get_sku() ),
            esc_attr( isset( $class ) ? $class : 'cart-btn' )
        );

    }

    public static function nft_product_counter( $icon = true ) {
        $axil_options = Helper::axil_get_options();
        global $product;
        if ( class_exists( 'ACF' ) ) {
            $product_counter = get_field( 'product_counter' );
            $product_counter = get_field( 'product_counter' );
        }

        ?>
		  <div class="axilcoutdown2">
		  	<div class="poster-countdown countdown mb--30" data-time="<?php echo esc_attr( $product_counter ); ?>"></div>
		  </div>
		  <?php
return true;
    }

    public static function print_quickview_icon( $icon = true, $text = false ) {
        $axil_options = Helper::axil_get_options();

        if ( !function_exists( 'YITH_WCQV_Frontend' ) ) {
            return false;
        }
        if ( is_shop() && !$axil_options['quickview'] ) {
            return false;
        }
        global $product;
        $html = '';
        if ( $icon ) {
            $html .= '<i class="far fa-eye"></i>';
        }
        if ( $text ) {
            $html .= '<span>' . esc_html__( 'QuickView', 'etrade' ) . '</span>';
        }
        $allowed_tags = wp_kses_allowed_html( 'post' );
        ?>
		<a href="#" class="yith-wcqv-button" data-product_id="<?php echo esc_attr( $product->get_id() ); ?>" title="<?php esc_attr_e( 'QuickView', 'etrade' );?>">
			<?php echo wp_kses( $html, $allowed_tags ); ?>
		</a>
		<?php
return true;
    }

    public static function axil_add_to_wishlist_icon( $icon = true, $text = false ) {
        if ( !defined( 'YITH_WCWL' ) ) {
            return false;
        }
        self::get_custom_template_part( 'wishlist-icon', compact( 'icon', 'text' ) );
    }

    public function add_to_wishlist() {
        check_ajax_referer( 'add_to_wishlist', 'nonce' );
        \YITH_WCWL_Ajax_Handler::add_to_wishlist();
        wp_die();
    }

    public function remove_from_wishlist() {
        check_ajax_referer( 'etrade_wishlist_nonce', 'nonce' );
        \YITH_WCWL_Ajax_Handler::remove_from_wishlist();
        wp_die();
    }

    public static function get_top_category_name() {
        global $product;

        $terms = wc_get_product_terms( $product->get_id(), 'product_cat', array( 'orderby' => 'parent', 'order' => 'DESC' ) );

        if ( empty( $terms ) ) {
            return '';
        }

        if ( $terms[0]->parent == 0 ) {
            $cat = $terms[0];
        } else {
            $ancestors = get_ancestors( $terms[0]->term_id, 'product_cat', 'taxonomy' );
            $cat_id = end( $ancestors );
            $cat = get_term( $cat_id, 'product_cat' );
        }

        return $cat->name;
    }
    public static function get_loop_category_name() {
        global $product;

        $terms = wc_get_product_terms( $product->get_id(), 'product_cat', array( 'orderby' => 'parent', 'order' => 'DESC' ) );

        if ( empty( $terms ) ) {
            return '';
        }

        if ( $terms[0]->parent == 0 ) {
            $cat = $terms[0];
        } else {
            $ancestors = get_ancestors( $terms[0]->term_id, 'product_cat', 'taxonomy' );
            $cat_id = end( $ancestors );
            $cat = get_term( $cat_id, 'product_cat' );
        }

        return '<a href="' . get_term_link( $cat->slug, 'product_cat' ) . '">' . $cat->name . '</a>';
    }

    public static function get_product_thumbnail( $product, $thumb_size = 'woocommerce_thumbnail' ) {
        $thumbnail = $product->get_image( $thumb_size, array(), false );
        if ( !$thumbnail ) {
            $thumbnail = wc_placeholder_img( $thumb_size );
        }
        return $thumbnail;
    }

    public static function product_display_hover_wrp_class( $product, $display_hover = true ) {

        $class = '';
        if ( $display_hover ) {
            $attachment_ids = $product->get_gallery_image_ids();
            if ( $attachment_ids && isset( $attachment_ids[0] ) ) {
                $product_thumbnail_second = wp_get_attachment_image_src( $attachment_ids[0], 'thumb_size' );
            }
            if ( isset( $product_thumbnail_second[0] ) ) {
                $class = 'has-gallery-image';
            }
        }
        return $class;

    }

    public static function get_product_thumbnail_link2( $product, $thumb_size = 'woocommerce_thumbnail', $display_hover = true ) {

        if ( $display_hover ) {
            return '<a  href="' . esc_attr( $product->get_permalink() ) . '">
		' . self::get_product_thumbnail_w_attr( $product, $thumb_size ) . self::get_product_thumbnail_w_attr_img_class( $product, $thumb_size ) . '</a>';

        } else {
            return '<a  href="' . esc_attr( $product->get_permalink() ) . '">
		' . self::get_product_thumbnail( $product, $thumb_size ) . '</a>';
        }

    }
    public static function get_product_thumbnail_link( $product, $thumb_size = 'woocommerce_thumbnail' ) {
        return '<a  href="' . esc_attr( $product->get_permalink() ) . '">
		' . self::get_product_thumbnail( $product, $thumb_size ) . '</a>';

    }
    public static function get_product_thumbnail_link3( $product, $thumb_size = 'woocommerce_thumbnail' ) {
        return '<a  href="' . esc_attr( $product->get_permalink() ) . '">
		' . self::get_product_thumbnail( $product, $thumb_size ) . '</a>';

    }

    public static function get_product_thumbnail_w_attr( $product, $thumb_size = 'woocommerce_thumbnail' ) {

        $arr = array();
        $thumbnail = $product->get_image( $thumb_size, $arr, false );

        if ( !$thumbnail ) {
            $thumbnail = wc_placeholder_img( $thumb_size );
        }
        return $thumbnail;
    }

    public static function get_product_thumbnail_w_attr_img_class( $product, $thumb_size = 'woocommerce_thumbnail' ) {

        $attachment_ids = $product->get_gallery_image_ids();
        $thumbnail = "";
        if ( $attachment_ids && isset( $attachment_ids[0] ) ) {
            $product_thumbnail_second = wp_get_attachment_image_src( $attachment_ids[0], 'thumb_size' );
            $image_alt = get_post_meta( $attachment_ids[0], '_wp_attachment_image_alt', TRUE );
            $thumbnail = '<img class="hover-img" src="' . $product_thumbnail_second[0] . '" alt="' . $image_alt . '">';

        }

        return $thumbnail;
    }

    public static function get_product_thumbnail_gallery( $product, $thumb_size = 'woocommerce_thumbnail' ) {
        $attachment_ids = $product->get_gallery_image_ids();

        if ( empty( $attachment_ids ) ) {
            return self::get_product_thumbnail_link( $product, $thumb_size );
        }

        $thumb = $product->get_image_id();
        if ( $thumb ) {
            array_unshift( $attachment_ids, $thumb );
        }

        $data = array(
            'slidesToShow' => 1,
            'prevArrow'    => '<button class="slick-prev slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
            'nextArrow'    => '<button class="slick-next slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>',
            'dots'         => false,
            'rtl'          => is_rtl() ? true : false,
        );
        $data = json_encode( $data );
        ?>
		<div class="wooc-slick-slider" data-slick="<?php echo esc_attr( $data ); ?>">
			<?php foreach ( $attachment_ids as $attachment_id ): ?>
				<a href="<?php echo esc_attr( $product->get_permalink() ); ?>"><?php echo wp_get_attachment_image( $attachment_id, $thumb_size ) ?></a>
			<?php endforeach;?>
		</div>
		<?php
}

    public static function get_stock_status() {
        global $product;
        return $product->is_in_stock() ? esc_html__( 'In Stock', 'etrade' ) : esc_html__( 'Out of Stock', 'etrade' );
    }

    public static function is_product_archive() {
        return is_shop() || is_product_taxonomy() ? true : false;
    }

    public static function kses_img( $img ) {
        $allowed_tags = wp_kses_allowed_html( 'post' );
        $attributes = array( 'srcset', 'sizes' );

        foreach ( $attributes as $attribute ) {
            $allowed_tags['img'][$attribute] = true;
        }

        return wp_kses( $img, $allowed_tags );
    }

    public static function etrade_wc_format_stock_for_display( $product ) {

        $display = __( 'In stock', 'etrade' );
        $stock_amount = $product->get_stock_quantity();

        switch ( get_option( 'woocommerce_stock_format' ) ) {
        case 'low_amount':
            if ( $stock_amount <= get_option( 'woocommerce_notify_low_stock_amount' ) ) {
                /* translators: %s: stock amount */
                $display = sprintf( __( 'Only %s left in stock', 'etrade' ), wc_format_stock_quantity_for_display( $stock_amount, $product ) );
            }
            break;
        case '':
            /* translators: %s: stock amount */
            $display = sprintf( __( '%s in stock', 'etrade' ), wc_format_stock_quantity_for_display( $stock_amount, $product ) );
            break;
        }

        if ( $product->backorders_allowed() && $product->backorders_require_notification() ) {
            $display .= ' ' . __( '(can be backordered)', 'etrade' );
        }
        return $display_html = '<span class="product-meta-title">' . esc_html( "Availability", "etrade" ) . ' : </span><span class="product-meta-content sku">' . $display . '<span>';

    }

    /*
     *    Category menu: Create single category list HTML
     */

    public static function wooc_category_menu_create_list( $category, $current_cat_id, $categories_menu_divider, $current_top_cat_id = null ) {
        $output = '<li class="cat-item-' . $category->term_id;

        // Is this the current category?
        if ( $current_cat_id == $category->term_id ) {
            $output .= ' current-cat';
        }
        // Is this the current top parent-category?
        else if ( $current_top_cat_id && $current_top_cat_id == $category->term_id ) {
            $output .= ' current-parent-cat';
        }

        $output .= '">' . $categories_menu_divider . '<a href="' . esc_url( get_term_link( (int) $category->term_id, 'product_cat' ) ) . '">' . esc_attr( $category->name ) . '</a></li>';

        return $output;
    }

    /*
     *    Product category menu
     */

    public static function wooc_category_menu() {
        global $wp_query;
        $axil_options = Helper::axil_get_options();
        $current_cat_id = ( is_tax( 'product_cat' ) ) ? $wp_query->queried_object->term_id : '';
        $is_category = ( strlen( $current_cat_id ) > 0 ) ? true : false;
        $hide_empty = ( $axil_options['shop_categories_hide_empty'] ) ? true : false;

        // Should top-level categories be displayed?
        if ( $axil_options['shop_categories_top_level'] == '0' && $is_category ) {
            self::wooc_sub_category_menu_output( $current_cat_id, $hide_empty );
        } else {
            self::wooc_category_menu_output( $is_category, $current_cat_id, $hide_empty );
        }
    }

/*
 *    Product category menu: Output
 */

    public static function wooc_category_menu_output( $is_category, $current_cat_id, $hide_empty ) {

        global $wp_query;

        $page_id = wc_get_page_id( 'shop' );
        $page_url = get_permalink( $page_id );
        $hide_sub = true;
        $current_top_cat_id = null;
        $all_categories_class = '';

        // Is this a category page?
        if ( $is_category ) {
            $hide_sub = false;

            // Get current category's top-parent id
            $current_cat_parents = get_ancestors( $current_cat_id, 'product_cat' );
            if ( !empty( $current_cat_parents ) ) {
                $current_top_cat_id = end( $current_cat_parents ); // Get last item from array
            }

            // Get current category's direct children
            $current_cat_direct_children = get_terms( 'product_cat',
                array(
                    'fields'       => 'ids',
                    'parent'       => $current_cat_id,
                    'hierarchical' => true,
                    'hide_empty'   => $hide_empty,
                )
            );
            $category_has_children = ( empty( $current_cat_direct_children ) ) ? false : true;
        } else {
            // No current category, set "All" as current (if not product tag archive or search)
            if ( !is_product_tag() && !isset( $_REQUEST['s'] ) ) {
                $all_categories_class = ' class="current-cat"';
            }
        }

        $output_cat = '<li' . $all_categories_class . '><a href="' . esc_url( $page_url ) . '">' . esc_html__( 'All', 'etrade' ) . '</a></li>';
        $output_sub_cat = '';
        $output_current_sub_cat = '';

        // Categories order
        $orderby = 'slug';
        $order = 'ASC';
        if ( isset( $axil_options['shop_categories_orderby'] ) ) {
            $orderby = $axil_options['shop_categories_orderby'];
            $order = $axil_options['shop_categories_order'];
        }

        $args = array(
            'taxonomy'     => 'product_cat',
            'type'         => 'post',
            'orderby'      => $orderby, // Note: 'name' sorts by product category "menu/sort order"
            'order' => strtoupper( $order ),
            'hide_empty'   => $hide_empty,
            'hierarchical' => 0,
        );
        // Note: The "force_menu_order_sort" parameter added in WooCommerce 3.6 must be set to make "orderby" work (the "name" option doesn't work otherwise)
        // - See the "../woocommerce/includes/wc-term-functions.php" file
        $args['force_menu_order_sort'] = ( $orderby == 'name' ) ? true : false;

        $categories = get_categories( $args );

        // Categories menu divider
        $categories_menu_divider = apply_filters( 'wooc_shop_categories_divider', '<span>|</span>' );

        foreach ( $categories as $category ) {
            // Is this a sub-category?
            if ( $category->parent != '0' ) {
                // Should sub-categories be included?
                if ( $hide_sub ) {
                    continue; // Skip to next loop item
                } else {
                    if (
                        $category->parent == $current_cat_id || // Include current sub-category's children
                        !$category_has_children && $category->parent == $wp_query->queried_object->parent// Include categories with the same parent (if current sub-category doesn't have children)
                    ) {
                        $output_sub_cat .= self::wooc_category_menu_create_list( $category, $current_cat_id, $categories_menu_divider );
                    } else if (
                        $category->term_id == $current_cat_id // Include current sub-category (save in a separate variable so it can be appended to the start of the category list)
                    ) {
                        $output_current_sub_cat = self::wooc_category_menu_create_list( $category, $current_cat_id, $categories_menu_divider );
                    }
                }
            } else {
                $output_cat .= self::wooc_category_menu_create_list( $category, $current_cat_id, $categories_menu_divider, $current_top_cat_id );
            }
        }

        if ( strlen( $output_sub_cat ) > 0 ) {
            $output_sub_cat = '<ul class="wooc-shop-sub-categories">' . $output_current_sub_cat . $output_sub_cat . '</ul>';
        }
        $output = $output_cat . $output_sub_cat;
        $allowed_tags = wp_kses_allowed_html( 'post' );
        echo wp_kses( $output, $allowed_tags );
    }

    public static function wooc_wc_get_star_rating_html( $rating, $count = 0 ) {
        $html = '<span style="width:' . (  ( $rating / 5 ) * 100 ) . '%">';

        if ( 0 < $count ) {
            /* translators: 1: rating 2: rating count */
            $html .= sprintf( _n( 'Rated %1$s out of 5 based on %2$s customer rating', 'Rated %1$s out of 5 based on %2$s customer ratings', $count, 'etrade' ), '<strong class="rating">' . esc_html( $rating ) . '</strong>', '<span class="rating">' . esc_html( $count ) . '</span>' );
        } else {
            /* translators: %s: rating */
            $html .= sprintf( esc_html__( '%s', 'etrade' ), '<strong class="rating">' . esc_html( $rating ) . '</strong>' );
        }

        $html .= '</span>';

        return apply_filters( 'woocommerce_get_star_rating_html', $html, $rating, $count );
    }

    public static function wooc_wc_get_star_rating_html2( $rating, $count = 0 ) {
        $html = '<span style="width:' . (  ( $rating / 5 ) * 100 ) . '%">';

        if ( 0 < $count ) {
            /* translators: 1: rating 2: rating count */
            $html .= sprintf( _n( 'Rated %1$s out of 5 based on %2$s customer rating', 'Rated %1$s out of 5 based on %2$s customer ratings', $count, 'etrade' ), '<span class="rating">' . esc_html( $rating ) . '</span>', '<span class="rating">' . esc_html( $count ) . '</span>' );
        } else {
            /* translators: %s: rating */
            $html .= sprintf( esc_html__( '%s', 'etrade' ), '<strong class="rating">' . esc_html( $rating ) . '</strong>' );
        }

        $html .= '</span>';

        return apply_filters( 'woocommerce_get_star_rating_html', $html, $rating, $count );
    }

    /**
     * Get HTML for ratings.
     *
     * @since  3.0.0
     * @param  float $rating Rating being shown.
     * @param  int   $count  Total number of ratings.
     * @return string
     */
    public static function wooc_wc_get_rating_html( $rating, $count = 0 ) {

        $html = '';

        if ( 0 < $rating ) {
            /* translators: %s: rating */
            $label = sprintf( __( 'Rated %s out of 5', 'etrade' ), $rating );

            $html = '<div class="product-rating"><i class="fas fa-star"></i><span class="rating-number" role="img" aria-label="' . esc_attr( $label ) . '">' . WooC_Functions::wooc_wc_get_star_rating_html( $rating, $count ) . '</span></div>';
        }

        return $html;
    }


/**
     * Get HTML for ratings.
     *
     * @since  3.0.0
     * @param  float $rating Rating being shown.
     * @param  int   $count  Total number of ratings.
     * @return string
     */
    public static function wooc_wc_get_sale() {

        $html = '';
        $axil_options = Helper::axil_get_options();

         $html = isset( $axil_options['wooc_sale_label_txt'] ) ? $axil_options['wooc_sale_label_txt'] : esc_html__( 'Sale!', 'etrade' );

        return $html;
    }

    /**
     * Get HTML for ratings.
     *
     * @since  3.0.0
     * @param  float $rating Rating being shown.
     * @param  int   $count  Total number of ratings.
     * @return string
     */
    public static function wooc_wc_get_rating_html2( $rating, $count = 0 ) {

        $html = '';

        if ( 0 < $rating ) {
            /* translators: %s: rating */
            $label = sprintf( __( 'Rated %s out of 5', 'etrade' ), $rating );

            $html = '<span class=" rating-number" role="img" aria-label="' . esc_attr( $label ) . '">' . WooC_Functions::wooc_wc_get_star_rating_html2( $rating, $count ) . '</span>';
        }

        return $html;
    }

    public static function wooc_sub_category_menu_back_link( $url, $categories_menu_divider, $class = '' ) {
        return '<li class="wooc-category-back-button' . esc_attr( $class ) . '"><a href="' . esc_url( $url ) . '"><i class="wooc-font wooc-font-arrow-left"></i> ' . esc_html__( 'Back', 'etrade' ) . '</a>' . $categories_menu_divider . '</li>';
    }

    public static function wooc_sub_category_menu_output( $current_cat_id, $hide_empty ) {
        global $wp_query;
        $axil_options = Helper::axil_get_options();
        // Categories menu divider
        $categories_menu_divider = apply_filters( 'wooc_shop_categories_divider', '<span>&frasl;</span>' );

        $output_sub_categories = '';

        // Categories order
        $orderby = 'slug';
        $order = 'asc';
        if ( isset( $axil_options['shop_categories_orderby'] ) ) {
            $orderby = $axil_options['shop_categories_orderby'];
            $order = $axil_options['shop_categories_order'];
        }

        $sub_categories = get_categories( array(
            'type'         => 'post',
            'parent'       => $current_cat_id,
            'orderby'      => $orderby, // Note: 'name' sorts by product category "menu/sort order"
            'order' => $order,
            'hide_empty'   => $hide_empty,
            'hierarchical' => 1,
            'taxonomy'     => 'product_cat',
        ) );

        $has_sub_categories = ( empty( $sub_categories ) ) ? false : true;

        // Is there any sub-categories available
        if ( $has_sub_categories ) {
            $current_cat_name = apply_filters( 'wooc_shop_parent_category_title', $wp_query->queried_object->name );

            foreach ( $sub_categories as $sub_category ) {
                $output_sub_categories .= self::wooc_category_menu_create_list( $sub_category, $current_cat_id, $categories_menu_divider );
            }
        } else {
            $current_cat_name = $wp_query->queried_object->name;
        }

        // "Back" link
        $output_back_link = '';
        if ( $axil_options['shop_categories_back_link'] ) {
            $parent_cat_id = $wp_query->queried_object->parent;

            if ( $parent_cat_id ) {
                // Back to parent-category link
                $parent_cat_url = get_term_link( (int) $parent_cat_id, 'product_cat' );
                $output_back_link = self::wooc_sub_category_menu_back_link( $parent_cat_url, $categories_menu_divider );
            } else if ( $axil_options['shop_categories_back_link'] == '1st' ) {
                // 1st sub-level - Back to top-level (main shop page) link
                $shop_page_id = wc_get_page_id( 'shop' );
                $shop_url = get_permalink( $shop_page_id );
                $output_back_link = self::wooc_sub_category_menu_back_link( $shop_url, $categories_menu_divider, ' 1st-level' );
            }
        }

        // Current category link
        $current_cat_url = get_term_link( (int) $current_cat_id, 'product_cat' );
        $output_current_cat = '<li class="current-cat"><a href="' . esc_url( $current_cat_url ) . '">' . esc_html( $current_cat_name ) . '</a></li>';

        $allowed_tags = wp_kses_allowed_html( 'post' );
        echo wp_kses( $output_back_link . $output_current_cat . $output_sub_categories, $allowed_tags );
    }

}
WooC_Functions::instance(); 