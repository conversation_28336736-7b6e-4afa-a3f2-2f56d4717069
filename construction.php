<?php
/**
 * Template Name: Maintenance
 * Maintenance / Coming Soon Mode Template.
 *
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package etrade
 */

$axil_options = Helper::axil_get_options();
$logo = empty( $axil_options['axil_head_logo']['url'] ) ? Helper::get_img( 'logo/logo.svg' ) : $axil_options['axil_head_logo']['url'];
$ucimage = empty( $axil_options['under_construction_page_image']['url'] ) ? Helper::get_img( 'logo/logo-2.svg' ) : $axil_options['under_construction_page_image']['url'];
?>
<!doctype html>
<html <?php language_attributes();?>>
<head>
    <meta charset="<?php echo esc_attr( get_bloginfo( 'charset' ) ) ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <?php wp_head();?>
</head>
<body <?php body_class();?>>
<?php
if ( function_exists( 'wp_body_open' ) ) {
    wp_body_open();
}?>
<div id="main-wrapper" class="main-wrapper">
    <div class="comming-soon-area">
        <div class="row align-items-center">
            <div class="col-xl-4 col-lg-6">
                <div class="comming-soon-banner">
                    <img src="<?php echo esc_url( $ucimage ); ?>" alt="<?php echo esc_attr__( 'Coming Soon', 'etrade' ); ?>">
                </div>
            </div>
            <div class="col-lg-5 offset-xl-1">
                <div class="comming-soon-content">
                    <div class="brand-logo">
                      <?php if ( isset( $axil_options['axil_logo_type'] ) ): ?>
                            <a href="<?php echo esc_url( home_url( '/' ) ); ?>"
                               title="<?php echo esc_attr( get_bloginfo( 'name' ) ); ?>" rel="home">
                                <?php if ( 'image' == $axil_options['axil_logo_type'] ): ?>
                                    <?php if ( $axil_options['axil_head_logo'] ) {?>
                                        <img class="dark-logo" src="<?php echo esc_url( $logo ); ?>" alt="<?php echo esc_attr( get_bloginfo( 'name' ) ); ?>">
                                    <?php }?>
                                <?php else: ?>
                                    <?php if ( 'text' == $axil_options['axil_logo_type'] ): ?>
                                        <?php echo esc_html( $axil_options['axil_logo_text'] ); ?>
                                    <?php endif?>
                                <?php endif?>
                            </a>
                            <?php else: ?>
                            <h3>
                                <a href="<?php echo esc_url( home_url( '/' ) ); ?>"
                                   title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>" rel="home">
                                    <?php if ( isset( $axil_options['axil_logo_text'] ) ? $axil_options['axil_logo_text'] : '' ) {
                                        echo esc_html( $axil_options['axil_logo_text'] );
                                    } else {
                                        bloginfo( 'name' );
                                    }
                                    ?>
                                </a>
                            </h3>
                            <?php $description = get_bloginfo( 'description', 'display' );
                            if ( $description || is_customize_preview() ) {?>
                                <p class="site-description"><?php echo esc_html( $description ); ?> </p>
                            <?php }?>
                        <?php endif?>
                    </div>
                    <h2 class="title"><?php echo esc_html( $axil_options['under_construction_title_text'] ); ?></h2>
                    <?php if ( isset( $axil_options['under_construction_subtitle_text'] ) ): ?>
                        <p><?php echo esc_html( $axil_options['under_construction_subtitle_text'] ); ?></p>
                    <?php endif?>
                    <?php if ( isset( $axil_options['under_construction_countdown_enable'] ) ): ?>
                        <div data-time="<?php echo esc_html( $axil_options['under_construction_date'] ); ?>" class="coming-countdown countdown"></div>
                    <?php endif?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php wp_footer();?>
</body>
</html>