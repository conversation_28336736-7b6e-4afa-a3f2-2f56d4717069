@font-face {
	font-family: 'Unycon';
	src:url('fonts/Unycon.eot?-s8ws3j');
	src:url('fonts/Unycon.eot?#iefix-s8ws3j') format('embedded-opentype'),
		url('fonts/Unycon.ttf?-s8ws3j') format('truetype'),
		url('fonts/Unycon.woff?-s8ws3j') format('woff'),
		url('fonts/Unycon.svg?-s8ws3j#Unycon') format('svg');
	font-weight: normal;
	font-style: normal;
}

.unycon {
	font-family: 'Unycon';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.unycon-photo-camera:before {
	content: "\e000";
}
.unycon-camera-diaphragm:before {
	content: "\e007";
}
.unycon-controls:before {
	content: "\e00e";
}
.unycon-vimeo:before {
	content: "\e01b";
}
.unycon-chat:before {
	content: "\e01d";
}
.unycon-scissors:before {
	content: "\e01e";
}
.unycon-avira-antivirus:before {
	content: "\e04b";
}
.unycon-kaspersky-antivirus:before {
	content: "\e05b";
}
.unycon-list-ul:before {
	content: "\e05d";
}
.unycon-gallery:before {
	content: "\e05e";
}
.unycon-desktop:before {
	content: "\e06d";
}
.unycon-toggle-on:before {
	content: "\e06e";
}
.unycon-hand-cash:before {
	content: "\e070";
}
.unycon-global-delivery-arrow:before {
	content: "\e072";
}
.unycon-global-delivery-check:before {
	content: "\e074";
}
.unycon-shopping-cart-delivery:before {
	content: "\e075";
}
.unycon-shopping-cart-check:before {
	content: "\e076";
}
.unycon-shopping-cart-time:before {
	content: "\e077";
}
.unycon-paper-delivery:before {
	content: "\e078";
}
.unycon-paper-check:before {
	content: "\e079";
}
.unycon-crown:before {
	content: "\e07a";
}
.unycon-codeguard:before {
	content: "\e07b";
}
.unycon-toggle-off:before {
	content: "\e07e";
}
.unycon-paper-time:before {
	content: "\e080";
}
.unycon-paper-arrow:before {
	content: "\e081";
}
.unycon-purse-time:before {
	content: "\e082";
}
.unycon-purse-check:before {
	content: "\e083";
}
.unycon-box-check:before {
	content: "\e087";
}
.unycon-box-arrow:before {
	content: "\e088";
}
.unycon-yahoo:before {
	content: "\e08b";
}
.unycon-basket:before {
	content: "\e093";
}
.unycon-basket-plus:before {
	content: "\e095";
}
.unycon-basket-minus:before {
	content: "\e096";
}
.unycon-map-marker:before {
	content: "\e09e";
}
.unycon-cup:before {
	content: "\e0ad";
}
.unycon-tooltip:before {
	content: "\e10b";
}
.unycon-at-circle:before {
	content: "\e11d";
}
.unycon-delivery-non-stop:before {
	content: "\e120";
}
.unycon-hand-free:before {
	content: "\e122";
}
.unycon-box-time-o:before {
	content: "\e123";
}
.unycon-box-lock-o:before {
	content: "\e124";
}
.unycon-box-check-o:before {
	content: "\e125";
}
.unycon-list-check:before {
	content: "\e12b";
}
.unycon-envelope-open:before {
	content: "\e12d";
}
.unycon-label-star-check:before {
	content: "\e135";
}
.unycon-label-star-usd:before {
	content: "\e136";
}
.unycon-label-star-percent:before {
	content: "\e138";
}
.unycon-label-star-1:before {
	content: "\e139";
}
.unycon-warning-circle2:before {
	content: "\e13b";
}
.unycon-apple:before {
	content: "\e13e";
}
.unycon-tag-check:before {
	content: "\e141";
}
.unycon-tag-empty:before {
	content: "\e142";
}
.unycon-tag-new:before {
	content: "\e143";
}
.unycon-price-tag-usd:before {
	content: "\e144";
}
.unycon-tag-percent:before {
	content: "\e146";
}
.unycon-label-circle:before {
	content: "\e147";
}
.unycon-warning:before {
	content: "\e14b";
}
.unycon-shopping-cart:before {
	content: "\e151";
}
.unycon-shopping-cart-full:before {
	content: "\e152";
}
.unycon-shopping-cart-full2:before {
	content: "\e153";
}
.unycon-shopping-cart-plus:before {
	content: "\e154";
}
.unycon-shopping-cart-minus:before {
	content: "\e155";
}
.unycon-shopping-cart2:before {
	content: "\e156";
}
.unycon-shopping-cart3:before {
	content: "\e157";
}
.unycon-credit-cards:before {
	content: "\e159";
}
.unycon-check-circle-o2:before {
	content: "\e15b";
}
.unycon-piggy-bank:before {
	content: "\e160";
}
.unycon-wallet:before {
	content: "\e162";
}
.unycon-box:before {
	content: "\e163";
}
.unycon-money:before {
	content: "\e165";
}
.unycon-coins:before {
	content: "\e166";
}
.unycon-open-sign:before {
	content: "\e167";
}
.unycon-gift:before {
	content: "\e168";
}
.unycon-desert:before {
	content: "\e16a";
}
.unycon-question-circle:before {
	content: "\e16b";
}
.unycon-box-upload:before {
	content: "\e16d";
}
.unycon-coffee:before {
	content: "\e16e";
}
.unycon-store:before {
	content: "\e172";
}
.unycon-certificate:before {
	content: "\e173";
}
.unycon-clothes-women:before {
	content: "\e174";
}
.unycon-shoes-women:before {
	content: "\e175";
}
.unycon-hat-women:before {
	content: "\e176";
}
.unycon-skirt:before {
	content: "\e177";
}
.unycon-shoes-women2:before {
	content: "\e178";
}
.unycon-cog:before {
	content: "\e179";
}
.unycon-cocktail:before {
	content: "\e17a";
}
.unycon-delete-circle:before {
	content: "\e17b";
}
.unycon-box-open:before {
	content: "\e17d";
}
.unycon-cogs:before {
	content: "\e180";
}
.unycon-cogs2:before {
	content: "\e181";
}
.unycon-cog2:before {
	content: "\e182";
}
.unycon-paper-clip:before {
	content: "\e183";
}
.unycon-lightning:before {
	content: "\e184";
}
.unycon-list:before {
	content: "\e185";
}
.unycon-hand-arrows:before {
	content: "\e186";
}
.unycon-link:before {
	content: "\e187";
}
.unycon-tools:before {
	content: "\e188";
}
.unycon-brush:before {
	content: "\e189";
}
.unycon-faq:before {
	content: "\e18b";
}
.unycon-wrench:before {
	content: "\e190";
}
.unycon-hammer:before {
	content: "\e191";
}
.unycon-wifi:before {
	content: "\e193";
}
.unycon-dashboard:before {
	content: "\e194";
}
.unycon-broken-link:before {
	content: "\e195";
}
.unycon-pin:before {
	content: "\e196";
}
.unycon-light-bulb:before {
	content: "\e197";
}
.unycon-pen-ruler:before {
	content: "\e198";
}
.unycon-minus-circle:before {
	content: "\e19b";
}
.unycon-pizza:before {
	content: "\e19e";
}
.unycon-medal:before {
	content: "\e1ad";
}
.unycon-chess:before {
	content: "\e1ea";
}
.unycon-scissors1:before {
	content: "\e200";
}
.unycon-tool-square:before {
	content: "\e202";
}
.unycon-tool-square2:before {
	content: "\e203";
}
.unycon-tool-square3:before {
	content: "\e204";
}
.unycon-tool-square4:before {
	content: "\e205";
}
.unycon-tool-square5:before {
	content: "\e206";
}
.unycon-tool-circle:before {
	content: "\e207";
}
.unycon-tool-double-square:before {
	content: "\e208";
}
.unycon-tool-double-square2:before {
	content: "\e209";
}
.unycon-coffee2:before {
	content: "\e20a";
}
.unycon-warning-triangle:before {
	content: "\e20b";
}
.unycon-restaurant:before {
	content: "\e20e";
}
.unycon-tool-double-square3:before {
	content: "\e210";
}
.unycon-tool-crop:before {
	content: "\e211";
}
.unycon-tool-square6:before {
	content: "\e212";
}
.unycon-tool-color-sample:before {
	content: "\e213";
}
.unycon-tool-color-sample2:before {
	content: "\e214";
}
.unycon-tool-brush:before {
	content: "\e215";
}
.unycon-tool-brush-arrow:before {
	content: "\e216";
}
.unycon-tool-pen:before {
	content: "\e217";
}
.unycon-arrow-mouse:before {
	content: "\e218";
}
.unycon-screwdriver:before {
	content: "\e219";
}
.unycon-rosted-chicken:before {
	content: "\e21a";
}
.unycon-not-allowed:before {
	content: "\e21b";
}
.unycon-pencil:before {
	content: "\e220";
}
.unycon-tool-dodge:before {
	content: "\e221";
}
.unycon-tool-blur:before {
	content: "\e222";
}
.unycon-tool-clone-stamp:before {
	content: "\e223";
}
.unycon-tool-pattern-stamp:before {
	content: "\e224";
}
.unycon-tool-move:before {
	content: "\e225";
}
.unycon-tool-magic-wand:before {
	content: "\e226";
}
.unycon-tool-paint-bucket:before {
	content: "\e227";
}
.unycon-tool-content-move:before {
	content: "\e228";
}
.unycon-tool-ruler:before {
	content: "\e229";
}
.unycon-crab:before {
	content: "\e22a";
}
.unycon-shield-delete:before {
	content: "\e22b";
}
.unycon-printer:before {
	content: "\e22d";
}
.unycon-flag-eu:before {
	content: "\e230";
}
.unycon-rectangle:before {
	content: "\e232";
}
.unycon-two-windows:before {
	content: "\e233";
}
.unycon-text-letter:before {
	content: "\e235";
}
.unycon-text-letter-height:before {
	content: "\e236";
}
.unycon-text-letter-scale:before {
	content: "\e237";
}
.unycon-eye:before {
	content: "\e238";
}
.unycon-document-check:before {
	content: "\e239";
}
.unycon-coffee3:before {
	content: "\e23a";
}
.unycon-warning-octagon:before {
	content: "\e23b";
}
.unycon-scanner:before {
	content: "\e23d";
}
.unycon-magnifying-glass:before {
	content: "\e23f";
}
.unycon-document-plus:before {
	content: "\e240";
}
.unycon-document-delete:before {
	content: "\e241";
}
.unycon-document-minus:before {
	content: "\e242";
}
.unycon-document-info:before {
	content: "\e243";
}
.unycon-document-warning:before {
	content: "\e244";
}
.unycon-document-question:before {
	content: "\e245";
}
.unycon-document-arrow:before {
	content: "\e246";
}
.unycon-document-stamp:before {
	content: "\e247";
}
.unycon-speech-bubble:before {
	content: "\e248";
}
.unycon-speech-bubble-waiting:before {
	content: "\e249";
}
.unycon-restaurant-plate:before {
	content: "\e24a";
}
.unycon-bell2:before {
	content: "\e24b";
}
.unycon-spinner:before {
	content: "\e24d";
}
.unycon-kat:before {
	content: "\e24e";
}
.unycon-speech-bubble-blogger:before {
	content: "\e250";
}
.unycon-speech-bubble-phone:before {
	content: "\e251";
}
.unycon-speech-bubble-rss:before {
	content: "\e252";
}
.unycon-speech-bubble-twitter:before {
	content: "\e253";
}
.unycon-speech-bubble-facebook:before {
	content: "\e254";
}
.unycon-speech-bubble-google-plus:before {
	content: "\e255";
}
.unycon-speech-bubble-twitter2:before {
	content: "\e256";
}
.unycon-speech-bubble-linkedin:before {
	content: "\e257";
}
.unycon-speech-bubble-warning:before {
	content: "\e258";
}
.unycon-speech-bubble-info:before {
	content: "\e259";
}
.unycon-martini:before {
	content: "\e25a";
}
.unycon-light-bulb2:before {
	content: "\e25b";
}
.unycon-crop:before {
	content: "\e25d";
}
.unycon-hospital-plus:before {
	content: "\e25f";
}
.unycon-speech-bubble-text:before {
	content: "\e260";
}
.unycon-speech-bubble-cog:before {
	content: "\e261";
}
.unycon-speech-bubble-check:before {
	content: "\e262";
}
.unycon-speech-bubble-delete:before {
	content: "\e263";
}
.unycon-speech-bubble-plus:before {
	content: "\e264";
}
.unycon-speech-bubble-minus:before {
	content: "\e265";
}
.unycon-speech-bubble-search:before {
	content: "\e266";
}
.unycon-speech-bubble-at:before {
	content: "\e267";
}
.unycon-speech-bubble-pen:before {
	content: "\e268";
}
.unycon-speech-bubble-time:before {
	content: "\e269";
}
.unycon-email-open:before {
	content: "\e26b";
}
.unycon-pie-chart:before {
	content: "\e26d";
}
.unycon-speech-bubbles:before {
	content: "\e270";
}
.unycon-twitter:before {
	content: "\e271";
}
.unycon-facebook:before {
	content: "\e272";
}
.unycon-blogger:before {
	content: "\e273";
}
.unycon-twitter2:before {
	content: "\e274";
}
.unycon-linkedin-square:before {
	content: "\e275";
}
.unycon-google-plus:before {
	content: "\e276";
}
.unycon-rss:before {
	content: "\e277";
}
.unycon-pencil2:before {
	content: "\e278";
}
.unycon-ellipsis:before {
	content: "\e279";
}
.unycon-orange:before {
	content: "\e27a";
}
.unycon-check-circle:before {
	content: "\e27d";
}
.unycon-bars:before {
	content: "\e280";
}
.unycon-speech-bubble-star:before {
	content: "\e281";
}
.unycon-speech-bubble-arrow:before {
	content: "\e282";
}
.unycon-speech-bubble-arrow2:before {
	content: "\e283";
}
.unycon-speech-bubble-question:before {
	content: "\e284";
}
.unycon-speech-bubble-network:before {
	content: "\e285";
}
.unycon-speech-bubble-pin:before {
	content: "\e286";
}
.unycon-speech-bubble-heart:before {
	content: "\e287";
}
.unycon-user-rss:before {
	content: "\e288";
}
.unycon-user-check:before {
	content: "\e289";
}
.unycon-user-plus:before {
	content: "\e290";
}
.unycon-user-delete:before {
	content: "\e291";
}
.unycon-user-minus:before {
	content: "\e292";
}
.unycon-user-info:before {
	content: "\e293";
}
.unycon-user-warning:before {
	content: "\e294";
}
.unycon-user-question:before {
	content: "\e295";
}
.unycon-user-arrow:before {
	content: "\e296";
}
.unycon-comment-dots:before {
	content: "\e297";
}
.unycon-comment-text:before {
	content: "\e298";
}
.unycon-copyright:before {
	content: "\e299";
}
.unycon-food-plate:before {
	content: "\e29a";
}
.unycon-star2:before {
	content: "\e29b";
}
.unycon-cloud:before {
	content: "\e29d";
}
.unycon-diamond:before {
	content: "\e29e";
}
.unycon-unyson:before {
	content: "\e2ad";
}
.unycon-original:before {
	content: "\e300";
}
.unycon-briefcase:before {
	content: "\e302";
}
.unycon-bar-chart:before {
	content: "\e303";
}
.unycon-idea-doubt:before {
	content: "\e304";
}
.unycon-master-card:before {
	content: "\e305";
}
.unycon-visa:before {
	content: "\e306";
}
.unycon-line-chart:before {
	content: "\e307";
}
.unycon-safe:before {
	content: "\e308";
}
.unycon-bank:before {
	content: "\e309";
}
.unycon-wine-bottle:before {
	content: "\e30a";
}
.unycon-battery-full-h:before {
	content: "\e30d";
}
.unycon-dollar-bill:before {
	content: "\e316";
}
.unycon-calendar:before {
	content: "\e318";
}
.unycon-calendar-check:before {
	content: "\e319";
}
.unycon-sound:before {
	content: "\e31b";
}
.unycon-arrow-circle-left-o:before {
	content: "\e31c";
}
.unycon-battery-half:before {
	content: "\e31d";
}
.unycon-calendar-plus:before {
	content: "\e320";
}
.unycon-calendar-delete:before {
	content: "\e321";
}
.unycon-calendar-minus:before {
	content: "\e322";
}
.unycon-calendar-info:before {
	content: "\e323";
}
.unycon-calendar-warning:before {
	content: "\e324";
}
.unycon-calendar-question:before {
	content: "\e325";
}
.unycon-calendar-arrow:before {
	content: "\e326";
}
.unycon-calendar-time:before {
	content: "\e327";
}
.unycon-calendar-deadline:before {
	content: "\e328";
}
.unycon-pie-chart2:before {
	content: "\e329";
}
.unycon-arrow-circle-up-o:before {
	content: "\e32c";
}
.unycon-battery-low:before {
	content: "\e32d";
}
.unycon-light-bulb3:before {
	content: "\e32e";
}
.unycon-heart:before {
	content: "\e32f";
}
.unycon-drawers:before {
	content: "\e333";
}
.unycon-database:before {
	content: "\e334";
}
.unycon-handshake:before {
	content: "\e336";
}
.unycon-hot-drink:before {
	content: "\e337";
}
.unycon-piggy-bank2:before {
	content: "\e339";
}
.unycon-cherries:before {
	content: "\e33a";
}
.unycon-arrow-circle-right-o:before {
	content: "\e33c";
}
.unycon-battery-low-h:before {
	content: "\e33d";
}
.unycon-business-woman:before {
	content: "\e340";
}
.unycon-business-man:before {
	content: "\e341";
}
.unycon-pencil3:before {
	content: "\e342";
}
.unycon-user-promotion:before {
	content: "\e343";
}
.unycon-user-structure:before {
	content: "\e344";
}
.unycon-user-graph:before {
	content: "\e345";
}
.unycon-office-chair:before {
	content: "\e346";
}
.unycon-clipboard-check:before {
	content: "\e347";
}
.unycon-notebook:before {
	content: "\e348";
}
.unycon-notebook-check:before {
	content: "\e349";
}
.unycon-arrow-circle-down-o:before {
	content: "\e34c";
}
.unycon-battery-full:before {
	content: "\e34d";
}
.unycon-plug:before {
	content: "\e34e";
}
.unycon-heart-rate:before {
	content: "\e34f";
}
.unycon-notebook-plus:before {
	content: "\e350";
}
.unycon-notebook-delete:before {
	content: "\e351";
}
.unycon-notebook-minus:before {
	content: "\e352";
}
.unycon-notebook-info:before {
	content: "\e353";
}
.unycon-notebook-warning:before {
	content: "\e354";
}
.unycon-notebook-question:before {
	content: "\e355";
}
.unycon-notebook-arrow:before {
	content: "\e356";
}
.unycon-notebook-time:before {
	content: "\e357";
}
.unycon-notebook-deadline:before {
	content: "\e358";
}
.unycon-event:before {
	content: "\e359";
}
.unycon-megaphone:before {
	content: "\e35b";
}
.unycon-arrow-circle-left:before {
	content: "\e35c";
}
.unycon-battery:before {
	content: "\e35d";
}
.unycon-socket:before {
	content: "\e35e";
}
.unycon-heart-plus:before {
	content: "\e35f";
}
.unycon-event-phone:before {
	content: "\e360";
}
.unycon-event-star:before {
	content: "\e361";
}
.unycon-event-check:before {
	content: "\e363";
}
.unycon-event-delete:before {
	content: "\e364";
}
.unycon-event-plus:before {
	content: "\e365";
}
.unycon-event-minus:before {
	content: "\e366";
}
.unycon-event-at:before {
	content: "\e367";
}
.unycon-event-edit:before {
	content: "\e368";
}
.unycon-event-time:before {
	content: "\e369";
}
.unycon-email-warning:before {
	content: "\e36b";
}
.unycon-arrow-circle-up:before {
	content: "\e36c";
}
.unycon-accumulator:before {
	content: "\e36d";
}
.unycon-trompete2:before {
	content: "\e36f";
}
.unycon-event-cog:before {
	content: "\e370";
}
.unycon-event-arrow:before {
	content: "\e371";
}
.unycon-event-location:before {
	content: "\e372";
}
.unycon-event-heart:before {
	content: "\e373";
}
.unycon-event-gift:before {
	content: "\e375";
}
.unycon-event-music:before {
	content: "\e376";
}
.unycon-event-warning:before {
	content: "\e377";
}
.unycon-toxic:before {
	content: "\e37b";
}
.unycon-arrow-circle-right:before {
	content: "\e37c";
}
.unycon-hand-stop:before {
	content: "\e38b";
}
.unycon-arrow-circle-down:before {
	content: "\e38c";
}
.unycon-star-check:before {
	content: "\e395";
}
.unycon-star-plus:before {
	content: "\e396";
}
.unycon-star-delete:before {
	content: "\e397";
}
.unycon-plane:before {
	content: "\e39b";
}
.unycon-arrow-right-top:before {
	content: "\e39c";
}
.unycon-stop-sign:before {
	content: "\e39e";
}
.unycon-star:before {
	content: "\e402";
}
.unycon-butterfly-bow:before {
	content: "\e404";
}
.unycon-24h:before {
	content: "\e407";
}
.unycon-event-checklist:before {
	content: "\e408";
}
.unycon-event-progress:before {
	content: "\e409";
}
.unycon-bluetooth:before {
	content: "\e40b";
}
.unycon-arrow-left-bottom:before {
	content: "\e40c";
}
.unycon-sparkle:before {
	content: "\e413";
}
.unycon-cake:before {
	content: "\e414";
}
.unycon-hot-coffee:before {
	content: "\e416";
}
.unycon-free-tag:before {
	content: "\e419";
}
.unycon-arrow-right-bottom:before {
	content: "\e41c";
}
.unycon-sound-bars:before {
	content: "\e41f";
}
.unycon-free-paper:before {
	content: "\e420";
}
.unycon-free-download:before {
	content: "\e421";
}
.unycon-free-upload:before {
	content: "\e422";
}
.unycon-free-mail:before {
	content: "\e423";
}
.unycon-free-phone:before {
	content: "\e424";
}
.unycon-free-coffee:before {
	content: "\e425";
}
.unycon-free-photo:before {
	content: "\e426";
}
.unycon-free-gift:before {
	content: "\e427";
}
.unycon-free-video:before {
	content: "\e428";
}
.unycon-warning-circle2-o:before {
	content: "\e42b";
}
.unycon-arrow-left-top:before {
	content: "\e42c";
}
.unycon-worker:before {
	content: "\e42e";
}
.unycon-free-recycle:before {
	content: "\e430";
}
.unycon-free-arrow:before {
	content: "\e431";
}
.unycon-free-barcode:before {
	content: "\e433";
}
.unycon-free-shopping:before {
	content: "\e435";
}
.unycon-free-message:before {
	content: "\e436";
}
.unycon-free-wifi:before {
	content: "\e437";
}
.unycon-eating-utensils:before {
	content: "\e43a";
}
.unycon-signal:before {
	content: "\e43b";
}
.unycon-angle-double-down:before {
	content: "\e43c";
}
.unycon-free-event:before {
	content: "\e441";
}
.unycon-free-sign:before {
	content: "\e442";
}
.unycon-free-mobile:before {
	content: "\e443";
}
.unycon-diamond2:before {
	content: "\e444";
}
.unycon-angle-double-left:before {
	content: "\e44c";
}
.unycon-diamond-o:before {
	content: "\e455";
}
.unycon-angle-double-up:before {
	content: "\e45c";
}
.unycon-star-circle:before {
	content: "\e465";
}
.unycon-hair-styling:before {
	content: "\e468";
}
.unycon-angle-double-right:before {
	content: "\e46c";
}
.unycon-angle-down:before {
	content: "\e47c";
}
.unycon-musical-note:before {
	content: "\e47f";
}
.unycon-bow-tie:before {
	content: "\e480";
}
.unycon-bow-tie2:before {
	content: "\e481";
}
.unycon-shirt-hanger:before {
	content: "\e483";
}
.unycon-scissors2:before {
	content: "\e484";
}
.unycon-pants:before {
	content: "\e486";
}
.unycon-tshirt2:before {
	content: "\e487";
}
.unycon-tshirt2-o:before {
	content: "\e488";
}
.unycon-mannequin:before {
	content: "\e489";
}
.unycon-beer:before {
	content: "\e48a";
}
.unycon-angle-up:before {
	content: "\e48c";
}
.unycon-folder-plus2:before {
	content: "\e48d";
}
.unycon-umbrella:before {
	content: "\e491";
}
.unycon-moustache:before {
	content: "\e492";
}
.unycon-angle-right:before {
	content: "\e49c";
}
.unycon-folder-delete:before {
	content: "\e49d";
}
.unycon-angle-left:before {
	content: "\e50c";
}
.unycon-face-man:before {
	content: "\e50d";
}
.unycon-bus:before {
	content: "\e513";
}
.unycon-arrow-up:before {
	content: "\e51c";
}
.unycon-face-female:before {
	content: "\e51d";
}
.unycon-arrow-down:before {
	content: "\e52c";
}
.unycon-comment-o:before {
	content: "\e53b";
}
.unycon-arrow-left:before {
	content: "\e53c";
}
.unycon-direction-left:before {
	content: "\e541";
}
.unycon-direction-right:before {
	content: "\e542";
}
.unycon-intersection-left:before {
	content: "\e543";
}
.unycon-intersection-right:before {
	content: "\e544";
}
.unycon-map-marker2:before {
	content: "\e546";
}
.unycon-map-marker-star:before {
	content: "\e547";
}
.unycon-map-marker-circle:before {
	content: "\e549";
}
.unycon-comment-half:before {
	content: "\e54b";
}
.unycon-arrow-right:before {
	content: "\e54c";
}
.unycon-map:before {
	content: "\e551";
}
.unycon-target-location:before {
	content: "\e557";
}
.unycon-waves:before {
	content: "\e558";
}
.unycon-see-side:before {
	content: "\e559";
}
.unycon-cake2:before {
	content: "\e55a";
}
.unycon-comment:before {
	content: "\e55b";
}
.unycon-arrows-both-ways:before {
	content: "\e55c";
}
.unycon-headphones:before {
	content: "\e55d";
}
.unycon-map-tracking:before {
	content: "\e561";
}
.unycon-marker-parking:before {
	content: "\e562";
}
.unycon-marker-hospital:before {
	content: "\e563";
}
.unycon-house:before {
	content: "\e571";
}
.unycon-house2:before {
	content: "\e572";
}
.unycon-cabin:before {
	content: "\e573";
}
.unycon-house3:before {
	content: "\e574";
}
.unycon-house4:before {
	content: "\e575";
}
.unycon-building:before {
	content: "\e576";
}
.unycon-building3:before {
	content: "\e578";
}
.unycon-soda:before {
	content: "\e57a";
}
.unycon-musical-key:before {
	content: "\e57f";
}
.unycon-building5:before {
	content: "\e580";
}
.unycon-building6:before {
	content: "\e581";
}
.unycon-house5:before {
	content: "\e582";
}
.unycon-house-square:before {
	content: "\e583";
}
.unycon-house6:before {
	content: "\e584";
}
.unycon-house-search:before {
	content: "\e585";
}
.unycon-sales-woman:before {
	content: "\e586";
}
.unycon-landlord:before {
	content: "\e587";
}
.unycon-sold-sign:before {
	content: "\e589";
}
.unycon-upload:before {
	content: "\e58c";
}
.unycon-man-in-glasses:before {
	content: "\e58d";
}
.unycon-rent-sign:before {
	content: "\e590";
}
.unycon-sale-sign:before {
	content: "\e591";
}
.unycon-house-sale:before {
	content: "\e592";
}
.unycon-house-rent:before {
	content: "\e593";
}
.unycon-house-search2:before {
	content: "\e594";
}
.unycon-sales-man:before {
	content: "\e595";
}
.unycon-sales-man2:before {
	content: "\e596";
}
.unycon-sales-man3:before {
	content: "\e597";
}
.unycon-rent-sale:before {
	content: "\e598";
}
.unycon-cross-square:before {
	content: "\e59b";
}
.unycon-download:before {
	content: "\e59c";
}
.unycon-head-phones:before {
	content: "\e59f";
}
.unycon-unyson-o:before {
	content: "\e5ad";
}
.unycon-woman:before {
	content: "\e601";
}
.unycon-man:before {
	content: "\e602";
}
.unycon-house7:before {
	content: "\e607";
}
.unycon-house-key:before {
	content: "\e608";
}
.unycon-key2:before {
	content: "\e609";
}
.unycon-cross-square-o:before {
	content: "\e60b";
}
.unycon-stop-circle:before {
	content: "\e60c";
}
.unycon-money-bag:before {
	content: "\e60e";
}
.unycon-head-phones1:before {
	content: "\e60f";
}
.unycon-key3:before {
	content: "\e615";
}
.unycon-house8:before {
	content: "\e616";
}
.unycon-sale-sign2:before {
	content: "\e617";
}
.unycon-house9:before {
	content: "\e618";
}
.unycon-minus-circle2:before {
	content: "\e61c";
}
.unycon-drop:before {
	content: "\e62b";
}
.unycon-plus-circle:before {
	content: "\e62c";
}
.unycon-peace-circle:before {
	content: "\e637";
}
.unycon-ying-yang:before {
	content: "\e638";
}
.unycon-peace-heart:before {
	content: "\e639";
}
.unycon-first-aid-kit:before {
	content: "\e63b";
}
.unycon-check-circle-o:before {
	content: "\e63c";
}
.unycon-darts:before {
	content: "\e63d";
}
.unycon-pigeon:before {
	content: "\e646";
}
.unycon-delete-circle2:before {
	content: "\e64c";
}
.unycon-eye-dropper:before {
	content: "\e65b";
}
.unycon-info-circle:before {
	content: "\e66c";
}
.unycon-go:before {
	content: "\e66d";
}
.unycon-usd-circle:before {
	content: "\e671";
}
.unycon-euro-circle:before {
	content: "\e672";
}
.unycon-pound-circle:before {
	content: "\e673";
}
.unycon-yen-circle:before {
	content: "\e674";
}
.unycon-rouble-circle:before {
	content: "\e675";
}
.unycon-bitcoin-circle:before {
	content: "\e676";
}
.unycon-lira-circle:before {
	content: "\e677";
}
.unycon-rupee-circle:before {
	content: "\e678";
}
.unycon-warning-circle:before {
	content: "\e67c";
}
.unycon-shekel-circle:before {
	content: "\e680";
}
.unycon-rand-circle:before {
	content: "\e681";
}
.unycon-usd:before {
	content: "\e682";
}
.unycon-euro:before {
	content: "\e683";
}
.unycon-pound:before {
	content: "\e684";
}
.unycon-yen:before {
	content: "\e685";
}
.unycon-rouble:before {
	content: "\e686";
}
.unycon-bitcoin:before {
	content: "\e687";
}
.unycon-lira:before {
	content: "\e688";
}
.unycon-rupee:before {
	content: "\e689";
}
.unycon-stethoscope:before {
	content: "\e68b";
}
.unycon-power-off:before {
	content: "\e68c";
}
.unycon-syringe:before {
	content: "\e68e";
}
.unycon-play-circle:before {
	content: "\e68f";
}
.unycon-lao-kip:before {
	content: "\e690";
}
.unycon-shekel:before {
	content: "\e691";
}
.unycon-rand:before {
	content: "\e692";
}
.unycon-usd-exchange:before {
	content: "\e693";
}
.unycon-euro-exchange:before {
	content: "\e694";
}
.unycon-pound-exchange:before {
	content: "\e695";
}
.unycon-yen-exchange:before {
	content: "\e696";
}
.unycon-rouble-exchange:before {
	content: "\e697";
}
.unycon-bitcoin-exchange:before {
	content: "\e698";
}
.unycon-lira-exchange:before {
	content: "\e699";
}
.unycon-clock:before {
	content: "\e69c";
}
.unycon-key:before {
	content: "\e69e";
}
.unycon-themefuse-o:before {
	content: "\e6ad";
}
.unycon-rupee-exchange:before {
	content: "\e700";
}
.unycon-lao-kip-exchange:before {
	content: "\e701";
}
.unycon-shekel-exchange:before {
	content: "\e702";
}
.unycon-rand-exchange:before {
	content: "\e703";
}
.unycon-usd-shield-o:before {
	content: "\e704";
}
.unycon-eur-shield-o:before {
	content: "\e705";
}
.unycon-pound-shield-o:before {
	content: "\e706";
}
.unycon-yen-shield-o:before {
	content: "\e707";
}
.unycon-rouble-shield-o:before {
	content: "\e708";
}
.unycon-bitcoin-shield-o:before {
	content: "\e709";
}
.unycon-joystick2:before {
	content: "\e70d";
}
.unycon-lira-shield-o:before {
	content: "\e710";
}
.unycon-rupee-shield-o:before {
	content: "\e711";
}
.unycon-lao-kip-shield-o:before {
	content: "\e712";
}
.unycon-shekel-shield-o:before {
	content: "\e713";
}
.unycon-rand-shield-o:before {
	content: "\e714";
}
.unycon-money-usd:before {
	content: "\e715";
}
.unycon-money-euro:before {
	content: "\e716";
}
.unycon-money-pound:before {
	content: "\e717";
}
.unycon-money-yen:before {
	content: "\e718";
}
.unycon-money-rouble:before {
	content: "\e719";
}
.unycon-caduceus:before {
	content: "\e71b";
}
.unycon-hours-24:before {
	content: "\e71c";
}
.unycon-pages-up:before {
	content: "\e71e";
}
.unycon-music-volume:before {
	content: "\e71f";
}
.unycon-money-bitcoin:before {
	content: "\e720";
}
.unycon-money-lira:before {
	content: "\e721";
}
.unycon-money-rupee:before {
	content: "\e722";
}
.unycon-money-lao-kip:before {
	content: "\e723";
}
.unycon-money-shekel:before {
	content: "\e724";
}
.unycon-money-rand:before {
	content: "\e725";
}
.unycon-usd-bills:before {
	content: "\e726";
}
.unycon-euro-bills:before {
	content: "\e727";
}
.unycon-pound-bills:before {
	content: "\e728";
}
.unycon-yen-bills:before {
	content: "\e729";
}
.unycon-syringe2:before {
	content: "\e72b";
}
.unycon-pages-down:before {
	content: "\e72e";
}
.unycon-music-volume-off:before {
	content: "\e72f";
}
.unycon-rouble-bills:before {
	content: "\e730";
}
.unycon-bitcoin-bills:before {
	content: "\e731";
}
.unycon-lira-bills:before {
	content: "\e732";
}
.unycon-rupee-bills:before {
	content: "\e733";
}
.unycon-lao-kip-bills:before {
	content: "\e734";
}
.unycon-shekel-bills:before {
	content: "\e735";
}
.unycon-rand-bills:before {
	content: "\e736";
}
.unycon-credit-cards2:before {
	content: "\e737";
}
.unycon-cash-bag2:before {
	content: "\e738";
}
.unycon-wallet2:before {
	content: "\e739";
}
.unycon-music-volume-off2:before {
	content: "\e73f";
}
.unycon-currency-bill:before {
	content: "\e741";
}
.unycon-bank2:before {
	content: "\e743";
}
.unycon-usd-circle-o:before {
	content: "\e744";
}
.unycon-usd-rate:before {
	content: "\e746";
}
.unycon-business-stats:before {
	content: "\e748";
}
.unycon-money-stack:before {
	content: "\e749";
}
.unycon-nurse:before {
	content: "\e74b";
}
.unycon-anchor:before {
	content: "\e74d";
}
.unycon-ipod:before {
	content: "\e74f";
}
.unycon-deal:before {
	content: "\e750";
}
.unycon-currency-exchange:before {
	content: "\e751";
}
.unycon-euro-circle-o:before {
	content: "\e752";
}
.unycon-safe-case:before {
	content: "\e753";
}
.unycon-bitcoin-circle-o:before {
	content: "\e756";
}
.unycon-currency-circle-o:before {
	content: "\e757";
}
.unycon-currency-circle:before {
	content: "\e758";
}
.unycon-calculator:before {
	content: "\e759";
}
.unycon-ambulance:before {
	content: "\e75b";
}
.unycon-gears:before {
	content: "\e75c";
}
.unycon-music-volume-bars:before {
	content: "\e75f";
}
.unycon-clepsydra:before {
	content: "\e760";
}
.unycon-presentation-board:before {
	content: "\e761";
}
.unycon-pie-chart22:before {
	content: "\e765";
}
.unycon-percentage-circle:before {
	content: "\e768";
}
.unycon-tooth:before {
	content: "\e76b";
}
.unycon-wrench2:before {
	content: "\e76c";
}
.unycon-zoom-out:before {
	content: "\e76d";
}
.unycon-usd-exchange2:before {
	content: "\e771";
}
.unycon-cash-register:before {
	content: "\e773";
}
.unycon-tag-o:before {
	content: "\e774";
}
.unycon-money-stack2:before {
	content: "\e775";
}
.unycon-tag-usd:before {
	content: "\e776";
}
.unycon-laptop-usd-o:before {
	content: "\e778";
}
.unycon-laptop-usd:before {
	content: "\e779";
}
.unycon-health-card:before {
	content: "\e77b";
}
.unycon-sharing:before {
	content: "\e77c";
}
.unycon-zoom-in:before {
	content: "\e77d";
}
.unycon-gift2:before {
	content: "\e77e";
}
.unycon-laptop-graph:before {
	content: "\e782";
}
.unycon-lightning2:before {
	content: "\e789";
}
.unycon-link2:before {
	content: "\e78c";
}
.unycon-zoom:before {
	content: "\e78d";
}
.unycon-cloud-rain:before {
	content: "\e790";
}
.unycon-snow:before {
	content: "\e791";
}
.unycon-rain-snow:before {
	content: "\e792";
}
.unycon-cloud-lightning:before {
	content: "\e793";
}
.unycon-sun-cloud:before {
	content: "\e794";
}
.unycon-cloud2:before {
	content: "\e795";
}
.unycon-sun:before {
	content: "\e796";
}
.unycon-sun2:before {
	content: "\e797";
}
.unycon-sun-o:before {
	content: "\e798";
}
.unycon-clouds:before {
	content: "\e799";
}
.unycon-wifi2:before {
	content: "\e79c";
}
.unycon-auction-hammer:before {
	content: "\e79e";
}
.unycon-themefuse:before {
	content: "\e7ad";
}
.unycon-night:before {
	content: "\e800";
}
.unycon-night-snow:before {
	content: "\e801";
}
.unycon-night-lightning:before {
	content: "\e803";
}
.unycon-umbrella2:before {
	content: "\e804";
}
.unycon-moon2:before {
	content: "\e807";
}
.unycon-moon-phase:before {
	content: "\e808";
}
.unycon-moon-phase2:before {
	content: "\e809";
}
.unycon-trash:before {
	content: "\e80c";
}
.unycon-hand:before {
	content: "\e80e";
}
.unycon-music-notes:before {
	content: "\e80f";
}
.unycon-rain2:before {
	content: "\e810";
}
.unycon-snow2:before {
	content: "\e816";
}
.unycon-sun-rain:before {
	content: "\e817";
}
.unycon-sun-snow:before {
	content: "\e818";
}
.unycon-rainbow:before {
	content: "\e819";
}
.unycon-male-sign-thin:before {
	content: "\e81b";
}
.unycon-pencil4:before {
	content: "\e81c";
}
.unycon-na:before {
	content: "\e822";
}
.unycon-home:before {
	content: "\e82e";
}
.unycon-cloud-rain3:before {
	content: "\e830";
}
.unycon-snow22:before {
	content: "\e831";
}
.unycon-rain-snow2:before {
	content: "\e832";
}
.unycon-sun-cloud2:before {
	content: "\e833";
}
.unycon-night2:before {
	content: "\e834";
}
.unycon-home2:before {
	content: "\e837";
}
.unycon-id-card:before {
	content: "\e838";
}
.unycon-hospital-square:before {
	content: "\e83b";
}
.unycon-file-edit:before {
	content: "\e83c";
}
.unycon-ruler:before {
	content: "\e83e";
}
.unycon-mechanical-pen:before {
	content: "\e840";
}
.unycon-edit-card:before {
	content: "\e842";
}
.unycon-presentation-board3:before {
	content: "\e843";
}
.unycon-file-word:before {
	content: "\e84c";
}
.unycon-mobile:before {
	content: "\e84d";
}
.unycon-folder-open3:before {
	content: "\e850";
}
.unycon-folder-plus3:before {
	content: "\e851";
}
.unycon-slider:before {
	content: "\e852";
}
.unycon-refresh:before {
	content: "\e855";
}
.unycon-briefcase2:before {
	content: "\e859";
}
.unycon-hospital-marker-h:before {
	content: "\e85b";
}
.unycon-footprint:before {
	content: "\e85e";
}
.unycon-plus-circle-o:before {
	content: "\e862";
}
.unycon-hospital-marker-plus:before {
	content: "\e86b";
}
.unycon-file-powerpoint:before {
	content: "\e86c";
}
.unycon-mobile-user:before {
	content: "\e86d";
}
.unycon-mail:before {
	content: "\e873";
}
.unycon-playlist:before {
	content: "\e87f";
}
.unycon-controls2:before {
	content: "\e881";
}
.unycon-rent:before {
	content: "\e882";
}
.unycon-sold:before {
	content: "\e883";
}
.unycon-slider2:before {
	content: "\e889";
}
.unycon-key32:before {
	content: "\e891";
}
.unycon-rocking-horse:before {
	content: "\e893";
}
.unycon-teddy-bear:before {
	content: "\e894";
}
.unycon-abc:before {
	content: "\e895";
}
.unycon-duck-toy:before {
	content: "\e896";
}
.unycon-spintop:before {
	content: "\e897";
}
.unycon-bike:before {
	content: "\e898";
}
.unycon-file:before {
	content: "\e89c";
}
.unycon-watches:before {
	content: "\e89d";
}
.unycon-stroller:before {
	content: "\e900";
}
.unycon-pacifier:before {
	content: "\e906";
}
.unycon-milk-bottle:before {
	content: "\e907";
}
.unycon-baby:before {
	content: "\e908";
}
.unycon-file-warning:before {
	content: "\e90c";
}
.unycon-comment-add:before {
	content: "\e93c";
}
.unycon-hand-right:before {
	content: "\e93e";
}
.unycon-gamepad:before {
	content: "\e943";
}
.unycon-scale2:before {
	content: "\e944";
}
.unycon-gavel:before {
	content: "\e945";
}
.unycon-justice-building:before {
	content: "\e946";
}
.unycon-search:before {
	content: "\e948";
}
.unycon-disabled:before {
	content: "\e94b";
}
.unycon-comment-remove:before {
	content: "\e94c";
}
.unycon-hand-left:before {
	content: "\e94e";
}
.unycon-building-column:before {
	content: "\e950";
}
.unycon-phone-talk:before {
	content: "\e95c";
}
.unycon-equalizer:before {
	content: "\e95d";
}
.unycon-sheriff-star:before {
	content: "\e960";
}
.unycon-aim:before {
	content: "\e961";
}
.unycon-lawyer2:before {
	content: "\e964";
}
.unycon-mic:before {
	content: "\e96d";
}
.unycon-license:before {
	content: "\e973";
}
.unycon-graduation-hat:before {
	content: "\e974";
}
.unycon-lock:before {
	content: "\e978";
}
.unycon-file-fill:before {
	content: "\e97c";
}
.unycon-mic-off:before {
	content: "\e97d";
}
.unycon-check-square:before {
	content: "\e97e";
}
.unycon-thick-glasses:before {
	content: "\e982";
}
.unycon-user-id:before {
	content: "\e983";
}
.unycon-ie:before {
	content: "\e984";
}
.unycon-chrome:before {
	content: "\e985";
}
.unycon-firefox:before {
	content: "\e986";
}
.unycon-youtube:before {
	content: "\e987";
}
.unycon-twitter3:before {
	content: "\e988";
}
.unycon-app-store:before {
	content: "\e989";
}
.unycon-arrows:before {
	content: "\e98c";
}
.unycon-music:before {
	content: "\e98d";
}
.unycon-close-square:before {
	content: "\e98e";
}
.unycon-app-store-circle:before {
	content: "\e990";
}
.unycon-safari:before {
	content: "\e991";
}
.unycon-windows-store:before {
	content: "\e992";
}
.unycon-dropbox:before {
	content: "\e993";
}
.unycon-amazon:before {
	content: "\e994";
}
.unycon-at:before {
	content: "\e995";
}
.unycon-drive-cloud:before {
	content: "\e996";
}
.unycon-wikipedia:before {
	content: "\e997";
}
.unycon-wordpress:before {
	content: "\e998";
}
.unycon-globe:before {
	content: "\e999";
}
