<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

// Security check
defined('ABSPATH') || die();

final class etradeAjaxWoocTemplateFileLoader{
    public function __construct(){
        // Hook Initialization
        add_action('wp_ajax_nopriv_cart_template_load', array(&$this, 'cart_template_load'));
        add_action('wp_ajax_cart_template_load', array(&$this, 'cart_template_load'));
   
    }
    public function cart_template_load(){   
        wc_get_template_part(
            'ajax/' . sanitize_text_field($_POST['template']), 
            sanitize_text_field($_POST['part'])
        );
        wp_die();
    }

}

new etradeAjaxWoocTemplateFileLoader();