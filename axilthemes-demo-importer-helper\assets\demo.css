.demo-Warning-info{	
	font-size: 18px;
	padding: 20px;
	border-top: 2px solid #d5d5d5;
	border-right: 2px solid #d5d5d5;
	border-bottom: 2px solid #d5d5d5;
}
.demo-Warning-info {
    background-image: url(warning.png);
    background-size: 25px;
    background-repeat: no-repeat;
    background-position: 15px center;
    padding: 20px 20px 20px 50px;
    font-size: 18px;
    font-weight: 500;
    color: #e44040;
    background-color: #87eaaf59;
    border: none;
    border-radius: 0;
    border-left: 4px solid #d63638;
    margin-bottom: 26px !important;
}

.fw-ext-backups-demo-item .button-primary {
	background: #3cb66e;
	border-color: #3cb66e;
	color: #fff;
	text-decoration: none;
	text-shadow: none;
	padding: 0 16px;
}

.demo-Warning-info:first-word{	
	color: #dc3232;
}
#fw-ext-backups-demo-list .fw-ext-backups-demo-item.active .theme-actions{
	display: block !important;
}
.wp-heading-inline.icon-left{
	background-image: url("fav.png");
	background-repeat: no-repeat;	
	padding: 0 0 0 39px;
	margin-bottom: 15px;
	    background-position: left;
    background-size: contain;
}

.theme.fw-ext-backups-demo-item{
	border: none;
	background: #bfbfbf24; 
	 

}

.theme.fw-ext-backups-demo-item  .theme-actions{
	background: #bfbfbf24;
	box-shadow: none;
}
.theme.fw-ext-backups-demo-item h3.theme-name{
	border: none;
	box-shadow: none;
	background: rgb(255 255 255 / 47%);
}

#fw-ext-backups-demo-list .active_demo h3.theme-name::before{
    content: "Activated Demo \2714";
    position: absolute;
    left: 0;
    top: 0;
    background: linear-gradient(to right, #6a67ce, #fc636b) !important;
    z-index: 9;
    color: #fff;
    text-transform: uppercase;
    line-height: 55px;
    font-weight: 500;
    width: 100%;
    height: 100%;
    text-align: center;
}


	
