<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;

$row_col_class  = "row-cols-xl-{$settings['col_xl']} row-cols-lg-{$settings['col_lg']} row-cols-md-{$settings['col_md']} row-cols-sm-{$settings['col_sm']} row-cols-{$settings['col_mobile']}";
$col_class  = "col ";
$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
$block_data = array(
	'layout'         		=> $settings['style'],
 
	'rating_display' 		=> $settings['rating_display'] ? true : false,
	'display_attributes' 	=> $settings['display_attributes'] ? true : false,
	'wishlist' 		 		=> $settings['wishlist'] ? true : false,
	'quickview' 	 		=> $settings['quickview'] ? true : false,
	'v_swatch'       		=> true,	
	
);

$query = $settings['query'];
$uniqueid = time().rand( 1, 99 ).'-';
?>
 <div class="axil-product-area bg-color-white pb--0"> 
    <div class="product-area pb--40">
        <div class="axil-isotope-wrapper">
            <div class="product-isotope-heading"> 
        	  <?php if ( $settings['section_title_display'] ):  ?> 
               		 <div class="section-title-wrapper">
                		<?php  if($settings['sub_title']){ ?>
                	        <span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?> sub-title"><?php Icons_Manager::render_icon( $settings['icon'] ); ?><?php echo wp_kses_post( $settings['sub_title'] );?></span>
                	    <?php  } ?>    
                    <h3 class="title"><?php echo wp_kses_post( $settings['title'] );?></h3>
                </div>
				<?php endif; ?>  
				 <?php if ( $settings['section_filter_display'] ):  ?>
					<div class="isotope-button">  
						<?php if ( $settings['filter_all_display'] == "yes"): ?>
							<button data-filter="*" class="is-checked"><span class="filter-text"><?php echo wp_kses_post( $settings['all_tab_text'] );?></span></button>
						<?php endif; ?> 
						<?php foreach ( $settings['navs'] as $key => $value ): ?>
							<button data-filter=".<?php echo esc_attr( $uniqueid.$key );?>" class=""><span class="filter-text"><?php echo esc_html( $value );?></span>
							</button> 
						<?php endforeach; ?> 
					</div> 
				<?php endif; ?> 
            </div>
            <div class="row row--15 isotope-list <?php echo esc_attr( $row_col_class );?>">
               <?php if ( $query->have_posts() ) :?>
					<?php while ( $query->have_posts() ) : $query->the_post();?>
						<?php
						$id = get_the_ID();
						$product = wc_get_product( $id );
						$term_slugs = array();		
						$terms = get_the_terms( $id, 'product_cat' );

						if ( $terms ) {
							foreach ( $terms as $term ) {
							$ancestors    = get_ancestors( $term->term_id, 'product_cat', 'taxonomy' );
							$top_term     = $term->term_id ? get_term( $term->term_id, 'product_cat' ) : $term;
								$term_slugs[] = $top_term->slug;
							}
						}
						$class = '';
						foreach ( $term_slugs as $slug ) {
							$class .= ' '.$uniqueid.$slug;
						}
						?> 
						<div <?php wc_product_class( $col_class.$class, $product ); ?>>
							<?php wc_get_template( "custom/product-block/blocks.php" , compact( 'product', 'block_data' ) );?>
						</div>
					<?php endwhile;?>
				<?php endif;?>
				<?php wp_reset_postdata();?>
            </div>
        </div> 
    </div>
</div> 