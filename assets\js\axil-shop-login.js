
(function($) {
	
	'use strict'; 
		
		var animTimeout = 150;
		
		/* Show register form */
		function showRegisterForm() {
			// Form wrapper elements
			var $loginWrap = $('#axil-login-wrap'),
				$registerWrap = $('#axil-register-wrap'),
				$alreadyMember = $('#axil-already-member'),
				$notMember = $('#axil-not-member');

			$alreadyMember.addClass('inline fade-in slide-up');
			$notMember.removeClass('inline fade-in slide-up');
			// Login/register form
			$loginWrap.removeClass('fade-in');
			setTimeout(function() {
				$registerWrap.addClass('inline fade-in slide-up');
				$loginWrap.removeClass('inline fade-in slide-up'); 

			}, animTimeout);
		}
        
		/* Show login form */
		function showLoginForm() {
			// Form wrapper elements
			var $loginWrap = $('#axil-login-wrap'),
				$registerWrap = $('#axil-register-wrap'),
				$alreadyMember = $('#axil-already-member'),
				$notMember = $('#axil-not-member');
			
			// Login/register form
			$registerWrap.removeClass('fade-in');
			$notMember.addClass('inline fade-in slide-up');
			$alreadyMember.removeClass('inline fade-in slide-up'); 
			setTimeout(function() {
				$loginWrap.addClass('inline fade-in slide-up');
				$registerWrap.removeClass('inline fade-in slide-up'); 
				

			}, animTimeout);
		};
		
		/* Bind: Show register form button */
		$('#axil-show-register-button').on('click', function(e) {
			e.preventDefault();
			showRegisterForm();
		});
		
		/* Bind: Show login form button */
		$('#axil-show-login-button').on('click', function(e) {
			e.preventDefault();
			showLoginForm();
		});
        
        // Show register form if "#register" is added to URL
        if (window.location.hash && window.location.hash == '#register') {
            showRegisterForm();
        }
		
	 
})(jQuery);

