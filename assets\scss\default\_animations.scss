/*----------------------
Animation  
-----------------------*/
.post-scale {
    overflow: hidden;
    @extend %radius;
    img {
        transition: 0.5s;
    }
    &:hover {
        img {
            transform: scale(1.1);
        }
    }
}

@keyframes signalanimation {
    0% { 
        opacity: 1;
    }
    100% { 
        opacity: 0; 
    }
}

@keyframes customOne {
    from {
        transform: scale(1);
    }

    50% {
        transform: scale(0.90);
    }

    to {
        transform: scale(1);
    }
}

@keyframes customTwo {
    0% {
        transform: (translate(0.0px, 0.0px));
    }

    50% {
        transform: (translate(100.0px, 0.0px));
    }

    100% {
        transform: (translate(50.0px, 50.0px));
    }
}

.customOne {
    animation: customOne 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 var(--color-primary);
    }

    70% {
        box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}



/* ------------------------
    Custom Animation 01 
----------------------------*/

@-webkit-keyframes headerSlideDown {
    0% {
        transform: translateY(-100px);
    }

    to {
       transform: translateY(0);
    }
}

@keyframes headerSlideDown {
    0% {
        transform: translateY(-100px);
    }

    to {
       transform: translateY(0);
    }
}


/*------------------------
	slidefadeinup
--------------------------*/

@-webkit-keyframes slideFadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 20%, 0);
        transform: translate3d(0, 20%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes slideFadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 20%, 0);
        transform: translate3d(0, 20%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.slideFadeInUp {
    -webkit-animation-name: slideFadeInUp;
    animation-name: slideFadeInUp;
}

/* -----------------------------------
    Custom Animation For All Page
---------------------------------------*/

@-webkit-keyframes moveVertical {
    to {
        opacity: 1;
        -webkit-transform: translateY(0)
    }
}

@keyframes moveVertical {
    to {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}


/*--------------------------------
Scroll Down Button Animation  
----------------------------------*/
@keyframes scrollDown {
    0% {
        opacity: 0;
    }

    10% {
        transform: translateY(0);
        opacity: 1;
    }

    100% {
        transform: translateY(10px);
        opacity: 0;
    }
}

@keyframes btnIconSlide {
    0% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-5px);
        opacity: 0;
    }
    80% {
        transform: translateY(5px);
        opacity: 0;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes prevNavSlide {
    0% {
        transform: translateX(0);
    }

    40% {
        transform: translateX(-5px);
        opacity: 0;
    }
    80% {
        transform: translateX(5px);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes nextNavSlide {
    0% {
        transform: translateX(0);
    }

    40% {
        transform: translateX(5px);
        opacity: 0;
    }
    80% {
        transform: translateX(-5px);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}
