<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Group_Control_Image_Size;
use Elementor\Icons_Manager;
$shape1 = $settings['shape1']['url'];
$shape2 = $settings['shape2']['url'];
$allowed_tags = wp_kses_allowed_html( 'post' );
?>
<div class="axil-main-slider-area main-slider-style-1">
<div class="container">
    <div class="row align-items-center">
        <div class="col-lg-5 col-sm-6">
            <div class="main-slider-content">
                <div class="slider-content-activation-one">
                 <?php
foreach ( $settings['list'] as $plist ):
    if ( '1' == $plist['axil_link_type'] ) {
        if ( !empty( $plist['url']['url'] ) ) {
            $attr = 'href="' . $plist['url']['url'] . '"';
            $attr .= !empty( $plist['url']['is_external'] ) ? ' target="_blank"' : '';
            $attr .= !empty( $plist['url']['nofollow'] ) ? ' rel="nofollow"' : '';

        }
        if ( !empty( $plist['btntext'] ) ) {
            $btn = '<a class="axil-btn btn-bg-white" ' . $attr . '><i class="fal fa-shopping-cart"></i>' . $plist['btntext'] . '</a>';
        }
    } else {
        $attr = 'href="' . get_permalink( $plist['axil_page_link'] ) . '"';
        $attr .= ' target="_self"';
        $attr .= ' rel="nofollow"';
        $btn = '<a class="axil-btn btn-bg-white" ' . $attr . '><i class="fal fa-shopping-cart"></i>' . $plist['btntext'] . '</a>';
    }

    $simage = !empty( $plist['image']['url'] ) ? $plist['image']['url'] : false;
    ?>
	                    <div class="single-slide slick-slide">
	                        <?php if ( $plist['list_before'] ) {?>
	                            <span class="subtitle title-highlighter highlighter-<?php echo esc_attr( $plist['beforetitlestyle'] ); ?>">
	                                <?php Icons_Manager::render_icon( $plist['before_icon'] );?>
	                                <?php echo wp_kses( $plist['list_before'], $allowed_tags ); ?>
	                            </span>
	                        <?php }?>
	                        <h1 class="title"><?php echo wp_kses( $plist['list_title'], $allowed_tags ); ?></h1>
	                        <div class="slide-action">
		                     <?php if ( $plist['btntext'] ) {?>
				                <div class="shop-btn">
				                	<?php echo wp_kses( $btn, $allowed_tags ); ?>
				                </div>
			                <?php }?>
	                        </div>
	                    </div>
	                    <?php endforeach;?>
                </div>
            </div>
        </div>
        <div class="col-lg-7 col-sm-6">
            <div class="main-slider-large-thumb">
                <div class="slider-thumb-activation-one axil-slick-dots">
                <?php
foreach ( $settings['list'] as $pimageslist ):
?>
                        <div class="single-slide slick-slide">
                            <?php echo Group_Control_Image_Size::get_attachment_image_html( $pimageslist, 'image_size', 'image' ); ?>

                            <?php if ( $pimageslist['list_prices_label'] || $pimageslist['list_prices'] ) {?>
                                <div class="product-price">
                                    <?php if ( $pimageslist['list_prices_label'] ) {?>
                                        <span class="text"><?php echo esc_attr( $pimageslist['list_prices_label'] ); ?></span>
                                    <?php }?>
                                    <?php if ( $pimageslist['list_prices'] ) {?>
                                        <span class="price-amount"><?php echo esc_attr( $pimageslist['list_prices'] ); ?></span>
                                    <?php }?>
                                </div>
                            <?php }?>
                        </div>
					 <?php endforeach;?>
                </div>
            </div>
        </div>
    </div>
</div>
 <?php if ( $settings['shape_style_on'] == 'yes' ) {?>
    <ul class="shape-group">
        <?php if ( !empty( $shape1 ) ) {?>
            <li class="shape-1"><img src="<?php echo esc_url( $shape1 ); ?>" alt="<?php esc_html_e( 'Shape', 'etrade-elements' );?>"></li>
        <?php }?>
        <?php if ( !empty( $shape1 ) ) {?>
            <li class="shape-2"><img src="<?php echo esc_url( $shape2 ); ?>" alt="<?php esc_html_e( 'Shape', 'etrade-elements' );?>"></li>
        <?php }?>
    </ul>
<?php }?>
</div>
