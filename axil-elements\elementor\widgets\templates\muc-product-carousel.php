<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;
use Elementor\Group_Control_Image_Size;

$query = $settings['query'];

if ( !empty( $settings['cat'] ) ) {
	$shop_permalink = get_category_link( $settings['cat'] );
}
else {
	$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
}
$sub_title       = $settings['sub_title'] ? ' sub_title' : ' no-sub_title';
$btn = '';
$attr  = '';
 if ('2' == $settings['axil_link_type']) {
		if ( !empty( $settings['url']['url'] ) ) {
			$attr  = 'href="' . get_permalink($settings['axil_page_link']) . '"';
			$attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
			$attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
		}
		$btn = '<a class="axil-btn btn-bg-lighter btn-load-more" ' . $attr . '>'.$settings['btntext'] .'</a>';

   } else {
	if ( $settings['url']['url'] ) {
		if ( !empty( $settings['url']['url'] ) ) {
			$attr  = 'href="' . $settings['url']['url'] . '"';
			$attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
			$attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
		}
		$btn = '<a class="axil-btn btn-bg-lighter btn-load-more" ' . $attr . '>'.$settings['btntext'] .'</a>';
		
	}
}

$block_data = array(
	'layout'         			=> $settings['style'],	
	'rating_display' 			=> $settings['rating_display'] ? true : false,	
	'gallery'        			=> false,	
	'display_attributes'  		=> $settings['display_attributes'] ? true : false,	
 
	'product_display_hover'     => $settings['product_display_hover'] ? true : false,	
	'sale_price_only'        	=> $settings['sale_price_only'] ? true : false,
);

$count = 1;
$i = 0;
if ( $block_data['layout'] == "3") {
	$slwVal ='15 ';
} else {	
	$slwVal ='15 ';
}
$row_col_class  = "col-xl-{$settings['col_xl']} col-lg-{$settings['col_lg']} col-md-{$settings['col_md']} col-sm-{$settings['col_sm']} col-{$settings['col_mobile']}";

$iscountdown ="is-not-countdown";

if ( '1' == $settings['title_style'] ) { 
	$iscountdown = $settings['iscountdown'] ? 'is-countdown' : 'is-not-countdown';
}

$slider_btn_style = $settings['slider_btn_style'] == '1' ? 'axil-slick-arrow' : 'axil-slick-angle';
?>

 <div class="axil-product-area has-axil-explore-area bg-color-white <?php echo esc_attr( $iscountdown ); ?>">
	<?php if ( $settings['section_title_display'] ): ?>		        
      
		<?php if ( '1' == $settings['title_style'] ) { ?>	 
	        <div class="d-flex align-items-center axilcoutdown2">
	            <div class="section-title-wrapper">
	            	<?php if ( $settings['sub_title'] ): ?>
	                	<span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?>"> <?php Icons_Manager::render_icon( $settings['icon'] ); ?> <?php echo wp_kses_post( $settings['sub_title'] );?></span>
	                  <?php endif; ?>	
	               
						<?php  if($settings['title']){ ?>
							<<?php echo esc_html( $settings['sec_title_tag'] );?> class="sec-title mb--0"><?php echo wp_kses_post( $settings['title'] );?></<?php echo esc_html( $settings['sec_title_tag'] );?>>   
						<?php  } ?> 
	            </div>                      
				<?php if ( $settings['iscountdown'] ): ?>
				    <?php if ( $settings['date'] ): ?>
						<div class="sale-countdown countdown" data-time="<?php echo esc_attr( $settings['date'] ); ?>"></div>
					<?php endif; ?>
				<?php endif; ?>
	  		</div>
		<?php }else{ ?>
		<div class="section-title-wrapper section-title-border"> 
		    <<?php echo esc_html( $settings['sec_title_tag'] );?> class="sec-title title">
		    <?php echo wp_kses_post( $settings['title'] );?><?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?> 
		    </<?php echo esc_html( $settings['sec_title_tag'] );?>>  
		</div>
		<?php } ?>	
	<?php endif; ?>	 
		<?php if ( $query->have_posts() ) :?>
			 <div class="explore-product-activation slick-layout-wrapper slick-layout-wrapper--15 <?php echo esc_attr($slider_btn_style );?> angle-top-slide">                   
				<?php
				while ( $query->have_posts() ) {			
					$query->the_post();
					$id = get_the_ID();
					$product = wc_get_product( $id );				
					$i++;
					$number = $settings['number'];
					$number_off_row = $settings['number_off_row']; 
					?>								
					<?php 	echo ($count == 1 ) ? '<div class="slick-single-layout"> <div class="row row--'. esc_attr( $slwVal ) .'">' : ''; 
						echo '<div class=" '. $row_col_class .'">';
						wc_get_template( "custom/product-block/blocks.php" , compact( 'product', 'block_data' ) );
						echo "</div>";
						 if( $count == $number_off_row || $i == $number ){
							echo "</div></div>";
								$count = 1;
						}else if($query->post_count == $i){
							echo "</div></div>";
							$count++;
						}
						else{
							$count++;
						}			
					}
					?>
				</div>	
		<?php else:?>
			<div><?php esc_html_e( 'No products available', 'etrade-elements' ); ?></div>
		<?php endif;?>
	</div>

	<?php if ( true == $settings['islink'] ): ?>
	 <div class="row">
        <div class="col-lg-12 text-center mt--20 mt_sm--0">
        	<?php echo wp_kses_post( $btn );?>					
        </div>
    </div>
	<?php endif; ?>
<?php wp_reset_postdata();?>
