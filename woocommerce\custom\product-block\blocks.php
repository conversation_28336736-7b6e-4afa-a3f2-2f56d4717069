<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

if ( !isset( $product ) ) {
    return;
}

if ( !isset( $block_data ) ) {
    $block_data = array();
}
$defaults = array(
    'layout'                    => 1,
    'thumb_size'                => 'woocommerce_thumbnail',
    'rating_display'            => true,
    'wishlist'                  => true,
    'quickview'                 => true,
    'compare'                   => true,
    'display_attributes'        => true,
    'product_display_hover'     => true,
    'display_title_badge_check' => false,
    'sale_price_only'           => true,

);
$block_data = wp_parse_args( $block_data, $defaults );
$block_data = apply_filters( 'etrade_block_args', $block_data );
wc_get_template( "custom/product-block/block-{$block_data['layout']}.php", compact( 'product', 'block_data' ) );