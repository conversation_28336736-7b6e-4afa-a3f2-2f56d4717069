<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options = Helper::axil_get_options();
$header_layout = Helper::axil_header_layout();
$header_sticky = $header_layout['header_sticky'];
$header_sticky = ("1" !== $header_sticky && "0" !== $header_sticky) ? " header-sticky " : "";
$axil_nav_menu_args = Helper::axil_nav_menu_args();
?>
<header class="header axil-header header-style-5 default-menu">
    <div id="axil-sticky-placeholder"></div>
    <div class="axil-mainmenu">
        <div class="container">
            <div class="header-navbar">
                 <?php get_template_part('template-parts/header/header-logo/header', 'logo'); ?>  
                 <?php get_template_part('template-parts/header/header-nav/header', 'nav'); ?>   
                 <?php get_template_part('template-parts/header/header-right/header-right', '2'); ?>
            </div>
        </div>
    </div>
</header>