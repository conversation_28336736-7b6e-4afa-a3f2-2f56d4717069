<?php
/**
 * Single variation cart button
 *
 * @see https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 7.0.1
 */

defined( 'ABSPATH' ) || exit;

global $product;
?>
<div class="product-action-wrapper  woocommerce-variation-add-to-cart variations_button"> 
	<!-- d-flex-center -->
	<?php do_action( 'woocommerce_before_add_to_cart_button' );?>
			<?php
			do_action( 'woocommerce_before_add_to_cart_quantity' );
			woocommerce_quantity_input(
				array(
					'min_value'   => apply_filters( 'woocommerce_quantity_input_min', $product->get_min_purchase_quantity(), $product ),
					'input_value' => isset( $_POST['quantity'] ) ? wc_stock_amount( wp_unslash( $_POST['quantity'] ) ) : $product->get_min_purchase_quantity(), // WPCS: CSRF ok, input var ok.
					'max_value' => apply_filters( 'woocommerce_quantity_input_max', $product->get_max_purchase_quantity(), $product ),
				)
			);
			do_action( 'woocommerce_after_add_to_cart_quantity' );
			?>
	<button type="submit" class="axil-btn single_add_to_cart_button button alt"><?php echo esc_html( $product->single_add_to_cart_text() ); ?></button>
	<?php do_action( 'woocommerce_after_add_to_cart_button' );?>
	<input class="qtybtn" type="hidden" name="add-to-cart" value="<?php echo absint( $product->get_id() ); ?>" />
	<input type="hidden" name="variation_id" class="variation_id" value="0" />
	<input class="qtybtn" type="hidden" name="product_id" value="<?php echo absint( $product->get_id() ); ?>" />
</div>

