<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Typography;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class axil_banner_slider_nft extends Widget_Base {

 public function get_name() {
        return 'banner-slider-nft';
    }    
    public function get_title() {
        return esc_html__( 'Banner Slider - NFTs', 'etrade-elements' );
    }
    public function get_icon() {
        return 'eicon-products';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }

	private function wooc_cat_dropdown_1() {
		$terms = get_terms( array( 'taxonomy' => 'product_cat' ) );
		$category_dropdown = array( '0' => esc_html__( 'All Categories', 'etrade-elements' ) );

		foreach ( $terms as $category ) {
			$category_dropdown[$category->term_id] = $category->name;
		}

		return $category_dropdown;
	}
 
	private function wooc_cat_dropdown_2() {
		$terms = get_terms( array( 'taxonomy' => 'product_cat', 'parent' => 0, 'hide_empty' => false ) );
		$category_dropdown = array();
		foreach ( $terms as $category ) {
			$category_dropdown[$category->term_id] = $category->name;
		}

		return $category_dropdown;
	}

 	private function axil_get_all_pages()
	    {

	        $page_list = get_posts(array(
	            'post_type' => 'page',
	            'orderby' => 'date',
	            'order' => 'DESC',
	            'posts_per_page' => -1,
	        ));

	        $pages = array();

	        if (!empty($page_list) && !is_wp_error($page_list)) {
	            foreach ($page_list as $page) {
	                $pages[$page->ID] = $page->post_title;
	            }
	        }

	        return $pages;
	    }


	private function wooc_build_query( $settings ) {

		if ( !$settings['custom_id'] ) {

			// Post type
			$number = $settings['number'];
			$args = array(
				'post_type'      => 'product',
				'posts_per_page' => $number ? $number : 3,
				'ignore_sticky_posts' => true,
				'post_status'         => 'publish',
				'suppress_filters'    => false,
			);

			$args['tax_query'] = array();

			// Category
			if ( !empty( $settings['cat'] ) ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_cat',
					'field'    => 'term_id',
					'terms'    => $settings['cat'],
				);
			}

			// Featured only
			if ( $settings['featured_only'] ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_visibility',
					'field'    => 'slug',
					'terms'    => 'featured',
				);
			}

			// Out-of-stock hide
			if ( $settings['out_stock_hide'] ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_visibility',
					'field'    => 'slug',
					'terms'    => 'outofstock',
					'operator' => 'NOT IN',
				);
			}

			// Order
			$args['orderby'] = $settings['orderby'];
			switch ( $settings['orderby'] ) {

				case 'title':
				case 'menu_order':
				$args['order']    = 'ASC';
				break;

				case 'bestseller':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = 'total_sales';
				break;

				case 'rating':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_wc_average_rating';
				break;

				case 'price_l':
				$args['orderby']  = 'meta_value_num';
				$args['order']    = 'ASC';
				$args['meta_key'] = '_price';
				break;

				case 'price_h':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_price';
				break;
			}
		}

		else {

			//$posts = array_map( 'trim' , explode( ',', $settings['product_ids'] ) );
			$posts = $settings['product_ids'];
			$args = array(
				'post_type'      => 'product',
				'ignore_sticky_posts' => true,
				'nopaging'       => true,
				'post__in'       => $posts,
				'orderby'        => 'post__in',
			);
		}

		return new \WP_Query( $args );
	}


    protected function register_controls() {
        
        $args = array(
			'post_type'           => 'product',
			'posts_per_page'      => -1,
			'post_status'         => 'publish',
			'suppress_filters'    => false,
			'ignore_sticky_posts' => true,
		);
		$products = get_posts( $args );
		$products_dropdown = array();

		foreach ( $products as $product ) {
			$products_dropdown[$product->ID] = $product->post_title;
		}
   

		$this->start_controls_section(
            'sec_general',
            [
                'label' => esc_html__( 'General', 'etrade-elements' ),                
            ]
        );


		$this->add_control(
		    'section_title_display',
		    [
		         'type' => Controls_Manager::SWITCHER,
				'label'       => __( 'Banner Content Display', 'etrade-elements' ),
				'label_on'    => __( 'On', 'etrade-elements' ),
				'label_off'   => __( 'Off', 'etrade-elements' ),
				'default'     => 'yes',
	        
		    ] 
		);   
        
      $this->add_group_control(
        \Elementor\Group_Control_Background::get_type(),
            [
                'name' => 'background',
                'label' => __( 'Banner Background', 'plugin-domain' ),
                'types' => [ 'classic', 'gradient' ],
                'selector' => '{{WRAPPER}} .axil-main-slider-area.main-slider-style-3',
                
            ]
        );
 
		$this->end_controls_section();	
 


		$this->start_controls_section(
            'sec_general_section_title',
            [
                'label' => esc_html__( 'Banner Content', 'etrade-elements' ),  
                'condition'   => array( 'section_title_display' => 'yes' ),              
            ]
        ); 

		$this->add_control(
		    'subtitle',
		    [
		        'label' => __( 'Before Title', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXT,
		        'default' => __( 'Largest NFT marketplace', 'etrade-elements' ),
		        'placeholder' => __( 'Before Title', 'etrade-elements' ),
		        
		    ]
		); 
		$this->add_control(
            'beforetitlestyle',
            [
                'label' => esc_html__( 'Before Color', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT,
                'default' => 'primary',
                'options' => [
                    'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                    'secondary'   => esc_html__( 'Secondary', 'etrade-elements' ),                                          
                    'primary2'   => esc_html__( 'Primary 2', 'etrade-elements' ),                                          
                                                              
                ],
            ] 
        );   
        $this->add_control(
            'icon',
            [
                'label' => __( 'Icons', 'etrade-elements' ),
                'type' => Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-fire',
                    'library' => 'solid',
                ],
                      
            ]
        );
 
		$this->add_control(
		    'title',
		    [
		        'label' => __( 'Title', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXTAREA,
		        'default' => __( 'Discover, collect, and sell extraordinary NFTs', 'etrade-elements' ),
		        'placeholder' => __( 'Title', 'etrade-elements' ),
		        
		    ]
		); 
 

		$this->add_control(
		    'islink',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Show Button', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'no',
		        
		    ] 
		); 		              
 
		$this->add_control(
		    'btntext',
		    [
		        'label'   => __( 'Button Text', 'etrade-elements' ),
		        'type'    => Controls_Manager::TEXT,
		        'default' => 'Explore',
		        'condition'   => array( 'islink' => 'yes' ),
		    ]
		);
 
		 $this->add_control(
            'axil_link_type',
            [
                'label' => esc_html__( 'See All Link Type', 'etrade-elements'),
                'type' => Controls_Manager::SELECT,
                'options' => [
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ],
                'default' => '1',
                'condition'   => array( 'islink' => 'yes' ),
                'label_block' => true
            ]
        );

     	$this->add_control(
            'axil_page_link',
            [
                'label' => esc_html__('Select See All Page', 'etrade-elements'),
                'type' => Controls_Manager::SELECT2,
                'label_block' => true,
                'options' => $this-> axil_get_all_pages(),
                'condition' => [
                    'axil_link_type' => '2',
                    'islink' => 'yes',
                ]
            ]
        );

		$this->add_control(
		    'url',
		    [
		        'label'   => __( 'Detail URL', 'etrade-elements' ),
		        'type'    => Controls_Manager::URL,
		        'placeholder' => 'https://your-link.com',	
		        'condition' => [
                    'axil_link_type' => '1',
                    'islink' => 'yes',
                ]	        
		    ]
		);   

		$this->end_controls_section();	

 

        $this->start_controls_section(
            'sec_filter',
            [
                'label' => esc_html__( 'Product Filtering', 'etrade-elements' ),
                
            ]
        );       

         $this->add_control(      
            'cat',
                [
                'label' => __( 'Categories', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT2,
               	'options'     => $this->wooc_cat_dropdown_2(),            
                'label_block'   => true,                
                'default'     => '0',
                'separator'     => 'before',
                 'multiple'  => true,
                ] 
            );

		$this->add_control(
		    'custom_id',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Custom Product ID', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		);   
		 
		$this->add_control(      
		   'product_ids',
		       [
		       'label' => __( 'Select The Products minimum 4 will display', 'etrade-elements' ),
		       'type' => Controls_Manager::SELECT2,
		       'options'       => wooc_product_post_name(),                  
		       'label_block'   => true,
		       'multiple'      => true,
		       'separator'     => 'before',
		       'condition'   => array( 'custom_id' => 'yes' ),
		       ] 
		   ); 
		  

	 	$this->add_control(
	        'number',
	            [
	                'label'   => esc_html__( 'Number of items', 'etrade-elements' ),
	                'type'    => Controls_Manager::NUMBER,
	                'default'     => 4,               
	                
	            ]

	        );
        $this->add_control(
		    'rating_display',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Rating Display', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',
		        
		        
		    ] 
		); 		

		$this->add_control(      
            'orderby',
                [
                'label' => esc_html__( 'Categories', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT2,
	               'options'     => array(
						'date'        => esc_html__( 'Date (Recents comes first)', 'etrade-elements' ),
						'title'       => esc_html__( 'Title', 'etrade-elements' ),
						'bestseller'  => esc_html__( 'Bestseller', 'etrade-elements' ),
						'rating'      => esc_html__( 'Rating(High-Low)', 'etrade-elements' ),
						'price_l'     => esc_html__( 'Price(Low-High)', 'etrade-elements' ),
						'price_h'     => esc_html__( 'Price(High-Low)', 'etrade-elements' ),
						'rand'        => esc_html__( 'Random(Changes on every page load)', 'etrade-elements' ),
						'menu_order'  => esc_html__( 'Custom Order (Available via Order field inside Page Attributes box)', 'etrade-elements' ),
					),    
                'label_block'   => true,                
                'default'     => 'date',
                'separator'     => 'before',
                ] 
            );

		$this->add_control(
		    'out_stock_hide',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Hide Out-of-stock Products', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		); 		

		$this->add_control(
		    'featured_only',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => esc_html__( 'Display only Featured Products', 'etrade-elements' ),
		        'label_on'    => esc_html__( 'On', 'etrade-elements' ),
		        'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		); 		
				
		
       $this->end_controls_section();   
 
        $this->start_controls_section(
            'title_style_section',
            [
                'label' => __( 'Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
            $this->add_control(
            'title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .sec-title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .sec-title',
            ]
        );
       
        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .sec-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
       
    $this->end_controls_section();


        $this->start_controls_section(
            'axil_sub_title_style_section',
            [
                'label' => __( 'Sub Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
        $this->add_control(
            'sub_title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .sub-title' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .subtitle i' => 'background-color: {{VALUE}}',
                ),
            ]
        );
         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'sub_title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .sub-title',
            ]
        );
       
        $this->add_responsive_control(
            'sub_title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .sub-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
       
    $this->end_controls_section();
 
    $this->start_controls_section(
        'axil_btn_style_section',
        [
            'label' => __( 'Button', 'etrade-elements' ),
            'tab' => Controls_Manager::TAB_STYLE,                
        ]
    );

    $this->add_control(
        'btn_color',
        [
            'label' => __( 'Color', 'etrade-elements' ),
            'type' => Controls_Manager::COLOR,  
            'default' => '',
            
            'selectors' => array(
                '{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white' => 'color: {{VALUE}}',
                 '{{WRAPPER}} a.axil-btn.right-icon i' => 'color: {{VALUE}}',
            ),
        ]
    );
    
    $this->add_control(
        'button_bg_color',
        [
            'label' => esc_html__( 'Background Color', 'etrade-elements' ),
            'type' => Controls_Manager::COLOR,  
            'default' => '',
           
            'selectors' => array(
                '{{WRAPPER}} .main-slider-content .shop-btn a' => 'background: {{VALUE}}',
                '{{WRAPPER}} .main-slider-content .shop-btn a:before' => 'background: {{VALUE}}',
                
            ),
        ]
    );

	$this->add_group_control(
		Group_Control_Typography::get_type(),
		[
			'name' => 'btn_font_size',
			'label' => __( 'Typography', 'etrade-elements' ),                
				
			'selector' => '{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white',
		]
	);
	
	$this->add_responsive_control(
		'btn_margin',
		[
			'label' => __( 'Margin', 'etrade-elements' ),
			'type' => Controls_Manager::DIMENSIONS,
			'size_units' => [ 'px', '%', 'em' ],
				
			'selectors' => [
				'{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				
			],
		]
	); 
	$this->add_responsive_control(
		'btn_padding',
		[
			'label' => __( 'Margin', 'etrade-elements' ),
			'type' => Controls_Manager::DIMENSIONS,
			'size_units' => [ 'px', '%', 'em' ],
				
			'selectors' => [
				'{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				
			],
		]
	);
	   
	$this->end_controls_section();
	 
  
    }
    
    private function slick_load_scripts(){
        wp_enqueue_style(  'slick' );
        wp_enqueue_style(  'slick-theme' );
        wp_enqueue_script( 'slick' );
    }

	protected function render() {
		$settings = $this->get_settings();	
		$this->slick_load_scripts();
		$template = 'nft-product-slider-1';	
		$settings['query'] = $this->wooc_build_query( $settings );	
		return wooc_Elements_Helper::wooc_element_template( $template, $settings );
	}
}