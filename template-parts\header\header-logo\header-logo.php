<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options = Helper::axil_get_options(); 
$logo  = empty($axil_options['axil_head_logo']['url'] ) ? Helper::get_img( 'logo/logo.svg' ) :$axil_options['axil_head_logo']['url'];
?>
<div class="header-brand">
    <?php if (isset($axil_options['axil_logo_type'])): ?>
    <a href="<?php echo esc_url(home_url('/')); ?>"
       title="<?php echo esc_attr(get_bloginfo('name')); ?>" rel="<?php echo esc_html__('home', 'etrade'); ?>">
        <?php if ('image' == $axil_options['axil_logo_type']): ?>
            <?php if($axil_options['axil_head_logo']){ ?>
                <img class="dark-logo" src="<?php echo esc_url( $logo ); ?>" alt="<?php echo esc_attr(get_bloginfo('name')); ?>">
            <?php } ?>                              
        <?php else: ?>
            <?php if ('text' == $axil_options['axil_logo_type']): ?>
                <?php echo esc_html($axil_options['axil_logo_text']); ?>
            <?php endif ?>
        <?php endif ?> 
    </a>
    <?php else: ?>
    <h3>
        <a href="<?php echo esc_url(home_url('/')); ?>"
           title="<?php echo esc_attr(get_bloginfo('name', 'display')); ?>" rel="home">
            <?php if (isset($axil_options['axil_logo_text']) ? $axil_options['axil_logo_text'] : '') {
                echo esc_html($axil_options['axil_logo_text']);
            } else {
                bloginfo('name');
            }
            ?>
        </a>
    </h3> 
    <?php $description = get_bloginfo('description', 'display');
    if ($description || is_customize_preview()) { ?>
        <p class="site-description"><?php echo esc_html($description); ?> </p>
    <?php } ?>
    <?php endif ?>
</div> <!-- End Logo-->