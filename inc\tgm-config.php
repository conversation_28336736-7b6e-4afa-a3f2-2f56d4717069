<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package etrade
 */

class TGM_Config {
    public $prfx = AXIL_THEME_FIX;
    public $path = "https://dev.axilthemes.com/themes/etrade/demo/plugins/";

    public function __construct() {
        add_action( 'tgmpa_register', array( $this, 'axil_tgn_plugins' ) );
    }

    public function axil_tgn_plugins() {
        $plugins = array(
            array(
                'name'     => esc_html__( 'Axil Elements', 'etrade' ),
                'slug'     => 'axil-elements',
                'source'   => 'axil-elements.zip',
                'required' => true,
                'version'  => '1.0',
            ),
            array(
                'name'     => esc_html__( 'axilthemes-demo-importer-helper', 'etrade' ),
                'slug'     => 'axilthemes-demo-importer-helper',
                'source'   => 'axilthemes-demo-importer-helper.zip',
                'required' => true,
                'version'  => '1.0',
            ), 
            array(
                'name'     => esc_html__( 'Advanced Custom Fields Pro', 'etrade' ),
                'slug'     => 'advanced-custom-fields-pro',
                'source'   => 'advanced-custom-fields-pro.zip',
                'required' => true,
            ),
            array(
				'name'         => esc_html__('Etrade Demo', 'etrade'),
				'slug'         => 'etrade-demo',
				'source'       => 'etrade-demo.zip',
				'required'     =>  true,
				'version'      => '1.0'
			),
            // Repository
            array(
                'name'     => esc_html__( 'Redux Framework', 'etrade' ),
                'slug'     => 'redux-framework',
                'required' => true,
            ),

            array(
                'name'     => esc_html__( 'Elementor Page Builder', 'etrade' ),
                'slug'     => 'elementor',
                'required' => true,
            ),
            array(
                'name'     => esc_html__( 'Contact Form 7', 'etrade' ),
                'slug'     => 'contact-form-7',
                'required' => false,
            ),
            array(
                'name'     => 'Contact Form 7 Extension For Mailchimp',
                'slug'     => 'contact-form-7-mailchimp-extension',
                'required' => false,
            ),
            array(
                'name'     => esc_html__( 'MailChimp for WordPress', 'etrade' ),
                'slug'     => 'mailchimp-for-wp',
                'required' => false,
            ),    
            array(
                'name'     => esc_html__( 'Contact Form 7 Database Addon – CFDB7', 'etrade' ),
                'slug'     => 'contact-form-cfdb7',
                'required' => false,
            ),

            array(
                'name'     => 'WooCommerce',
                'slug'     => 'woocommerce',
                'required' => false,
            ),
            array(
                'name'     => 'YITH WooCommerce Quick View',
                'slug'     => 'yith-woocommerce-quick-view',
                'required' => false,
            ),
            array(
                'name'     => 'YITH WooCommerce Wishlist',
                'slug'     => 'yith-woocommerce-wishlist',
                'required' => false,
            ),
            array(
                'name'     => 'Classic Widgets',
                'slug'     => 'classic-widgets',
                'required' => false,
            ),  
             array(
                'name'     => 'YITH WooCommerce Compare',
                'slug'     => 'yith-woocommerce-compare',
                'required' => false,
            ), 

        );

        $config = array(
            'id' => $this->prfx, // Unique ID for hashing notices for multiple instances of TGMPA.
            'default_path' => $this->path, // Default absolute path to bundled plugins.
            'menu' => $this->prfx . '-install-plugins', // Menu slug.
            'is_automatic' => false, // Automatically activate plugins after installation or not.
           
        );

        tgmpa( $plugins, $config );
    }
}

new TGM_Config;