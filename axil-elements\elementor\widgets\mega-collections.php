<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base; 
use Elementor\Controls_Manager; 
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class axil_mega_collections extends Widget_Base {

 public function get_name() {
        return 'axil-mega-collections';
    }    
    public function get_title() {
        return esc_html__( 'Mega Collections', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-image-box';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }  
    public function axil_get_img($img)
     {
         $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
         return $img;
    } 
    
  public function get_product_name ( $post_type = 'product' ){
        $options = array();
        $options = ['0' => esc_html__( 'None', 'etrade-elements' )];
        $axil_post = array( 'posts_per_page' => -1, 'post_type'=> $post_type );
        $axil_post_terms = get_posts( $axil_post );
        if ( ! empty( $axil_post_terms ) && ! is_wp_error( $axil_post_terms ) ){
            foreach ( $axil_post_terms as $term ) {
                $options[ $term->ID ] = $term->post_title;
            }
            return $options;
        }
    }
	 private function axil_get_all_pages()
	    {

	        $page_list = get_posts(array(
	            'post_type' => 'page',
	            'orderby' => 'date',
	            'order' => 'DESC',
	            'posts_per_page' => -1,
	        ));

	        $pages = array();

	        if (!empty($page_list) && !is_wp_error($page_list)) {
	            foreach ($page_list as $page) {
	                $pages[$page->ID] = $page->post_title;
	            }
	        }

	        return $pages;
	    }

    protected function register_controls() {

		$this->start_controls_section(
			'mega_layout_by_content',
			[
				'label' => esc_html__( 'Info/Image/link', 'etrade-elements' ),
		
			]
		); 

        $this->add_group_control(

            \Elementor\Group_Control_Background::get_type(),

            [
                'name' => 'background',

                'label' => esc_html__( 'Background', 'textdomain' ),

                'types' => [ 'classic', 'gradient', 'video' ],

                'selector' => '{{WRAPPER}} .product-collection',               

            ]

        );  
		$this->add_control(
			'title',
			[
				'label' => esc_html__( 'Title', 'etrade-elements' ),
				'type' => Controls_Manager::TEXTAREA,
				'default' => esc_html__( 'Explore The Sunglass', 'etrade-elements' ),
				'placeholder' => esc_html__( 'Title', 'etrade-elements' ),
			]
		);
		$this->add_control(
			'subtitle',
			[
				'label' => esc_html__( 'Sub Title', 'etrade-elements' ),
				'type' => Controls_Manager::TEXTAREA,
				'default' => esc_html__( 'The Bouguessa FW21 collection is.', 'etrade-elements' ),
				'placeholder' => esc_html__( 'Subtitle Here', 'etrade-elements' ),
			]
		);
			
		$this->add_control(
			'current_price_label',
			[
				'label' => esc_html__( 'Price Label', 'etrade-elements' ),
				'type' => Controls_Manager::TEXT,
				'default' => esc_html__( 'Starting From', 'etrade-elements' ),
					
			]
		);
		$this->add_control(
			'current_price',
			[
				'label' => esc_html__( 'Price', 'etrade-elements' ),
				'type' => Controls_Manager::TEXT,
				'default' => esc_html__( '$35.00', 'etrade-elements' ),
					
			]
		); 

		$this->add_control(
			'image',
			[
				'label' => esc_html__('Image','etrade-elements'),
				'type'=>Controls_Manager::MEDIA,			        
				'default' => [
					'url' =>  $this->axil_get_img( 'collection_1.jpg' ),
				],

				'dynamic' => [
					'active' => true,
				],
					
			]
		);
		$this->add_group_control(
			Group_Control_Image_Size::get_type(),
			[
				'name' => 'image_size',
				'default'  => 'full',
				'separator' => 'none',	
								
			]
		);		 

		$this->add_control(
			'product_badget',
			[
				'label' 	=> esc_html__('Product Badget','etrade-elements'),
				'type' 		=> Controls_Manager::TEXT,
				'default' 	=> esc_html__('SUNGLASS','etrade-elements'), 
				'title' 	=> esc_html__('Enter Product Badget','etrade-elements'),
				
			]
		);

		$this->add_control(
			'link_text',
			[
				'label' => esc_html__('Link Text','etrade-elements'),
				'type' => Controls_Manager::TEXT,
				'default' => esc_html__('SHOP BRAND','etrade-elements'), 
					'separator'     => 'before', 
				'title' => esc_html__('Enter button text','etrade-elements'),
					
			]
		);

		$this->add_control(
			'link_type',
			[
				'label' => esc_html__('Link Type','etrade-elements'),
				'type' => Controls_Manager::SELECT,
				'options' => [
					'1' => 'Custom Link',
					'2' => 'Internal Page',
				],
				'default' => '1',
			]
		);
		$this->add_control(
			'link',
			[
				'label' => esc_html__('Link link','etrade-elements'),
				'type' => Controls_Manager::URL,
				'dynamic' => [
					'active' => true,
				],
				'placeholder' => esc_html__('https://your-link.com','etrade-elements'),
				'show_external' => true,
				'default' => [
					'url' => '#',
					'is_external' => true,
					'nofollow' => true,
				],
				'condition' => [
					'link_type' => '1'
				]
			]
		);
		$this->add_control(
			'page_link',
			[
				'label' => esc_html__('Select Link Page','etrade-elements'),
				'type' => Controls_Manager::SELECT2,
				'label_block' => true,
				'options' =>  $this-> axil_get_all_pages(),
				'condition' => [
					'link_type' => '2'
				]
			]
		);


		$this->end_controls_section();

  		$this->start_controls_section(
            'title_style_section',
            [
                'label' => esc_html__( 'Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        ); 
 
        $this->add_control(
            'mega_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .collection-content .title' => 'color: {{VALUE}}',
                     
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'mega_font_size',
                'label' => esc_html__( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .collection-content .title',
            ]
        );
       
        $this->add_responsive_control(
            'mega_title_margin',
            [
                'label' => esc_html__( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .collection-content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                     
                    
                ],
            ]
        ); 
        $this->end_controls_section();  


  		$this->start_controls_section(
            'subtitle_style_section',
            [
                'label' => esc_html__( 'Sub Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        ); 
 
        $this->add_control(
            'mega_subtitle_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .collection-content .item-subtitle' => 'color: {{VALUE}}',
                     
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'mega_subfont_size',
                'label' => esc_html__( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .collection-content .item-subtitle',
            ]
        );
       
        $this->add_responsive_control(
            'mega_subtitle_margin',
            [
                'label' => esc_html__( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .collection-content .item-subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                     
                    
                ],
            ]
        ); 
        $this->end_controls_section();

  		$this->start_controls_section(
            'price_label_style_section',
            [
                'label' => esc_html__( 'Prices Label', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        ); 
 
          $this->add_control(
            'price_label_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .collection-content .price-text' => 'color: {{VALUE}}',
                     
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'price_label_size',
                'label' => esc_html__( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .collection-content .price-text',
            ]
        );
       
        $this->add_responsive_control(
            'price_label_margin',
            [
                'label' => esc_html__( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .collection-content .price-text' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                     
                    
                ],
            ]
        ); 

        $this->end_controls_section(); 
        	$this->start_controls_section(
            'price_style_section',
            [
                'label' => esc_html__( 'Prices', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        ); 
 
        $this->add_control(
            'price_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .collection-content .price' => 'color: {{VALUE}}',
                     
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'price_font_size',
                'label' => esc_html__( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .collection-content .price',
            ]
        );
       
        $this->add_responsive_control(
            'price_margin',
            [
                'label' => esc_html__( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .collection-content .price' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                     
                    
                ],
            ]
        ); 
        $this->end_controls_section(); 
 
  		$this->start_controls_section(
            'plus_icon_style_section',
            [
                'label' => esc_html__( 'Plus Icon', 'etrade-elements' ),
                                
            ]
        );  
        $this->add_control(
		    'plus_icon_display',
		    [
		         'type' => Controls_Manager::SWITCHER,
				'label'       => esc_html__( 'Plus Icon Display', 'etrade-elements' ),
				'label_on'    => esc_html__( 'On', 'etrade-elements' ),
				'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
				'default'     => 'yes',
				 'separator'     => 'before',
	        
		    ] 
		);   
		$this->add_control(
			    'popup_icon_active',
			    [
			         'type' => Controls_Manager::SWITCHER,
					'label'       => esc_html__( 'Active Display', 'etrade-elements' ),
					'label_on'    => esc_html__( 'On', 'etrade-elements' ),
					'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
					'default'     => 'yes',
					 'separator'  => 'before',
					 'condition'   => array( 'plus_icon_display' => 'yes' ),
		        
			    ] 
			);   

	          $this->add_control(   
                'product_ids',
                [
                    'label'         => __( 'Select Product will display', 'etrade-elements' ),
                    'type'          => Controls_Manager::SELECT,
                    'options'       => $this->get_product_name(),                  
                    'label_block'   => true,
                    'multiple'      => true,
                    'separator'     => 'before',
                    'condition'   => array( 'plus_icon_display' => 'yes' ),
                    
                ] 
            );
    
            $this->add_control(
                'rating_display',
                [
                    
                    'type'          => Controls_Manager::SWITCHER,
                    'label'         => esc_html__( 'Rating Display', 'etrade-elements' ),
                    'label_on'      => esc_html__( 'On', 'etrade-elements' ),
                    'label_off'     => esc_html__( 'Off', 'etrade-elements' ),
                    'default'       => 'yes',
                    'condition'   => array( 'plus_icon_display' => 'yes' ),
                    
                    
                ] 
            );  
             $this->add_control(
            'sale_price_only',
            [
                
                'type' => Controls_Manager::SWITCHER,
                'label'       => __( 'Display only sale price', 'etrade-elements' ),
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => '',              
                
            ] 
        );      
		$this->add_control(
			'pos_x_type',
			[
				'type' => Controls_Manager::CHOOSE,
				'label' => esc_html__( 'Horizontal Position', 'etrade-elements' ),
				'condition'   => array( 'plus_icon_display' => 'yes' ),
				'options' => [
					'left' => [
						'title' => esc_html__( 'Left', 'etrade-elements' ),
						'icon' => 'eicon-h-align-left',
					], 
					'right' => [
						'title' => esc_html__( 'Right', 'etrade-elements' ),
						'icon' => 'eicon-h-align-right',
					],
				],
				'default' => 'left',
			]
		);

		$this->add_responsive_control(
			'pos_x',
			[
				'type' => Controls_Manager::SLIDER,
				'label' => esc_html__( 'Spacing', 'etrade-elements' ),
				'condition'   => array( 'plus_icon_display' => 'yes' ),
				'size_units' => array( 'px', '%' ),
				'range' => array(
					'px' => array(
						'min' => -500,
						'max' => 500,
					),
					'%' => array(
						'min' => -100,
						'max' => 100,
					),
				),
				'default' => array(
					'unit' => '%',
					'size' => 40,
				),

				'selectors' => [
					'{{WRAPPER}} .plus-btn.axil-pos-left'  => 'left: {{SIZE}}{{UNIT}};',
					'{{WRAPPER}} .plus-btn.axil-pos-right' => 'right: {{SIZE}}{{UNIT}};',
				],
			]
		); 

		$this->add_control(
			'pos_y_type',
			[
				'type' => Controls_Manager::CHOOSE,
				'label' => esc_html__( 'Vertical Position', 'etrade-elements' ),
				'condition'   => array( 'plus_icon_display' => 'yes' ),
				'options' => [
					'top' => [
						'title' => esc_html__( 'Left', 'etrade-elements' ),
						'icon' => 'eicon-v-align-top',
					], 
					'bottom' => [
						'title' => esc_html__( 'Right', 'etrade-elements' ),
						'icon' => 'eicon-v-align-bottom',
					],
				],
				'default' => 'top',
			]
		);

		$this->add_responsive_control(
			'pos_y',
			[
				'type' => Controls_Manager::SLIDER,
				'label' => esc_html__( 'Spacing', 'etrade-elements' ),
				'size_units' => array( 'px', '%' ),
				'condition'   => array( 'plus_icon_display' => 'yes' ),
				'range' => array(
					'px' => array(
						'min' => -500,
						'max' => 500,
					),
					'%' => array(
						'min' => -100,
						'max' => 100,
					),
				),
				'default' => array(
					'unit' => '%',
					'size' => 30,
				),
				'selectors' => [
					'{{WRAPPER}} .plus-btn.axil-pos-top'    => 'top: {{SIZE}}{{UNIT}};',
					'{{WRAPPER}} .plus-btn.axil-pos-bottom' => 'bottom: {{SIZE}}{{UNIT}};',
				],
			]
		); 
 
 		$this->end_controls_section();


    }

	protected function render() {
		$settings = $this->get_settings();  
		$template   = 'mega-deals';                 
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
  
	
	}
}

