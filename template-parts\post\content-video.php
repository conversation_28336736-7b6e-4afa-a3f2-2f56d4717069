<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */
 
$video_url      = axil_get_acf_data("axil_video_link");
$axil_options   = Helper::axil_get_options();
$thumb_size         = ($axil_options['axil_blog_sidebar'] === 'no') ? 'axil-single-blog-thumb':'axil-blog-list';
$content        = get_the_content();
$content        = apply_filters( 'the_content', $content );
$content        = wp_trim_words( get_the_excerpt(),  $axil_options['post_content_limit'], '.' );
$readmore       = $axil_options['read_more_btn_txt'];
$allowed_tags = wp_kses_allowed_html( 'post' );
?>
<div id="post-<?php the_ID(); ?>" <?php post_class('content-blog format-video mt--60'); ?>>
    <div class="inner">
        <?php if(has_post_thumbnail()){ ?>
            <div class="thumbnail">
                <a href="<?php the_permalink(); ?>">
                    <?php the_post_thumbnail($thumb_size, ['class' => 'w-100']) ?>
                </a>
                <?php if(!empty($video_url)){ ?>
                    <div class="popup-video">
                        <a class="play-btn popup-youtube" href="<?php echo esc_url($video_url); ?>">
                            <span class="fas fa-play"></span>
                        </a>
                    </div>
                <?php } ?>
            </div>
        <?php } ?>  
        <div class="content">
            <h4 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
             <?php Helper::axil_postmeta(); ?>
            <p><?php echo wp_kses( $content, $allowed_tags ); ?></p>
             <?php if( !empty($readmore)  ){ ?>
                <div class="read-more-btn">
                    <a class="axil-btn btn-bg-primary" href="<?php the_permalink(); ?>"><?php echo esc_html($readmore); ?></a>
                </div>
              <?php } ?>  
        </div>
    </div>
</div>
 