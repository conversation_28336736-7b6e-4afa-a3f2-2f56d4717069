<?php
/**
 * The template for displaying product category thumbnails within loops
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product_cat.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce/Templates
 * @version 4.7.0
 */

if ( !defined( 'ABSPATH' ) ) {
    exit;
}

$block_data = axil_product_block_data();
$product_class = axil_product_col_class();
$axil_options = Helper::axil_get_options();
$product_layout = $axil_options['wooc_product_layout'];

?>
<div <?php wc_product_cat_class( $product_class, $category );?>>

    <div class="product-cat-block axil-product product-style-<?php echo esc_attr( $product_layout ); ?>">
        <?php
            /**
             * woocommerce_before_subcategory hook.
             *
             * @hooked woocommerce_template_loop_category_link_open - 10
             */
            do_action( 'woocommerce_before_subcategory', $category );

            ?>
                    <div class="thumbnail">
                    <?php
            /**
             * woocommerce_before_subcategory_title hook.
             *
             * @hooked woocommerce_subcategory_thumbnail - 10
             */
            do_action( 'woocommerce_before_subcategory_title', $category );
            ?>
        </div> 
        <div class="product-content">
        <div class="inner">
    <?php 
            /**
             * woocommerce_shop_loop_subcategory_title hook.
             *
             * @hooked woocommerce_template_loop_category_title - 10
             */
            do_action( 'woocommerce_shop_loop_subcategory_title', $category );

            /**
             * woocommerce_after_subcategory_title hook.
             */
            do_action( 'woocommerce_after_subcategory_title', $category );
            ?>
            </div>
        </div>

    <?php
        /**
         * woocommerce_after_subcategory hook.
         *
         * @hooked woocommerce_template_loop_category_link_close - 10
         */
        do_action( 'woocommerce_after_subcategory', $category );?> 
        </div>
    </div> 