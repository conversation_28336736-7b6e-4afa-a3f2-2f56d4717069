<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;
use Elementor\Icons_Manager;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class axil_btn extends Widget_Base {

 public function get_name() {
        return 'axil-btn';
    }    
    public function get_title() {
        return __( 'eTrade Button', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-button';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }

    private function axil_get_all_pages()
        { 
            $page_list = get_posts(array(
                'post_type' => 'page',
                'orderby' => 'date',
                'order' => 'DESC',
                'posts_per_page' => -1,
            ));

            $pages = array();

            if (!empty($page_list) && !is_wp_error($page_list)) {
                foreach ($page_list as $page) {
                    $pages[$page->ID] = $page->post_title;
                }
            }

            return $pages;
        }

        protected function register_controls() {  

            $this->start_controls_section(
                'btn_info',
                [
                    'label' => __( 'Button', 'etrade-elements' ),
                ]
            );      
            
            $this->add_control(
                'layout',
                [
                    'label' => __( 'Layout', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'default' => '1',
                    'options' => [
                        '1' => __( 'Style 1', 'etrade-elements' ),
                        '2' => __( 'Style 2', 'etrade-elements' ),                               
                        '3' => __( 'Style 3', 'etrade-elements' ),                               
                    ],
                ] 
            );    
        $this->add_responsive_control(
            'btn_align',
            [
                'label'   => __( 'Layout Alignment', 'etrade-elements' ),
                'type'    => Controls_Manager::CHOOSE,                
                'options' => [
                    'left'    => [
                        'title' => __( 'Left', 'etrade-elements' ),
                        'icon'  => 'fa fa-align-left',
                    ],
                    'center' => [
                        'title' => __( 'Center', 'etrade-elements' ),
                        'icon'  => 'fa fa-align-center',
                    ],
                    'right' => [
                        'title' => __( 'Right', 'etrade-elements' ),
                        'icon'  => 'fa fa-align-right',
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .btnwrp'   => 'text-align: {{VALUE}}',
                    
                ],
                'default' => 'center',
             
               
            ]
        ); 

        $this->add_responsive_control(
            'btn_width',
            [
                'type' => Controls_Manager::SLIDER,
                'label' => esc_html__( 'Width', 'etrade-elements' ),
              
                'size_units' => array( 'px', '%' ),
                'range' => array(
                    'px' => array(
                        'min' => 0,
                        'max' => 300,
                    ),
                    '%' => array(
                        'min' => 2,
                        'max' => 100,
                    ),
                ),
               

                'selectors' => [
                    '{{WRAPPER}} .btnwrp .axil-btn'  => 'width: {{SIZE}}{{UNIT}};',
                   
                ],
            ]
        ); 
 
            $this->add_control(
                'link_text',
                [
                    'label' => esc_html__('Link Text','etrade-elements'),
                    'type' => Controls_Manager::TEXT,
                    'default' => 'View All Items',
                      'separator'     => 'before', 
                    'title' => esc_html__('Enter button text','etrade-elements'),
                     
                ]
            );

            $this->add_control(
            'display_icon',
            [
                
                'type' => Controls_Manager::SWITCHER,
                'label'       => __( 'Display Icon', 'etrade-elements' ),
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => '',              
                
            ] 
        );      
        $this->add_control(
                'icon',
                [
                    'label' => __( 'Icons', 'etrade-elements' ),
                    'type' => Controls_Manager::ICONS,
                    'default' => [
                        'value' => 'fa fa-university',
                        'library' => 'solid',
                    ],
                    'condition' => [
                            'display_icon' => true,
                    ],      
                ]
            );


            $this->add_control(
                'link_type',
                [
                    'label' => esc_html__('Link Type','etrade-elements'),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '1' => 'Custom Link',
                        '2' => 'Internal Page',
                    ],
                    'default' => '1',
                ]
            );
              $this->add_control(
                'link',
                [
                    'label' => esc_html__('Link link','etrade-elements'),
                    'type' => Controls_Manager::URL,
                    'dynamic' => [
                        'active' => true,
                    ],
                    'placeholder' => esc_html__('https://your-link.com','etrade-elements'),
                    'show_external' => true,
                    'default' => [
                        'url' => '#',
                        'is_external' => true,
                        'nofollow' => true,
                    ],
                    'condition' => [
                        'link_type' => '1'
                    ]
                ]
            );
              $this->add_control(
                'page_link',
                [
                    'label' => esc_html__('Select Link Page','etrade-elements'),
                    'type' => Controls_Manager::SELECT2,
                    'label_block' => true,
                    'options' =>  $this-> axil_get_all_pages(),
                    'condition' => [
                        'link_type' => '2'
                    ]
                ]
            );
 
    $this->end_controls_section(); 


    $this->start_controls_section(
        'button_style_section',
            [
                'label' => esc_html__( 'Button', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
 
      $this->add_control(
        'button_color',
        [
            'label' => esc_html__( 'Text Color', 'etrade-elements' ),
            'type' => Controls_Manager::COLOR,  
            'default' => '',
           
            'selectors' => array(
                '{{WRAPPER}} .poster-countdown-content a.axil-btn' => 'color: {{VALUE}}',
                
                
            ),
        ]
    );
    $this->add_control(
        'button_bg_color',
        [
            'label' => esc_html__( 'Background Color', 'etrade-elements' ),
            'type' => Controls_Manager::COLOR,  
            'default' => '',
           
            'selectors' => array(
                  
                '{{WRAPPER}} .poster-countdown-content a.axil-btn:before' => 'background: {{VALUE}}',
                
            ),
        ]
    );
     $this->add_group_control(
        Group_Control_Typography::get_type(),
        [
            'name' => 'button_font_size',
            'label' => esc_html__( 'Typography', 'etrade-elements' ),                
            
            'selector' => '{{WRAPPER}} .poster-countdown-content a.axil-btn',
        ]
    );
   
    $this->add_responsive_control(
        'button_margin',
        [
            'label' => esc_html__( 'Margin', 'etrade-elements' ),
            'type' => Controls_Manager::DIMENSIONS,
            'size_units' => [ 'px', '%', 'em' ],
            
            'selectors' => [
                '{{WRAPPER}} .poster-countdown-content a.axil-btn' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                
            ],
        ]
    );
    
$this->end_controls_section(); 
    }
 
    protected function render() {
        $settings = $this->get_settings(); 
        
        $attr = ''; 
        $btn = ''; 
        $btn_icon = '';
        if( $settings['display_icon'] ){
            $btn_icon = '<i class="fal fa-shopping-cart"></i>';
        }

 
        if ('1' == $settings['link_type']) {
        if ( !empty( $settings['link']['url'] ) ) {
        $attr  = 'href="' . $settings['link']['url'] . '"';
        $attr .= !empty( $settings['link']['is_external'] ) ? ' target="_blank"' : '';
        $attr .= !empty( $settings['link']['nofollow'] ) ? ' rel="nofollow"' : '';
         
        }

        if ( !empty( $settings['link_text'] ) ) {

            if ( $settings['layout'] == "1" ) {
                 $btn = '<a class="axil-btn btn-bg-primary text-center" ' . $attr . '>'. $btn_icon . $settings['link_text'] . '</a>';
            }elseif( $settings['layout'] == "2" ){ 
                  $btn = '<a class=" axil-btn btn-bg-lighter btn-load-more text-center" ' . $attr . '>' . $btn_icon . $settings['link_text'] . '</a>';
            }else{ 
                 
                 $btn = '<a class="axil-btn btn-outline btn-border-light text-center" ' . $attr . '>' . $btn_icon . $settings['link_text'] . '</a>';
            }
            
        }
        }else {
            $attr  = 'href="' . get_permalink($settings['page_link']) . '"';
            $attr .= ' target="_self"';
            $attr .= ' rel="nofollow"';    

            if ( $settings['layout'] == "1" ) {
                $btn = '<a class="axil-btn btn-bg-primary text-center" ' . $attr . '>' . $btn_icon . $settings['link_text'] . '</a>';
            }elseif( $settings['layout'] == "2" ){ 
                $btn = '<a class="axil-btn btn-bg-lighter btn-load-more text-center" ' . $attr . '>' . $btn_icon . $settings['link_text'] . '</a>';
            }else{ 
                $btn = '<a class="axil-btn btn-outline text-center" ' . $attr . '>' . $btn_icon . $settings['link_text'] . '</a>';
            }       
                
        } 

        $allowed_tags = wp_kses_allowed_html( 'post' );
        ?>
         
        <?php  if ( !empty( $settings['link_text'] ) ) { ?> 
            <div class="btnwrp"> 
                <?php echo wp_kses( $btn, $allowed_tags ); ?> 
            </div>
        <?php } ?>  

    <?php }

    }
