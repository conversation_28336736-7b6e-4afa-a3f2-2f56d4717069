<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
 
 
use Elementor\Group_Control_Image_Size;
use Elementor\Utils;
use Elementor\Group_Control_Typography;

$attr = ''; 
$btn = ''; 
if ('1' == $settings['link_type']) {
        if ( !empty( $settings['link']['url'] ) ) {
            $attr  = 'href="' . $settings['link']['url'] . '"';
            $attr .= !empty( $settings['link']['is_external'] ) ? ' target="_blank"' : '';
            $attr .= !empty( $settings['link']['nofollow'] ) ? ' rel="nofollow"' : '';
            $title = '<a ' . $attr . '>' . $settings['title'] . '</a>';
        } 
    }else {
    $attr  = 'href="' . get_permalink($settings['page_link']) . '"';
    $attr .= ' target="_self"';
    $attr .= ' rel="nofollow"';     
    $title = '<a ' . $attr . '>' . $settings['title'] . '</a>';                   
     
}
$allowed_tags = wp_kses_allowed_html( 'post' );
?>
<div class="product-collection-three box-height-two">
    <div class="collection-content">
      <?php  if ( !empty( $settings['title'] ) ) { ?>    
            <h6 class="title"><?php echo wp_kses( $title , $allowed_tags ); ?></h6> 
        <?php } ?>  
         <div class="price-warp">
            <?php  if ( !empty( $settings['subtitle'] ) ) { ?>    
                <span class="price-text"><?php echo wp_kses( $settings['subtitle'] , $allowed_tags ); ?></span>
            <?php } ?>  
            <?php  if ( !empty( $settings['current-price'] ) ) { ?>
                <span class="price"><?php echo wp_kses( $settings['current-price'] , $allowed_tags ); ?></span>
            <?php } ?>  
        </div>   
    </div>
    <div class="collection-thumbnail">
       <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>
    </div>
</div>
 
