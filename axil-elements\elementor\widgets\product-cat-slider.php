<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly
class axil_product_cat_slider extends Widget_Base {
   
    public function get_name() {
        return 'axil-product-catslider';
    }
    
    public function get_title() {
        return esc_html__( 'Product Category Slider', 'etrade-elements' );
    }

    public function get_icon() {
        return 'eicon-slider-push';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
    public function axil_get_img($img)
     {
         $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
         return $img;
    }
  
    private function wooc_cat_dropdown() {
            $terms = get_terms( array( 'taxonomy' => 'product_cat','parent' => 0,  'orderby' => 'count', 'hide_empty' => 0 ) );
            $category_dropdown = array( '0' => esc_html__( 'All Categories', 'etrade-elements' ) );
            foreach ( $terms as $category ) {  
                $category_dropdown[$category->term_id] = $category->name; 
            }
            return $category_dropdown;
      }

    private function wooc_sub_cat_dropdown() {
        $terms = get_terms( array( 'taxonomy' => 'product_cat', 'orderby' => 'count', 'hide_empty' => 0 ) );
        $category_dropdown = array( '0' => esc_html__( 'All Categories', 'etrade-elements' ) );

        foreach ( $terms as $category ) {
          $category_dropdown[$category->term_id] = $category->name;
        }

        return $category_dropdown;
    }



    protected function register_controls() {


    $this->start_controls_section(
            'product_cat_layout',
            [
                'label' => esc_html__( 'Layouts', 'etrade-elements' ),
            ]
        );    
        
        $this->add_control(
            'style',
            [
                'label' => esc_html__( 'Layout', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT,
                'default' => '1',
                'options' => [
                    '1' => esc_html__(  'Style 1', 'etrade-elements' ),
                    '2' => esc_html__( 'Style 2', 'etrade-elements' ),
                    '3' => esc_html__( 'Style 3', 'etrade-elements' ),
                    '4' => esc_html__( 'Style 4', 'etrade-elements' ),                                 
                ],
            ] 
        ); 
    $this->end_controls_section();

    $this->start_controls_section(
            'section_title_layout',
            [
                'label' => esc_html__( 'Sectiton Title', 'etrade-elements' ),
            ]
        );   
      $this->add_control(
            'section_title_display',
            [
            'type' => Controls_Manager::SWITCHER,
            'label'       => esc_html__( 'Section Title Display', 'etrade-elements' ),
            'label_on'    => esc_html__( 'On', 'etrade-elements' ),
            'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
            'default'     => 'yes',
              'separator'     => 'before',
              
            ] 
        );    

      $this->add_control(
            'sub_title',
            [
            'type'    => Controls_Manager::TEXT,
            'label'       => esc_html__( 'Before Title', 'etrade-elements' ),
            'default'     => 'Categories',
            'label_block'   => true,     
            'condition'   => array( 'section_title_display' => 'yes', 'style' => ['1', '2', '3'], ),     
            ]
        );

    $this->add_control(
        'icon',
        [
            'label' => __( 'Icons', 'etrade-elements' ),
            'type' => Controls_Manager::ICONS,
           'condition'   => array( 'section_title_display' => 'yes', 'style' => ['1', '2', '3'], ),    
            'default' => [
                'value' => 'fas fa-tags',
                'library' => 'solid',
            ],
                  
        ]
    );
    $this->add_control(
        'beforetitlestyle',
        [
            'label' => esc_html__( 'Before Color', 'etrade-elements' ),
            'type' => Controls_Manager::SELECT,
            'default' => 'primary',
            'condition'   => array( 'section_title_display' => 'yes', 'style' => ['1', '2', '3'], ),      
            'options' => [
                'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                'secondary'   => esc_html__( 'Secondary', 'etrade-elements' ),                                          
                'primary2'   => esc_html__( 'Primary 2', 'etrade-elements' ),                                          
                                                          
            ],
        ] 
    );   
    $this->add_control(
        'title',
        [
        'type'    => Controls_Manager::TEXTAREA,
        'label'       => esc_html__( 'Title', 'etrade-elements' ),
        'default'     => esc_html__( 'Browse by Category', 'etrade-elements' ),
        'condition'   => array( 'section_title_display' => 'yes' ),     
        ]
    );

       $this->add_control(
         'image',
            [
                'label' => esc_html__('Product Image','etrade-elements'),
                'type'=>Controls_Manager::MEDIA,
                'condition'   => array( 'section_title_display' => 'yes', 'style' => '4', ),    
                'default' => [
                    'url' =>  $this->axil_get_img( 'featured-icon.svg' ),
                ],
                'dynamic' => [
                    'active' => true,
                ],
                    
            ]
        );       

        $this->add_group_control(
                Group_Control_Image_Size::get_type(),
                [
                    'name' => 'image_size',
                    'default' => 'full',
                    'separator' => 'none',
                     'condition'   => array( 'section_title_display' => 'yes', 'style' => '4', ),   
                       
                ]
            );

    $this->add_control(
        'sec_title_tag',
        [
            'label' => esc_html__('Title HTML Tag','etrade-elements'),
            'type' => Controls_Manager::CHOOSE,
            'options' => [
                'h1' => [
                    'title' => esc_html__('H1','etrade-elements'),
                    'icon' => 'eicon-editor-h1'
                ],
                'h2' => [
                    'title' => esc_html__('H2','etrade-elements'),
                    'icon' => 'eicon-editor-h2'
                ],
                'h3' => [
                    'title' => esc_html__('H3','etrade-elements'),
                    'icon' => 'eicon-editor-h3'
                ],
                'h4' => [
                    'title' => esc_html__('H4','etrade-elements'),
                    'icon' => 'eicon-editor-h4'
                ],
                'h5' => [
                    'title' => esc_html__('H5','etrade-elements'),
                    'icon' => 'eicon-editor-h5'
                ],
                'h6' => [
                    'title' => esc_html__('H6','etrade-elements'),
                    'icon' => 'eicon-editor-h6'
                ]
            ],
            'default' => 'h2',
            'toggle' => false,
            'label_block' => true,

        ]
    ); 
    $this->end_controls_section();

    $this->start_controls_section(
            'cat_title_layout',
            [
                'label' => esc_html__( 'General options', 'etrade-elements' ),
            ]
        ); 

        $this->add_control(
          'nav_style',
          [
              'label' => esc_html__( 'Nav Style', 'etrade-elements' ),
              'type' => Controls_Manager::SELECT,
              'default' => 'arrow-top-slide',   
               'condition'   => array(  'style' => ['1', '2', '3'], ),
               'separator'     => 'before',          
              'options' => [
                  'arrow-top-slide'       => esc_html__( 'Top', 'etrade-elements' ),
                  'arrow-both-side-2'    => esc_html__( 'Middle', 'etrade-elements' ),                  
                 
              ],
          ] 
      );

      $this->add_control(
            'show_only_sub_category',
            [
                'type' => Controls_Manager::SWITCHER,
                'label'       => esc_html__( 'Show Sub Categorys Also' , 'etrade-elements' ),
                'label_on'    => esc_html__( 'On', 'etrade-elements' ),
                'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
                'default'     => 'no',
                 'separator'     => 'before',
            
            ] 
        );   
 
        $this->add_control(      
            'select_categories',
                [
                'label'         => esc_html__( 'Categories', 'etrade-elements' ),
                'type'          => Controls_Manager::SELECT2,
                'options'       => $this->wooc_sub_cat_dropdown(),            
                'label_block'   => true,                
                'default'       => '0',
               
                'multiple'     => true,
                'condition'   => array( 'show_only_sub_category' => 'yes' ),     
                ] 
            );

        $this->add_control(      
            'sub_select_categories',
                [
                'label'         => esc_html__( 'Select Parent to Child Categories', 'etrade-elements' ),
                'type'          => Controls_Manager::SELECT,
                'options'       => $this->wooc_cat_dropdown(),            
                'label_block'   => true,                
                'default'       => '0',
               
                'multiple'     => true,
                'condition'   => array( 'show_only_sub_category!' => 'yes' ),     
                ] 
            );


        $this->add_control(
            'cat_image_show',
            [
                 'type' => Controls_Manager::SWITCHER,
                'label'       => esc_html__( 'Image Show', 'etrade-elements' ),
                'label_on'    => esc_html__( 'On', 'etrade-elements' ),
                'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
                 'separator'     => 'before',
            
            ] 
        );   
         $this->add_control(      
            'cat_ordering',
                [
                    'label' => __( 'Ordering', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT2,
                    'options' => array(
                       'DESC'  => esc_html__( 'Desecending', 'etrade-elements' ),
                       'ASC'   => esc_html__( 'Ascending', 'etrade-elements' ),
                    ),
                 'default' => 'ASC',
                ] 
            );
        $this->add_control(
            'hide_empty_category',
            [
                 'type' => Controls_Manager::SWITCHER,
                'label'       => esc_html__( 'Hide Empty Category', 'etrade-elements' ),
                'label_on'    => esc_html__( 'On', 'etrade-elements' ),
                'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
            
            ] 
        );   
        $this->add_control(
            'sliderinfinite',
            [
                 'type' => Controls_Manager::SWITCHER,
                'label'       => esc_html__( 'Is infinite', 'etrade-elements' ),
                'label_on'    => esc_html__( 'On', 'etrade-elements' ),
                'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
            
            ] 
        );    
        $this->add_control(
            'sliderautoplay',
            [
                 'type' => Controls_Manager::SWITCHER,
                'label'       => esc_html__( 'Is Autoplay', 'etrade-elements' ),
                'label_on'    => esc_html__( 'On', 'etrade-elements' ),
                'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
            
            ] 
        );   
        $this->add_control(
        'number',
            [
                'label'   => esc_html__( 'Number of items', 'etrade-elements' ),
                'type'    => Controls_Manager::NUMBER,
                'description' => '"0" to display all items, default items "12"',
                'default' => 12,                   
                               
                ]
            ); 
        $this->add_control(
        'slidesToShow',
            [
                'label'   => esc_html__( 'Slides To Show', 'etrade-elements' ),
                'type'    => Controls_Manager::NUMBER, 
                'default' => 6,     
                'condition'   => array( 'style' => ['1', '2', '3'] ),                   
                               
            ]
        );
     
        $this->add_control(
        'slidesToScroll',
            [
                'label'   => esc_html__( 'Slides To Scroll', 'etrade-elements' ),
                'type'    => Controls_Manager::NUMBER, 
                'default' => 6,     
                'condition'   => array( 'style' => ['1', '2', '3'] ),                   
                               
            ]
        );
     
        $this->add_control(
        'itemsPerSlide',
            [
                'label'   => esc_html__( 'Items Per Slide', 'etrade-elements' ),
                'type'    => Controls_Manager::NUMBER, 
                'default' => 6,     
                'condition'   => array( 'style' => '4' ),                   
                               
            ]
        );

       
         $this->end_controls_section();

   


        $this->start_controls_section(
           'sub_title_style_section',
           [
               'label' => __( 'Section Title before', 'etrade-elements' ),
               'tab' => Controls_Manager::TAB_STYLE,                
           ]
        );
   
   
        $this->add_control(
           'sub_title_color',
           [
               'label' => __( 'Color', 'etrade-elements' ),
               'type' => Controls_Manager::COLOR,  
               'default' => '',
               
               'selectors' => array(
                   '{{WRAPPER}} .title-highlighter' => 'color: {{VALUE}}',
                   '{{WRAPPER}} .title-highlighter i' => 'background-color: {{VALUE}}',
               ),
           ]
        );

        $this->add_group_control(
           Group_Control_Typography::get_type(),
           [
               'name' => 'sub_title_font_size',
               'label' => __( 'Typography', 'etrade-elements' ),                
                
               'selector' => '{{WRAPPER}} .title-highlighter',
           ]
       );
      
       $this->add_responsive_control(
           'sub_title_margin',
           [
               'label' => __( 'Margin', 'etrade-elements' ),
               'type' => Controls_Manager::DIMENSIONS,
               'size_units' => [ 'px', '%', 'em' ],
                
               'selectors' => [
                   '{{WRAPPER}} .title-highlighter' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                   
               ],
           ]
       );
      
   $this->end_controls_section();

       $this->start_controls_section(
           'section_title_style',
           [
               'label' => __( 'Section Title', 'etrade-elements' ),
               'tab' => Controls_Manager::TAB_STYLE,                
           ]
       );
   

   
         $this->add_control(
           'stitle_color',
           [
               'label' => __( 'Color', 'etrade-elements' ),
               'type' => Controls_Manager::COLOR,  
               'default' => '',
              
               'selectors' => array(
                   '{{WRAPPER}} .sec-title' => 'color: {{VALUE}}',
               ),
           ]
       );

        $this->add_group_control(
           Group_Control_Typography::get_type(),
           [
               'name' => 'stitle_font_size',
               'label' => __( 'Typography', 'etrade-elements' ),                
               
               'selector' => '{{WRAPPER}} .sec-title',
           ]
       );
      
       $this->add_responsive_control(
           'stitle_margin',
           [
               'label' => __( 'Margin', 'etrade-elements' ),
               'type' => Controls_Manager::DIMENSIONS,
               'size_units' => [ 'px', '%', 'em' ],
               
               'selectors' => [
                   '{{WRAPPER}} .sec-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                   
               ],
           ]
       );
      
   $this->end_controls_section();

     $this->start_controls_section(
            'ctitle_style_section',
            [
                'label' => esc_html__( 'Category Name', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
 
          $this->add_control(
            'ctitle_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .cat-title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'ctitle_font_size',
                'label' => esc_html__( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .cat-title',
            ]
        );
       
        $this->add_responsive_control(
            'ctitle_margin',
            [
                'label' => esc_html__( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .cat-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
        
    $this->end_controls_section();

      $this->start_controls_section(
            'etrade_responsive',
                [
                'label' => __( 'Responsive Columns', 'etrade-elements' ),
                'condition'   => ['style' => '4' ],
                ]
            );

            $this->add_control(
                'col_xl',
                [
                    'label' => __( 'Desktops: > 1199px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '5',
                ] 
            );
            $this->add_control(
            'col_lg',
                [
                    'label' => __( 'Desktops: > 991px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '4',
                ] 
            );
            $this->add_control(
            'col_md',
                [
                    'label' => __( 'Tablets: > 767px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                             '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '3',
                ] 
            );

            $this->add_control(
            'col_sm',
                [
                    'label' => __( 'Phones: >575px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '2',
                ] 
            );         
            $this->add_control(
            'col_mobile',
                [
                    'label' => __( 'Small Phones: <576px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '1'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '2'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '4 Col', 'etrade-elements' ),                        
                        '5'  => esc_html__( '5 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '2',
                ] 
            );
       $this->end_controls_section();
	}   
    private function slick_load_scripts(){
        wp_enqueue_style(  'slick' );
        wp_enqueue_style(  'slick-theme' );
        wp_enqueue_script( 'slick' );
    }
 
    protected function render() {
		$settings = $this->get_settings();  				
        $this->slick_load_scripts();   
        $template   = 'product-cat-slider-' . str_replace("style", "", $settings['style']);  
        return wooc_Elements_Helper::wooc_element_template( $template, $settings ); 
	    }
	}
