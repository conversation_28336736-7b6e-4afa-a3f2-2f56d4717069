<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package etrade
 */
?>
<!doctype html>
<html <?php language_attributes();?>>
<head>
    <meta charset="<?php bloginfo( 'charset' );?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head();?>
</head>
<body <?php body_class();?>>
<?php
$axil_options = Helper::axil_get_options();
if ( function_exists( 'wp_body_open' ) ) {
    wp_body_open();
}
?>
<div class="axil-signin-area">
    <div class="signin-header">
        <div class="row align-items-center">
           <div class="col-xl-4 col-sm-6">
               <?php if ( isset( $axil_options['axil_logo_type'] ) ): ?>
                <a href="<?php echo esc_url( home_url( '/' ) ); ?>"
                   title="<?php echo esc_attr( get_bloginfo( 'name' ) ); ?>" rel="<?php echo esc_attr__( 'home', 'etrade' ); ?>">
                    <?php if ( 'image' == $axil_options['axil_logo_type'] ): ?>
                        <?php if ( $axil_options['axil_head_logo'] ) {?>
                            <img class="dark-logo" src="<?php echo esc_url( $axil_options['axil_head_logo']['url'] ); ?>" alt="<?php echo esc_attr( get_bloginfo( 'name' ) ); ?>">
                        <?php }?>
                    <?php else: ?>
                        <?php if ( 'text' == $axil_options['axil_logo_type'] ): ?>
                            <?php echo esc_html( $axil_options['axil_logo_text'] ); ?>
                        <?php endif?>
                    <?php endif?>
                </a>
            <?php else: ?>
                <h3>
                    <a href="<?php echo esc_url( home_url( '/' ) ); ?>"
                       title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>" rel="<?php echo esc_attr__( 'home', 'etrade' ); ?>">
                        <?php if ( isset( $axil_options['axil_logo_text'] ) ? $axil_options['axil_logo_text'] : '' ) {
                            echo esc_html( $axil_options['axil_logo_text'] );
                        } else {
                            bloginfo( 'name' );
                        }
                        ?>
                    </a>
                </h3>
                <?php $description = get_bloginfo( 'description', 'display' );
                    if ( $description || is_customize_preview() ) {?>
                    <p class="site-description"><?php echo esc_html( $description ); ?> </p>
                <?php }?>
            <?php endif?>
            </div>

            <div class="col-md-2 d-lg-block d-none">
                <?php if ( in_array( "", array( 'lostpassword', 'register' ) ) ) {?>
                    <button id='axil-back-button' class='back-btn' onclick='javascript:history.back()'><i class="far fa-angle-left"></i></button>
                <?php }?>
            </div>
             <div class="col-xl-6 col-lg-4 col-sm-6">
                <?php do_action( 'etrade_topbar_menu' );?>
            </div>
        </div>
    </div>