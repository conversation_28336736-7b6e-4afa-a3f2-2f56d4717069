{"version": 3, "sources": ["defaults/_variables.scss", "defaults/_spacing.scss", "defaults/_mixins.scss", "defaults/_base.scss", "defaults/_typography.scss", "_style.scss"], "names": [], "mappings": "AAEA,MAGI,oCAAe,CAGf,wBAAgB,CAChB,sBAAc,CACd,uBAAe,CAEf,oBAAY,CACZ,iBAAY,CACZ,mBAAW,CACX,qBAAa,CAEb,mBAAW,CAEX,wBAAgB,CAChB,sBAAc,CACd,qBAAa,CACb,sBAAc,CAGd,aAAS,CACT,iBAAa,CACb,mBAAe,CAGf,cAAU,CACV,gBAAY,CACZ,eAAW,CACX,kBAAc,CACd,aAAS,CACT,mBAAe,CACf,cAAU,CACb,KC7BW,WAAQ,CACX,KAGG,YAAS,CACZ,OAGG,eAAwB,CAC3B,OAGG,gBAA0B,CCTrC,0BDaW,QACI,sBAAoC,CACvC,QAGG,uBAAqC,CACxC,UAIG,0BAAoD,CACvD,UAGG,2BAAsD,CACzD,CClCZ,0BDsCW,QACI,sBAAqC,CACxC,QAGG,uBAAsC,CACzC,UAIG,0BAAqD,CACxD,UAGG,2BAAuD,CAC1D,CAnDL,KACI,WAAQ,CACX,KAGG,YAAS,CACZ,OAGG,iBAAwB,CAC3B,OAGG,kBAA0B,CCTrC,0BDaW,QACI,sBAAoC,CACvC,QAGG,uBAAqC,CACxC,UAIG,4BAAoD,CACvD,UAGG,6BAAsD,CACzD,CClCZ,0BDsCW,QACI,sBAAqC,CACxC,QAGG,uBAAsC,CACzC,UAIG,4BAAqD,CACxD,UAGG,6BAAuD,CAC1D,CAnDL,KACI,WAAQ,CACX,KAGG,YAAS,CACZ,OAGG,kBAAwB,CAC3B,OAGG,mBAA0B,CCTrC,0BDaW,QACI,sBAAoC,CACvC,QAGG,uBAAqC,CACxC,UAIG,6BAAoD,CACvD,UAGG,8BAAsD,CACzD,CClCZ,0BDsCW,QACI,sBAAqC,CACxC,QAGG,uBAAsC,CACzC,UAIG,6BAAqD,CACxD,UAGG,8BAAuD,CAC1D,CAnDL,KACI,WAAQ,CACX,KAGG,YAAS,CACZ,OAGG,gBAAwB,CAC3B,OAGG,iBAA0B,CCTrC,0BDaW,QACI,sBAAoC,CACvC,QAGG,uBAAqC,CACxC,UAIG,2BAAoD,CACvD,UAGG,4BAAsD,CACzD,CClCZ,0BDsCW,QACI,sBAAqC,CACxC,QAGG,uBAAsC,CACzC,UAIG,2BAAqD,CACxD,UAGG,4BAAuD,CAC1D,CAnDL,KACI,YAAQ,CACX,KAGG,aAAS,CACZ,OAGG,gBAAwB,CAC3B,OAGG,iBAA0B,CCTrC,0BDaW,QACI,uBAAoC,CACvC,QAGG,wBAAqC,CACxC,UAIG,2BAAoD,CACvD,UAGG,4BAAsD,CACzD,CClCZ,0BDsCW,QACI,uBAAqC,CACxC,QAGG,wBAAsC,CACzC,UAIG,2BAAqD,CACxD,UAGG,4BAAuD,CAC1D,CAnDL,KACI,YAAQ,CACX,KAGG,aAAS,CACZ,OAGG,kBAAwB,CAC3B,OAGG,mBAA0B,CCTrC,0BDaW,QACI,uBAAoC,CACvC,QAGG,wBAAqC,CACxC,UAIG,6BAAoD,CACvD,UAGG,8BAAsD,CACzD,CClCZ,0BDsCW,QACI,uBAAqC,CACxC,QAGG,wBAAsC,CACzC,UAIG,6BAAqD,CACxD,UAGG,8BAAuD,CAC1D,CAnDL,KACI,YAAQ,CACX,KAGG,aAAS,CACZ,OAGG,mBAAwB,CAC3B,OAGG,oBAA0B,CCTrC,0BDaW,QACI,uBAAoC,CACvC,QAGG,wBAAqC,CACxC,UAIG,8BAAoD,CACvD,UAGG,+BAAsD,CACzD,CClCZ,0BDsCW,QACI,uBAAqC,CACxC,QAGG,wBAAsC,CACzC,UAIG,8BAAqD,CACxD,UAGG,+BAAuD,CAC1D,CAnDL,KACI,YAAQ,CACX,KAGG,aAAS,CACZ,OAGG,iBAAwB,CAC3B,OAGG,kBAA0B,CCTrC,0BDaW,QACI,uBAAoC,CACvC,QAGG,wBAAqC,CACxC,UAIG,4BAAoD,CACvD,UAGG,6BAAsD,CACzD,CClCZ,0BDsCW,QACI,uBAAqC,CACxC,QAGG,wBAAsC,CACzC,UAIG,4BAAqD,CACxD,UAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,eAAwB,CAC3B,QAGG,gBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,0BAAoD,CACvD,WAGG,2BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,0BAAqD,CACxD,WAGG,2BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,MACI,WAAQ,CACX,MAGG,YAAS,CACZ,QAGG,gBAAwB,CAC3B,QAGG,iBAA0B,CCTrC,0BDaW,SACI,sBAAoC,CACvC,SAGG,uBAAqC,CACxC,WAIG,2BAAoD,CACvD,WAGG,4BAAsD,CACzD,CClCZ,0BDsCW,SACI,sBAAqC,CACxC,SAGG,uBAAsC,CACzC,WAIG,2BAAqD,CACxD,WAGG,4BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,iBAAwB,CAC3B,QAGG,kBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,4BAAoD,CACvD,WAGG,6BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,4BAAqD,CACxD,WAGG,6BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,mBAAwB,CAC3B,QAGG,oBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,8BAAoD,CACvD,WAGG,+BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,8BAAqD,CACxD,WAGG,+BAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,oBAAwB,CAC3B,QAGG,qBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,+BAAoD,CACvD,WAGG,gCAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,+BAAqD,CACxD,WAGG,gCAAuD,CAC1D,CAnDL,MACI,aAAQ,CACX,MAGG,cAAS,CACZ,QAGG,kBAAwB,CAC3B,QAGG,mBAA0B,CCTrC,0BDaW,SACI,wBAAoC,CACvC,SAGG,yBAAqC,CACxC,WAIG,6BAAoD,CACvD,WAGG,8BAAsD,CACzD,CClCZ,0BDsCW,SACI,wBAAqC,CACxC,SAGG,yBAAsC,CACzC,WAIG,6BAAqD,CACxD,WAGG,8BAAuD,CAC1D,CAnDL,OACI,YAAQ,CACX,OAGG,aAAS,CACZ,SAGG,gBAAwB,CAC3B,SAGG,iBAA0B,CCTrC,0BDaW,UACI,uBAAoC,CACvC,UAGG,wBAAqC,CACxC,YAIG,2BAAoD,CACvD,YAGG,4BAAsD,CACzD,CClCZ,0BDsCW,UACI,uBAAqC,CACxC,UAGG,wBAAsC,CACzC,YAIG,2BAAqD,CACxD,YAGG,4BAAuD,CAC1D,CAnDL,OACI,YAAQ,CACX,OAGG,aAAS,CACZ,SAGG,kBAAwB,CAC3B,SAGG,mBAA0B,CCTrC,0BDaW,UACI,uBAAoC,CACvC,UAGG,wBAAqC,CACxC,YAIG,6BAAoD,CACvD,YAGG,8BAAsD,CACzD,CClCZ,0BDsCW,UACI,uBAAqC,CACxC,UAGG,wBAAsC,CACzC,YAIG,6BAAqD,CACxD,YAGG,8BAAuD,CAC1D,CAnDL,OACI,YAAQ,CACX,OAGG,aAAS,CACZ,SAGG,mBAAwB,CAC3B,SAGG,oBAA0B,CCTrC,0BDaW,UACI,uBAAoC,CACvC,UAGG,wBAAqC,CACxC,YAIG,8BAAoD,CACvD,YAGG,+BAAsD,CACzD,CClCZ,0BDsCW,UACI,uBAAqC,CACxC,UAGG,wBAAsC,CACzC,YAIG,8BAAqD,CACxD,YAGG,+BAAuD,CAC1D,CAnDL,OACI,YAAQ,CACX,OAGG,aAAS,CACZ,SAGG,iBAAwB,CAC3B,SAGG,kBAA0B,CCTrC,0BDaW,UACI,uBAAoC,CACvC,UAGG,wBAAqC,CACxC,YAIG,4BAAoD,CACvD,YAGG,6BAAsD,CACzD,CClCZ,0BDsCW,UACI,uBAAqC,CACxC,UAGG,wBAAsC,CACzC,YAIG,4BAAqD,CACxD,YAGG,6BAAuD,CAC1D,CExDb,KACC,cAAe,CACf,QAAS,CACT,SAAU,CACV,iBAAkB,CDWlB,2BCfD,KAOE,aAAc,CAMf,CDJA,0BCTD,KAWE,aAAc,CAEf,CAED,KACC,iBAAkB,CAClB,+BAAgC,CAChC,cAAe,CACf,eAAgB,CAChB,QAAS,CACT,SAAU,CACV,sBAAuB,CAOvB,YAGA,0BAA6B,CAC7B,UAAW,CACX,gBAAiB,CACjB,GAGA,aAAc,CACd,UAAW,CACX,QAAS,CAET,QAAS,CACT,SAAU,CACV,kCAQA,qBAAsB,CACtB,SAGA,QAAS,CACT,QAAS,CACT,SAAU,CACV,SAGA,eAAgB,CAChB,SAIA,YAAa,CACb,0BAAY,CAAZ,kBAAmB,CACnB,EAGA,yBAA0B,CAC1B,QAGA,yBAA0B,CAC1B,yBAA0B,CAC1B,EAGA,6BAAY,CAAZ,qBAAsB,CACtB,WAGA,YAAa,CACb,aAAc,CACd,iBAAkB,CAClB,kBAAmB,CDzEnB,2BCqED,WAOE,WAAY,CAUb,CD5FA,0BC2ED,WAWE,WAAY,CAMb,CDlGA,0BCiFD,WAeE,UAAW,CAEZ,CAED,iBACC,cAAe,CDrGf,0BCoGD,iBAGE,cAAe,CAEhB,CAED,OACC,QAAS,CACT,SAAU,CDvGV,0BCqGD,WAMG,UAAW,CACX,WAAY,CACZ,CD7GF,0BCqHD,aAGE,iBAAkB,CAClB,oBAAqB,CAEtB,CAED,mBACC,mBAAY,CAAZ,gBAAiB,CACjB,qBAGA,qBAAY,CAAZ,kBAAmB,CACnB,gBAGA,QAAS,CACT,kBAAmB,CACnB,UAAW,CACX,WAAY,CACZ,eAAgB,CAChB,SAAU,CACV,iBAAkB,CAClB,SAAU,CACV,kBAAmB,CACnB,iEAIA,SAAU,CACV,WAAY,CACZ,QAAS,CACT,gBAAiB,CACjB,eAAgB,CAChB,UAAW,CACX,mBAAoB,CACpB,WAGA,iBAAkB,CAClB,eAGA,2BAA4B,CAC5B,aAGA,eAAgB,CAChB,eAGA,uCAAwC,CACxC,cAIA,+BAAgC,CAChC,eAIA,0BAA2B,CAC3B,WAGA,sBAAuB,CACvB,WAGA,sBAAuB,CACvB,UAGA,qBAAsB,CACtB,YAGA,uBAAwB,CACxB,aAGA,wBAAyB,CACzB,eAGA,0BAA2B,CAC3B,aAGA,wBAAyB,CACzB,YAGA,uBAAwB,CACxB,aAGA,wBAAyB,CACzB,kBAGA,uBAAwB,CACxB,aAGA,UAAW,CACX,eAGA,0BAA2B,CAC3B,kBAKA,qCAAsC,CACtC,cAGA,iCAAkC,CAClC,cAGA,iCAAkC,CAClC,aAGA,gCAAiC,CACjC,eAGA,kCAAmC,CACnC,UAGA,gCAAiC,CACjC,aAGA,mCAAoC,CACpC,kBAGA,qCAAsC,CACtC,gBAGA,mCAAoC,CACpC,eAGA,kCAAmC,CACnC,gBAGA,mCAAoC,CACpC,UAGA,qBAAsB,CACtB,QAKA,2BAA4B,CAC5B,YAGA,+BAAgC,CAChC,SAGA,mFAAY,CAAZ,uDAA2D,CAC3D,WAKA,kDAAwB,CAAxB,0CAA8C,CAC9C,WAGA,iDAAwB,CAAxB,yCAA6C,CAC7C,kBAGA,cAAe,CACf,8BAA+B,CAC/B,YAGA,aAAc,CACd,aAgBA,mBAGC,iCAAkC,CAGlC,kCAAmC,CACnC,0BAA2B,CAC3B,2BAA4B,CAC5B,YAIA,yBAA0B,CAC1B,cAGA,2BAA2B,CAC3B,kBAGA,4BAA4B,CAC5B,gDAKA,UAAW,CACX,IAGA,+BAAgC,CAChC,eAKA,uBAAwB,CACxB,MAIA,0BAA2B,CAC3B,OAIA,uBAAwB,CACxB,QAKA,SAAU,CACV,QAAS,CACT,MAIA,sBAAuB,CACvB,CC7YF,kBAMC,iCAAkC,CAClC,sBAAuB,CACvB,yBAA0B,CAC1B,eAAgB,CAChB,GAGA,gBAAiB,CFHjB,0BEED,GAIE,gBAAiB,CAElB,CAED,GACC,gBAAiB,CFXjB,0BEUD,GAIE,cAAe,CAEhB,CAED,GACC,gBAAiB,CACjB,GAGA,cAAe,CACf,yBAA0B,CAF3B,OAKE,gBAAiB,CACjB,GAID,gBAAiB,CACjB,yBAA0B,CAC1B,GAGA,gBAAiB,CACjB,8BAA+B,CAC/B,EAGA,sBAAuB,CF1CvB,0BEyCD,EAIE,cAAe,CAEhB,CAED,SACC,0BAA2B,CAC3B,WAGA,4BAA6B,CAC7B,aAGA,8BAA+B,CAC/B,QAGA,yBAA0B,CAC1B,WAGA,+BAAgC,CAChC,aC3EG,qBAAsB,CACtB,WAAY,CACZ,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,UAAW,CACX,4CAAsB,CAAtB,oCAAoC,CHHvC,0BGHD,aASQ,WAAY,CAEnB,CAED,UACI,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,wBAAiB,CAAjB,qBAAiB,CAAjB,6BAA8B,CHbjC,0BGUD,UAMQ,aAAc,CANtB,qBASY,aAAc,CACd,kBAAmB,CACnB,iBAAkB,CACrB,CAIT,UACI,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CAHvB,YAMQ,cAAe,CACf,2BAA4B,CAC5B,sBAAuB,CACvB,wBAAyB,CACzB,oBAAqB,CACrB,gBAAiB,CAXzB,2BAcY,aAAc,CAd1B,kBAkBY,0BAA2B,CAlBvC,yBAuBQ,cAAe,CACf,aAAc,CACd,YAAa,CHvCpB,2BGcD,yBA4BY,aAAc,CAErB,CHxDJ,0BG0BD,UAiCQ,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CAlC3B,YAqCY,cAAe,CACf,gBAAiB,CACpB,CAKT,cACI,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAW,CAAX,cAAe,CAClB,oBAGG,cAAe,CACf,SAAU,CACV,WAAY,CACZ,WAAY,CACZ,YAAa,CACb,iBAAkB,CAClB,0BAAY,CAAZ,kBAAmB,CAPvB,iCAUQ,QAAS,CHzEhB,2BG+DD,iCAaY,KAAM,CAEb,CH9EJ,2BG+DD,oBAkBQ,cAAe,CACf,KAAM,CACN,QAAS,CACT,MAAO,CACP,WAAY,CACZ,oCAA6B,CAA7B,4BAA6B,CAC7B,0BAAmB,CAAnB,kBAAmB,CACnB,YAAa,CACb,6CAA4B,CAA5B,qCAAgC,CAUvC,CApCD,yBA8BQ,+BAAW,CAAX,uBAAwB,CHzG/B,0BG2ED,oBAkCQ,WAAY,CAEnB,CAED,SACI,cAAe,CACf,eAAgB,CAFpB,YAKQ,oBAAqB,CAL7B,YASQ,iBAAkB,CAClB,eAAgB,CAVxB,YAeQ,iBAAkB,CAf1B,WAmBQ,aAAc,CACd,cAAe,CACf,gBAAiB,CACjB,sBAAuB,CACvB,oBAAqB,CACrB,SAAU,CAxBlB,mCA4BY,yBAA0B,CAC7B,MAOL,kBAAO,CAAP,UAAO,CAAP,MAAO,CACP,cAAe,CACf,iBAAkB,CAClB,qBAAsB,CH5IzB,2BGwID,MAOQ,eAAgB,CAChB,SAAU,CAEjB,CAED,sBAEI,aAAc,CACjB,eAIG,UAAW,CACX,iBAAkB,CAClB,eAAgB,CAChB,iCAAkC,CAClC,cAAe,CACf,gBAAiB,CACjB,sBAAuB,CAC1B,aAGG,iBAAkB,CADtB,oBAIQ,iBAAkB,CAClB,sBAAuB,CACvB,WAAY,CACZ,KAAM,CACN,OAAQ,CACR,cAAe,CACf,gBAAiB,CACjB,mBAAoB,CACpB,cAAe,CACf,0BAAY,CAAZ,kBAAmB,CAb3B,sBAgBY,2BAA4B,CAC5B,sBAAuB,CACvB,0BAAY,CAAZ,kBAAmB,CAlB/B,4BAuBgB,0BAA2B,CAC9B,SAOL,4BAA6B,CAC7B,mBAAoB,CACpB,kBAAmB,CACnB,eAAgB,CAChB,4BAAY,CAAZ,oBAAqB,CAN7B,SAUQ,eAAgB,CAVxB,QAcQ,kBAAmB,CAd3B,kBAmBQ,eAAgB,CAnBxB,UAuBQ,kBAAmB,CACnB,6CAAuB,CAAvB,qCAAwC,CAC3C,IAKD,cAAe,CACf,WAAY,CACf,aAIG,iCAAkC,CAClC,cAAe,CACf,iBAAkB,CAHtB,eAMQ,UAAW,CACX,QAAS,CAPjB,eAWQ,UAAW,CAXnB,qBAcY,0BAA2B,CAC9B,KAKL,2BAAgB,CAAhB,4BAAgB,CAAhB,yBAAgB,CAAhB,qBAAsB,CHxPzB,2BGuPD,KAIQ,aAAc,CAErB,CH7PA,2BG+PD,cAEQ,eAAgB,CAChB,mBAAoB,CAE3B,CAED,kBAEQ,yBAA0B,CAC7B,uBAID,qBAAsB,CACtB,oBAAqB,CAFzB,oCAMY,yBAA0B,CAC1B,mCAAoC,CACvC,aAKL,cAAe,CACf,YAAa,CACb,WAAY,CACZ,aAAc,CACd,UAAW,CACX,gBAAiB,CACjB,8BAA+B,CAC/B,UAAW,CACX,iBAAkB,CAClB,oBAAqB,CACrB,2BAA4B,CAC5B,SAAU,CACV,4BAAoB,CAApB,oBAAoB,CACpB,+CAA2C,CAA3C,uCAA2C,CAC3C,SAAU,CACV,0BAAY,CAAZ,kBAAmB,CAhBvB,kBAmBQ,WAAY,CACZ,UAAW,CACX,UAAW,CACX,0BAAW,CAAX,kBAAmB,CAtB3B,mBA0BQ,UAAW,CACX,aAAc,CACd,SAAU,CACb,gBAKG,yBAA0B,CAC7B,OAKG,aAAc,CACd,eAAgB", "file": "main.css", "sourcesContent": ["// Custom Variables\r\n\r\n:root {\r\n\r\n    //Fonts\r\n    --primary-font: '<PERSON><PERSON>', sans-serif;\r\n\r\n    //Colors\r\n    --primary-color: #0dc270;\r\n    --theme-color: #50D8D1;\r\n    --action-color: #ff2c54;\r\n\r\n    --typo-dark: #111111;\r\n    --typo-body: #555;\r\n    --typo-mid: #8A94A6;\r\n    --typo-light: #A6AEBC;\r\n\r\n    --bg-light: #F8F9FB;\r\n\r\n    --color-success: #00865A;\r\n    --color-alert: #F07300;\r\n    --color-info: #0D55CF;\r\n    --color-error: #DD2727;\r\n\r\n    //Border-radius\r\n    --radius: 4px;\r\n    --radius-big: 6px;\r\n    --radius-small: 2px;\r\n\r\n    //Font weight\r\n    --w-light: 300;\r\n    --w-regular: 400;\r\n    --w-medium: 500;\r\n    --w-semi-bold: 600;\r\n    --w-bold: 700;\r\n    --w-extra-bold: 800;\r\n    --w-black: 900;\r\n}\r\n\r\n//Media queries\r\n$tablet-mini: 768px;\r\n$tablet-pro: 992px;\r\n$laptop: 1200px;\r\n$desktop: 1600px;\r\n$large-desktop: 1920px;", "//Spacing\r\n\r\n$spaceamounts: (0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100); // Adjust this to include the pixel amounts you need.\r\n$sides: (top, right, bottom, left); // Leave this variable alone\r\n\r\n@each $space in $spaceamounts {\r\n    @each $side in $sides {\r\n        .m-#{$space} {\r\n            margin: #{$space/10}rem;\r\n        }\r\n\r\n        .p-#{$space} {\r\n            padding: #{$space/10}rem;\r\n        }\r\n\r\n        .m-#{str-slice($side, 0, 1)}-#{$space} {\r\n            margin-#{$side}: #{$space/10}rem;\r\n        }\r\n\r\n        .p-#{str-slice($side, 0, 1)}-#{$space} {\r\n            padding-#{$side}: #{$space/10}rem;\r\n        }\r\n\r\n        @include tablet-mini {\r\n            .lg-m-#{$space} {\r\n                margin: #{$space/10}rem!important;\r\n            }\r\n\r\n            .lg-p-#{$space} {\r\n                padding: #{$space/10}rem!important;\r\n            }\r\n\r\n\r\n            .lg-m-#{str-slice($side, 0, 1)}-#{$space} {\r\n                margin-#{$side}: #{$space/10}rem!important;\r\n            }\r\n\r\n            .lg-p-#{str-slice($side, 0, 1)}-#{$space} {\r\n                padding-#{$side}: #{$space/10}rem!important;\r\n            }\r\n        }\r\n\r\n        @include mobile {\r\n            .md-m-#{$space} {\r\n                margin: #{$space/10}rem !important;\r\n            }\r\n\r\n            .md-p-#{$space} {\r\n                padding: #{$space/10}rem !important;\r\n            }\r\n\r\n\r\n            .md-m-#{str-slice($side, 0, 1)}-#{$space} {\r\n                margin-#{$side}: #{$space/10}rem !important;\r\n            }\r\n\r\n            .md-p-#{str-slice($side, 0, 1)}-#{$space} {\r\n                padding-#{$side}: #{$space/10}rem !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// Mixins\r\n\r\n//Media queries\r\n\r\n@mixin mobile {\r\n\t@media (max-width: #{$tablet-mini - 1px}) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin tablet-mini {\r\n\t@media (max-width: #{$tablet-pro - 1px}) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin tablet-pro {\r\n\t@media (max-width : #{$laptop - 1px}) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin laptop {\r\n\t@media (max-width : #{$desktop - 1px}) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin desktop {\r\n\t@media (min-width: #{$desktop}) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin large-desktop {\r\n\t@media (min-width: #{$large-desktop + 1px}) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin only-chrome {\r\n\t@media all and (-webkit-min-device-pixel-ratio: 0) and (min-resolution: .001dpcm) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin only-moz {\r\n\t@media all and (min--moz-device-pixel-ratio: 0) and (min-resolution: 3e1dpcm) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin only-ie {\r\n\t@media screen and (min-width: 0\\0) {\r\n\t\t@content;\r\n\t}\r\n}", "/* -- Base css\r\n-------------------------------------------- -- */\r\nhtml {\r\n\tfont-size: 10px;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\toverflow-x: hidden;\r\n\r\n\t@include tablet-pro {\r\n\t\tfont-size: 9px;\r\n\t}\r\n\r\n\t@include tablet-mini {\r\n\t\tfont-size: 8px;\r\n\t}\r\n}\r\n\r\nbody {\r\n\tposition: relative;\r\n\tfont-family: var(--primary-font);\r\n\tfont-size: 16px;\r\n\tline-height: 1.7;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\tcolor: var(--typo-body);\r\n}\r\n\r\n::-moz-selection {\r\n\tbackground: rgba(0, 0, 0, .7);\r\n\tcolor: #fff;\r\n\ttext-shadow: none;\r\n}\r\n\r\n::selection {\r\n\tbackground: rgba(0, 0, 0, .7);\r\n\tcolor: #fff;\r\n\ttext-shadow: none;\r\n}\r\n\r\nhr {\r\n\tdisplay: block;\r\n\theight: 1px;\r\n\tborder: 0;\r\n\t// border-top: 1px solid $border-color;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n}\r\n\r\naudio,\r\ncanvas,\r\niframe,\r\nimg,\r\nsvg,\r\nvideo {\r\n\tvertical-align: middle;\r\n}\r\n\r\nfieldset {\r\n\tborder: 0;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n}\r\n\r\ntextarea {\r\n\tresize: vertical;\r\n}\r\n\r\na,\r\nbutton {\r\n\toutline: none;\r\n\ttransition: all .5s;\r\n}\r\n\r\na {\r\n\tcolor: var(--action-color);\r\n}\r\n\r\na:hover {\r\n\tcolor: var(--action-color);\r\n\ttext-decoration: underline;\r\n}\r\n\r\n* {\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.container {\r\n\twidth: 1260px;\r\n\tmargin: 0 auto;\r\n\tpadding-left: 15px;\r\n\tpadding-right: 15px;\r\n\r\n\t@include tablet-pro {\r\n\t\twidth: 960px;\r\n\t}\r\n\r\n\t@include tablet-mini {\r\n\t\twidth: 650px;\r\n\t}\r\n\r\n\t@include mobile {\r\n\t\twidth: 100%;\r\n\t}\r\n}\r\n\r\n.container-fluid {\r\n\tpadding: 0 4rem;\r\n\t@include mobile {\r\n\t\tpadding: 0 2rem;\r\n\t}\r\n}\r\n\r\nfigure {\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\r\n\t@include tablet-mini {\r\n\t\timg {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: auto;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n/* -- Helper Classes\r\n----------------------------------------- -- */\r\n\r\n.section-gap {\r\n\r\n\t@include tablet-mini {\r\n\t\tpadding-top: 10rem;\r\n\t\tpadding-bottom: 10rem;\r\n\t}\r\n}\r\n\r\n.object-fit__cover {\r\n\tobject-fit: cover;\r\n}\r\n\r\n.object-fit__contain {\r\n\tobject-fit: contain;\r\n}\r\n\r\n.visuallyhidden {\r\n\tborder: 0;\r\n\tclip: rect(0 0 0 0);\r\n\theight: 1px;\r\n\tmargin: -1px;\r\n\toverflow: hidden;\r\n\tpadding: 0;\r\n\tposition: absolute;\r\n\twidth: 1px;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.visuallyhidden.focusable:active,\r\n.visuallyhidden.focusable:focus {\r\n\tclip: auto;\r\n\theight: auto;\r\n\tmargin: 0;\r\n\toverflow: visible;\r\n\tposition: static;\r\n\twidth: auto;\r\n\twhite-space: inherit;\r\n}\r\n\r\n.invisible {\r\n\tvisibility: hidden;\r\n}\r\n\r\n.border-radius {\r\n\tborder-radius: var(--radius);\r\n}\r\n\r\n.no-overflow {\r\n\toverflow: hidden;\r\n}\r\n\r\n.bottom-border {\r\n\tborder-bottom: 2px solid var(--bg-light);\r\n}\r\n\r\n//Fonts Classes\r\n.primary-font {\r\n\tfont-family: var(--primary-font);\r\n}\r\n\r\n//Colors Classes\r\n.primary-color {\r\n\tcolor: var(--primary-color);\r\n}\r\n\r\n.typo-dark {\r\n\tcolor: var(--typo-dark);\r\n}\r\n\r\n.typo-body {\r\n\tcolor: var(--typo-body);\r\n}\r\n\r\n.typo-mid {\r\n\tcolor: var(--typo-mid);\r\n}\r\n\r\n.typo-light {\r\n\tcolor: var(--typo-light);\r\n}\r\n\r\n.bg-ex-light {\r\n\tcolor: var(--bg-ex-light);\r\n}\r\n\r\n.color-success {\r\n\tcolor: var(--color-success);\r\n}\r\n\r\n.color-alert {\r\n\tcolor: var(--color-alert);\r\n}\r\n\r\n.color-info {\r\n\tcolor: var(--color-info);\r\n}\r\n\r\n.color-error {\r\n\tcolor: var(--color-error);\r\n}\r\n\r\n.color-mixed-grey {\r\n\tcolor: var(--mixed-grey);\r\n}\r\n\r\n.color-white {\r\n\tcolor: #fff;\r\n}\r\n\r\n.color-hilight {\r\n\tcolor: var(--color-hilight);\r\n}\r\n\r\n//bg color classes\r\n\r\n.primary-bg-color {\r\n\tbackground-color: var(--primary-color);\r\n}\r\n\r\n.typo-bg-dark {\r\n\tbackground-color: var(--typo-dark);\r\n}\r\n\r\n.typo-bg-body {\r\n\tbackground-color: var(--typo-body);\r\n}\r\n\r\n.typo-bg-mid {\r\n\tbackground-color: var(--typo-mid);\r\n}\r\n\r\n.typo-bg-light {\r\n\tbackground-color: var(--typo-light);\r\n}\r\n\r\n.bg-light {\r\n\tbackground-color: var(--bg-light);\r\n}\r\n\r\n.bg-ex-light {\r\n\tbackground-color: var(--bg-ex-light);\r\n}\r\n\r\n.color-bg-success {\r\n\tbackground-color: var(--color-success);\r\n}\r\n\r\n.color-bg-alert {\r\n\tbackground-color: var(--color-alert);\r\n}\r\n\r\n.color-bg-info {\r\n\tbackground-color: var(--color-info);\r\n}\r\n\r\n.color-bg-error {\r\n\tbackground-color: var(--color-error);\r\n}\r\n\r\n.bg-white {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n\r\n//Border-radius\r\n.radius {\r\n\tborder-radius: var(--radius);\r\n}\r\n\r\n.radius-big {\r\n\tborder-radius: var(--radius-big);\r\n}\r\n\r\n.grad-bg {\r\n\tbackground: linear-gradient(0deg, #EAEDF3 0%, #FFFFFF 100%);\r\n}\r\n\r\n\r\n//Shadows\r\n.shadow-p1 {\r\n\tbox-shadow: 0 1px 1px 0 rgba(10, 31, 68, 0.08);\r\n}\r\n\r\n.shadow-p2 {\r\n\tbox-shadow: 0 3px 6px 0 rgba(10, 31, 68, 0.1);\r\n}\r\n\r\n.section-subtitle {\r\n\tfont-size: 2rem;\r\n\tfont-weight: var(--w-semi-bold);\r\n}\r\n\r\n.brand-logo {\r\n\tdisplay: block;\r\n}\r\n\r\n\r\n/* -- Print Media query\r\n---------------------------------------- -- */\r\n@media print,\r\n(-webkit-min-device-pixel-ratio: 1.25),\r\n(min-resolution: 1.25dppx),\r\n(min-resolution: 120dpi) {\r\n\t/* Style adjustments for high resolution devices */\r\n}\r\n\r\n/* -- Print styles- Inlined to avoid the additional HTTP request:\r\n----------------------------------------------------------------------------- */\r\n@media print {\r\n\r\n\t*,\r\n\t*:before,\r\n\t*:after {\r\n\t\tbackground: transparent !important;\r\n\t\t// color: $typo-heading !important;\r\n\t\t/* Black prints faster */\r\n\t\t-webkit-box-shadow: none !important;\r\n\t\tbox-shadow: none !important;\r\n\t\ttext-shadow: none !important;\r\n\t}\r\n\r\n\ta,\r\n\ta:visited {\r\n\t\ttext-decoration: underline;\r\n\t}\r\n\r\n\ta[href]:after {\r\n\t\tcontent: \" (\"attr(href) \")\";\r\n\t}\r\n\r\n\tabbr[title]:after {\r\n\t\tcontent: \" (\"attr(title) \")\";\r\n\t}\r\n\r\n\t/* -- Don't show links that are fragment identifiers -- */\r\n\ta[href^=\"#\"]:after,\r\n\ta[href^=\"javascript:\"]:after {\r\n\t\tcontent: \"\";\r\n\t}\r\n\r\n\tpre {\r\n\t\twhite-space: pre-wrap !important;\r\n\t}\r\n\r\n\tpre,\r\n\tblockquote {\r\n\t\t// border: 1px solid $border-color;\r\n\t\tpage-break-inside: avoid;\r\n\t}\r\n\r\n\t/* -- Printing Tables -- */\r\n\tthead {\r\n\t\tdisplay: table-header-group;\r\n\t}\r\n\r\n\ttr,\r\n\timg {\r\n\t\tpage-break-inside: avoid;\r\n\t}\r\n\r\n\tp,\r\n\th2,\r\n\th3 {\r\n\t\torphans: 3;\r\n\t\twidows: 3;\r\n\t}\r\n\r\n\th2,\r\n\th3 {\r\n\t\tpage-break-after: avoid;\r\n\t}\r\n}", "//typography\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6 {\r\n\tfont-family: var(--secondary-font);\r\n\tcolor: var(--typo-dark);\r\n\tfont-weight: var(--w-bold);\r\n\tline-height: 1.5;\r\n}\r\n\r\nh1 {\r\n\tfont-size: 4.4rem;\r\n\r\n\t@include tablet-mini {\r\n\t\tfont-size: 3.2rem;\r\n\t}\r\n}\r\n\r\nh2 {\r\n\tfont-size: 3.6rem;\r\n\r\n\t@include tablet-mini {\r\n\t\tfont-size: 3rem;\r\n\t}\r\n}\r\n\r\nh3 {\r\n\tfont-size: 2.4rem;\r\n}\r\n\r\nh4 {\r\n\tfont-size: 2rem;\r\n\tfont-weight: var(--w-bold);\r\n\r\n\t&.sml {\r\n\t\tfont-size: 2.2rem;\r\n\t}\r\n}\r\n\r\nh5 {\r\n\tfont-size: 1.8rem;\r\n\tfont-weight: var(--w-bold);\r\n}\r\n\r\nh6 {\r\n\tfont-size: 1.6rem;\r\n\tfont-weight: var(--w-semi-bold);\r\n}\r\n\r\np {\r\n\tcolor: var(--typo-body);\r\n\r\n\t@include tablet-mini {\r\n\t\tfont-size: 16px;\r\n\t}\r\n}\r\n\r\n.w-light {\r\n\tfont-weight: var(--w-light);\r\n}\r\n\r\n.w-regular {\r\n\tfont-weight: var(--w-regular);\r\n}\r\n\r\n.w-semi-bold {\r\n\tfont-weight: var(--w-semi-bold);\r\n}\r\n\r\n.w-bold {\r\n\tfont-weight: var(--w-bold);\r\n}\r\n\r\n.w-ex-bold {\r\n\tfont-weight: var(--w-extra-bold);\r\n}", "//Custom styles\r\n//header\r\n.page-header {\r\n    background-color: #fff;\r\n    height: 8rem;\r\n    display: flex;\r\n    align-items: center;\r\n    width: 100%;\r\n    box-shadow: 0 1px 5px rgba(0,0,0,.1);\r\n\r\n    @include mobile {\r\n        height: auto;\r\n    }\r\n}\r\n\r\n.page-nav {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n\r\n    @include mobile {\r\n        display: block;\r\n\r\n        .nav-brand {\r\n            display: block;\r\n            margin: 20px 0 20px;\r\n            text-align: center;\r\n        }\r\n    }\r\n}\r\n\r\n.nav-list {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n\r\n    a {\r\n        font-size: 14px;\r\n        font-weight: var(--w-medium);\r\n        color: var(--typo-body);\r\n        text-transform: uppercase;\r\n        text-decoration: none;\r\n        margin-left: 3rem;\r\n\r\n        &:nth-of-type(1) {\r\n            margin-left: 0;\r\n        }\r\n\r\n        &:hover {\r\n            color: var(--primary-color);\r\n        }\r\n    }\r\n\r\n    .toggle-navbar {\r\n        font-size: 20px;\r\n        line-height: 1;\r\n        display: none;\r\n\r\n        @include tablet-pro {\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    @include mobile {\r\n        justify-content: center;\r\n        margin: 10px 0 15px;\r\n\r\n        a {\r\n            font-size: 12px;\r\n            margin-left: 2rem;\r\n        }\r\n    }\r\n}\r\n\r\n//sidebar\r\n.main-content {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.sidebar-navigation {\r\n    position: fixed;\r\n    top: 120px;\r\n    bottom: 40px;\r\n    width: 350px;\r\n    padding: 3rem;\r\n    overflow-x: hidden;\r\n    transition: all .5s;\r\n\r\n    &.stick-to-top {\r\n        top: 40px;\r\n\r\n        @include tablet-pro {\r\n            top: 0;\r\n        }\r\n    }\r\n\r\n    @include tablet-pro {\r\n        position: fixed;\r\n        top: 0;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 300px;\r\n        transform: translateX(-310px);\r\n        transition: all .5s;\r\n        z-index: 9999;\r\n        box-shadow: 3px 0 10px rgba(#000, .1);\r\n    }\r\n\r\n    &.show {\r\n        transform: translateX(0);\r\n    }\r\n\r\n    @include mobile {\r\n        width: 260px;\r\n    }\r\n}\r\n\r\n.doc-nav {\r\n    padding-left: 0;\r\n    list-style: none;\r\n\r\n    >li {\r\n        margin-bottom: 1.5rem;\r\n    }\r\n\r\n    ul {\r\n        padding-left: 3rem;\r\n        list-style: none;\r\n\r\n    }\r\n\r\n    ol {\r\n        padding-left: 3rem;\r\n    }\r\n\r\n    a {\r\n        display: block;\r\n        font-size: 16px;\r\n        line-height: 3rem;\r\n        color: var(--typo-body);\r\n        text-decoration: none;\r\n        padding: 0;\r\n\r\n        &.active,\r\n        &:hover {\r\n            color: var(--action-color);\r\n        }\r\n    }\r\n\r\n}\r\n\r\n//main content\r\n.main {\r\n    flex: 1;\r\n    max-width: 100%;\r\n    margin-left: 410px;\r\n    padding: 0 0 30px 30px;\r\n\r\n    @include tablet-pro {\r\n        margin: 60px 0 0;\r\n        padding: 0;\r\n    }\r\n}\r\n\r\n.section-1,\r\n.section-2 {\r\n    height: 2000px;\r\n}\r\n\r\ninput,\r\ntextarea {\r\n    width: 100%;\r\n    padding: 1rem 2rem;\r\n    background: #fff;\r\n    border: 1px solid var(--typo-body);\r\n    font-size: 16px;\r\n    line-height: 30px;\r\n    color: var(--typo-body);\r\n}\r\n\r\n.input-group {\r\n    position: relative;\r\n\r\n    button {\r\n        position: absolute;\r\n        background: transparent;\r\n        border: none;\r\n        top: 0;\r\n        right: 0;\r\n        font-size: 16px;\r\n        line-height: 34px;\r\n        padding: 1.5rem 2rem;\r\n        cursor: pointer;\r\n        transition: all .5s;\r\n\r\n        i {\r\n            font-weight: var(--w-medium);\r\n            color: var(--typo-body);\r\n            transition: all .5s;\r\n        }\r\n\r\n        &:hover {\r\n            i {\r\n                color: var(--primary-color);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main {\r\n    h3 {\r\n        border-bottom: 1px solid #eee;\r\n        padding-bottom: 1rem;\r\n        margin-bottom: 3rem;\r\n        margin-top: 5rem;\r\n        transition: color .5s;\r\n    }\r\n\r\n    h4 {\r\n        margin: 0 0 2rem;\r\n    }\r\n\r\n    p {\r\n        margin-bottom: 2rem;\r\n    }\r\n\r\n    ul,\r\n    ol {\r\n        margin: 0 0 2rem;\r\n    }\r\n\r\n    img {\r\n        margin-bottom: 3rem;\r\n        box-shadow: 0 2px 10px rgba(0, 0, 0, .1);\r\n    }\r\n\r\n}\r\n\r\nimg {\r\n    max-width: 100%;\r\n    height: auto;\r\n}\r\n\r\n//footer\r\n.page-footer {\r\n    background-color: var(--typo-dark);\r\n    padding: 19px 0;\r\n    position: relative;\r\n\r\n    p {\r\n        color: #fff;\r\n        margin: 0;\r\n    }\r\n\r\n    a {\r\n        color: #fff;\r\n\r\n        &:hover {\r\n            color: var(--primary-color);\r\n        }\r\n    }\r\n}\r\n\r\n.nav {\r\n    flex-direction: column;\r\n\r\n    @include tablet-pro {\r\n        display: block;\r\n    }\r\n}\r\n\r\n.main-content {\r\n    @include tablet-pro {\r\n        padding-top: 1px;\r\n        padding-bottom: 50px;\r\n    }\r\n}\r\n\r\nsection.active {\r\n    h3 {\r\n        color: var(--action-color);\r\n    }\r\n}\r\n\r\n.sidebar-navigation ol {\r\n    counter-reset: section;\r\n    list-style-type: none;\r\n\r\n    li a {\r\n        &::before {\r\n            counter-increment: section;\r\n            content: counters(section, \".\") \". \";\r\n        }\r\n    }\r\n}\r\n\r\n.back-to-top {\r\n    position: fixed;\r\n    bottom: -6rem;\r\n    right: -6rem;\r\n    display: block;\r\n    width: 4rem;\r\n    line-height: 4rem;\r\n    background: var(--action-color);\r\n    color: #fff;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    border-radius: var(--radius);\r\n    opacity: 0;\r\n    transform: scale(.3);\r\n    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.5);\r\n    z-index: 9;\r\n    transition: all .3s;\r\n\r\n    &.show {\r\n        bottom: 3rem;\r\n        right: 3rem;\r\n        opacity: .7;\r\n        transform: scale(1);\r\n    }\r\n\r\n    &:hover {\r\n        color: #fff;\r\n        bottom: 3.2rem;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.nav-link {\r\n    &:hover {\r\n        text-decoration: underline;\r\n    }\r\n}\r\n\r\nli {\r\n    img {\r\n        display: block;\r\n        margin-top: 20px;\r\n    }\r\n}"]}