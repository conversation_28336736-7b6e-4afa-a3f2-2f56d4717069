<?php
/**
 * Checkout coupon form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-coupon.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 7.0.1
 */

defined( 'ABSPATH' ) || exit;

if ( !wc_coupons_enabled() ) { // @codingStandardsIgnoreLine.
    return;
}

?>

<div class="axil-checkout-notice">
	<div class="axil-toggle-box">
		<div class="woocommerce-form-coupon-toggle toggle-bar">
			<i class="fas fa-pencil"></i>
			<?php wc_print_notice( apply_filters( 'woocommerce_checkout_coupon_message', esc_html__( 'Have a coupon?', 'etrade' ) . 
			' <a href="#" class="showcoupon">' . esc_html__( 'Click here to enter your code', 'etrade' ) . '<i class="fas fa-angle-down"></i></a>' ), 'notice' );?>

		</div>

		<form class="checkout_coupon woocommerce-form-coupon axil-checkout-coupon" method="post" style="display:none">

			<p><?php esc_html_e( 'If you have a coupon code, please apply it below.', 'etrade' );?></p>

		<div class="input-group">
			<div class="form-row form-row-first">
				<input type="text" name="coupon_code" class="input-text" placeholder="<?php esc_attr_e( 'Coupon code', 'etrade' );?>" id="coupon_code" value="" />
			</div>
		<div class="apply-btn">
			<div class="form-row form-row-last">
				<button type="submit" class="button axil-btn btn-outline" name="apply_coupon" value="<?php esc_attr_e( 'Apply coupon', 'etrade' );?>"><?php esc_html_e( 'Apply coupon', 'etrade' );?></button>
			</div>
		</div>
		</div>
			<div class="clear"></div>
		</form>
	</div>
</div>