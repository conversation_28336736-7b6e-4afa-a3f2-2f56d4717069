<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Repeater;
use Elementor\Controls_Manager;
use Elementor\Utils;
use Elementor\Group_Control_Typography;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly
class Testimonial_Carousel extends Widget_Base {
   
    public function get_name() {       
        return 'wooc-testimonial';
    }    
    public function get_title() {      
          return esc_html__( 'Testimonial Carousel', 'etrade-elements' );
    }
    public function get_icon() {
        return 'eicon-testimonial-carousel';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
    protected function register_controls() {

        $this->start_controls_section(
            'testimonial_layout',
            [
                'label' => esc_html__( 'Layout', 'etrade-elements' ),
            ]
        );
		$this->add_control(
		    'style',
		    [
		        'label' => esc_html__( 'Style', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT,
		        'default' => '1',
		        'options' => [
		            '1'   => esc_html__( 'Style One', 'etrade-elements' ),
		            '2'   => esc_html__( 'Style Two', 'etrade-elements' ),		        
                    '3'   => esc_html__( 'Style Three', 'etrade-elements' ),              
		        ],
		    ] 
		);  

        $this->add_control(
            'section_title_display',
            [
            'type' => Controls_Manager::SWITCHER,
            'label'       => esc_html__( 'Section Title Display', 'etrade-elements' ),
            'label_on'    => esc_html__( 'On', 'etrade-elements' ),
            'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
            'default'     => 'yes',
            'separator'     => 'before',
            ] 
        );   

        $this->add_control(
            'sub_title',
            [
            'type'    => Controls_Manager::TEXT,
            'label'       => esc_html__( 'Before Title', 'etrade-elements' ),
            'default'     => 'Lorem Ipsum',
            'condition'   => array( 'section_title_display' => 'yes' ),     
                'label_block'   => true,  
            ]
        );
        
        $this->add_control(
        'beforetitlestyle',
            [
                'label' => esc_html__( 'Before Color', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT,
                'default' => 'primary',
                    'condition'   => array( 'section_title_display' => 'yes' ),    
                'options' => [
                    'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                    'secondary'   => esc_html__( 'Secondary', 'etrade-elements' ),                                          
                    'primary2'   => esc_html__( 'Primary 2', 'etrade-elements' ),                                          
                                                                
                ],
            ] 
        );   
                    
        $this->add_control(
            'icon',
            [
                'label' => __( 'Icons', 'etrade-elements' ),
                'type' => Controls_Manager::ICONS,
                    'condition'   => array( 'section_title_display' => 'yes' ),    
                'default' => [
                    'value' => 'far fa-shopping-basket',
                    'library' => 'solid',
                ],
                        
            ]
        );

        $this->add_control(
            'title',
            [
            'type'    => Controls_Manager::TEXT,
            'label'       => esc_html__( 'Title', 'etrade-elements' ),
            'default'     => 'Lorem Ipsum',
            'condition'   => array( 'section_title_display' => 'yes' ),   
                'label_block'   => true,    
            ]
        );

        $this->add_control(
            'sec_title_tag',
            [
                'label' => esc_html__('Title HTML Tag','etrade-elements'),
                'type' => Controls_Manager::CHOOSE,
                'options' => [
                    'h1' => [
                        'title' => esc_html__('H1','etrade-elements'),
                        'icon' => 'eicon-editor-h1'
                    ],
                    'h2' => [
                        'title' => esc_html__('H2','etrade-elements'),
                        'icon' => 'eicon-editor-h2'
                    ],
                    'h3' => [
                        'title' => esc_html__('H3','etrade-elements'),
                        'icon' => 'eicon-editor-h3'
                    ],
                    'h4' => [
                        'title' => esc_html__('H4','etrade-elements'),
                        'icon' => 'eicon-editor-h4'
                    ],
                    'h5' => [
                        'title' => esc_html__('H5','etrade-elements'),
                        'icon' => 'eicon-editor-h5'
                    ],
                    'h6' => [
                        'title' => esc_html__('H6','etrade-elements'),
                        'icon' => 'eicon-editor-h6'
                    ]
                ],
                'default' => 'h2',
                'condition'   => array( 'section_title_display' => 'yes' ),	 
                'label_block' => true,
    
            ]
        );
            
		$this->end_controls_section();
		$this->start_controls_section(
            'testimonial_content',
            [
                'label' => esc_html__( 'Testimonial', 'etrade-elements' ),
            ]
        );

		$repeater = new Repeater();

		$repeater->add_control(
		    'testimonial_image',
		    [
		        'label' => esc_html__( 'Testimonial Logo', 'etrade-elements' ),
		        'type' => Controls_Manager::MEDIA,
		        'default' => [
		            'url' => Utils::get_placeholder_image_src(),
		        ],
		    ]
		);
		
		$repeater->add_control(
		    'title',
		    [
		        'label'   => esc_html__('Title', 'etrade-elements' ),
		        'type'    => Controls_Manager::TEXT,
		        'default' => 'Lorem Ipsum',
                 'label_block'   => true,  
		    ]
		);
		$repeater->add_control(
		    'designation',
		    [
		        'label'   => esc_html__('Designation', 'etrade-elements' ),
		        'type'    => Controls_Manager::TEXT,
		        'default' => 'Lorem Ipsum',
                 'label_block'   => true,  
		    ]
		);
		$repeater->add_control(
		    'content',
		    [
		        'label'   => esc_html__('Content', 'etrade-elements' ),
		        'type'    => Controls_Manager::TEXTAREA,		        
		        'default' => esc_html__( 'Very good Design. Flexible. Fast Support.', 'etrade-elements' ),  
		    ]
		);
		    
	    $this->add_control(
            'list_testimonial',
            [
                'type' => Controls_Manager::REPEATER,
                'fields' => $repeater->get_controls(),
                'show_label' => false,
                'default' => [
                    [
                        'title' => esc_html__( 'Amy Smith', 'etrade-elements' ),
                        'content' => 'This is the best website I have ordered something from. I highly recommend.',
                    ], 
                    [
                        'title' => esc_html__( 'Amy Smith', 'etrade-elements' ),
                        'content' => 'This is the best website I have ordered something from. I highly recommend.',
                    ],
                    [
                        'title' => esc_html__( 'Amy Smith', 'etrade-elements' ),
                        'content' => 'This is the best website I have ordered something from. I highly recommend.',
                    ],
                    [
                        'title' => esc_html__( 'Amy Smith', 'etrade-elements' ),
                        'content' => 'This is the best website I have ordered something from. I highly recommend.',
                    ],

                ],
                 'title_field' => '{{{ title }}}',
            ]
        );

		$this->end_controls_section();

        $this->start_controls_section(
            'title_style_section',
            [
                'label' => esc_html__( 'Testimonial Content', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
 
        $this->add_control(
            'content_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .testimonial-style-one-content .thumb-content p' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .testimonial-style-two-wrapper .thumb-content p' => 'color: {{VALUE}}',
                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'content_font_size',
                'label' => esc_html__( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .testimonial-style-one-content .thumb-content p, {{WRAPPER}} .testimonial-style-two-wrapper .thumb-content p',
            ]
        );
       
        $this->add_responsive_control(
            'content_title_margin',
            [
                'label' => esc_html__( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .testimonial-style-one-content .thumb-content p' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .testimonial-style-two-wrapper .thumb-content p' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
        
    $this->end_controls_section();


        $this->start_controls_section(
            'section_title_style',
            [
                'label' => __( 'Testimonial Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
 
 
          $this->add_control(
            'stitle_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
               
                'selectors' => array(
                    '{{WRAPPER}} .testimonial-style-one-content .thumb-content .title' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .testimonial-style-two-wrapper .thumb-content .item-title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'stitle_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                
                'selector' => '{{WRAPPER}} .testimonial-style-one-content .thumb-content .title, {{WRAPPER}} .testimonial-style-two-wrapper .thumb-content .item-title',
            ]
        );
       
        $this->add_responsive_control(
            'stitle_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                
                'selectors' => [
                    '{{WRAPPER}} .testimonial-style-one-content .thumb-content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .testimonial-style-two-wrapper .thumb-content .item-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    
                ],
            ]
        );
       
    $this->end_controls_section();

        $this->start_controls_section(
            'sub_title_style_section',
            [
                'label' => __( 'Testimonial Sub Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );

       

          $this->add_control(
            'sub_title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
              
                'selectors' => array(
                    '{{WRAPPER}} .testimonial-style-one-content .thumb-content .subtitle' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .testimonial-style-two-wrapper .thumb-content .subtitle' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'sub_title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
               
                'selector' => '{{WRAPPER}} .testimonial-style-one-content .thumb-content .subtitle, {{WRAPPER}}  .testimonial-style-two-wrapper .thumb-content .subtitle',
            ]
        );
       
        $this->add_responsive_control(
            'sub_title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
               
                'selectors' => [
                    '{{WRAPPER}} .testimonial-style-one-content .thumb-content .subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    
                    
                ],
            ]
        );
       
    $this->end_controls_section(); 

    } 
	
    private function slick_load_scripts(){
        wp_enqueue_style(  'slick' );
        wp_enqueue_style(  'slick-theme' );
        wp_enqueue_script( 'slick' );
    }	

    private function magnific_load_scripts(){
        wp_enqueue_script(  'jquery-magnific-popup' );
        wp_enqueue_style(  'magnific-popup' );    
    } 
	protected function render() {
			$settings = $this->get_settings();	
			$this->magnific_load_scripts();	
			$this->slick_load_scripts();
   			$template   = 'testimonial-carousel-' . str_replace("style", "", $settings['style']);  
			return wooc_Elements_Helper::wooc_element_template( $template, $settings );
	}
  
}
