<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

get_header();
// Get Value
$axil_options = Helper::axil_get_options();
$axil_blog_sidebar_class = ( $axil_options['axil_blog_sidebar'] === 'no' ) || !is_active_sidebar( 'sidebar-1' ) ? 'col-lg-12 axil-post-wrapper' : 'col-lg-8 axil-post-wrapper';
$axil_blog_grid_post_class = ( $axil_options['axil_blog_sidebar'] === 'no' ) || !is_active_sidebar( 'sidebar-1' ) ? 'col-lg-4 axil-post-wrapper' : 'col-md-6';
$axil_is_post_archive = is_home() || ( is_archive() && get_post_type() == 'post' ) ? true : false;
?>

<!-- Start Blog Area  -->
<div class="axil-blog-area axil-section-gapTop  axil-section-gapBottom">
    <div class="container">
       <div class="row row--20">
            <?php if ( is_active_sidebar( 'sidebar-1' ) && $axil_options['axil_blog_sidebar'] == 'left' ) {?>
                <div class="col-lg-4 col-xl-4 mt_md--40 mt_sm--40">
                    <aside class="axil-sidebar-area">
                            <?php dynamic_sidebar();?>
                    </aside>
                </div>
            <?php }?>
            <div class="<?php echo esc_attr( $axil_blog_sidebar_class ); ?>">
                <?php
                    if ( have_posts() ):
                        if ( $axil_is_post_archive && $axil_options['axil_blog_layout'] == 'grid' ) {
                            echo '<div class="row g-5">';
                            while ( have_posts() ): the_post();
                                echo '<div class="' . $axil_blog_grid_post_class . '">';
                                get_template_part( 'template-parts/post/content-grid', get_post_format() );
                                echo '</div>';
                            endwhile;
                            echo '</div>';
                        } else {
                            while ( have_posts() ): the_post();
                                get_template_part( 'template-parts/post/content', get_post_format() );
                            endwhile;
                        }
                        axil_blog_pagination();

                    else:
                        get_template_part( 'template-parts/content', 'none' );

                    endif;
                    ?>
            </div>
            <?php if ( is_active_sidebar( 'sidebar-1' ) && $axil_options['axil_blog_sidebar'] == 'right' ) {?>
                <div class="col-lg-4 col-xl-4">
                    <aside class="axil-sidebar-area">
                        <?php dynamic_sidebar();?>
                    </aside>
                </div>
            <?php }?>
        </div>
    </div>
</div>
<!-- End Blog Area  -->
<?php
get_footer();