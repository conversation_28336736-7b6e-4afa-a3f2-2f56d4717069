.fw-backend-option-type-multi-picker {
	padding-left: 0;
	padding-right: 0;
}

/* hide last inner option border */
.fw-postbox .fw-option-type-multi-picker > .fw-backend-options-group.choice-group:after,
.postbox-with-fw-options .fw-option-type-multi-picker > .fw-backend-options-group.choice-group:after,
.fw-option-type-multi-picker.fw-option-type-multi-picker-with-borders > .choice-group > .fw-backend-option:after,
.fw-option-type-multi-picker.fw-option-type-multi-picker-with-borders > .choice-group .fw-options-tabs-contents > .fw-inner > .fw-options-tab:after {
	content: ' ';
	display: block;
	border-top: 1px solid #fff;
	margin-top: -1px;
	position: relative;
}

.fw-option-type-multi-picker > .choice-group {
	display: none;
	padding-bottom: 0;
	border-bottom: 0;
}

.fw-option-type-multi-picker > .choice-group.chosen {
	display: block;

	-webkit-animation-name: fwFadeIn;
	animation-name: fwFadeIn;

	-webkit-animation-duration: .5s;
	animation-duration: .5s;
}

.fw-option-type-multi-picker-dynamic-container.fw-backend-option-type-multi-picker:not(.fw-has-dynamic-choice) {
	display: none;
}

.fw-option-type-multi-picker.fw-option-type-multi-picker-with-borders:not(.has-choice) > .picker-group > .fw-backend-option,
.fw-option-type-multi-picker.fw-option-type-multi-picker-with-borders > .choice-group > .fw-backend-option-type-multi > div > div > div > div > .fw-backend-option:last-child,
.fw-option-type-multi-picker.fw-option-type-multi-picker-without-borders > .choice-group > .fw-backend-option-type-multi > div > div > div > div > .fw-backend-option {
	padding-bottom: 0; /* prevent backend-option double padding */
}

.fw-option-type-multi-picker.fw-option-type-multi-picker-with-borders.has-choice > .picker-group,
.fw-option-type-multi-picker.fw-option-type-multi-picker-with-borders > .choice-group > .fw-backend-option:not(:last-child) {
	border-bottom-width: 1px;
}

.fw-option-type-multi-picker.fw-option-type-multi-picker-without-borders > .picker-group,
.fw-option-type-multi-picker.fw-option-type-multi-picker-with-borders:not(.has-choice) > .picker-group {
	border-bottom-width: 0;
}

.fw-option-type-multi-picker > .picker-group > .fw-backend-option {
	padding-top: 0; /* prevent backend-option double padding */
	padding-bottom: 0; /* prevent backend-option double padding */
}

.fw-option-type-multi-picker.fw-option-type-multi-picker-with-borders > .picker-group > .fw-backend-option {
	padding-bottom: 21px;
}

.fw-backend-option-type-multi-picker.fw-backend-option-design-customizer .fw-option-type-multi-picker > .picker-group {
	padding-bottom: 10px;
}

/* tabs fixes */

.fw-option-type-multi-picker > .choice-group .fw-options-tabs-wrapper > .fw-options-tabs-list,
.fw-option-type-multi-picker > .choice-group .fw-options-tabs-wrapper > .fw-options-tabs-contents > .fw-inner > .fw-options-tab > .fw-options-tabs-wrapper > .fw-options-tabs-list {
	padding-left: 27px; /* same as .fw-backend-option-design-default */
}

.fw-option-type-multi-picker > .choice-group .fw-options-tabs-wrapper > .fw-options-tabs-list {
	padding-top: 24px; /* same as .fw-backend-option-design-default */
}

.fw-option-type-multi-picker > .choice-group .fw-options-tabs-wrapper > .fw-options-tabs-contents > .fw-inner > .fw-options-tab > .fw-options-tabs-wrapper > .fw-options-tabs-list {
	padding-top: 20px;
}

.fw-option-type-multi-picker > .choice-group .fw-options-tabs-wrapper > .fw-options-tabs-contents {
	margin-top: 0;
}

/* end: tabs fixes */

