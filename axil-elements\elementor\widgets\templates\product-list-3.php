<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */


$thumb_size = array( 158, 155 );
$query = $settings['query'];
$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
$row_col_class  = "row-cols-xl-{$settings['col_xl']} row-cols-lg-{$settings['col_lg']} row-cols-md-{$settings['col_md']} row-cols-sm-{$settings['col_sm']} row-cols-{$settings['col_mobile']}";
$col_class  = "col ";
?>

<div class="axil-most-sold-product">	
	<?php if ( $query->have_posts() ) :?>		
		<div class="row <?php echo esc_attr( $row_col_class );?>">
			<?php while ( $query->have_posts() ) : $query->the_post();?>
				<?php
				$cat = '';
				$id = get_the_ID();
				$product = wc_get_product( $id );

				
				if ( $settings['sale_price_only'] ) {
					$price_html = wc_price( wc_get_price_to_display( $product ) ) . $product->get_price_suffix();
				}
				else {
					$price_html = $product->get_price_html();
				}				
				?>
	 
				<div class="col">  
					<div class="axil-product-list product-list-style-2">
				        <div class="thumbnail">
				           <a href="<?php the_permalink();?>">
				                   <?php the_post_thumbnail('thumbnail'); ?>
				                </a>
				        </div>
				        <div class="product-content">
				            <h6 class="product-title"><a href="<?php the_permalink();?>"><?php the_title();?></a><span class="verified-icon"><i class="fas fa-badge-check"></i></span></h6>

                            <?php 
            				if ( $settings['price_display'] ) { ?>
            					<div class="product-price-variant">
                            		 <span class="price current-price"><?php echo wp_kses( $price_html, 'alltext_allow' );?></span>
                            	</div>

                            <?php }	
                            ?>      

				            <div class="product-cart">
				            	 <?php WooC_Functions::wooc_print_add_to_cart_icon( false, true, false );?>	
				              
				            </div>
				        </div>
			    	</div> 
			    </div>		
			<?php endwhile;?>					
			<?php else:?>
			<div><?php esc_html_e( 'No products available', 'etrade-elements' ); ?></div>
		<?php endif;?>
	</div>
</div>
<?php wp_reset_postdata();?>