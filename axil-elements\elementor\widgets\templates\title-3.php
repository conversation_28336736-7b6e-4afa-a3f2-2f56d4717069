<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
use Elementor\Icons_Manager;
use Elementor\Group_Control_Image_Size;
$attr = ''; 
$btn = ''; 
if ('1' == $settings['title_link_type']) {
    if ( !empty( $settings['title_link']['url'] ) ) {
        $attr  = 'href="' . $settings['title_link']['url'] . '"';
        $attr .= !empty( $settings['title_link']['is_external'] ) ? ' target="_blank"' : '';
        $attr .= !empty( $settings['title_link']['nofollow'] ) ? ' rel="nofollow"' : '';
        $title = '<a ' . $attr . '>' . $settings['title'] . '</a>';
    }
    if ( !empty( $settings['title_link_text'] ) ) {
        $btn = '<a class="title-view-link" ' . $attr . '>' . $settings['title_link_text'] . '</a>';
    }
    }else {
    $attr  = 'href="' . get_permalink($settings['title_page_link']) . '"';
    $attr .= ' target="_self"';
    $attr .= ' rel="nofollow"';                        
    $btn = '<a class="title-view-link" ' . $attr . '>' . $settings['title_link_text'] . '</a>';
}
$allowed_tags = wp_kses_allowed_html( 'post' );
?> 
<div class="section-title-wrapper section-title-border"> 
    <?php  if($settings['title']){ ?>
        <<?php echo esc_html( $settings['sec_title_tag'] );?> class="title"><?php echo wp_kses_post( $settings['title'] );?> <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?> </<?php echo esc_html( $settings['sec_title_tag'] );?>>
    <?php  } ?>
    <?php  if ( !empty( $settings['title_link_text'] ) ) { ?>    
        <div class="view-btn">                
            <?php echo wp_kses( $btn, $allowed_tags ); ?>
        </div>
    <?php } ?>  
</div>
