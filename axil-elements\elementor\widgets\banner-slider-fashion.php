<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Repeater;
use Elementor\Widget_Base;

if ( !defined( 'ABSPATH' ) ) {
    exit;
}
// Exit if accessed directly

class axil_banner_slider_fashion extends Widget_Base {

    public function get_name() {
        return 'axil-banner-slider-fashion';
    }
    public function get_title() {
        return __( 'Banner Slider - Fashion', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-banner';
    }
    public function get_categories() {
        return array( ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' );
    }
    public function axil_get_img( $img ) {
        $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
        return $img;
    }

    public function get_product_name( $post_type = 'product' ) {
        $options = array();
        $options = array( '0' => esc_html__( 'None', 'etrade-elements' ) );
        $axil_post = array( 'posts_per_page' => -1, 'post_type' => $post_type );
        $axil_post_terms = get_posts( $axil_post );
        if ( !empty( $axil_post_terms ) && !is_wp_error( $axil_post_terms ) ) {
            foreach ( $axil_post_terms as $term ) {
                $options[$term->ID] = $term->post_title;
            }
            return $options;
        }
    }
    private function axil_get_all_pages() {

        $page_list = get_posts( array(
            'post_type'      => 'page',
            'orderby'        => 'date',
            'order'          => 'DESC',
            'posts_per_page' => -1,
        ) );

        $pages = array();

        if ( !empty( $page_list ) && !is_wp_error( $page_list ) ) {
            foreach ( $page_list as $page ) {
                $pages[$page->ID] = $page->post_title;
            }
        }

        return $pages;
    }

    private function wooc_cat_dropdown() {
        $terms = get_terms( array( 'taxonomy' => 'product_cat' ) );
        $category_dropdown = array( '0' => esc_html__( 'All Categories', 'etrade-elements' ) );

        foreach ( $terms as $category ) {
            $category_dropdown[$category->term_id] = $category->name;
        }

        return $category_dropdown;
    }

    protected function register_controls() {

        $this->start_controls_section(
            'axilbanner_content_sec',
            array(
                'label' => esc_html__( ' Banner Content', 'etrade-elements' ),
            )
        );

        $repeater = new Repeater();

        $repeater->add_control(
            'beforetitlestyle',
            array(
                'label'   => esc_html__( 'Before Color', 'etrade-elements' ),
                'type'    => Controls_Manager::SELECT,
                'default' => 'primary',
                'options' => array(
                    'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                    'secondary' => esc_html__( 'Secondary', 'etrade-elements' ),
                    'primary2'  => esc_html__( 'Primary 2', 'etrade-elements' ),

                ),
            )
        );

        $repeater->add_control(
            'list_before',
            array(
                'label'       => esc_html__( 'Before Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => esc_html__( 'Before titlet', 'etrade-elements' ),
                'label_block' => true,

            )
        );

        $repeater->add_control(
            'before_icon',
            array(
                'label'   => esc_html__( 'Before Title Icons', 'etrade-elements' ),
                'type'    => Controls_Manager::ICONS,
                'default' => array(
                    'value'   => 'fas fa-stopwatch',
                    'library' => 'solid',
                ),

            )
        );

        $repeater->add_control(
            'list_title',
            array(
                'label'       => esc_html__( 'Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => esc_html__( 'Banner Title', 'etrade-elements' ),
                'label_block' => true,
            )
        );

        $repeater->add_control(
            'image',
            array(
                'label'   => esc_html__( 'Banner Image', 'etrade-elements' ),
                'type'    => Controls_Manager::MEDIA,
                'default' => array(
                    'url' => "",
                ),
                'dynamic' => array(
                    'active' => true,
                ),

            )
        );

        $repeater->add_group_control(
            Group_Control_Image_Size::get_type(),
            array(
                'name'      => 'image_size',
                'default'   => 'full',
                'separator' => 'none',

            )
        );

        $repeater->add_control(
            'btntext',
            array(
                'label'     => __( 'Button Text', 'etrade-elements' ),
                'type'      => Controls_Manager::TEXT,
                'default'   => 'Shop Now',
                'separator' => 'before',

            )
        );

        $repeater->add_control(
            'axil_link_type',
            array(
                'label'       => esc_html__( 'See All Link Type', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT,
                'options'     => array(
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ),
                'default'     => '1',

                'label_block' => true,
            )
        );

        $repeater->add_control(
            'axil_page_link',
            array(
                'label'       => esc_html__( 'Select See All Page', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT2,
                'label_block' => true,
                'options'     => $this->axil_get_all_pages(),
                'condition'   => array(
                    'axil_link_type' => '2',

                ),
            )
        );

        $repeater->add_control(
            'url',
            array(
                'label'       => __( 'Detail URL', 'etrade-elements' ),
                'type'        => Controls_Manager::URL,
                'placeholder' => 'https://your-link.com',
                'condition'   => array(
                    'axil_link_type' => '1',

                ),
            )
        );

        $this->add_control(
            'list',
            array(
                'label'       => esc_html__( 'Slider List', 'etrade-elements' ),
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $repeater->get_controls(),

                'default'     => array(
                    array(
                        'list_before' => esc_html__( ' Smartwatch', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Bloosom Smat Watch', 'etrade-elements' ),
                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-40.png' ),
                        ),

                    ),
                    array(
                        'list_before' => esc_html__( ' Smartwatch', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Delux Brand Watch', 'etrade-elements' ),
                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-46.png' ),
                        ),

                    ),

                    array(
                        'list_before' => esc_html__( ' Smartwatch', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Bloosom Smat Watch', 'etrade-elements' ),
                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-40.png' ),
                        ),

                    ),
                ),
                'title_field' => '{{{ list_title }}}',
            )
        );
        $this->end_controls_section();

    }

    private function slick_load_scripts() {
        wp_enqueue_style( 'slick' );
        wp_enqueue_style( 'slick-theme' );
        wp_enqueue_script( 'slick' );
    }

    protected function render() {
        $settings = $this->get_settings();
        $this->slick_load_scripts();

        $template = 'banner-slider-fashion';
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
    }

}