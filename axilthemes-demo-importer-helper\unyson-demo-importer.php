<?php
if ( ! defined( 'ABSPATH' ) ) exit;
class Demo_Importer {
	public function __construct() {		
		add_filter( 'axilthemes_demo_installer_warning', array( $this, 'axilthemes_data_loss_warning' ) );
		add_filter( 'fw:ext:backups-demo:demos', array( $this, 'axilthemes_demo_config' ) );
		add_action( 'fw:ext:backups:tasks:success:id:demo-content-install', array( $this, 'axilthemes_after_demo_install' ) );
		//add_action( 'admin_menu', array( $this, 'wpdocs_register_my_custom_menu_page' ) );
		//add_filter( 'fw:ext:backups:add-restore-task:image-sizes-restore', '__return_false' ); // Enable it to skip image restore step
		  add_action('admin_footer', array($this, 'axilthemes_demo_footer_add_scripts') );
	}	
	public function wpdocs_register_my_custom_menu_page() {
		add_management_page(esc_html__( 'Demo Contents Install', 'axilthemesdemo' ), 'Install Demo Data', 'manage_options', 'tools.php?page=fw-backups-demo-content');
	}	
	
	public function axilthemes_data_loss_warning( $links ) {
		$html  = '<div class="demo-Warning-info notice notice-error">';
		$html .= esc_html__( 'Warning: All your old data will be lost if you install One Click demo data from here, so it is suitable only for a new website.', 'axilthemesdemo');
		$html .= '</div>';
		return $html;
	}
	public function axilthemes_demo_config( $demos ) {
		$demos_array = array(
			'demo1' => array(
				'title' => esc_html__( 'Home - Electronics', 'axilthemesdemo' ),
				'screenshot' 		=> AXILTHEME_PREVIEW . 'home-01.png',
				'preview_link' 		=> AXILTHEME_PREVIEW_LINK . 'home2',
			),
			'demo2' => array(
				'title' 			=> esc_html__( 'Home - NFT', 'axilthemesdemo' ),
				'screenshot' 		=> AXILTHEME_PREVIEW . 'home-02.png',
				'preview_link' 		=> AXILTHEME_PREVIEW_LINK . 'home3',
			),	

			'demo3' => array(
				'title' 			=> esc_html__( 'Home - Fashion', 'axilthemesdemo'),
				'screenshot' 		=> AXILTHEME_PREVIEW . 'home-03.png',
				'preview_link' 		=> AXILTHEME_PREVIEW_LINK . 'home4',
			),	
			'demo4' => array(
				'title' => esc_html__( 'Home - Jewellery', 'axilthemesdemo' ),
				'screenshot' 		=> AXILTHEME_PREVIEW . 'home-04.png',
				'preview_link' 		=> AXILTHEME_PREVIEW_LINK . 'home5',
			),	
			'demo5' => array(
				'title' => esc_html__( 'Home - Multipurpose', 'axilthemesdemo' ),
				'screenshot' 		=> AXILTHEME_PREVIEW . 'home-06.png',
				'preview_link' 		=> AXILTHEME_PREVIEW_LINK . 'home1',
			),
			'demo6' => array(
				'title' => esc_html__( 'Home - Furniture', 'axilthemesdemo' ),
				'screenshot' 		=> AXILTHEME_PREVIEW . 'home-05.png',
				'preview_link' 		=> AXILTHEME_PREVIEW_LINK . 'home6',
			),
				

		);

		$download_url = AXILTHEME_DEMO_DATA_URL;		
		foreach ($demos_array as $id => $data) {
			$demo = new \FW_Ext_Backups_Demo($id, 'piecemeal', array(
				'url' => $download_url,
				'file_id' => $id,
			));
			$demo->set_title($data['title']);
			$demo->set_screenshot($data['screenshot']);
			$demo->set_preview_link($data['preview_link']);
			$demos[ $demo->get_id() ] = $demo;
			unset($demo);
		}

		return $demos;
	}

	public function axilthemes_after_demo_install( $collection ){
		// Update front page id
		$demos = array(
			'demo1'  => 33,
			'demo2'  => 269,
			'demo3'  => 277,			
			'demo4'  => 282,			
			'demo5'  => 14,
			'demo6'  => 286,
		);
		$data = $collection->to_array();
		foreach( $data['tasks'] as $task ) {
			if( $task['id'] == 'demo:demo-download' ){
				$demo_id = $task['args']['demo_id'];
				$page_id = $demos[$demo_id];
				update_option( 'page_on_front', $page_id );
				flush_rewrite_rules();
				break;
			}
		}

		// Update contact form 7 email
		$cf7ids = array( 6, 1515 );
		foreach ( $cf7ids as $cf7id ) {
			$mail = get_post_meta( $cf7id, '_mail', true );
			$mail['recipient'] = get_option( 'admin_email' );

			if ( class_exists( 'WPCF7_ContactFormTemplate' ) ) {
				$pattern = "/<[^@\s]*@[^@\s]*\.[^@\s]*>/"; // <<EMAIL>>
				$replacement = '<'. \WPCF7_ContactFormTemplate::from_email().'>';
				$mail['sender'] = preg_replace($pattern, $replacement, $mail['sender']);
			}			
			update_post_meta( $cf7id, '_mail', $mail );		
		}	
		 // Update WooCommerce email options //todo
        $admin_email = get_option('admin_email');
        $name        = get_bloginfo('name', 'display');

        update_option('woocommerce_stock_email_recipient', $admin_email);
        update_option('woocommerce_email_from_address', $admin_email);
        update_option('woocommerce_email_from_name', $name);

        // Update post author id
        global $wpdb;
        $id = get_current_user_id();
        $query = "UPDATE $wpdb->posts SET post_author = $id";
        $wpdb->query($query);	
	}

	    public function axilthemes_demo_footer_add_scripts()
    {
        $frontpage_id = get_option( 'page_on_front' );
        $frontpage_title = get_the_title( $frontpage_id );

        if (!empty($frontpage_title)) {
            ?>
            <script>
                jQuery(document).ready(function ($) {

                    $('.theme-browser .theme.fw-ext-backups-demo-item').each(function () {

                        var axilthemes_theme_title = $( this ).find('h3.theme-name').html();
                        var axilthemes_theme_title_slug = axilthemes_theme_title.toLowerCase();

                        var frontpage_title_slgu = '<?php echo strtolower($frontpage_title); ?>';

                        if (axilthemes_theme_title_slug == frontpage_title_slgu) {
                            $(this).addClass('active_demo');
                            return false;
                        }
                    });
                });
            </script>
            <?php
        }
    }
}

new Demo_Importer;