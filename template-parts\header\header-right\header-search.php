<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

$axil_options = Helper::axil_get_options();
$search       = isset( $_GET['s'] ) ? $_GET['s'] : '';
$product_cat  = isset( $_GET['product_cat'] ) ? $_GET['product_cat'] : '';
$all_label = $label = esc_html__( 'All Categories', 'etrade' );
if ( isset( $_GET['product_cat'] ) ) {
	$pcat = $_GET['product_cat'];
	if ( isset( $category_dropdown[$pcat] ) ) {
		$label = $category_dropdown[$pcat]['name'];
	}
}
if( WOOC_WOO_ACTIVED ): 
    $shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
endif ; 
?>
<?php if( $axil_options['axil_enable_header_search'] ):?>
    <li class="axil-search">   
        <a href="javascript:void(0)" class="header-search-icon" title="<?php echo esc_html__('Search', 'etrade'); ?>">
            <i class="fal fa-search"></i>
        </a> 
    </li> 
<?php endif ?>  
<div class="header-search-modal" id="header-search-modal">
    <button class="card-close sidebar-close"><i class="fas fa-times"></i></button>
    <div class="header-search-wrap">
        <div class="card-header">
            <form action="#">
                <div class="input-group">
                    <input type="search" class="form-control" name="prod-search" id="prod-search" placeholder="<?php echo esc_html__('Write Something....', 'etrade'); ?>">
                    <button type="submit" class="axil-btn btn-bg-primary"><i class="fal fa-search"></i></button>
                </div>
            </form>
        </div>
        <div class="card-body search-results-body">
            <div class="search_input_loader"></div>
            <div class="search-result-header">
                <h6 class="title"><?php echo esc_html__('24 Result Found', 'etrade'); ?></h6>
                <a href="<?php echo esc_url($shop_permalink );?>" class="view-all"><?php echo esc_html__('View All', 'etrade'); ?></a>
            </div>
            <div class="psearch-results">
               <?php axil_is_recent_product();?>  
            </div>
        </div>
    </div>
</div>