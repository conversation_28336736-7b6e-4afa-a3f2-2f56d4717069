<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
?>  
<div class="testimonial-style-three-wrapper">

	<?php if ( $settings['section_title_display'] ): ?> 
	    <div class="heading-title">
	           <?php  if($settings['title']){ ?>
                <<?php echo esc_html( $settings['sec_title_tag'] );?> class="title">
                <?php echo wp_kses_post( $settings['title'] );?></<?php echo esc_html( $settings['sec_title_tag'] );?>>   
            <?php  } ?> 
	    </div>
    <?php endif; ?> 

    <div class="testimonial-slick-activation-three"> 
    	<?php foreach ( $settings['list_testimonial'] as $testimonial ):  
			$has_designation  = $testimonial['designation'] == 'yes' ? true : false;			
			$designation  	  = $testimonial['designation'];
			$title  			= $testimonial['title'];
			$content  			= $testimonial['content']; 
			$size 				= 'axil-thumbnail-sm'; 
			$img 				= wp_get_attachment_image( $testimonial['testimonial_image']['id'], $size );
			?> 
	        <div class="slick-single-layout testimonial-style-three">
	            <p><?php echo esc_html($content);?></p>
	            <div class="author-info">
	                <h6 class="author-name"><?php echo esc_html($title);?></h6>
	                <span class="author-desg"><?php echo esc_html($designation);?></span>
	            </div>
	        </div>
      <?php  endforeach;?>
    </div>
    <div class="testimonial-custom-nav">
        <div class="row align-items-center">
            <div class="col-6">
                <div class="slick-slide-count"></div>
            </div>
            <div class="col-6">
                <div class="slide-custom-nav">
                    <button class="prev-custom-nav"><i class="fal fa-angle-left"></i><?php esc_html__( 'Prev', 'etrade-elements' ), ?></button>
                    <button class="next-custom-nav"><?php esc_html__( 'Next', 'etrade-elements' ), ?>  <i class="fal fa-angle-right"></i></button>
                </div>
            </div>
        </div>
    </div>
</div>