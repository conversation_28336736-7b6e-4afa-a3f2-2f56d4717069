# 
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2015
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Unyson\n"
"POT-Creation-Date: 2016-02-02 15:48+0300\n"
"PO-Revision-Date: 2016-03-01 16:18+0000\n"
"Last-Translator: moldcraft <<EMAIL>>\n"
"Language-Team: Russian (Russia) (http://www.transifex.com/themefuse/unyson/language/ru_RU/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru_RU\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"
"X-Generator: Poedit 1.5.4\n"
"X-Poedit-Basepath: .\n"
"X-Poedit-KeywordsList: _:1;gettext:1;dgettext:2;ngettext:1,2;dngettext:2,3;__:1;_e:1;_c:1;_n:1,2;_n_noop:1,2;_nc:1,2;__ngettext:1,2;__ngettext_noop:1,2;_x:1,2c;_ex:1,2c;_nx:1,2,4c;_nx_noop:1,2,3c;_n_js:1,2;_nx_js:1,2,3c;esc_attr__:1;esc_html__:1;esc_attr_e:1;esc_html_e:1;esc_attr_x:1,2c;esc_html_x:1,2c;comments_number_link:2,3;t:1;st:1;trans:1;transChoice:1,2\n"
"X-Poedit-SearchPath-0: ..\n"
"X-Poedit-SourceCharset: UTF-8\n"

#: ../framework/manifest.php:5
msgid "Unyson"
msgstr "Unyson"

#: ../framework/helpers/class-fw-wp-filesystem.php:43
msgid "Cannot connect to Filesystem directly"
msgstr "Не удается подключиться к файловой системе напрямую"

#: ../framework/helpers/class-fw-wp-filesystem.php:271
#, php-format
msgid "Cannot create directory \"%s\". It must be inside \"%s\""
msgstr "Не удается создать папку \"%s\". Она должна быть внутри \"%s\""

#: ../framework/helpers/class-fw-wp-filesystem.php:273
msgid "\" or \""
msgstr "\" или \""

#: ../framework/helpers/class-fw-flash-messages.php:95
#, php-format
msgid "Invalid flash message type: %s"
msgstr "Недействительный тип мгновенного сообщения: %s"

#: ../framework/helpers/class-fw-wp-list-table.php:185
msgid "No items found."
msgstr "Ничего не найдено."

#: ../framework/helpers/class-fw-wp-list-table.php:309
msgid "Bulk Actions"
msgstr "Групповые действия"

#: ../framework/helpers/class-fw-wp-list-table.php:319
msgid "Apply"
msgstr "Применить"

#: ../framework/helpers/class-fw-wp-list-table.php:403
msgid "All dates"
msgstr "Все даты"

#: ../framework/helpers/class-fw-wp-list-table.php:416
#, php-format
msgid "%1$s %2$d"
msgstr "%1$s %2$d"

#: ../framework/helpers/class-fw-wp-list-table.php:432
msgid "List View"
msgstr "Список"

#: ../framework/helpers/class-fw-wp-list-table.php:433
msgid "Excerpt View"
msgstr "Отрывок"

#: ../framework/helpers/class-fw-wp-list-table.php:459
#, php-format
msgid "%s pending"
msgstr "%s в ожидании"

#: ../framework/helpers/class-fw-wp-list-table.php:714
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:33
msgid "Select All"
msgstr "Выбрать все"

#: ../framework/helpers/general.php:1150
msgid "year"
msgstr "год"

#: ../framework/helpers/general.php:1151
msgid "years"
msgstr "лет"

#: ../framework/helpers/general.php:1153
msgid "month"
msgstr "месяц"

#: ../framework/helpers/general.php:1154
msgid "months"
msgstr "месяцы"

#: ../framework/helpers/general.php:1156
msgid "week"
msgstr "неделя"

#: ../framework/helpers/general.php:1157
msgid "weeks"
msgstr "недели"

#: ../framework/helpers/general.php:1159
msgid "day"
msgstr "день"

#: ../framework/helpers/general.php:1160
msgid "days"
msgstr "дни"

#: ../framework/helpers/general.php:1162
msgid "hour"
msgstr "час"

#: ../framework/helpers/general.php:1163
msgid "hours"
msgstr "часов"

#: ../framework/helpers/general.php:1165
msgid "minute"
msgstr "минута"

#: ../framework/helpers/general.php:1166
msgid "minutes"
msgstr "минуты"

#: ../framework/helpers/general.php:1168
msgid "second"
msgstr "секунда"

#: ../framework/helpers/general.php:1169
msgid "seconds"
msgstr "секунды"

#: ../framework/helpers/general.php:1558
msgid "Maximum stack depth exceeded"
msgstr "Превышена максимальная глубина стека"

#: ../framework/helpers/general.php:1561
msgid "Underflow or the modes mismatch"
msgstr "Потеря значимости или несоответствие режимов"

#: ../framework/helpers/general.php:1564
msgid "Unexpected control character found"
msgstr "Найден некорректный управляющий символ"

#: ../framework/helpers/general.php:1567
msgid "Syntax error, malformed JSON"
msgstr "Синтаксическая ошибка, неправильный JSON"

#: ../framework/helpers/general.php:1570
msgid "Malformed UTF-8 characters, possibly incorrectly encoded"
msgstr " Возможно неправильна кодировка UTF-8 символов"

#: ../framework/helpers/general.php:1573
#: ../framework/extensions/backups/class-fw-extension-backups.php:550
msgid "Unknown error"
msgstr "Неизвестная ошибка"

#: ../framework/helpers/class-fw-form.php:80
#, php-format
msgid "Form with id \"%s\" was already defined"
msgstr "Форма с id \"%s\" уже существует"

#: ../framework/helpers/class-fw-form.php:168
msgid "Nonce verification failed"
msgstr "Некорректный ключ nonce"

#: ../framework/helpers/class-fw-form.php:331
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/views/form.php:13
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:66
msgid "Submit"
msgstr "Отправить"

#: ../framework/extensions/update/class-fw-extension-update.php:285
#: ../framework/extensions/update/class-fw-extension-update.php:602
msgid "Cannot delete: "
msgstr "Не удалось удалить:"

#: ../framework/extensions/update/class-fw-extension-update.php:292
msgid "Cannot create: "
msgstr "Не удалось создать:"

#: ../framework/extensions/update/class-fw-extension-update.php:370
msgid "Cannot remove old temporary directory: "
msgstr "Не удалось удалить старую временную папку:"

#: ../framework/extensions/update/class-fw-extension-update.php:376
#: ../framework/extensions/update/class-fw-extension-update.php:572
msgid "Cannot create directory: "
msgstr "Не удалось создать папку:"

#: ../framework/extensions/update/class-fw-extension-update.php:381
#, php-format
msgid "Downloading the %s..."
msgstr "Загрузка %s..."

#: ../framework/extensions/update/class-fw-extension-update.php:386
#, php-format
msgid "Cannot download the %s."
msgstr "Не удалось загрузить %s."

#: ../framework/extensions/update/class-fw-extension-update.php:396
#, php-format
msgid "Installing the %s..."
msgstr "Установка %s..."

#: ../framework/extensions/update/class-fw-extension-update.php:402
#: ../framework/extensions/update/class-fw-extension-update.php:431
#: ../framework/extensions/update/class-fw-extension-update.php:531
#: ../framework/extensions/update/class-fw-extension-update.php:552
#: ../framework/extensions/update/class-fw-extension-update.php:582
msgid "Cannot access directory: "
msgstr "Не удалось открыть папку:"

#: ../framework/extensions/update/class-fw-extension-update.php:421
msgid "Cannot remove: "
msgstr "Не удалось удалить:"

#: ../framework/extensions/update/class-fw-extension-update.php:456
#: ../framework/extensions/update/class-fw-extension-update.php:522
#: ../framework/extensions/update/class-fw-extension-update.php:628
#: ../framework/extensions/update/class-fw-extension-update.php:652
#, php-format
msgid "Cannot move \"%s\" to \"%s\""
msgstr "Не удалось переместить \"%s\" в \"%s\""

#: ../framework/extensions/update/class-fw-extension-update.php:472
#, php-format
msgid "Cannot merge \"%s\" with \"%s\""
msgstr "Не удалось объединить \"%s\" с \"%s\""

#: ../framework/extensions/update/class-fw-extension-update.php:485
#, php-format
msgid "The %s has been successfully updated."
msgstr "%s был успешно обновлен."

#: ../framework/extensions/update/class-fw-extension-update.php:492
#, php-format
msgid "Cannot remove temporary directory \"%s\"."
msgstr "Не удалось удалить временную папку \"%s\"."

#: ../framework/extensions/update/class-fw-extension-update.php:672
#: ../framework/extensions/update/class-fw-extension-update.php:740
#: ../framework/extensions/update/class-fw-extension-update.php:808
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:930
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1427
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1837
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2054
msgid "Invalid nonce."
msgstr "Неверный ключ nonce."

#: ../framework/extensions/update/class-fw-extension-update.php:683
msgid "Framework Update"
msgstr "Обновление фреймворка"

#: ../framework/extensions/update/class-fw-extension-update.php:699
msgid "Failed to get framework latest version."
msgstr "Не удалось получить последнюю версию фреймворка."

#: ../framework/extensions/update/class-fw-extension-update.php:716
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:360
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:374
#: ../framework/extensions/update/views/updates-list.php:10
#: ../framework/core/class-fw-manifest.php:353
msgid "Framework"
msgstr "Фреймворк"

#: ../framework/extensions/update/class-fw-extension-update.php:751
msgid "Theme Update"
msgstr "Обновление Темы"

#: ../framework/extensions/update/class-fw-extension-update.php:767
msgid "Failed to get theme latest version."
msgstr "Не удалось получить последнюю версию темы."

#: ../framework/extensions/update/class-fw-extension-update.php:784
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:393
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:407
msgid "Theme"
msgstr "Тема"

#: ../framework/extensions/update/class-fw-extension-update.php:817
msgid "Please check the extensions you want to update."
msgstr "Пожалуйста, выберите расширения для обновления."

#: ../framework/extensions/update/class-fw-extension-update.php:841
msgid "Extensions Update"
msgstr "Обновление расширений"

#: ../framework/extensions/update/class-fw-extension-update.php:879
msgid "No extensions updates found."
msgstr "Обновления для расширений не найдены."

#: ../framework/extensions/update/class-fw-extension-update.php:886
#, php-format
msgid "Extension \"%s\" does not exist or is disabled."
msgstr "Расширение \"%s\" не существует или неактивно."

#: ../framework/extensions/update/class-fw-extension-update.php:893
#, php-format
msgid "No update found for the \"%s\" extension."
msgstr "Для расширения \"%s\" обновлений не найдено."

#: ../framework/extensions/update/class-fw-extension-update.php:915
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:426
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:440
#, php-format
msgid "%s extension"
msgstr "расширение %s"

#: ../framework/extensions/update/manifest.php:5
#: ../framework/extensions/builder/includes/option-types/builder/includes/fullscreen.php:82
msgid "Update"
msgstr "Обновление"

#: ../framework/extensions/update/manifest.php:6
msgid "Keep you framework, extensions and theme up to date."
msgstr "Обновляйте фреймворк, расширения и тему вовремя."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:91
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:232
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2439
msgid "Github error:"
msgstr "Ошибка github'а:"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:100
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:241
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2446
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (Response code: %d)"
msgstr "Нет доступа к релизам в репозитории \"%s\" Github'а. (Код ответа: %d)"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:108
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:249
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2462
#, php-format
msgid "Failed to access Github repository \"%s\" releases."
msgstr "Нет доступа к релизам в репозитории \"%s\" Github'а."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:122
#, php-format
msgid "No releases found in repository \"%s\"."
msgstr "В репозитории \"%s\" релизов не найдено."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:143
#, php-format
msgid ""
"%s manifest has invalid \"github_update\" parameter. Please use "
"\"user/repo\" format."
msgstr "Манифест %s имеет неправильный \"github_update\" параметр. Пожалуйста, используйте формат \"user/repo\"."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:171
#, php-format
msgid "Failed to fetch %s latest version from github \"%s\"."
msgstr "Не удалось установить последнюю версию %s из \"%s\" github'а."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:264
#, php-format
msgid "%s github repository \"%s\" does not have the \"%s\" release."
msgstr "%s Репозиторий github'а \"%s\" не содержит релиз \"%s\"."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:284
#, php-format
msgid "Cannot download %s zip."
msgstr "Не удалось скачать %s zip-архив."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:297
#, php-format
msgid "Cannot save %s zip."
msgstr "Не удалось сохранить %s zip-архив."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:316
#, php-format
msgid "Cannot remove %s zip."
msgstr "Не удалось удалить %s zip-архив."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:325
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2566
msgid "Cannot access the unzipped directory files."
msgstr "Нет доступа к папке с распакованными файлам."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:341
#, php-format
msgid "The unzipped %s directory not found."
msgstr "Распакованная %s папка не найдена."

#: ../framework/extensions/update/views/updates-list.php:12
#, php-format
msgid "You have the latest version of %s."
msgstr "У вас последняя версия %s."

#: ../framework/extensions/update/views/updates-list.php:25
msgid "Update Framework"
msgstr "Обновить фреймворк"

#: ../framework/extensions/update/views/updates-list.php:37
msgid "Your theme is up to date."
msgstr "Версия темы актуальна."

#: ../framework/extensions/update/views/updates-list.php:50
msgid "Update Theme"
msgstr "Обновить Тему"

#: ../framework/extensions/update/views/updates-list.php:60
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:844
#, php-format
msgid "%s Extensions"
msgstr "%s Расширений"

#: ../framework/extensions/update/views/updates-list.php:62
#, php-format
msgid "You have the latest version of %s Extensions."
msgstr "Вы используете последнюю версию расширений %s."

#: ../framework/extensions/update/views/updates-list.php:80
#: ../framework/extensions/update/views/updates-list.php:95
#: ../framework/extensions/update/views/updates-list.php:100
msgid "Update Extensions"
msgstr "Обновить расширения"

#: ../framework/extensions/update/views/updates-list.php:99
msgid "New extensions updates available."
msgstr "Доступны обновления для расширений."

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:16
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:14
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:16
msgid "Go to updates page"
msgstr "Перейти на страницу обновлений"

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:19
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:17
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:19
msgid "Return to Updates page"
msgstr "Вернуться на страницу обновлений"

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:84
#, php-format
msgid "You have version %s installed. Update to %s."
msgstr "Вы используете версию %s. Обновить до %s."

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:126
msgid "No Extensions for update."
msgstr "Нет доступных обновлений для расширений."

#: ../framework/extensions/portfolio/manifest.php:7
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:260
#: ../framework/core/components/extensions/manager/available-extensions.php:72
msgid "Portfolio"
msgstr "Портфолио"

#: ../framework/extensions/portfolio/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:73
msgid ""
"This extension will add a fully fledged portfolio module that will let you "
"display your projects using the built in portfolio pages."
msgstr "Это расширение добавит полноценное портфолио, которое позволит вам отображать ваши проекты используя созданные в портфолио страницы."

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:115
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Project"
msgstr "Проект"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:116
msgid "Projects"
msgstr "Проекты"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:123
#: ../framework/extensions/learning/class-fw-extension-learning.php:63
#: ../framework/extensions/learning/class-fw-extension-learning.php:127
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:58
#: ../framework/extensions/events/class-fw-extension-events.php:76
#: ../framework/extensions/media/extensions/slider/posts.php:8
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:32
msgid "Add New"
msgstr "Создать новый"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:124
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:198
#: ../framework/extensions/learning/class-fw-extension-learning.php:64
#: ../framework/extensions/learning/class-fw-extension-learning.php:128
#: ../framework/extensions/events/class-fw-extension-events.php:77
#: ../framework/extensions/events/class-fw-extension-events.php:131
#, php-format
msgid "Add New %s"
msgstr "Создать новый %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:125
#: ../framework/extensions/learning/class-fw-extension-learning.php:129
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:89
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:76
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:88
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:86
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:68
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:34
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:50
#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:110
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:45
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:37
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:73
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:81
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:57
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:50
#: ../framework/extensions/events/class-fw-extension-events.php:78
#: ../framework/includes/option-types/popup/class-fw-option-type-popup.php:158
#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:151
#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:177
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:161
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:187
msgid "Edit"
msgstr "Редактировать"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:126
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:196
#: ../framework/extensions/learning/class-fw-extension-learning.php:65
#: ../framework/extensions/learning/class-fw-extension-learning.php:66
#: ../framework/extensions/learning/class-fw-extension-learning.php:130
#: ../framework/extensions/learning/class-fw-extension-learning.php:192
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:59
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:60
#: ../framework/extensions/events/class-fw-extension-events.php:79
#: ../framework/extensions/events/class-fw-extension-events.php:129
#, php-format
msgid "Edit %s"
msgstr "Редактировать %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:127
#: ../framework/extensions/learning/class-fw-extension-learning.php:67
#: ../framework/extensions/learning/class-fw-extension-learning.php:131
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:61
#: ../framework/extensions/events/class-fw-extension-events.php:80
#, php-format
msgid "New %s"
msgstr "Новый %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:128
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:193
#: ../framework/extensions/learning/class-fw-extension-learning.php:68
#: ../framework/extensions/learning/class-fw-extension-learning.php:132
#: ../framework/extensions/learning/class-fw-extension-learning.php:189
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:62
#: ../framework/extensions/events/class-fw-extension-events.php:81
#: ../framework/extensions/events/class-fw-extension-events.php:126
#, php-format
msgid "All %s"
msgstr "Все %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:129
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:130
#: ../framework/extensions/learning/class-fw-extension-learning.php:69
#: ../framework/extensions/learning/class-fw-extension-learning.php:70
#: ../framework/extensions/learning/class-fw-extension-learning.php:133
#: ../framework/extensions/learning/class-fw-extension-learning.php:134
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:63
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:64
#: ../framework/extensions/events/class-fw-extension-events.php:82
#: ../framework/extensions/events/class-fw-extension-events.php:83
#, php-format
msgid "View %s"
msgstr "Просмотреть %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:131
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:192
#: ../framework/extensions/learning/class-fw-extension-learning.php:71
#: ../framework/extensions/learning/class-fw-extension-learning.php:135
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:65
#: ../framework/extensions/events/class-fw-extension-events.php:84
#: ../framework/extensions/events/class-fw-extension-events.php:125
#, php-format
msgid "Search %s"
msgstr "Поиск %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:132
#: ../framework/extensions/learning/class-fw-extension-learning.php:72
#: ../framework/extensions/learning/class-fw-extension-learning.php:136
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:66
#: ../framework/extensions/events/class-fw-extension-events.php:85
#, php-format
msgid "No %s Found"
msgstr "%s не найдены"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:133
#: ../framework/extensions/learning/class-fw-extension-learning.php:73
#: ../framework/extensions/learning/class-fw-extension-learning.php:137
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:67
#: ../framework/extensions/events/class-fw-extension-events.php:86
#, php-format
msgid "No %s Found In Trash"
msgstr "%s не найдены в корзине"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:136
msgid "Create a portfolio item"
msgstr "Создать портфолио"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:183
#: ../framework/extensions/events/class-fw-extension-events.php:116
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:268
msgid "Category"
msgstr "Категория"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:184
#: ../framework/extensions/events/class-fw-extension-events.php:117
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:168
msgid "Categories"
msgstr "Категории"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:194
#: ../framework/extensions/learning/class-fw-extension-learning.php:190
#: ../framework/extensions/events/class-fw-extension-events.php:127
#, php-format
msgid "Parent %s"
msgstr "Родитель %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:195
#: ../framework/extensions/learning/class-fw-extension-learning.php:191
#: ../framework/extensions/events/class-fw-extension-events.php:128
#, php-format
msgid "Parent %s:"
msgstr "Родитель %s:"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:197
#: ../framework/extensions/learning/class-fw-extension-learning.php:193
#: ../framework/extensions/events/class-fw-extension-events.php:130
#: ../framework/core/components/extensions/manager/views/extension.php:234
#: ../framework/core/components/extensions/manager/views/extension.php:272
#, php-format
msgid "Update %s"
msgstr "Обновить %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:199
#: ../framework/extensions/learning/class-fw-extension-learning.php:195
#: ../framework/extensions/events/class-fw-extension-events.php:132
#, php-format
msgid "New %s Name"
msgstr "Новое имя %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:200
#: ../framework/extensions/learning/class-fw-extension-learning.php:196
#: ../framework/extensions/events/class-fw-extension-events.php:133
#, php-format
msgid "%s"
msgstr "%s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Gallery"
msgstr "Галерея"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:240
msgid "Set project gallery"
msgstr "Создать галерею проекта"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:241
msgid "Edit project gallery"
msgstr "Редактировать галерею проекта"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:271
msgid "Project Cover Image"
msgstr "Изображение-обложка проекта"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:287
msgid "Edit this item"
msgstr "Редактировать элемент"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:348
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:361
#: ../framework/extensions/learning/class-fw-extension-learning.php:320
#: ../framework/extensions/learning/class-fw-extension-learning.php:333
msgid "View all categories"
msgstr "Открыть все категории"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:381
msgid "Cover Image"
msgstr "Изображение-обложка"

#: ../framework/extensions/seo/settings-options.php:17
#: ../framework/extensions/social/settings-options.php:11
msgid "General"
msgstr "Общие"

#: ../framework/extensions/seo/settings-options.php:21
#: ../framework/extensions/social/settings-options.php:15
msgid "General Settings"
msgstr "Общие настройки"

#: ../framework/extensions/seo/class-fw-extension-seo.php:89
msgid "Site name"
msgstr "Имя сайта"

#: ../framework/extensions/seo/class-fw-extension-seo.php:95
msgid "Site description"
msgstr "Описание сайта"

#: ../framework/extensions/seo/class-fw-extension-seo.php:101
msgid "Current time"
msgstr "Текущее время"

#: ../framework/extensions/seo/class-fw-extension-seo.php:107
msgid "Current date"
msgstr "Текущая дата"

#: ../framework/extensions/seo/class-fw-extension-seo.php:113
msgid "Current month"
msgstr "Текущий месяц"

#: ../framework/extensions/seo/class-fw-extension-seo.php:119
msgid "Current year"
msgstr "Текущий год"

#: ../framework/extensions/seo/class-fw-extension-seo.php:125
msgid "Date of the post/page"
msgstr "Дата создания записи/страницы"

#: ../framework/extensions/seo/class-fw-extension-seo.php:131
msgid "Title of the post/page/term"
msgstr "Заголовок записи/страницы/элемента"

#: ../framework/extensions/seo/class-fw-extension-seo.php:137
msgid "Excerpt of the current post, of auto-generate if it is not set"
msgstr "Автоматически сгенерированный отрывок текущей записи"

#: ../framework/extensions/seo/class-fw-extension-seo.php:143
msgid "Excerpt of the current post, without auto-generation"
msgstr "Отрывок текущей записи, заданный вручную"

#: ../framework/extensions/seo/class-fw-extension-seo.php:149
msgid "Post tags, separated by coma"
msgstr "Метки записи, разделенные запятой"

#: ../framework/extensions/seo/class-fw-extension-seo.php:155
msgid "Post categories, separated by coma"
msgstr "Категории записи, разделенные запятой"

#: ../framework/extensions/seo/class-fw-extension-seo.php:161
msgid "Category/tag/term description"
msgstr "Описание категории/метки/элемента"

#: ../framework/extensions/seo/class-fw-extension-seo.php:167
msgid "Term title"
msgstr "Заголовок элемента"

#: ../framework/extensions/seo/class-fw-extension-seo.php:173
msgid "Post modified time"
msgstr "Дата изменения записи"

#: ../framework/extensions/seo/class-fw-extension-seo.php:179
msgid "Post/page id"
msgstr "Id записи/страницы"

#: ../framework/extensions/seo/class-fw-extension-seo.php:185
msgid "Post/page author \"nicename\""
msgstr "Имя автора записи/страницы"

#: ../framework/extensions/seo/class-fw-extension-seo.php:191
msgid "Post/page author id"
msgstr "Id автора записи/страницы"

#: ../framework/extensions/seo/class-fw-extension-seo.php:197
msgid "Search phrase in search page"
msgstr "Искать фразу на странице поиска"

#: ../framework/extensions/seo/class-fw-extension-seo.php:203
#: ../framework/extensions/seo/class-fw-extension-seo.php:209
msgid "Page number"
msgstr "Номер страницы"

#: ../framework/extensions/seo/class-fw-extension-seo.php:215
msgid "Attachment caption"
msgstr "Подпись вложения"

#: ../framework/extensions/seo/class-fw-extension-seo.php:435
#: ../framework/extensions/seo/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:120
msgid "SEO"
msgstr "SEO"

#: ../framework/extensions/seo/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:121
msgid ""
"This extension will enable you to have a fully optimized WordPress website "
"by adding optimized meta titles, keywords and descriptions."
msgstr "Это расширение позволит Вам иметь полностью оптимизированный веб-сайт WordPress, добавив оптимизированные тайтлы, ключевые слова и описания."

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:22
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:310
msgid "Titles & Meta"
msgstr "Заголовки и мета данные"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:26
#: ../framework/extensions/breadcrumbs/settings-options.php:6
#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:9
msgid "Homepage"
msgstr "Главная страница"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:30
msgid "Homepage Title"
msgstr "Заголовок главной страницы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:31
msgid "Set homepage title format"
msgstr "Выбрать формат заголовка главной страницы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:36
msgid "Homepage Description"
msgstr "Описание главной страницы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:37
msgid "Set homepage description"
msgstr "Задать описание для главной страницы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:44
msgid "Homepage Meta Keywords"
msgstr "Ключевые мета слова для главной страницы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:45
msgid "Set homepage meta keywords"
msgstr "Задать ключевые мета слова для главной страницы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:54
msgid "Custom Posts"
msgstr "Пользовательские Посты"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:59
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:111
#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:120
#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:7
#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:15
#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:12
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:15
#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:56
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:480
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:62
msgid "Title"
msgstr "Заголовок"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:60
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:112
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:287
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:315
msgid "Set title format"
msgstr "Указать формат заголовка"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:63
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:74
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:87
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:115
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:126
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:139
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:247
msgid "Here are some tags examples:"
msgstr "Здесь даны некоторые примеры меток:"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:70
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:122
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:320
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:66
msgid "Description"
msgstr "Описание"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:71
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:123
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:293
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:321
msgid "Set description format"
msgstr "Указать формат описания"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:83
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:135
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:328
msgid "Meta Keywords"
msgstr "Ключевые мета слова"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:84
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:136
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:329
msgid "Set meta keywords"
msgstr "Задать ключевые мета слова"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:96
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:148
msgid "Meta Robots"
msgstr "Мета роботы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:97
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:149
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:188
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:227
msgid "noindex, follow"
msgstr "noindex, follow"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:105
msgid "Taxonomies"
msgstr "Таксономии"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:158
msgid "Other"
msgstr "Другое"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:165
msgid "Author Page Title"
msgstr "Заголовок страницы автора"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:166
msgid "Set author page title format"
msgstr "Указать формат заголовка страницы автора"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:171
msgid "Author Page Description"
msgstr "Описание страницы автора"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:172
msgid "Set author page description"
msgstr "Задать описание для страницы автора"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:179
msgid "Author Meta Keywords"
msgstr "Ключевые мета слова для страницы автора"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:180
msgid "Set author page meta keywords"
msgstr "Задать ключевые мета слова для страницы автора"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:187
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:226
msgid "Metarobots"
msgstr "Мета роботы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:193
msgid "Disable Author Archives"
msgstr "Отключить архивы автора"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:194
msgid "Disable Author archives SEO settings"
msgstr "Отключить настройки SEO для архивов автора"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:204
msgid "Date Achieves Title"
msgstr "Заголовок архивов"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:205
msgid "Set date achieves title format"
msgstr "Указать формат заголовка архивов"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:210
msgid "Date Achieves Description"
msgstr "Описание архивов"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:211
msgid "Set date achieves description"
msgstr "Задать описание для архивов"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:218
msgid "Date achieves Meta Keywords"
msgstr "Ключевые мета слова для архивов"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:219
msgid "Set date achieves meta keywords"
msgstr "Задать ключевые слова для архивов"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:232
msgid "Disable Date Archives"
msgstr "Отключить архивы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:233
msgid "Disable date archives SEO settings"
msgstr "Отключить настройки SEO для архивов"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:243
msgid "Search Page Title"
msgstr "Заголовок страницы поиска"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:244
msgid "Set search page title format"
msgstr "Указать формат заголовка страницы поиска"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:259
msgid "404 Page Title"
msgstr "Заголовок страницы 404"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:260
msgid "Set 404 page title format"
msgstr "Указать формат заголовка страницы 404"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:262
#: ../framework/extensions/breadcrumbs/settings-options.php:8
msgid "404 Not Found"
msgstr "404 ничего не найдено"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:286
msgid "SEO Title"
msgstr "Заголовок SEO"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:292
msgid "SEO Description"
msgstr "Описание SEO"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:314
msgid "Page Title"
msgstr "Заголовок страницы"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:356
msgid "Use Meta Keywords"
msgstr "Использовать ключевые мета слова"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:357
msgid "Allow the use of meta keywords in posts and taxonomies"
msgstr "Разрешить использование ключевых мета слов в записях и таксономиях"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:33
msgid "Google Webmasters"
msgstr "Google Вебмастера"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:34
msgid "Insert Google Webmasters verification code"
msgstr "Вставить код проверки Google Вебмастера"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:41
msgid "Bing Webmasters"
msgstr "Bing Вебмастера"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:42
msgid "Insert Bing Webmasters verification code"
msgstr "Вставить код проверки Bing Вебмастера"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:80
msgid "Webmasters"
msgstr "Вебмастера"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:89
#, php-format
msgid "Webmaster %s already exists"
msgstr "Вебмастер %s уже существует"

#: ../framework/extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:634
msgid "Google"
msgstr "Google"

#: ../framework/extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:638
msgid "Bing"
msgstr "Bing"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:19
msgid "Check if you want to exclude this page"
msgstr "Поставьте галочку если хотите исключить эту страницу"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:40
msgid "Check if you want to exclude this category"
msgstr "Поставьте галочку если хотите исключить эту категорию"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:57
msgid "Sitemap"
msgstr "Карта сайта"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:68
msgid "View Sitemap"
msgstr "Открыть карту сайта"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:69
msgid "Press button to view sitemap file"
msgstr "Нажмите на кнопку чтобы открыть карту сайта"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:71
msgid "XML Sitemap"
msgstr "XML карта сайта"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:81
msgid "Search Engines"
msgstr "Поисковые системы"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:83
msgid "After adding content the extension will automatically ping to:"
msgstr "После добавления содержания экстенсия автоматически отправит пинг:"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:93
msgid "Exclude Pages"
msgstr "Исключить страницы"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:95
msgid "Please check the pages you do not want to include in sitemap"
msgstr "Пожалуйста, выделите страницы, которые вы не хотите включать в карту сайта"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:106
msgid "Exclude Categories"
msgstr "Исключить категории"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:108
msgid "Please check the categories you do not want to include in sitemap"
msgstr "Пожалуйста, выделите категории, которые вы не хотите включать в карту сайта"

#: ../framework/extensions/mailer/manifest.php:5
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:187
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:188
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:103
#: ../framework/core/components/extensions/manager/available-extensions.php:240
msgid "Mailer"
msgstr "Отправитель"

#: ../framework/extensions/mailer/manifest.php:6
#: ../framework/core/components/extensions/manager/available-extensions.php:241
msgid ""
"This extension will let you set some global email options and it is used by "
"other extensions (like Forms) to send emails."
msgstr "Это расширение позволит вам задавать глобальные настройки электронной почты и использовать их в других расширениях (например Forms) для отправки имейлов."

#: ../framework/extensions/mailer/class-fw-extension-mailer.php:48
msgid "Invalid send method"
msgstr "Неверный способ отправки"

#: ../framework/extensions/mailer/class-fw-extension-mailer.php:81
msgid "The message has been successfully sent!"
msgstr "Сообщение было успешно отправлено!"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:27
msgid "Invalid email configuration"
msgstr "Неверная конфигурация электронной почты"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:145
#: ../framework/extensions/mailer/includes/class-mailer-sender.php:161
msgid "Email sent"
msgstr "Письмо отправлено"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:146
msgid "Could not send via smtp"
msgstr "Не удалось отправить через smtp"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:162
msgid "Could not send via wp_mail"
msgstr "Не удалось отправить через wp_mail"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:25
msgid "Server Address"
msgstr "Адрес сервера"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:26
msgid "Enter your email server"
msgstr "Введите сервер электронной почты"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:31
msgid "Username"
msgstr "Имя пользователя"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:32
msgid "Enter your username"
msgstr "Введите имя пользователя"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:37
msgid "Password"
msgstr "Пароль"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:38
msgid "Enter your password"
msgstr "Введите пароль"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:43
msgid "Secure Connection"
msgstr "Безопасное соединение"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:54
msgid "Custom Port"
msgstr "Пользовательский порт"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:55
msgid "Optional - SMTP port number to use."
msgstr "Опционально - номер порта SMTP."

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:56
msgid "Leave blank for default (SMTP - 25, SMTPS - 465)"
msgstr "Оставить пустым для значения по умолчанию (SMTP - 25, SMTPS - 465)"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:82
msgid "Username cannot be empty"
msgstr "Имя пользователя не может быть пустым"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:89
msgid "Password cannot be empty"
msgstr "Пароль не может быть пустым"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:96
msgid "Invalid host"
msgstr "Неверное имя хоста"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:176
#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-wpmail.php:56
msgid "Could not send the email"
msgstr "Не удалось отправить письмо"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:76
msgid "Send Method"
msgstr "Способ отправки"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:77
msgid "Select the send form method"
msgstr "Выберите способ отправки"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:94
msgid "From Name"
msgstr "Имя отправителя"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:95
msgid "The name you'll see in the From filed in your email client."
msgstr "Имя, которое будет отображаться для получателя в поле От "

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:102
msgid "From Address"
msgstr "Адрес отправителя"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:103
msgid "The form will look like was sent from this email address."
msgstr "Этот адрес будет отображаться как адрес отправителя."

#: ../framework/extensions/learning/class-fw-extension-learning.php:56
msgid "Lesson"
msgstr "Урок"

#: ../framework/extensions/learning/class-fw-extension-learning.php:57
#: ../framework/extensions/learning/views/content-course.php:14
msgid "Lessons"
msgstr "Уроки"

#: ../framework/extensions/learning/class-fw-extension-learning.php:78
msgid "Create a lesson"
msgstr "Создать урок"

#: ../framework/extensions/learning/class-fw-extension-learning.php:120
#: ../framework/extensions/learning/hooks.php:53
msgid "Course"
msgstr "Курс"

#: ../framework/extensions/learning/class-fw-extension-learning.php:121
#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:108
msgid "Courses"
msgstr "Курсы"

#: ../framework/extensions/learning/class-fw-extension-learning.php:142
msgid "Create a course"
msgstr "Создать курс"

#: ../framework/extensions/learning/class-fw-extension-learning.php:181
#: ../framework/extensions/learning/class-fw-extension-learning.php:222
msgid "Course Category"
msgstr "Категория курса"

#: ../framework/extensions/learning/class-fw-extension-learning.php:182
#: ../framework/extensions/learning/class-fw-extension-learning.php:223
msgid "Course Categories"
msgstr "Категории курсов"

#: ../framework/extensions/learning/class-fw-extension-learning.php:188
msgid "Search categories"
msgstr "Поиск категорий"

#: ../framework/extensions/learning/class-fw-extension-learning.php:194
msgid "Add New category"
msgstr "Добавить новую категорию"

#: ../framework/extensions/learning/class-fw-extension-learning.php:285
msgid "View all courses"
msgstr "Просмотреть все курсы"

#: ../framework/extensions/learning/class-fw-extension-learning.php:511
msgid "No courses available"
msgstr "Нет доступных курсов"

#: ../framework/extensions/learning/class-fw-extension-learning.php:513
msgid "Without Course"
msgstr "Без курса"

#: ../framework/extensions/learning/class-fw-extension-learning.php:520
msgid "Select Course"
msgstr "Выбрать курс"

#: ../framework/extensions/learning/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:216
msgid "Learning"
msgstr "Обучение"

#: ../framework/extensions/learning/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:217
msgid ""
"This extension adds a Learning module to your theme. Using this extension "
"you can add courses, lessons and tests for your users to take."
msgstr "Это расширение добавляет учебный модуль к вашей теме. С помощью этого расширения вы можете добавить курсы, уроки и тесты для пользователей."

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:118
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:145
msgid "Quiz"
msgstr "Тест"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:259
msgid "Quiz Elements"
msgstr "Элементы теста"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:272
msgid "Quiz settings"
msgstr "Настройки теста"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:276
msgid "Quiz Passmark Points"
msgstr "Проходные баллы теста"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:278
msgid "The points number at which the test will be passed."
msgstr "Количество баллов необходимое для прохождения теста."

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:286
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:296
msgid "Lesson Quiz"
msgstr "Обучающий тест"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:535
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:544
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:552
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:559
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:568
msgid "Invalid Quiz"
msgstr "Тест провален"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:21
#, php-format
msgid "You require %d points in oder to pass the test"
msgstr "Необходимо набрать %d баллов для прохождения теста"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:27
msgid "Sorry, you did not pass the test"
msgstr "К сожалению, вы не прошли тест"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:29
msgid "Congratulation, you passed the test"
msgstr "Поздравляем, вы прошли тест"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:43
#, php-format
msgid "You answered correctly %s questions from %s"
msgstr "Вы ответили правильно на %s вопросов из %s"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:69
#: ../framework/extensions/learning/views/content-lesson.php:18
msgid "Back to"
msgstr "Назад к"

#: ../framework/extensions/learning/extensions/learning-quiz/views/start-quiz.php:11
msgid "Start Quiz"
msgstr "Начать тест"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:20
msgid "Correct answers"
msgstr "Правильные ответы"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:21
msgid "Add correct answer variants"
msgstr "Добавьте правильные варианты ответов"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:24
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:23
msgid "Set Correct Answer"
msgstr "Введите правильный ответ"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:34
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:31
msgid "Wrong answers"
msgstr "Неправильные ответы"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:35
msgid "Add wrong answer variants"
msgstr "Добавьте неправильны варианты ответов"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:38
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:35
msgid "Set Wrong Answer"
msgstr "Введите неправильный ответ"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:42
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:55
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:53
msgid "Creates a"
msgstr "Создает"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:59
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:91
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:82
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:180
msgid "Multiple Choice"
msgstr "Несколько вариантов"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
msgid "item"
msgstr "элемент"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:87
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:73
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:86
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:84
#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:76
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:75
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:80
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:80
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:48
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:75
msgid "Label"
msgstr "Ярлык"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:88
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:75
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:87
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:85
msgid "Add/Edit Question"
msgstr "Добавить/редактировать ответ"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:90
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:77
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:90
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:87
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:58
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:51
msgid "Delete"
msgstr "Удалить"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:92
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:90
msgid "More"
msgstr "Больше"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:93
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:91
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:57
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:57
msgid "Close"
msgstr "Закрыть"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:94
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:78
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:91
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:56
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:52
msgid "Edit Label"
msgstr "Редактировать ярлык"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:96
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:80
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:93
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:93
msgid "The question label is empty"
msgstr "Текст вопроса пуст"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:97
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:81
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:94
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:94
msgid "Invalid mark point number"
msgstr "Недопустимый знак номер точки"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:98
msgid "There needs to be at least one correct answer"
msgstr "Должен быть хотя бы один правильный ответ"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:18
msgid "Correct answer"
msgstr "Правильный ответ"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:19
msgid "The question answer will be true or false"
msgstr "Ответ на вопрос будет истина или ложь"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:22
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:20
msgid "True"
msgstr "Истина"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:26
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:24
msgid "False"
msgstr "Ложь"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:45
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:74
msgid "True/False"
msgstr "Истина/Ложь"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:19
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:21
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:97
msgid "Text before gap"
msgstr "Текст перед пробелом"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:28
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:30
msgid "Gap"
msgstr "Пробел"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:37
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:39
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:98
msgid "Text after gap"
msgstr "Текст после пробела"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:58
msgid "Gap Fill"
msgstr "Заполнение пробела"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:89
msgid "Gap _____ Fill"
msgstr "Заполнение _____ пробела"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:96
#, php-format
msgid "At least one of the fields ( %s or %s ) has to ve filled with text"
msgstr "Как минимум одно поле ( %s или %s ) должно быть заполнено"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:19
msgid "Correct Answer"
msgstr "Правильный ответ"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:20
msgid "Write the correct answer text"
msgstr "Напишите правильный ответ"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:32
msgid "Add wrong answers variants"
msgstr "Добавьте варианты неправильных ответов"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:82
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:180
msgid "Single Choice"
msgstr "Единственный вариант"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:95
msgid "Correct answer cannot be empty"
msgstr "Правильный ответ не может быть пустым"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:96
msgid "There are not any wrong answers set"
msgstr "Не добавлено ни одного неправильного ответа"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:51
msgid "Question"
msgstr "Вопрос"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:53
msgid "Type the question..."
msgstr "Введите вопрос..."

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:58
msgid "Points"
msgstr "Баллы"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:18
msgid "Get list of courses"
msgstr "Получить список курсов"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:20
msgid "Lesson Courses"
msgstr "Обучающие курсы"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:126
msgid "Number of courses"
msgstr "Количество курсов"

#: ../framework/extensions/analytics/settings-options.php:11
msgid "Google Analytics"
msgstr "Google Analytics"

#: ../framework/extensions/analytics/settings-options.php:12
msgid "Enter your Google Analytics code (Ex: UA-XXXXX-X)"
msgstr "Введите ваш код Google Analytics (Напр: UA-XXXXX-X)"

#: ../framework/extensions/analytics/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:192
msgid "Analytics"
msgstr "Аналитика"

#: ../framework/extensions/analytics/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:193
msgid ""
"Enables the possibility to add the Google Analytics tracking code that will "
"let you get all the analytics about visitors, page views and more."
msgstr "Дает вам возможность добавить  Google Analytics код отслеживания, который позволит вам получить всю аналитику о посетителях, просмотре страниц и больше аналитики."

#: ../framework/extensions/blog/class-fw-extension-blog.php:36
#: ../framework/extensions/blog/class-fw-extension-blog.php:37
#: ../framework/extensions/breadcrumbs/settings-options.php:7
#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:10
msgid "Blog"
msgstr "Блог"

#: ../framework/extensions/blog/class-fw-extension-blog.php:38
msgid "Add blog post"
msgstr "Добавить запись"

#: ../framework/extensions/blog/class-fw-extension-blog.php:39
msgid "Add new blog post"
msgstr "Добавить новую"

#: ../framework/extensions/blog/class-fw-extension-blog.php:40
msgid "All blog posts"
msgstr "Все страницы блога"

#: ../framework/extensions/blog/class-fw-extension-blog.php:41
msgid "Edit blog post"
msgstr "Редактировать страницу блога"

#: ../framework/extensions/blog/class-fw-extension-blog.php:42
#: ../framework/extensions/blog/class-fw-extension-blog.php:43
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:49
msgid "Blog Post"
msgstr "Страница блога"

#: ../framework/extensions/blog/class-fw-extension-blog.php:44
msgid "New blog post"
msgstr "Новая страница в блоге"

#: ../framework/extensions/blog/class-fw-extension-blog.php:45
msgid "No blog posts found"
msgstr "Записей не найдено"

#: ../framework/extensions/blog/class-fw-extension-blog.php:46
msgid "No blog posts found in trash"
msgstr "В корзине записей не найдено"

#: ../framework/extensions/blog/class-fw-extension-blog.php:47
msgid "Search blog posts"
msgstr "Поиск записей"

#: ../framework/extensions/blog/class-fw-extension-blog.php:48
msgid "View blog post"
msgstr "Просмотреть запись"

#: ../framework/extensions/blog/class-fw-extension-blog.php:67
#: ../framework/extensions/blog/class-fw-extension-blog.php:87
#: ../framework/extensions/blog/manifest.php:7
#: ../framework/extensions/blog/manifest.php:8
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:50
msgid "Blog Posts"
msgstr "Записи"

#: ../framework/extensions/blog/class-fw-extension-blog.php:76
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:74
msgid "Blog Categories"
msgstr "Рубрики"

#: ../framework/extensions/styling/class-fw-extension-styling.php:60
#: ../framework/extensions/styling/class-fw-extension-styling.php:61
#: ../framework/extensions/styling/class-fw-extension-styling.php:78
#: ../framework/extensions/styling/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:48
msgid "Styling"
msgstr "Стилизация"

#: ../framework/extensions/styling/class-fw-extension-styling.php:104
#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:278
#: ../framework/core/components/backend.php:357
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2277
msgid "Save"
msgstr "Сохранить"

#: ../framework/extensions/styling/class-fw-extension-styling.php:118
msgid "You have no permission to change Styling options"
msgstr "У вас недостаточно прав для изменения настроек дизайна"

#: ../framework/extensions/styling/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:49
msgid ""
"This extension lets you control the website visual style. Starting from "
"predefined styles to changing specific fonts and colors across the website."
msgstr "Это расширение позволяет вам управлять визуальным стилем веб-сайта. Начиная от предопределенных стилей и до выбора персонализованных шрифтов и цветов."

#: ../framework/extensions/styling/extensions/switch-style-panel/manifest.php:7
msgid "Switch Style Panel"
msgstr "Панель стилей"

#: ../framework/extensions/styling/extensions/switch-style-panel/manifest.php:8
msgid ""
"Show on the front-end a panel that allows the user to make the switch "
"between predefined styles."
msgstr "Включите на фронт-энде панель, позволяющую пользователям переключать предустановленные стили."

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:9
msgid "Frontend Style Switcher"
msgstr "Переключатель стилей для фронт-энда"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:10
msgid "Enable frontend style switcher"
msgstr "Включить переключатель стилей для фронт-энда"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:13
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:274
#: ../framework/extensions/shortcodes/shortcodes/map/options.php:45
#: ../framework/extensions/shortcodes/shortcodes/button/options.php:24
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:42
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:34
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:48
#: ../framework/includes/option-types/simple.php:454
#: ../framework/includes/option-types/switch/class-fw-option-type-switch.php:151
msgid "Yes"
msgstr "Да"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:17
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:278
#: ../framework/extensions/shortcodes/shortcodes/map/options.php:49
#: ../framework/extensions/shortcodes/shortcodes/button/options.php:28
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:46
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:38
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:52
#: ../framework/includes/option-types/switch/class-fw-option-type-switch.php:147
msgid "No"
msgstr "Нет"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:24
msgid "The text that will be displayed at the top of the panel."
msgstr "Текст, который будет отображаться вверху панели."

#: ../framework/extensions/styling/includes/option-types/style/views/settings.php:119
msgid "Background"
msgstr "Фон"

#: ../framework/extensions/styling/includes/option-types/style/views/predefined.php:26
msgid "Predefined Styles"
msgstr "Предустановленные стили"

#: ../framework/extensions/styling/includes/option-types/style/views/preview.php:41
msgid "This is a simplified preview, not changes are reflected."
msgstr "Это упрощенный пред просмотр, изменения не отражаются."

#: ../framework/extensions/feedback/class-fw-extension-feedback.php:64
#: ../framework/core/components/extensions/manager/available-extensions.php:144
msgid "Feedback"
msgstr "Обратная связь"

#: ../framework/extensions/feedback/class-fw-extension-feedback.php:111
msgid "Reviews"
msgstr "Отзывы"

#: ../framework/extensions/feedback/settings-options.php:10
#: ../framework/extensions/shortcodes/extensions/page-builder/settings-options.php:11
msgid "Activate for"
msgstr "Активировать для"

#: ../framework/extensions/feedback/settings-options.php:16
msgid "Select the options you want the Feedback extension to be activated for"
msgstr "Выберите варианты для которых будет активировано расширение Feedback"

#: ../framework/extensions/feedback/manifest.php:7
msgid "FeedBack"
msgstr "Обратная связь"

#: ../framework/extensions/feedback/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:145
msgid ""
"Adds the possibility to leave feedback (comments, reviews and rating) about "
"your products, articles, etc. This replaces the default comments system."
msgstr "Добавляет возможность оставлять отзывы (комментарии, отзывы и рейтинг) о Вашей продукции, статей и др. Это заменяет стандартную систему комментариев."

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:84
msgid "Rating:"
msgstr "Рейтинг:"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:195
msgid "Feedback Stars"
msgstr "Отзывы Звезд"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:221
#: ../framework/extensions/feedback/extensions/feedback-stars/views/rate.php:12
msgid "Rating"
msgstr "Рейтинг"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "ERROR"
msgstr "Ошибка"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "please rate the post."
msgstr "оцените, пожалуйста, пост."

#: ../framework/extensions/feedback/extensions/feedback-stars/manifest.php:7
msgid "FeedBack Stars"
msgstr "Звездный рейтинг"

#: ../framework/extensions/feedback/extensions/feedback-stars/manifest.php:8
msgid "Allows visitors to appreciate a post using star rating"
msgstr "Позволяет посетителям Оценить пост через звезды"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
msgid "Pingback:"
msgstr "Обратный пинг:"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
msgid "(Edit)"
msgstr "(Редактировать)"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:37
msgid "Post author"
msgstr "Автор записи"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:57
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
#, php-format
msgid "%1$s at %2$s"
msgstr "%1$s в %2$s"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:63
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:53
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:56
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:122
msgid "Your comment is awaiting moderation."
msgstr "Ваш комментарий ожидает модерации."

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:24
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:40
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:119
msgid "says"
msgstr "говорит"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/view-rates.php:24
#, php-format
msgid "Based on %s Votes"
msgstr "Основано на %s голосах"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:6
msgid "Rating System"
msgstr "Рейтинговая система"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:9
msgid "Enter the number of stars you want in the rating system"
msgstr "Введите необходимое количество звезд в рейтинговой системе"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:11
msgid "5 stars"
msgstr "5 звезд"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:12
msgid "7 stars"
msgstr "7 звезд"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:13
msgid "10 stars"
msgstr "10 звезд"

#: ../framework/extensions/feedback/views/reviews.php:32
#: ../framework/extensions/feedback/views/reviews.php:53
msgid "Comment navigation"
msgstr "Навигация комментариев"

#: ../framework/extensions/feedback/views/reviews.php:35
#: ../framework/extensions/feedback/views/reviews.php:56
msgid "&larr; Older Comments"
msgstr "&larr; Предыдущие комментарии"

#: ../framework/extensions/feedback/views/reviews.php:36
#: ../framework/extensions/feedback/views/reviews.php:57
msgid "Newer Comments &rarr;"
msgstr "Следующие комментарии &rarr;"

#: ../framework/extensions/feedback/views/reviews.php:62
msgid "Comments are closed."
msgstr "Комментарии закрыты."

#: ../framework/extensions/shortcodes/extensions/page-builder/settings-options.php:18
msgid ""
"Select the posts you want the Page Builder extension to be activated for"
msgstr "Выберите типы постов, для которых будет активировано расширение Page Builder"

#: ../framework/extensions/shortcodes/extensions/page-builder/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:84
msgid "Page Builder"
msgstr "Конструктор страниц"

#: ../framework/extensions/shortcodes/extensions/page-builder/manifest.php:9
msgid ""
"Lets you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr "Вы можете с лёгкостью создавать бесчисленное количество страниц с функцией перетаскивания в Visual Page Builder, который содержит множество готовых шорткодов."

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:51
msgid ""
"There must not be more than one page Editor integrated with the wp post "
"editor per page"
msgstr "Не должно быть больше одного пейдж едитор интегрированного с wp post editor но странице  "

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:96
msgid "Visual Page Builder"
msgstr "Визуальный конструктор страниц"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:97
msgid "Default Editor"
msgstr "Стандартный редактор"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:126
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:117
#: ../framework/extensions/shortcodes/shortcodes/section/config.php:5
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:22
msgid "Layout Elements"
msgstr "Элементы макета"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:99
#, php-format
msgid "No Page Builder tab specified for shortcode: %s"
msgstr "Не задано вкладку конструктора страниц для шорткода: %s"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:111
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:75
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:83
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:57
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:238
#: ../framework/core/components/extensions/manager/views/extension.php:141
#: ../framework/core/components/extensions/manager/views/extension.php:345
msgid "Remove"
msgstr "Удалить"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:112
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:74
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:82
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:56
msgid "Duplicate"
msgstr "Дублировать"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:12
msgid "Testimonials"
msgstr "Отзывы"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:9
msgid "Add some Testimonials"
msgstr "Добавьте несколько отзывов"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/table/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/map/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/button/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:10
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:10
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:106
msgid "Content Elements"
msgstr "Элементы контента"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:8
msgid "Option Testimonials Title"
msgstr "Заголовок для блока Отзывы"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:13
msgid "Add/Edit Testimonial"
msgstr "Добавить/редактировать отзыв"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:14
msgid "Here you can add, remove and edit your Testimonials."
msgstr "Здесь вы можете добавлять, удалять и редактировать отзывы."

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:19
msgid "Quote"
msgstr "Цитата"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:20
msgid "Enter the testimonial here"
msgstr "Введите сюда текст отзыва"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:25
#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:6
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:102
msgid "Image"
msgstr "Изображение"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:26
#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:9
msgid ""
"Either upload a new, or choose an existing image from your media library"
msgstr "Загрузите новое изображение, или выберите из загруженных в медиа галерее"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:30
msgid "Name"
msgstr "Имя"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:31
msgid "Enter the Name of the Person to quote"
msgstr "Введите имя цитирующего"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:35
msgid "Position"
msgstr "Должность"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:36
msgid "Can be used for a job description"
msgstr "Может служить описанием профессии"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:40
msgid "Website Name"
msgstr "Имя сайта"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:41
msgid "Linktext for the above Link"
msgstr "Текст ссылки для вышеуказанной ссылки"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:45
msgid "Website Link"
msgstr "Ссылка на сайт"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:46
msgid "Link to the Persons website"
msgstr "Ссылка на сайт "

#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:8
msgid "Accordion"
msgstr "Аккордеон"

#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:9
msgid "Add an Accordion"
msgstr "Добавить аккордеон"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:8
msgid "Tabs"
msgstr "Табы"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:9
msgid "Add/Edit Tabs"
msgstr "Добавить/редактировать табы"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:10
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:10
msgid "Create your tabs"
msgstr "Создайте табы"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:19
#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:24
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/text-block/options.php:10
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:19
msgid "Content"
msgstr "Контент"

#: ../framework/extensions/shortcodes/shortcodes/table/config.php:8
msgid "Table"
msgstr "Таблица"

#: ../framework/extensions/shortcodes/shortcodes/table/config.php:9
msgid "Add a Table"
msgstr "Добавить таблицу"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:64
msgid "table-builder option type must be inside the table shortcode"
msgstr "типа опции table-builder должен быть внутри шорткода таблицы"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:171
msgid "Table Styling"
msgstr "Стиль таблицы"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:172
msgid "Choose the table styling options"
msgstr "Выберите стиль таблицы"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:174
msgid "Use the table as a pricing table"
msgstr "Использовать таблицу как таблицу ценообразования"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:175
msgid "Use the table to display tabular data"
msgstr "Использовать таблицу для отображения табличных данных"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:197
msgid "Default row"
msgstr "Стандартная строка"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:198
msgid "Heading row"
msgstr "Заглавная строка"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:199
msgid "Pricing row"
msgstr "Строка ценообразования"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:200
msgid "Button row"
msgstr "Строка кнопок"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:201
msgid "Row switch"
msgstr "Строка переключателей"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:211
msgid "Default column"
msgstr "Стандартная колонка"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:212
msgid "Description column"
msgstr "Колонка описания"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:213
msgid "Highlight column"
msgstr "Выделенная колонка"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:214
msgid "Center text column"
msgstr "Колонка с текстом по центру"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:251
msgid "per month"
msgstr "в месяц"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:262
#: ../framework/extensions/shortcodes/shortcodes/button/config.php:8
msgid "Button"
msgstr "Кнопка"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:263
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:39
#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:148
#: ../framework/includes/option-types/addable-option/class-fw-option-type-addable-option.php:20
#: ../framework/includes/option-types/addable-popup/class-fw-option-type-addable-popup.php:153
#: ../framework/includes/option-types/addable-box/class-fw-option-type-addable-box.php:186
msgid "Add"
msgstr "Добавить"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-head-template.php:27
msgid "Add Column"
msgstr "Добавить колонку"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/view.php:161
msgid "Add Row"
msgstr "Добавить строку"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:24
#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:25
msgid "Custom"
msgstr "Вручную"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:27
msgid "Locations"
msgstr "Местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:28
msgid "Add/Edit Location"
msgstr "Добавить/редактировать местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:31
msgid "Note: Please set location"
msgstr "Внимание: Пожалуйста, задайте местоположение"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:35
#: ../framework/extensions/events/class-fw-extension-events.php:187
msgid "Location"
msgstr "Местоположение"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:39
msgid "Location Title"
msgstr "Заголовок местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:40
msgid "Set location title"
msgstr "Введите заголовок местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:44
msgid "Location Description"
msgstr "Описание местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:45
msgid "Set location description"
msgstr "Введите описание местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:49
msgid "Location Url"
msgstr "Ссылка местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:50
msgid "Set page url (Ex: http://example.com)"
msgstr "Введите ссылку на страницу (Напр: http://example.com)"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:53
msgid "Location Image"
msgstr "Изображение местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:54
msgid "Add location image"
msgstr "Добавьте изображение местоположения"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:113
msgid "No location provider specified for map shortcode"
msgstr "Не указано провайдера местоположения для шорткода карты"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:115
msgid "Map Placeholder"
msgstr "Заполнитель для карты"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:122
#, php-format
msgid "Unknown location provider \"%s\" specified for map shortcode"
msgstr "Неизвестный провайдер местоположения \"%s\" заданный для шорткода карты"

#: ../framework/extensions/shortcodes/shortcodes/map/config.php:8
msgid "Map"
msgstr "Карта"

#: ../framework/extensions/shortcodes/shortcodes/map/config.php:9
msgid "Add a Map"
msgstr "Добавить карту"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:13
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:214
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:473
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:11
msgid "Population Method"
msgstr "Метод наполнения"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:14
msgid "Select map population method (Ex: events, custom)"
msgstr "Метод наполнения карты (напр., события, что-то другое)"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:24
msgid "Map Type"
msgstr "Тип карты"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:25
msgid "Select map type"
msgstr "Выберите тип карты"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:27
msgid "Roadmap"
msgstr "Дорожная карта"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:28
msgid "Terrain"
msgstr "Ландшафт"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:29
msgid "Satellite"
msgstr "Спутниковая"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:30
msgid "Hybrid"
msgstr "Гибридная"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:34
msgid "Map Height"
msgstr "Высота карты"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:35
msgid "Set map height (Ex: 300)"
msgstr "Задайте высоту карты (Напр: 300)"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:41
msgid "Disable zoom on scroll"
msgstr "Отключить зум при прокрутке"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:42
msgid "Prevent the map from zooming when scrolling until clicking on the map"
msgstr "Предотвратите масштабирование карты при прокрутке до момента клика по карте"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:76
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:65
msgid "Column"
msgstr "Колонка"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:120
#, php-format
msgid "Add a %s column"
msgstr "Добавьте %s колонку"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:12
msgid "Columns"
msgstr "Колонки"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:47
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:47
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:51
msgid "No Templates Saved"
msgstr "Нет сохраненных шаблонов"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:50
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:50
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:54
msgid "Load Template"
msgstr "Загрузить шаблон"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:91
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:91
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:94
msgid "Template Name"
msgstr "Название шаблона"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:92
msgid "Save Column"
msgstr "Сохранить колонку"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:93
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:93
msgid "Save as Template"
msgstr "Сохранить как шаблон"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:174
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:174
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:206
msgid "No Title"
msgstr "Без заголовка"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:6
msgid "Special Heading"
msgstr "Специальный заголовок"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:7
msgid "Add a Special Heading"
msgstr "Добавьте специальный заголовок"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:8
msgid "Heading Title"
msgstr "Заголовок"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:9
msgid "Write the heading title content"
msgstr "Введите текст заголовка"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:13
msgid "Heading Subtitle"
msgstr "Подзаголовок"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:14
msgid "Write the heading subtitle content"
msgstr "Введите текст подзаголовка"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:18
msgid "Heading Size"
msgstr "Размер заголовка"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:30
msgid "Centered"
msgstr "По центру"

#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:8
msgid "Team Member"
msgstr "Участник команды"

#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:9
msgid "Add a Team Member"
msgstr "Добавьте участника команды"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:7
msgid "Team Member Image"
msgstr "Изображения участника команды"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:12
msgid "Team Member Name"
msgstr "Имя участника команды"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:13
msgid "Name of the person"
msgstr "Имя человека"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:18
msgid "Team Member Job Title"
msgstr "Должность участника команды"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:19
msgid "Job title of the person."
msgstr "Название должности человека."

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:24
msgid "Team Member Description"
msgstr "Описание участника команды"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:25
msgid "Enter a few words that describe the person"
msgstr "Опишите человека несколькими словами"

#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:6
#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:8
#: ../framework/extensions/megamenu/class-fw-extension-megamenu.php:115
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:188
msgid "Icon"
msgstr "Иконка"

#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:7
msgid "Add an Icon"
msgstr "Добавьте иконку"

#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:13
msgid "Icon title"
msgstr "Название иконки"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:6
msgid "Icon Box"
msgstr "Блок с иконкой"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:7
msgid "Add an Icon Box"
msgstr "Добавьте блок с иконкой"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:8
msgid "Box Style"
msgstr "Стиль блока"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:10
msgid "Icon above title"
msgstr "Иконка над заголовком"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:11
msgid "Icon in line with title"
msgstr "Иконка на уровне с заголовком"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:16
msgid "Choose an Icon"
msgstr "Выберите иконку"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:20
msgid "Title of the Box"
msgstr "Заголовок блока"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:25
msgid "Enter the desired content"
msgstr "Введите необходимый контент"

#: ../framework/extensions/shortcodes/shortcodes/button/config.php:9
msgid "Add a Button"
msgstr "Добавьте кнопку"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:7
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:17
msgid "Button Label"
msgstr "Текст на кнопке"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:18
msgid "This is the text that appears on your button"
msgstr "Это текст, который появится на кнопке"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:23
msgid "Button Link"
msgstr "Ссылка на кнопке"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:14
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:24
msgid "Where should your button link to"
msgstr "Куда должна вести кнопка"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:20
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:38
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:30
msgid "Open Link in New Window"
msgstr "Открыть ссылку в новом окне"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:21
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:39
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:31
msgid "Select here if you want to open the linked page in a new window"
msgstr "Выберите Да, если хотите открывать страницу в новом окне"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:32
msgid "Button Color"
msgstr "Цвет кнопки"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:33
msgid "Choose a color for your button"
msgstr "Выберите цвет для кнопки"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:36
msgid "Default"
msgstr "Стандартный"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:37
msgid "Black"
msgstr "Черный"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:38
msgid "Blue"
msgstr "Синий"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:39
msgid "Green"
msgstr "Зеленый"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:40
msgid "Red"
msgstr "Красный"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:6
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:108
msgid "Video"
msgstr "Видео"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:7
msgid "Add a Video"
msgstr "Добавьте видео"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:8
#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:10
msgid "Media Elements"
msgstr "Элементы Мультимедиа"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:8
msgid "Insert Video URL"
msgstr "Вставьте ссылку на видео"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:9
#: ../framework/extensions/shortcodes/shortcodes/section/options.php:24
msgid "Insert Video URL to embed this video"
msgstr "Вставьте видео ссылку, чтобы встроить видео"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:13
msgid "Video Width"
msgstr "Ширина видео"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:14
msgid "Enter a value for the width"
msgstr "Введите значение ширины"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:19
msgid "Video Height"
msgstr "Высота видео"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:20
msgid "Enter a value for the height"
msgstr "Введите значение высоты"

#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:8
#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:16
msgid "Calendar"
msgstr "Календарь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:9
msgid "Add a Calendar"
msgstr "Добавьте календарь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:14
msgid "Select calendar population method (Ex: events, custom)"
msgstr "Выберите метод наполнения календаря (Напр: события, вручную)"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:23
msgid "Calendar Type"
msgstr "Тип календаря"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:24
msgid "Select calendar type"
msgstr "Выберите тип календаря"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:27
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:23
msgid "Daily"
msgstr "Ежедневно"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:28
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:22
msgid "Weekly"
msgstr "Еженедельно"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:29
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:21
msgid "Monthly"
msgstr "Ежемесячно"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:33
msgid "Start Week On"
msgstr "Неделя начинается с"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:34
msgid "Select first day of week"
msgstr "Выберите первый день недели "

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:37
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:110
msgid "Monday"
msgstr "Понедельник"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:38
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:109
msgid "Sunday"
msgstr "Суббота"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:29
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:56
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:82
#: ../framework/extensions/events/class-fw-extension-events.php:69
#: ../framework/extensions/events/class-fw-extension-events.php:74
#: ../framework/extensions/events/manifest.php:7
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:77
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:185
#: ../framework/core/components/extensions/manager/available-extensions.php:180
msgid "Events"
msgstr "События"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:30
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:59
msgid "Add/Edit Date & Time"
msgstr "Добавить/редактировать дату и время"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:33
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:63
msgid "Note: Please set start & end event datetime"
msgstr "Примечание: пожалуйста, установите время начала и конца события типа датавремя"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:37
msgid "Event Title"
msgstr "Заголовок события"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:38
msgid "Enter the event title"
msgstr "Введите заголовок события"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:42
msgid "Event URL"
msgstr "Ссылка события"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:43
msgid "Enter the event URL (Ex: http://your-domain.com/event)"
msgstr "Введите ссылку на событие (Напр: http://your-domain.com/event)"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:47
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:58
msgid "Date & Time"
msgstr "Дата и время"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:48
msgid "Enter the event date & time"
msgstr "Введите дату и время события"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:129
msgid "No events provider specified for calendar shortcode"
msgstr "Не указано провайдера события для шорткода календаря"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:138
#, php-format
msgid "Unknown events provider \"%s\" specified for calendar shortcode"
msgstr "Неизвестный провайдер события \"%s\" заданный для шорткода календаря"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:55
#: ../framework/extensions/events/class-fw-extension-events.php:68
#: ../framework/extensions/events/class-fw-extension-events.php:75
msgid "Event"
msgstr "Событие"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:57
#: ../framework/extensions/shortcodes/shortcodes/calendar/views/view.php:23
msgid "Today"
msgstr "Сегодня"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:66
#, php-format
msgid "Calendar: View %s not found"
msgstr "Календарь: Просмотр %s не найден"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:67
#, php-format
msgid "Calendar: Wrong date format %s. Should be either \"now\" or \"yyyy-mm-dd\""
msgstr "Календарь: Неверный формат даты %s. Должно быть или \"now\" или \"yyyy-mm-dd\""

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:69
msgid "Calendar: Event URL is not set"
msgstr "Календарь: ссылка на событие не задана"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:70
#, php-format
msgid ""
"Calendar: Wrong navigation direction %s. Can be only \"next\" or \"prev\" or"
" \"today\""
msgstr "Календарь: Неверное направление навигации %s. Может быть только \"next\" или \"prev\" или \"today\""

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:72
msgid ""
"Calendar: Time split parameter should divide 60 without decimals. Something "
"like 10, 15, 30"
msgstr "Календарь: Параметр деления времени должен поделить 60 без дробных значений. Например 10, 15, 30"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:74
msgid "No events in this day."
msgstr "Никаких событий в этот день."

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:77
#, php-format
msgid "week %s of %s"
msgstr "неделя %s из %s"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:79
msgid "Week "
msgstr "Неделя"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:80
msgid "All day"
msgstr "Все дни"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:81
msgid "Time"
msgstr "Время"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:83
msgid "Ends before timeline"
msgstr "Заканчивается до обозначенного срока"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:84
msgid "Starts after timeline"
msgstr "Начинается после обозначенного срока"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:85
msgid "January"
msgstr "Январь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:86
msgid "February"
msgstr "Февраль"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:87
msgid "March"
msgstr "Март"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:88
msgid "April"
msgstr "Апрель"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:89
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:101
msgid "May"
msgstr "Май"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:90
msgid "June"
msgstr "Июнь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:91
msgid "July"
msgstr "Июль"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:92
msgid "August"
msgstr "Август"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:93
msgid "September"
msgstr "Сентябрь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:94
msgid "October"
msgstr "Октябрь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:95
msgid "November"
msgstr "Ноябрь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:96
msgid "December"
msgstr "Декабрь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:97
msgid "Jan"
msgstr "Янв"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:98
msgid "Feb"
msgstr "Фев"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:99
msgid "Mar"
msgstr "Мар"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:100
msgid "Apr"
msgstr "Апр"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:102
msgid "Jun"
msgstr "Июнь"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:103
msgid "Jul"
msgstr "Июль"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:104
msgid "Aug"
msgstr "Авг"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:105
msgid "Sep"
msgstr "Сент"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:106
msgid "Oct"
msgstr "Окт"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:107
msgid "Nov"
msgstr "Ноя"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:108
msgid "Dec"
msgstr "Дек"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:111
msgid "Tuesday"
msgstr "Вторник"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:112
msgid "Wednesday"
msgstr "Среда"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:113
msgid "Thursday"
msgstr "Четверг"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:114
msgid "Friday"
msgstr "Пятница"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:115
msgid "Saturday"
msgstr "Суббота"

#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:7
msgid "Add an Image"
msgstr "Добавьте изображение"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:8
msgid "Choose Image"
msgstr "Выберите изображение"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:16
msgid "Width"
msgstr "Ширина"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:17
msgid "Set image width"
msgstr "Задайте ширину изображения"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:22
#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:24
msgid "Height"
msgstr "Высота"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:23
msgid "Set image height"
msgstr "Задайте высоту изображения"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:33
msgid "Image Link"
msgstr "Ссылка на изображение"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:34
msgid "Where should your image link to?"
msgstr "К чему должно привязываться изображение?"

#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:8
msgid "Notification"
msgstr "Уведомление"

#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:9
msgid "Add a Notification Box"
msgstr "Добавьте блок с уведомлением"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:7
msgid "Message"
msgstr "Сообщение"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:8
msgid "Notification message"
msgstr "Уведомительное сообщение"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:10
msgid "Message!"
msgstr "Сообщение!"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:13
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:444
msgid "Type"
msgstr "Тип"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:14
msgid "Notification type"
msgstr "Тип уведомления"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:17
msgid "Congratulations"
msgstr "Поздравление"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:18
msgid "Information"
msgstr "Информация"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:19
msgid "Alert"
msgstr "Предупреждение"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:20
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:456
msgid "Error"
msgstr "Ошибка"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:9
msgid "Congratulations!"
msgstr "Поздравление!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:13
msgid "Information!"
msgstr "Информация!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:17
msgid "Alert!"
msgstr "Предупреждение!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:21
msgid "Error!"
msgstr "Ошибка!"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:8
msgid "Widget Area"
msgstr "Область виджета"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:9
msgid "Add a Widget Area"
msgstr "Добавьте область виджета"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/options.php:5
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:5
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:7
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:40
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:91
msgid "Sidebar"
msgstr "Сайдбар"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:8
msgid "Call To Action"
msgstr "Призыв к действию"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:9
msgid "Add a Call to Action"
msgstr "Добавьте Призыв к действию"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:9
msgid "This can be left blank"
msgstr "Это поле можно оставить пустым"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:14
msgid "Enter some content for this Info Box"
msgstr "Добавьте контент в этот Инфо блок"

#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:8
msgid "Text Block"
msgstr "Текстовый блок"

#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:9
msgid "Add a Text Block"
msgstr "Добавьте текстовый блок"

#: ../framework/extensions/shortcodes/shortcodes/text-block/options.php:11
msgid "Enter some content for this texblock"
msgstr "Добавьте контент в этот текстовый блок"

#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:6
msgid "Divider"
msgstr "Разделитель"

#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:7
msgid "Add a Divider"
msgstr "Добавьте разделитель"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:13
msgid "Ruler Type"
msgstr "Тип разделителя"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:14
msgid "Here you can set the styling and size of the HR element"
msgstr "Здесь вы можете задать стиль и размер элемента HR"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:16
msgid "Line"
msgstr "Линия"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:17
msgid "Whitespace"
msgstr "Белое пространство"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:25
msgid ""
"How much whitespace do you need? Enter a pixel value. Positive value will "
"increase the whitespace, negative value will reduce it. eg: '50', '-25', "
"'200'"
msgstr "Сколько пустого пространства необходимо? Введите значение в пикселях. Положительное значение будет увеличиваться пустое пространство, отрицательное значение будет его уменьшать. например: '50', '-25', '200'"

#: ../framework/extensions/shortcodes/shortcodes/section/config.php:6
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:23
msgid "Section"
msgstr "Секция"

#: ../framework/extensions/shortcodes/shortcodes/section/config.php:7
msgid "Add a Section"
msgstr "Добавить секцию"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:7
msgid "Full Width"
msgstr "На всю ширину"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:11
msgid "Background Color"
msgstr "Фоновый цвет"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:12
msgid "Please select the background color"
msgstr "Выберите фоновый цвет"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:16
msgid "Background Image"
msgstr "Фоновое изображение"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:17
msgid "Please select the background image"
msgstr "Выберите фоновое изображение"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:23
msgid "Background Video"
msgstr "Фоновое видео"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:12
msgid "Sections"
msgstr "Секции"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:92
msgid "Save Section"
msgstr "Сохранить секцию"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:24
msgid "Creates a section"
msgstr "Создает секцию"

#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:9
msgid "Add some Tabs"
msgstr "Добавьте табы"

#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:9
msgid "Add/Edit Tab"
msgstr "Добавить/редактировать таб"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcode.php:194
#, php-format
msgid "No default view (views/view.php) found for shortcode: %s"
msgstr "Не найден шаблон по умолчанию (views/view.php) для шорткода: %s"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:203
#, php-format
msgid "Shortcode \"%s\" from %s was already defined at %s"
msgstr "Шорткод \"%s\" с %s уже был определен в %s"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:264
#, php-format
msgid "Class file found for shortcode %s but no class %s found"
msgstr "Файл класса найден для шорткода %s но не найден класс %s"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:269
#, php-format
msgid "The class %s must extend from FW_Shortcode"
msgstr "Класс %s должен расширяться от FW_Shortcode"

#: ../framework/extensions/builder/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:204
msgid "Builder"
msgstr "Построитель"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:306
msgid "Full Screen"
msgstr "Во весь экран"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:307
msgid "Exit Full Screen"
msgstr "Выйти из полноэкранного режима"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:334
msgid "Undo"
msgstr "Отменить"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:335
msgid "Redo"
msgstr "Вернуть"

#: ../framework/extensions/builder/includes/option-types/builder/includes/fullscreen.php:81
msgid "Preview Changes"
msgstr "Предварительный просмотр"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/class-fw-ext-builder-templates.php:119
msgid "Templates"
msgstr "Шаблоны"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:12
msgid "Full Templates"
msgstr "Целые шаблоны"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:61
msgid "Save Full Template"
msgstr "Сохраните шаблон"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:95
msgid "Save Builder Template"
msgstr "Сохраните шаблон конструктора"

#: ../framework/extensions/social/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:252
msgid "Social"
msgstr "Социальные сети"

#: ../framework/extensions/social/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:253
msgid ""
"Use this extension to configure all your social related APIs. Other "
"extensions will use the Social extension to connect to your social accounts."
msgstr "Используйте это расширение для настройки всех ключей API социальных сетей. Другие расширения смогут использовать расширение Социальные сети для подключения к своим социальным аккаунтам."

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:62
#: ../framework/core/components/backend.php:584
msgid "Facebook"
msgstr "Facebook"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:66
#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:33
msgid "API Settings"
msgstr "Настройки API"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:72
msgid "App ID/API Key:"
msgstr "ID Приложения/API Ключ:"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:73
msgid "Enter Facebook App ID / API Key."
msgstr "Введите Facebook ID Приложения/API Ключ."

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:78
msgid "App Secret:"
msgstr "Секрет Приложения:"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:79
msgid "Enter Facebook App Secret."
msgstr "Введите Facebook Секрет Приложения."

#: ../framework/extensions/social/extensions/social-facebook/manifest.php:7
#: ../framework/extensions/social/extensions/social-facebook/manifest.php:8
msgid "Social Facebook"
msgstr "Социальная сеть Facebook"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:29
#: ../framework/core/components/backend.php:592
msgid "Twitter"
msgstr "Twitter"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:39
msgid "Consumer Key"
msgstr "Ключ Потребителя"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:40
msgid "Enter Twitter Consumer Key."
msgstr "Введите Twitter Ключ Потребителя."

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:45
msgid "Consumer Secret"
msgstr "Секрет Потребителя"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:46
msgid "Enter Twitter App Secret."
msgstr "Введите Twitter Секрет Приложения."

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:51
msgid "Access Token"
msgstr "Токен Доступа"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:52
msgid "Enter Twitter Access Token."
msgstr "Введите Twitter Токен Доступа."

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:57
msgid "Access Token Secret"
msgstr "Секрет Токена Доступа"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:58
msgid "Enter Twitter Access Token Secret."
msgstr "Введите Twitter Секрет Токена Доступа"

#: ../framework/extensions/social/extensions/social-twitter/manifest.php:7
#: ../framework/extensions/social/extensions/social-twitter/manifest.php:8
msgid "Social Twitter"
msgstr "Социальная сеть Twitter"

#: ../framework/extensions/forms/class-fw-extension-forms.php:112
#: ../framework/extensions/forms/class-fw-extension-forms.php:123
#: ../framework/extensions/forms/class-fw-extension-forms.php:131
#: ../framework/extensions/forms/class-fw-extension-forms.php:142
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:102
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:114
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:124
msgid "Unable to process the form"
msgstr "Не удается обработать форму"

#: ../framework/extensions/forms/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:228
msgid "Forms"
msgstr "Формы"

#: ../framework/extensions/forms/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:229
msgid ""
"This extension adds the possibility to create a contact form. Use the drag &"
" drop form builder to create any contact form you'll ever want or need."
msgstr "Это расширение дает возможность создавать контактные формы. Используйте конструктор форм с функцией перетягивания для создания любой контактной формы, которая вам необходима."

#: ../framework/extensions/forms/extensions/contact-forms/manifest.php:5
msgid "Contact Forms"
msgstr "Контактные формы"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:134
msgid "Invalid destination email (please contact the site administrator)"
msgstr "Неверный адрес получателя (пожалуйста обратитесь к администратору сайта)"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:158
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:77
msgid "Message sent!"
msgstr "Сообщение отправлено!"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:164
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:85
msgid "Oops something went wrong."
msgstr "Что-то пошло не так."

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:190
msgid "Please configure the {mailer_link} extension."
msgstr "Настройте расширение {mailer_link}."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:8
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:107
msgid "Contact form"
msgstr "Контактная форма"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:9
msgid "Build contact forms"
msgstr "Создайте контактные формы"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:15
msgid "Form Fields"
msgstr "Поля формы"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:39
#: ../framework/core/components/extensions/manager/views/extension.php:82
#: ../framework/core/components/extensions/manager/views/extension-page-header.php:31
msgid "Settings"
msgstr "Настройки"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:42
msgid "Options"
msgstr "Опции"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:53
msgid "Subject Message"
msgstr "Тема"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:54
msgid "This text will be used as subject message for the email"
msgstr "Этот текст будет использован в качестве темы сообщения   в электронном письме"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:55
msgid "New message"
msgstr "Новое сообщение"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:64
msgid "Submit Button"
msgstr "Кнопка Отправить"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:65
msgid "This text will appear in submit button"
msgstr "Этот текст будет отображаться в кнопке Отправить"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:66
msgid "Send"
msgstr "Отправить"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:75
msgid "Success Message"
msgstr "Сообщение об успешной отправке"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:76
msgid "This text will be displayed when the form will successfully send"
msgstr "Этот текст будет отображаться, когда форма будет успешно отправлена"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:83
msgid "Failure Message"
msgstr "Сообщение о невыполнении"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:84
msgid "This text will be displayed when the form will fail to be sent"
msgstr "Этот текст будет отображаться, когда форма не сможет быть отправлена"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:94
msgid "Email To"
msgstr "Кому"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:95
msgid "We recommend you to use an email that you verify often"
msgstr "Мы рекомендуем использовать электронный адрес, который вы часто проверяете"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:96
msgid "The form will be sent to this email address."
msgstr "Форма будет отправлена на этот электронный адрес."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:52
msgid "Contact Form"
msgstr "Контактная форма"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:54
msgid "Configure Mailer"
msgstr "Настройте мейлер"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:108
msgid "Add a Contact Form"
msgstr "Добавьте контактную форму"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:9
msgid "Note that the type can't be changed later."
msgstr "Внимание, тип нельзя будет изменить позже."

#: ../framework/extensions/forms/views/backend/submit-box-add.php:11
msgid ""
"You will need to create a new form in order to have a different form type."
msgstr "Вам нужно будет создать новую форму, чтобы выбрать другой тип формы."

#: ../framework/extensions/forms/views/backend/submit-box-add.php:20
#: ../framework/extensions/forms/views/backend/submit-box-edit.php:16
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:14
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:31
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-error.php:8
msgid "Delete Permanently"
msgstr "Удалить Навсегда"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:22
#: ../framework/extensions/forms/views/backend/submit-box-edit.php:18
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:16
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:33
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-error.php:10
msgid "Move to Trash"
msgstr "Переместить в корзину"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:33
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:35
msgid "Create"
msgstr "Создать"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:24
msgid "Add a Recaptcha field"
msgstr "Добавьте поле Recaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:26
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:78
msgid "Recaptcha"
msgstr "Recaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:59
msgid "Set site key"
msgstr "Введите ключ сайта"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:60
msgid "Set secret key"
msgstr "Введите секретный ключ"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:76
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:81
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:81
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:76
msgid "Enter field label (it will be displayed on the web site)"
msgstr "Введите название поля (оно будет отображаться на сайте)"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:133
msgid "Security Code"
msgstr "Код безопасности"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:147
msgid "Could not validate the form"
msgstr "Не удалось проверить форму"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:148
msgid "Please fill the recaptcha"
msgstr "Введите recaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:37
msgid "Site key"
msgstr "Ключ сайта"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:38
msgid "Your website key. More on how to configure ReCaptcha"
msgstr "Ключ вашего сайта. Больше о том, как настроить ReCaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:42
msgid "Secret key"
msgstr "Секретный ключ"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:43
msgid "Your secret key. More on how to configure ReCaptcha"
msgstr "Ваш секретный ключ. Больше о том, как настроить ReCaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:18
msgid "Add a Paragraph Text"
msgstr "Добавьте текст сообщения"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:20
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:48
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:77
msgid "Paragraph Text"
msgstr "Текст сообщения"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:49
msgid "Toggle mandatory field"
msgstr "Включите обязательное поле"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:83
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:83
msgid "Mandatory Field"
msgstr "Поле обязательное для заполнения"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:84
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:84
msgid "Make this field mandatory?"
msgstr "Сделать это поле обязательным для заполнения?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:98
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:98
msgid "Placeholder"
msgstr "Заполнитель"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:99
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:99
msgid "This text will be used as field placeholder"
msgstr "Этот текст будет использоваться как заполнитель поля"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:105
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:107
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:107
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:105
msgid "Default Value"
msgstr "Значение по умолчанию"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:106
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:108
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:108
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:106
msgid "This text will be used as field default value"
msgstr "Этот текст будет использоваться как значение по умолчанию"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:126
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:128
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:126
msgid "Restrictions"
msgstr "Ограничения"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:127
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:127
msgid "Set characters or words restrictions for this field"
msgstr "Задайте ограничения символов или слов для этого поля"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:131
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:131
msgid "Characters"
msgstr "Символы"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:132
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:132
msgid "Words"
msgstr "Слова"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:140
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:154
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:142
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:156
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:140
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:154
msgid "Min"
msgstr "Мин"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:141
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:155
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:143
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:157
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:141
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:155
msgid "Minim value"
msgstr "Минимальное значение"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:146
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:160
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:148
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:162
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:146
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:160
msgid "Max"
msgstr "Макс"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:147
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:161
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:149
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:163
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:147
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:161
msgid "Maxim value"
msgstr "Максимальное значение"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:178
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:180
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:121
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:137
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:137
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:114
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:121
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:178
msgid "Instructions for Users"
msgstr "Инструкции для пользователей"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:179
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:181
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:122
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:138
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:138
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:115
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:122
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:179
msgid "The users will see these instructions in the tooltip near the field"
msgstr "Пользователи будут видеть эти инструкции во всплывающей подсказке рядом с полем"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:331
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:368
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:230
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:244
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:244
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:210
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:217
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:332
msgid "The {label} field is required"
msgstr "Поле {label} обязательно"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:336
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:337
#, php-format
msgid "The {label} field must contain minimum %d character"
msgstr "Поле {label} должно содержать минимум %d символ"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:341
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:342
#, php-format
msgid "The {label} field must contain minimum %d characters"
msgstr "Поле {label} должно содержать минимум %d символов"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:346
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:347
#, php-format
msgid "The {label} field must contain maximum %d character"
msgstr "Поле {label} должно содержать максимум %d символ"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:351
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:352
#, php-format
msgid "The {label} field must contain maximum %d characters"
msgstr "Поле {label} должно содержать максимум %d символов"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:356
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:357
#, php-format
msgid "The {label} field must contain minimum %d word"
msgstr "Поле {label} должно содержать минимум %d слово"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:361
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:362
#, php-format
msgid "The {label} field must contain minimum %d words"
msgstr "Поле {label} должно содержать минимум %d слов"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:366
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:367
#, php-format
msgid "The {label} field must contain maximum %d word"
msgstr "Поле {label} должно содержать максимум %d слово"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:371
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:372
#, php-format
msgid "The {label} field must contain maximum %d words"
msgstr "Поле {label} должно содержать максимум %d слов"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:20
msgid "Add a Number field"
msgstr "Добавьте поле Число"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:79
msgid "Number"
msgstr "Число"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:129
msgid "Set digits or values restrictions of this field"
msgstr "Задайте ограничения цифр или значений этого поля"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:133
msgid "Digits"
msgstr "Цифры"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:134
msgid "Value"
msgstr "Значение"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:363
msgid "The {label} field must be a valid number"
msgstr "Поле {label} должно быть действительным числом"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:373
#, php-format
msgid "The {label} field must have minimum %d digit"
msgstr "Поле {label} должно содержать минимум %d цифру"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:378
#, php-format
msgid "The {label} field must have minimum %d digits"
msgstr "Поле {label} должно содержать минимум %d цифр"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:383
#, php-format
msgid "The {label} field must have maximum %d digit"
msgstr "Поле {label} должно содержать максимум %d цифру"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:388
#, php-format
msgid "The {label} field must have maximum %d digits"
msgstr "Поле {label} должно содержать максимум %d цифр"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:393
#, php-format
msgid "The {label} field minimum value must be %s"
msgstr "Минимальное значение поля {label} должно быть %s"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:398
#, php-format
msgid "The {label} field maximum value must be %s"
msgstr "Максимальное значение поля {label} должно быть %s"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:18
msgid "Add a Dropdown"
msgstr "Добавьте выпадающий список"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:79
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:164
msgid "Dropdown"
msgstr "Выпадающий список"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:103
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:103
msgid "Choices"
msgstr "Варианты"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:104
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:104
msgid "Add choice"
msgstr "Добавить вариант"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:110
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:113
msgid "Randomize"
msgstr "Перемешать"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:111
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:114
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:114
msgid "Do you want choices to be displayed in random order?"
msgstr "Вы хотите отображать варианты в случайном порядке?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:235
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:249
msgid "{label}: Submitted data contains not existing choice"
msgstr "{label}: Отправленные данные содержат несуществующий вариант"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:38
msgid "Edit Title"
msgstr "Редактировать заголовок"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:39
msgid "Edit Subtitle"
msgstr "Редактировать подзаголовок"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:57
msgid "The title will be displayed on contact form header"
msgstr "Заголовок будет отображаться в шапке контактной формы "

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:62
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:7
msgid "Subtitle"
msgstr "Подзаголовок"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:63
msgid "The form header subtitle text"
msgstr "Текст подзаголовка в шапке формы"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:18
msgid "Add a Single Choice field"
msgstr "Добавьте поле единичного выбора"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:56
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:56
msgid "{x} More"
msgstr "{x} Больше"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:113
msgid "Randomize?"
msgstr "Перемешать?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:124
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:124
msgid "Field Layout"
msgstr "Макет поля"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:125
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:125
msgid "Select choice display layout"
msgstr "Выберите макет отображения выбора"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:127
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:127
msgid "One column"
msgstr "Одна колонка"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:128
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:128
msgid "Two columns"
msgstr "Две колонки"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:129
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:129
msgid "Three columns"
msgstr "Три колонки"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:130
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:130
msgid "Side by side"
msgstr "Бок о бок"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:18
msgid "Add a Multiple Choices field"
msgstr "Добавьте поле множественного выбора"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:249
msgid "{label}: Submitted data contains not existing choices"
msgstr "{label}: Отправленные данные содержат несуществующие варианты"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:18
msgid "Add an Email field"
msgstr "Добавьте поле электронного адреса"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:79
msgid "Email"
msgstr "Электронный адрес"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:215
msgid "The {label} field must contain a valid email"
msgstr "{label} поле должно содержать действительный адрес электронной почты"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:18
msgid "Add a Website field"
msgstr "Добавьте поле вебсайта"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:79
msgid "Website"
msgstr "Вебсайт"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:222
msgid "The {label} field must be a valid website name"
msgstr "{label} поле должно содержать действительное имя сайта"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:18
msgid "Add a Single Line Text"
msgstr "Добавьте текст одиночной строки"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:20
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:47
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:77
msgid "Single Line Text"
msgstr "Текст одиночной строки"

#: ../framework/extensions/breadcrumbs/settings-options.php:17
msgid "Text for Homepage"
msgstr "Текст для домашней страницы"

#: ../framework/extensions/breadcrumbs/settings-options.php:18
msgid "The homepage anchor will have this text"
msgstr "Якорь домашней страницы будет содержать этот текст"

#: ../framework/extensions/breadcrumbs/settings-options.php:23
msgid "Text for Blog Page"
msgstr "Текст для страницы блога"

#: ../framework/extensions/breadcrumbs/settings-options.php:24
msgid ""
"The blog page anchor will have this text. In case homepage will be set as "
"blog page, will be taken the homepage text"
msgstr "Якорь страницы блога будет содержать этот текст. Если главная страница будет выбрана как страница блога, будет применяться текст главной страницы"

#: ../framework/extensions/breadcrumbs/settings-options.php:29
msgid "Text for 404 Page"
msgstr "Текст для страницы 404"

#: ../framework/extensions/breadcrumbs/settings-options.php:30
msgid "The 404 anchor will have this text"
msgstr "Якорь 404 будет содержать этот текст"

#: ../framework/extensions/breadcrumbs/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:108
msgid "Breadcrumbs"
msgstr "\"Хлебные крошки\""

#: ../framework/extensions/breadcrumbs/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:109
msgid ""
"Creates a simplified navigation menu for the pages that can be placed "
"anywhere in the theme. This will make navigating the website much easier."
msgstr "Создает упрощенное меню навигации для страниц, которая может быть размещена в любом месте в теме. Это сделает навигацию по сайту намного проще."

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:11
msgid "404 Not found"
msgstr "404 Не найдено"

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:124
msgid "Searching for:"
msgstr "Поиск:"

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:248
#: ../framework/includes/option-types/multi-select/class-fw-option-type-multi-select.php:472
msgid "No title"
msgstr "Без названия"

#: ../framework/extensions/events/class-fw-extension-events.php:89
msgid "Create a event item"
msgstr "Создать событие"

#: ../framework/extensions/events/class-fw-extension-events.php:186
msgid "Date"
msgstr "Дата"

#: ../framework/extensions/events/class-fw-extension-events.php:204
msgid "Event Options"
msgstr "Настройки события"

#: ../framework/extensions/events/class-fw-extension-events.php:310
msgid "Multi Interval Event"
msgstr "Регулярно проводимое событие"

#: ../framework/extensions/events/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:181
msgid ""
"This extension adds a fully fledged Events module to your theme. It comes "
"with built in pages that contain a calendar where events can be added."
msgstr "Это расширение добавляет полноценный модуль событий в вашу тему. Он поставляется со встроенными страницами, содержащими календарь, в котором могут быть добавлены события."

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:81
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:189
msgid "Event Categories"
msgstr "Категории событий"

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:82
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:190
msgid "Select an event category"
msgstr "Выберите категорию событий"

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:84
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:192
msgid "All Events"
msgstr "Все события"

#: ../framework/extensions/events/extensions/events-tags/manifest.php:7
msgid "Event-search-tags"
msgstr "Событие - Поиск - Тег"

#: ../framework/extensions/events/extensions/events-tags/manifest.php:8
msgid "Connect extension event with shortcodes map & calendar"
msgstr "Подключение расширения события с шорткодом карта и календарь"

#: ../framework/extensions/events/views/content.php:16
msgid "Google Calendar"
msgstr "Календарь Google"

#: ../framework/extensions/events/views/content.php:17
msgid "Ical Export"
msgstr "Экспорт Ical"

#: ../framework/extensions/events/views/content.php:20
msgid "Start"
msgstr "Старт"

#: ../framework/extensions/events/views/content.php:21
msgid "End"
msgstr "Конец"

#: ../framework/extensions/events/views/content.php:25
msgid "Speakers"
msgstr "Говорящая"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:37
msgid "Event Location"
msgstr "Место проведения события"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:39
msgid "Where does the event take place?"
msgstr "Где состоится событие?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:43
msgid "All Day Event?"
msgstr "Событие на целый день?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:44
msgid "Is your event an all day event?"
msgstr "Это ваше мероприятие, событие на весь день?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:67
msgid "Start & End of Event"
msgstr "Время начала и конца события"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:68
msgid "Set start and end events datetime"
msgstr "Установить время начала и окончания события типа датавремя"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:97
msgid "Associated User"
msgstr "Пользователь связанный с событием"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:99
msgid "Link this event to a specific user"
msgstr "Привязать данное событие к определенному пользователю"

#: ../framework/extensions/sidebars/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:132
msgid "Sidebars"
msgstr "Боковые панели"

#: ../framework/extensions/sidebars/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:133
msgid ""
"Brings a new layer of customization freedom to your website by letting you "
"add more than one sidebar to a page, or different sidebars on different "
"pages."
msgstr "Дает новый уровень свободы настройки вашего веб-сайта, позволяя добавить больше чем одну боковую панель на страницу, или разные боковые панели на разных страницах."

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:131
msgid "No matches found"
msgstr "Совпадений не найдено"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:139
msgid "Do you realy want to change without saving?"
msgstr "Вы действительно хотите перейти без сохранения?"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:141
msgid "Missing ID. Check that you provided all mandatory data."
msgstr "Отсутствует ID. Убедитесь, что вы указали все обязательные данные."

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:142
#: ../framework/extensions/sidebars/views/backend-main-view.php:27
msgid "Created"
msgstr "Создан"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:143
msgid "(For Grouped Pages)"
msgstr "(Для сгруппированных страниц)"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:144
msgid "(For Specific Pages)"
msgstr "(Для определенных страниц)"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:145
msgid "No sidebar name specified"
msgstr "Не указано имя сайдбара"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:146
msgid "Sidebar Name"
msgstr "Имя сайдбара"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:147
msgid "New Sidebar"
msgstr "Новый сайдбар"

#: ../framework/extensions/sidebars/views/frontend-no-widgets.php:5
msgid "Widgets Page"
msgstr "Страница с виджетами"

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:5
msgid "For specific"
msgstr "Для определенной"

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:23
msgid "Type to search ..."
msgstr "Введите для поиска ..."

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:30
msgid "Search for a specific page you want to set a sidebar for"
msgstr "Поиск определенной страницы, для которой вы хотите задать сайдбар"

#: ../framework/extensions/sidebars/views/backend-tab-grouped.php:6
msgid "For group"
msgstr "Для группы"

#: ../framework/extensions/sidebars/views/backend-tab-grouped.php:25
msgid "Select group of pages you want to set a sidebar for."
msgstr "Выберите группу страниц, для которых вы хотите задать сайдбар."

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:18
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:60
msgid "Choose the position for your sidebar(s)"
msgstr "Выберите положение для сайдбара(ов)"

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:43
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:40
msgid "Add Sidebar"
msgstr "Добавить сайдбар"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:4
msgid "Sidebars for"
msgstr "Сайдбары для"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Specific Page"
msgstr "Для определенной страницы"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Grouped Page"
msgstr "Для сгруппированных страниц"

#: ../framework/extensions/sidebars/views/backend-main-view.php:11
#: ../framework/extensions/sidebars/views/backend-main-view.php:15
msgid "Manage Sidebars"
msgstr "Управление сайдбарами"

#: ../framework/extensions/sidebars/views/backend-main-view.php:18
msgid ""
"Use this section to create and/or set different sidebar(s) for different "
"page(s)"
msgstr "Используйте этот раздел, чтобы создавать и/или задавать сайдбар(ы) для разных страниц."

#: ../framework/extensions/sidebars/views/backend-main-view.php:24
msgid "For Grouped Pages"
msgstr "Для сгруппированных страниц"

#: ../framework/extensions/sidebars/views/backend-main-view.php:25
msgid "For Specific Pages"
msgstr "Для определенных страниц"

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:32
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:121
msgid "Select sidebar you wish to replace."
msgstr "Выберите сайдбар, который вы хотите заменить."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:241
msgid "No sidebar name specified."
msgstr "Не указано имя сайдбара."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:270
msgid "Dynamic sidebar doesn't exixt"
msgstr "Динамический сайдбар не существует"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:277
msgid ""
"The placeholder can't be deleted because it is used in one of sidebars "
"below."
msgstr "Заполнитель не может быть удален, так как используется в одном из сайдбаров ниже."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:280
msgid ""
"Please replace it first so that you will not have visual gaps in your "
"layout."
msgstr "Пожалуйста, замените его сначала, чтобы не было визуальных зазоров на вашей странице."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:293
msgid "Successfully removed"
msgstr "Успешно удален"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:543
msgid "Default for all pages"
msgstr "По умолчанию для всех страниц"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:613
msgid " (no title)"
msgstr "(без названия)"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:780
msgid "Error: Type or sub_type error"
msgstr "Ошибка типа или под_типа"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:838
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:869
msgid "Error: Sidebars not set"
msgstr "Ошибка: Сайдбары не заданы"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:865
msgid "Error: Position doesn't exists. Please check config file."
msgstr "Ошибка: Позиция не существует. Проверьте конфигурационный файл."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:55
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:253
msgid "Page"
msgstr "Страница"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:56
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:153
msgid "Pages"
msgstr "Страницы"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:61
msgid "Portfolio Project"
msgstr "Проект портфолио"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:62
msgid "Portfolio Projects"
msgstr "Проекты портфолио"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:73
msgid "Blog Category"
msgstr "Рубрика"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:79
msgid "Portfolio Category"
msgstr "Категория портфолио"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:85
msgid "Home Page"
msgstr "Домашняя страница"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:90
msgid "Search Page"
msgstr "Страница поиска"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:95
msgid "404 Page"
msgstr "Страница 404"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:100
msgid "Author Page"
msgstr "Страница автора"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:105
msgid "Archive Page"
msgstr "Архивная страница"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:149
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:308
msgid "All Pages"
msgstr "Все страницы"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:184
msgid "Others"
msgstr "Другие"

#: ../framework/extensions/megamenu/manifest.php:7
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:64
#: ../framework/core/components/extensions/manager/available-extensions.php:60
msgid "Mega Menu"
msgstr "Мега Меню"

#: ../framework/extensions/megamenu/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:61
msgid ""
"The Mega Menu extension adds a user-friendly drop down menu that will let "
"you easily create highly customized menu configurations."
msgstr "Mega Menu расширение, добавляет удобное выпадающее меню, которое позволит вам легко создавать персонализованные меню конфигурации."

#: ../framework/extensions/megamenu/class-fw-extension-megamenu.php:58
msgid "Select Icon"
msgstr "Выбрать иконку"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:44
#, php-format
msgid "%s (Invalid)"
msgstr "%s (Неверный)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:48
#, php-format
msgid "%s (Pending)"
msgstr "%s (В ожидании)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:61
msgid "sub item"
msgstr "подпункт "

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:97
msgid "Edit Menu Item"
msgstr "Редактировать пункт меню"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:108
msgid "URL"
msgstr "Ссылка"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:117
msgid "Navigation Label"
msgstr "Название пункта меню"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:125
msgid "Title Attribute"
msgstr "Атрибут заголовка"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:134
msgid "Open link in a new window/tab"
msgstr "Открывать ссылку в новом окне/вкладке"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:141
msgid "CSS Classes (optional)"
msgstr "CSS классы (опционально)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:149
msgid "Link Relationship (XFN)"
msgstr "Ссылка Отношения (XFN)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:157
msgid "Mega Menu Column Title"
msgstr "Название колонки мега меню"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:158
msgid "Item Title"
msgstr "Название пункта"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:166
msgid "Hide"
msgstr "Спрятать"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:172
msgid "This column should start a new row"
msgstr "Эта колонка должна начинаться с нового ряда"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:178
msgid "Description (HTML)"
msgstr "Описание (HTML)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:181
msgid ""
"The description will be displayed in the menu if the current theme supports "
"it."
msgstr "Описание будет отображаться в меню, если данная тема поддерживает его."

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:190
msgid "Add Icon"
msgstr "Добавить иконку"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:191
msgid "Edit Icon"
msgstr "Редактировать иконку"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:205
msgid "Use as Mega Menu"
msgstr "Использовать как мега меню"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:211
msgid "Move"
msgstr "Переместить"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:212
msgid "Up one"
msgstr "На один выше"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:213
msgid "Down one"
msgstr "На один ниже"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:216
msgid "To the top"
msgstr "Наверх"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:225
#, php-format
msgid "Original: %s"
msgstr "Оригинал: %s"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:239
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:60
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2282
msgid "Cancel"
msgstr "Отменить"

#: ../framework/extensions/backups/class-fw-extension-backups.php:299
#: ../framework/extensions/backups/class-fw-extension-backups.php:366
msgid "File not specified"
msgstr "Файл не определен"

#: ../framework/extensions/backups/class-fw-extension-backups.php:399
#: ../framework/extensions/backups/class-fw-extension-backups.php:400
#: ../framework/core/components/extensions/manager/available-extensions.php:156
msgid "Backup"
msgstr "Резервная копия"

#: ../framework/extensions/backups/class-fw-extension-backups.php:554
#: ../framework/extensions/backups/class-fw-extension-backups.php:568
msgid "Access Denied"
msgstr "В доступе отказано"

#: ../framework/extensions/backups/class-fw-extension-backups.php:561
msgid "Archive not found"
msgstr "Архив не найден"

#: ../framework/extensions/backups/class-fw-extension-backups.php:575
#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:202
msgid "Failed to open file"
msgstr "Не удалось открыть файл"

#: ../framework/extensions/backups/helpers.php:37
msgid "Invalid JSON response"
msgstr "Неверный ответ JSON"

#: ../framework/extensions/backups/helpers.php:44
msgid ""
"HTTP Loopback Connections are not enabled on this server. If you need to "
"contact your web host, tell them that when PHP tries to connect back to the "
"site at the URL `{url}` and it gets the error `{error}`. There may be a "
"problem with the server configuration (eg local DNS problems, mod_security, "
"etc) preventing connections from working properly."
msgstr "Соединения HTTP Loopback не активированы на данном сервере. Если вы собираетесь связаться с вашим хостингом, скажите им что когда PHP пытается соединиться обратно к сайту по адресу `{url}` то он получает ошибку `{error}`. Может быть проблема с настройками сервера (к примеру локальный DNS, mod_security) которое предотвращает роботу соединенный должным образом."

#: ../framework/extensions/backups/helpers.php:123
#: ../framework/extensions/backups/helpers.php:145
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:363
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:431
#, php-format
msgid "Failed to create dir: %s"
msgstr "Не удалось создать папку: %s"

#: ../framework/extensions/backups/helpers.php:152
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:137
#, php-format
msgid "Failed to copy: %s"
msgstr "Не удалось скопировать: %s"

#: ../framework/extensions/backups/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:168
msgid "Backup & Demo Content"
msgstr "Резервная копия и демо контент"

#: ../framework/extensions/backups/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:169
msgid ""
"This extension lets you create an automated backup schedule, import demo "
"content or even create a demo content archive for migration purposes."
msgstr "Это расширение позволяет вам создавать график автоматизированного резервного копирования, импортировать демо контент или даже создавать архивы с демо контентом с целью переноса на другой домен."

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:97
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:98
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:385
msgid "Demo Content Install"
msgstr "Установка демо контента"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:282
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:329
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:362
msgid "Forbidden"
msgstr "Запрещено"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:342
msgid "Invalid demo"
msgstr "Неверное демо"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:349
msgid "A content install is currently running"
msgstr "Установка контента выполняется в данный момент"

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:28
#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:39
#: ../framework/extensions/backups/views/page.php:17
#: ../framework/extensions/backups/views/page.php:28
msgid "Important"
msgstr "Важно"

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:30
#: ../framework/extensions/backups/views/page.php:19
#, php-format
msgid "You need to activate %s."
msgstr "Необходимо активировать %s."

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:31
#: ../framework/extensions/backups/views/page.php:20
msgid "zip extension"
msgstr "расширение zip"

#: ../framework/extensions/backups/views/page.php:70
msgid "Archives"
msgstr "Архивы"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:76
msgid "Full Backup"
msgstr "Полная резервная копия"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:85
msgid "Content Backup"
msgstr "Резервная копия контента"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:88
msgid ""
"Warning! \n"
"You are about to delete a backup, it will be lost forever. \n"
"Are you sure?"
msgstr "Внимание!\nВы собираетесь удалить резервную копию без возможности восстановления.\nВы уверены?"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:16
msgid "Interval"
msgstr "Интервал"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:20
#: ../framework/core/components/extensions/manager/views/extension.php:180
msgid "Disabled"
msgstr "Отключить"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:25
msgid "Select how often do you want to backup your website."
msgstr "Выберите как часто вы хотите делать резервную копию вашего сайта."

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:32
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:45
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:58
msgid "Age Limit"
msgstr "Время хранения"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:33
msgid "Age limit of backups in months"
msgstr "Время хранения резервных копий в месяцах"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:46
msgid "Age limit of backups in weeks"
msgstr "Время хранения резервных копий в неделях"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:59
msgid "Age limit of backups in days"
msgstr "Время хранения резервных копий в днях"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:186
msgid "Backup Schedule"
msgstr "График резервного копирования"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:288
msgid "Once Weekly"
msgstr "Раз в неделю"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:292
msgid "Once a month"
msgstr "Раз в месяц"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:139
msgid "undefined"
msgstr "Не определено"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:279
msgid "Task type not registered"
msgstr "Тип задания не зарегистрирован"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:286
msgid "Execution stopped (next step did not started)"
msgstr "Выполнение остановлено (следующий шаг не начат)"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:306
msgid "Timed out"
msgstr "Превышение времени"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:366
msgid "Invalid execution end time"
msgstr "Неверное время завершения выполнения"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:537
msgid "Invalid execution result"
msgstr "Неверный результат выполнения"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:639
msgid "Invalid token"
msgstr "Недействительный токен"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:658
msgid "Invalid tasks hash"
msgstr "Недействительный хэш заданий"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-restore.php:9
msgid "Image Sizes Restore"
msgstr "Восстановление размеров изображений"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:12
msgid "Archive Unzip"
msgstr "Распаковка архива"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:39
msgid "Zip file not specified"
msgstr "Zip файл не определен"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:47
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:50
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:49
#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:114
msgid "Destination dir not specified"
msgstr "Целевая папка не определена"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:56
msgid "Destination dir is not empty"
msgstr "Целевая папка не пуста"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:63
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:59
#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:50
msgid "Zip extension missing"
msgstr "Zip расширение отсутствует"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:71
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:70
#, php-format
msgid "Cannot open zip (Error code: %s)"
msgstr "Не удалось открыть zip (Код ошибки: %s)"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:38
msgid "Database export"
msgstr "Экспорт базы данных"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:71
msgid "Database table disappeared"
msgstr "Таблица базы данных исчезла"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:91
msgid "Cannot create file"
msgstr "Не удалось создать файл"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:97
msgid "Cannot reopen file"
msgstr "Не удалось вновь открыть файл"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:141
msgid "Cannot export CREATE TABLE sql"
msgstr "Не удалось экспортировать CREATE TABLE sql"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:211
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:267
msgid "Cannot get next database table"
msgstr "Не удалось получить следующую таблицу базы данных"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:9
msgid "Files Restore"
msgstr "Восстановление файлов"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:32
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:41
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:45
msgid "Source dir not specified"
msgstr "Исходная папка не определена"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:39
msgid "Invalid source dir"
msgstr "Недействительная исходная папка"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:46
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:25
msgid "Source dirs not specified"
msgstr "Исходные папки не определены"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:116
msgid "No filesystem access, credentials required"
msgstr "Нет доступа к системе файлов, требуются учетные данные"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:120
msgid "No filesystem access, invalid credentials"
msgstr "Нет доступа к системе файлов, недействительные учетные данные"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:126
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:135
msgid "Filesystem init failed"
msgstr "Инициализация файловой системы не удалась"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:192
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:317
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:341
#, php-format
msgid "Cannot convert Filesystem path: %s"
msgstr "Не удалось сконвертировать путь к файловой системе: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:197
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:322
#, php-format
msgid "Failed to list dir: %s"
msgstr "Не удалось получить список папки: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:227
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:281
#, php-format
msgid "Failed to remove dir: %s"
msgstr "Не удалось удалить папку: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:234
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:288
#, php-format
msgid "Failed to remove file: %s"
msgstr "Не удалось удалить файл: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:376
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:444
#, php-format
msgid "Failed to copy file: %s"
msgstr "Не удалось скопировать файл: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:12
msgid "Archive Zip"
msgstr "Zip архив"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:97
msgid "Cannot close the zip file"
msgstr "Не удалось закрыть zip файл"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:103
msgid "Cannot move zip in destination dir"
msgstr "Не удалось переместить zip в целевую папку"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:9
msgid "Files Export"
msgstr "Экспорт файлов"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:33
msgid "Destination not specified"
msgstr "Местоположение не указано"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:74
#, php-format
msgid "Source dir %s is empty"
msgstr "Исходная папка %s пуста"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:116
msgid "Failed to get dir chmod"
msgstr "Не удалось получить chmod папки"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:123
msgid "Failed to create destination dir"
msgstr "Не удалось создать целевую папку"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:214
#, php-format
msgid "Failed to restore dir listing from: %s"
msgstr "Не удалось восстановить список папки из: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:12
msgid "Database restore"
msgstr "Восстановление базы данных"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:63
msgid "Database file not found"
msgstr "Файл базы данных не найден"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:111
#, php-format
msgid "Cannot drop temporary table: %s"
msgstr "Не удалось сбросить временную таблицу: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:129
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:285
msgid "Cannot open db file"
msgstr "Не удалось открыть файл базы данных"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:138
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:294
msgid "Cannot move cursor in db file"
msgstr "Не удается переместить курсор в файл базы данных"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:154
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:377
#, php-format
msgid "Failed to decode line %d from db file."
msgstr "Не удалось декодировать строку %d из файла базы данных."

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:186
#, php-format
msgid "Cannot read line %d from db file"
msgstr "Не удается прочитать строку %d из файла базы данных"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:195
msgid "Required params not found"
msgstr "Необходимые параметры не найдены"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:232
msgid "Cannot do full db restore because backup is missing some tables"
msgstr "Не удается восстановить базу данных полностью, так как в резервной копии отсутствуют некоторые таблицы"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:396
#, php-format
msgid "Failed to drop tmp table %s"
msgstr "Не удалось сбросить временную таблицу %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:435
#, php-format
msgid "Failed to create tmp table %s"
msgstr "Не удалось создать временную таблицу %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:447
#, php-format
msgid "Tried to insert data in table that was not imported %s"
msgstr "Попытался вставить данные в таблицу, что не были импортированы %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:606
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:628
#, php-format
msgid "Failed insert row from line %d"
msgstr "Не удалось вставить ряд из строки %d"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:642
#, php-format
msgid "Invalid json type %s in db file"
msgstr "Недействительный тип json %s в файле базы данных"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:649
msgid "Cannot read line from db file"
msgstr "Не удается прочитать строку из файла базы данных"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:688
msgid "Failed to restore options keeping step"
msgstr "Не удалось восстановить парамерты сохраняя последовательность"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:723
#, php-format
msgid "Failed to keep option: %s"
msgstr "Не удалось сохранить параметр: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:774
msgid "Tables drop failed"
msgstr "Не удалось сбросить таблицы"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:787
msgid "Tables rename failed."
msgstr "Не удалось переименовать таблицы."

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:799
#, php-format
msgid "Invalid sub task %s"
msgstr "Недействительная под задача %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:12
msgid "Directory Cleanup"
msgstr "Очистка папки"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:21
msgid "Dir not specified"
msgstr "Папка не определена"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:30
msgid "Cannot remove directory"
msgstr "Не удается удалить папку"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:37
msgid "Cannot create directory"
msgstr "Не удается создать папку"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:55
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:70
#, php-format
msgid "Cannot create file: %s"
msgstr "Не удается создать файл: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:9
msgid "Image Sizes Remove"
msgstr "Удаление размеров изображений"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:23
msgid "Uploads dir not specified"
msgstr "Папка для загрузок не определена"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:18
msgid "Download"
msgstr "Загрузить"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:119
msgid "Invalid destination dir"
msgstr "Недействительная целевая папка"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:132
#, php-format
msgid "Invalid type: %s"
msgstr "Недействительный тип: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:141
#, php-format
msgid "Args not specified for type: %s"
msgstr "Аргументы не указаны для типа: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:9
msgid "Local Download"
msgstr "Локальное скачивание"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:24
msgid "Source not specified"
msgstr "Исходник не определен"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:29
msgid "Invalid source"
msgstr "Недействительный исходник"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:38
msgid "Invalid source type"
msgstr "Недействительный тип исходника"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:59
#, php-format
msgid "Cannot open zip: %s"
msgstr "Не удается открыть zip: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:70
msgid "Unhandled type"
msgstr "Неизвестный тип"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:13
msgid "Downloading..."
msgstr "Скачивание..."

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:16
msgid "Download finished. Doing unzip..."
msgstr "Скачивание завершено. Идет распаковка..."

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:19
#, php-format
msgid "Downloading... %s of %s"
msgstr "Скачивание... %s из %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:24
#, php-format
msgid "Downloading... %s"
msgstr "Скачивание... %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:67
msgid "Url not specified"
msgstr "Адрес не определен"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:72
msgid "Invalid url"
msgstr "Недействительный адрес"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:79
msgid "File id not specified"
msgstr "ID файла не определен"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:84
msgid "Invalid file id"
msgstr "Недействительный ID файла"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:105
#, php-format
msgid "Zip open failed (code %d). Please try again"
msgstr "Не удалось открыть zip (код %d). Попробуйте еще раз позже"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:112
msgid "Zip extract failed"
msgstr "Не удалось распаковать zip"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:119
msgid "Failed to close the zip after extract"
msgstr "Не удалось закрыть zip после распаковки"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:152
#, php-format
msgid "Request failed. Error code: %d"
msgstr "Запрос не выполнен. Код ошибки: %d"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:163
msgid "Invalid byte position"
msgstr "Недействительная позиция байта"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:168
msgid "Empty response body"
msgstr "Пустое тело ответа"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:188
msgid "File ended without content"
msgstr "Файл закончился без содержания"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:213
msgid "Failed to write data to file"
msgstr "Не удалось записать данные в файл"

#: ../framework/extensions/translation/settings-options.php:11
msgid "Default Language"
msgstr "Язык по умолчанию"

#: ../framework/extensions/translation/settings-options.php:12
msgid "This is the default language of your website."
msgstr "Это язык по умолчанию на вашем сайте."

#: ../framework/extensions/translation/settings-options.php:19
msgid "Translate to"
msgstr "Перевести на"

#: ../framework/extensions/translation/settings-options.php:20
msgid "Choose the languages you want your website translated to."
msgstr "Выберите языки, на которые вы хотите перевести ваш сайт. "

#: ../framework/extensions/translation/settings-options.php:27
msgid "Convert data"
msgstr "Преобразование данных"

#: ../framework/extensions/translation/settings-options.php:28
msgid ""
"Set to default language the posts, pages categories or tags that don't have "
"a language set ?"
msgstr "Задать язык по умолчанию для записей, страниц, категорий или меток, для которых язык не задан? "

#: ../framework/extensions/translation/manifest.php:7
#: ../framework/extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:336
#: ../framework/core/components/extensions/manager/available-extensions.php:264
msgid "Translations"
msgstr "Переводы"

#: ../framework/extensions/translation/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:265
msgid ""
"This extension lets you translate your website in any language or even add "
"multiple languages for your users to change at their will from the front-"
"end."
msgstr "Это расширение позволяет переводить свой сайт на любой язык или даже добавлять несколько языков, чтобы ваши пользователи могли переключать их по их усмотрению из внешнего интерфейса."

#: ../framework/extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:114
msgid "All Languages"
msgstr "Все языки"

#: ../framework/extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:332
msgid "The term translation does already exists.ACTION +++ "
msgstr "Перевод термина уже существует. ДЕЙСТВИЕ +++"

#: ../framework/extensions/translation/extensions/translate-terms/manifest.php:7
msgid "Translate Terms"
msgstr "Перевести термины"

#: ../framework/extensions/translation/extensions/translate-terms/manifest.php:8
msgid "This extension translate terms"
msgstr "Это расширение переводит термины"

#: ../framework/extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:341
msgid "Language of this post"
msgstr "Язык данной записи"

#: ../framework/extensions/translation/extensions/translate-posts/manifest.php:7
msgid "Translate Posts"
msgstr "Перевести записи"

#: ../framework/extensions/translation/extensions/translate-posts/manifest.php:8
msgid "This extension translate posts"
msgstr "Это расширение переводит записи"

#: ../framework/extensions/translation/extensions/translate-widgets/manifest.php:7
msgid "Translate Widgets"
msgstr "Перевести виджеты"

#: ../framework/extensions/translation/extensions/translate-widgets/manifest.php:8
msgid "This extension translate Widgets"
msgstr "Это расширение переводит виджеты"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:10
msgid "Language Switcher"
msgstr "Переключатель языков"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:11
msgid "A Language Switcher Widget"
msgstr "Виджет Переключатель языков"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:42
msgid "New title"
msgstr "Новый заголовок"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:45
#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:49
msgid "Title:"
msgstr "Заголовок:"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:14
msgid "A calendar of your site&#8217;s Posts."
msgstr "Календарь записей своего сайта."

#: ../framework/extensions/translation/extensions/translate-menus/manifest.php:7
msgid "Translate Menus"
msgstr "Перевести меню"

#: ../framework/extensions/translation/extensions/translate-menus/manifest.php:8
msgid "This extension translate menus"
msgstr "Это расширение переводит меню"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:211
msgid "Slider Design"
msgstr "Дизайн слайдера"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:213
msgid "Number of Images"
msgstr "Количество изображений"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:231
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:234
#, php-format
msgid "%s updated."
msgstr "%s обновлен."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:232
msgid "Custom field updated."
msgstr "Пользовательское поле обновлено."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:233
msgid "Custom field deleted."
msgstr "Пользовательское поле удалено."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:235
#, php-format
msgid "%s restored to revision from %s"
msgstr "%s восстановлен для пересмотра из %s"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:237
#, php-format
msgid "%s published."
msgstr "%s опубликован."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:238
msgid "Page saved."
msgstr "Страница сохранена."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:239
#, php-format
msgid "%s submitted."
msgstr "%s отправлен."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:240
#, php-format
msgid "%s scheduled for: %s."
msgstr "%s запланирован на: %s."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:242
#, php-format
msgid "%s draft updated."
msgstr "%s черновик обновлен."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:272
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:52
msgid "Publish"
msgstr "Опубликовать"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:342
msgid ""
"This slider was created correctly, but the code implementation was delete "
"from source code or blocked from filter.Delete this post or recovery slider "
"implementation"
msgstr "Этот слайдер был создан правильно, но реализация кода была удалена из исходного кода или заблокирована фильтром. Удалите эту запись или создайте слайдер заново"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:351
msgid ""
"This slider was created correctly, but the multimedia_types from config.php "
"file was deleted, please set multimedia_types for this slider type."
msgstr "Слайдер был создан правильно, но multimedia_types из файла config.php был удален, пожалуйста, задайте multimedia_types  для этого типа слайдера."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:361
msgid "Slider Configuration"
msgstr "Конфигурация слайдера"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:375
msgid "Slider Title"
msgstr "Название слайдера"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:377
msgid "Choose a title for your slider only for internal use: Ex: \"Homepage\"."
msgstr "Выберите название для своего слайдера только для внутреннего использования: Напр: \"Homepage\"."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:433
msgid "Slider Settings"
msgstr "Настройки слайдера"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:461
msgid ""
"You don't have slider extensions, please create at least one extension for "
"properly work"
msgstr "У вас нет расширений слайдера, пожалуйста, создайте хотя бы одно для корректной работы"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:474
msgid "Choose the population method for your slider"
msgstr "Выберите метод наполнения для своего слайдера"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:542
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:94
msgid "(no title)"
msgstr "(без названия)"

#: ../framework/extensions/media/extensions/slider/posts.php:6
#: ../framework/extensions/media/extensions/slider/posts.php:12
#: ../framework/extensions/media/extensions/slider/posts.php:18
#: ../framework/extensions/media/extensions/slider/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:12
msgid "Sliders"
msgstr "Слайдер"

#: ../framework/extensions/media/extensions/slider/posts.php:7
#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:8
msgid "Slider"
msgstr "Слайдер"

#: ../framework/extensions/media/extensions/slider/posts.php:9
msgid "Add New Slider"
msgstr "Добавить слайдер"

#: ../framework/extensions/media/extensions/slider/posts.php:10
msgid "Edit Slider"
msgstr "Редактривать слайдер"

#: ../framework/extensions/media/extensions/slider/posts.php:11
msgid "New Slider"
msgstr "Новый слайдер"

#: ../framework/extensions/media/extensions/slider/posts.php:13
msgid "View Slider"
msgstr "Просмотр слайдера"

#: ../framework/extensions/media/extensions/slider/posts.php:14
msgid "Search Sliders"
msgstr "Поиск слайдеров"

#: ../framework/extensions/media/extensions/slider/posts.php:15
msgid "No Sliders found"
msgstr "Слайдер не найден"

#: ../framework/extensions/media/extensions/slider/posts.php:16
msgid "No Sliders found in Trash"
msgstr "Не найдено слайдеров в корзине"

#: ../framework/extensions/media/extensions/slider/manifest.php:6
msgid ""
"Adds the Sliders extension to your website. You'll be able to create "
"different built in jQuery sliders for your homepage and all the other "
"website pages."
msgstr "Добавляет расширение Слайдеры на сайт. Вы сможете создавать различные слайдеры, сделанные с помощью jQuery, для главной страницы, а также для других страниц на вашем сайте."

#: ../framework/extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:6
msgid "Nivo Slider"
msgstr "Слайдер Nivo"

#: ../framework/extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:6
msgid "Owl Slider"
msgstr "Слайдер Owl"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/manifest.php:6
msgid "Bx-Slider"
msgstr "Bx-слайдер"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:4
msgid "Population Method Categories opt 1"
msgstr "Метод наполнения категорий opt 1"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:5
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:11
msgid "Option description"
msgstr "Описание опции"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:10
msgid "Population Method Categories opt 2"
msgstr "Метод наполнения категорий opt 2"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:5
msgid "Type of Transition"
msgstr "Тип перехода"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:6
msgid "Type of transition between slides"
msgstr "Тип перехода между слайдами"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:9
msgid "Horizontal"
msgstr "Горизонтально"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:10
msgid "Vertical"
msgstr "Вертикальный"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:11
msgid "Fade"
msgstr "Выцветать"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:9
msgid "Choose a subtitle for your slide."
msgstr "Выберите подзаголовок для своего слайда."

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:9
msgid "Add a Slider"
msgstr "Добавить слайдер"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:9
msgid "Select Slider"
msgstr "Выбрать слайдер"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:14
msgid "Set width"
msgstr "Установить ширену"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:19
msgid "Set height"
msgstr "Установить высоту"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:34
msgid "No Sliders Available"
msgstr "Нет доступных слайдеров"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:47
msgid "create a new Slider"
msgstr "создать новый слайдер"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:49
msgid ""
"No Sliders created yet. Please go to the {br}Sliders page and "
"{add_slider_link}."
msgstr "Пока не создано ни одного слайдера.  Перейдите на страницу {br}слайдеров и {add_slider_link}."

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:5
msgid ""
"Note that the type and population can't be changed later. You'll need to "
"create a new slider to have a different slider type or population method."
msgstr "Внимание, тип и наполнение не могут быть изменены позже. Вам нужно будет создать новый слайдер, чтобы выбрать другой тип слайдера или метод наполнения."

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:31
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:48
msgid "Schedule"
msgstr "Расписание"

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:40
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:57
msgid "Submit for Review"
msgstr "Представляет на рассмотрение"

#: ../framework/extensions/media/extensions/population-method/class-fw-extension-population-method.php:36
#, php-format
msgid "Specified population method does not exists: %s"
msgstr "Выбранный метод наполнения не существует: %s"

#: ../framework/extensions/media/extensions/population-method/class-fw-extension-population-method.php:55
#, php-format
msgid "Population method %s does not exist"
msgstr "Метод наполнения %s не существует"

#: ../framework/extensions/media/extensions/population-method/manifest.php:3
msgid "Population Methods"
msgstr "Методы наполнения "

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:21
msgid "Automatically, fetch images from categories"
msgstr "Автоматически, подгружать изображения из категорий"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:29
#, php-format
msgid "%s extension needs configured categories in post types"
msgstr "%s расширение требует наличие категорий в типах постов"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:34
msgid "Categories Population Method"
msgstr "Метод наполнения категорий"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:44
msgid "Choose Category"
msgstr "Выбрать категорию"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:53
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:53
msgid "Number of Images in the slider"
msgstr "Количество изображений в слайдере"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:107
msgid "Select Specific Categories"
msgstr "Выберите определенные категории"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:4
msgid "Population Method - Categories"
msgstr "Метод наполнения - категории"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:21
msgid "Automatically, fetch images from tags"
msgstr "Автоматически, подгружать изображения из меток"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:29
#, php-format
msgid "%s extension needs configured tags in post types"
msgstr "%s расширение требует наличие меток в типах постов"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:34
msgid "Tags Population Method"
msgstr "Методы наполнения  меток"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:44
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:44
msgid "Choose Tag"
msgstr "Выбрать метку"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:107
msgid "Select Specific tags"
msgstr "Выберите определенные метки"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:4
msgid "Population Method - Tags"
msgstr "Метод наполнения - метки"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:21
msgid "Automatically, fetch images from posts"
msgstr "Автоматически, подгружать изображения из записей"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:29
#, php-format
msgid "%s extension needs configured post categories in post types"
msgstr "%s расширение требует наличие рубрик в типах постов"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:34
msgid "Posts Population Method"
msgstr "Метод наполнения записей"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:101
msgid "Select Specific posts"
msgstr "Выберите определенные записи"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:4
msgid "Population Method - Posts"
msgstr "Метод наполнения - записи"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:22
msgid "Manually, I'll upload the images myself"
msgstr "Вручную, я сам загружу изображения"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:33
msgid "Click to edit / Drag to reorder"
msgstr "Кликните для редактирования/ перетащите, чтобы упорядочить"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:54
msgid "Choose"
msgstr "Выбрать"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:4
msgid "Population Method - Custom"
msgstr "Метод наполнения - собственный"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:57
msgid "Add Slide"
msgstr "Добавить слайд"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:59
#: ../framework/views/backend-settings-form.php:47
msgid "Save Changes"
msgstr "Сохранить изменения"

#: ../framework/core/Fw.php:73
msgid "Framework requirements not met:"
msgstr "Не соответствует требованиям фреймворка:"

#: ../framework/core/class-fw-manifest.php:293
msgid "minimum required version is"
msgstr "минимальная требуемая версия"

#: ../framework/core/class-fw-manifest.php:296
msgid "maximum required version is"
msgstr "максимальная требуемая версия"

#: ../framework/core/class-fw-manifest.php:301
msgid "and"
msgstr "и"

#: ../framework/core/class-fw-manifest.php:308
#, php-format
msgid "Current WordPress version is %s, %s"
msgstr "Установленная версия WordPress %s, %s"

#: ../framework/core/class-fw-manifest.php:314
#, php-format
msgid "Current Framework version is %s, %s"
msgstr "Текущая версия фреймворка - %s, %s"

#: ../framework/core/class-fw-manifest.php:323
#, php-format
msgid "Current version of the %s extension is %s, %s"
msgstr "Текущая версия расширения %s - %s, %s"

#: ../framework/core/class-fw-manifest.php:329
#, php-format
msgid "%s extension is required"
msgstr "%s расширение обязательно"

#: ../framework/core/class-fw-manifest.php:334
#, php-format
msgid "%s extension is required (%s)"
msgstr "расширению %s требуется (%s)"

#: ../framework/core/extends/class-fw-option-type.php:283
#, php-format
msgid "Option type %s has no default value"
msgstr "У типа опций %s нет значения по умолчанию"

#: ../framework/core/components/backend.php:355
msgid "Done"
msgstr "Сделано"

#: ../framework/core/components/backend.php:356
msgid "Ah, Sorry"
msgstr "Ой, извините"

#: ../framework/core/components/backend.php:358
#: ../framework/includes/option-types/color-picker/class-fw-option-type-color-picker.php:39
#: ../framework/includes/option-types/color-picker/class-fw-option-type-color-picker.php:40
#: ../framework/includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:41
#: ../framework/includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:42
msgid "Reset"
msgstr "Сброс"

#: ../framework/core/components/backend.php:541
#: ../framework/core/components/backend.php:542
#: ../framework/core/components/backend.php:650
msgid "Theme Settings"
msgstr "Настройки Темы"

#: ../framework/core/components/backend.php:577
msgid "leave a review"
msgstr "оставить отзыв"

#: ../framework/core/components/backend.php:588
msgid ""
"Unyson WordPress Framework is the fastest and easiest way to develop a "
"premium theme. I highly recommend it"
msgstr "Фреймворк для WordPress Unyson - это самый быстрый и простой способ разработать премиум тему. Я настоятельно рекомендую его"

#: ../framework/core/components/backend.php:594
msgid ""
"If you like Unyson, {wp_review_link}, share on {facebook_share_link} or "
"{twitter_share_link}."
msgstr "Если вам нравится Unyson, {wp_review_link}, поделитесь этим на {facebook_share_link} или {twitter_share_link}."

#: ../framework/core/components/backend.php:1303
msgid "You have no permissions to change settings options"
msgstr "У вас не достаточно прав для изменения настроек"

#: ../framework/core/components/backend.php:1316
msgid "The options were successfully reset"
msgstr "Настройки были успешно сброшены"

#: ../framework/core/components/backend.php:1327
msgid "The options were successfully saved"
msgstr "Настройки были успешно сохранены"

#: ../framework/core/components/backend.php:1440
msgid "Unknown collected group"
msgstr "Неизвестная группа"

#: ../framework/core/components/backend.php:1779
#, php-format
msgid "Undefined option type: %s"
msgstr "Не определенный тип опции: %s"

#: ../framework/core/components/backend.php:1826
#, php-format
msgid "Undefined container type: %s"
msgstr "Не определенный тип контейнера: %s"

#: ../framework/core/components/extensions.php:447
#: ../framework/core/components/extensions.php:525
#, php-format
msgid "Extension %s is invalid."
msgstr "Расширение %s не рабочее."

#: ../framework/core/components/theme.php:206
msgid "Theme requirements not met:"
msgstr "Не соответствует требованиям темы:"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:351
msgid "Cannot remove the old extensions backup dir"
msgstr "Не могу удалить старые расширения резервного копирования dir"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:357
msgid "Cannot create the extensions backup dir"
msgstr "Не могу создать папку бекапа для расширения"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:362
msgid "Cannot backup the extensions"
msgstr "Невозможно резервное копирование расширения"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:408
msgid "Cannot clear the extensions directory"
msgstr "Невозможно очистить каталог расширений"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:413
msgid "Cannot recreate the extensions directory"
msgstr "Невозможно пересоздать каталог расширений"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:419
msgid "Cannot recover the extensions"
msgstr "Невозможно восстановить расширения"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:645
#, php-format
msgid "Cannot activate hidden standalone extension %s"
msgstr "Не удается активировать скрытое автономное расширение %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:874
msgid "You are not allowed to install extensions."
msgstr "У вас недостаточно прав для установки расширений."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:891
msgid "All supported extensions are already installed."
msgstr "Все поддерживаемые расширения уже установлены."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:967
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2384
#, php-format
msgid "Cannot remove temporary directory: %s"
msgstr "Невозможно удалить временный каталог: %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1037
msgid "You have no permissions to install extensions"
msgstr "У вас не достаточно прав для установки расширений"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1044
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1517
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1901
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2116
msgid "No extensions provided"
msgstr "Нет доступных расширений"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1053
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1527
msgid "WP Filesystem is not initialized"
msgstr "Файловая система WP не инициализирована"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1079
#, php-format
msgid "Extension \"%s\" is already installed."
msgstr "Расширение \"%s\" уже установлено."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1094
#, php-format
msgid "Extension \"%s\" is not available for install."
msgstr "Расширение \"%s\" не доступно для установки."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1122
#, php-format
msgid "Parent extension \"%s\" not available."
msgstr "Родительское расширение \"%s\" не доступно."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1156
#, php-format
msgid "Downloading the \"%s\" extension..."
msgstr "Скачивается расширение \"%s\"..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1200
#, php-format
msgid "Installing the \"%s\" extension..."
msgstr "Устанавливается расширение \"%s\"..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1238
#, php-format
msgid "The %s extension has been successfully installed."
msgstr "Расширение %s было успешно установлено."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1399
msgid "You are not allowed to delete extensions."
msgstr "У вас недостаточно прав для удаления расширений."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1510
msgid "You have no permissions to uninstall extensions"
msgstr "У вас не достаточно прав для удаления расширений"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1578
#, php-format
msgid "Deleting the \"%s\" extension..."
msgstr "Удаляется расширение \"%s\"..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1590
#, php-format
msgid "Cannot delete the \"%s\" extension."
msgstr "Невозможно удалить расширение \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1602
#, php-format
msgid "The %s extension has been successfully deleted."
msgstr "Расширение %s было успешно удалено."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1703
msgid "Extension not specified."
msgstr "Расширение не определено."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1708
#, php-format
msgid "Extension \"%s\" is not installed."
msgstr "Расширение \"%s\" не установлено."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1776
#, php-format
msgid "Extension \"%s\" does not exist or is not active."
msgstr "Расширение \"%s\" не существует или не активно."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1785
#, php-format
msgid "%s extension does not have settings."
msgstr "расширение %s не имеет параметров настройки."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1806
msgid "Extension has no Install Instructions"
msgstr "Расширение не имеет инструкций по установке"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1830
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2047
msgid "Invalid request method."
msgstr "Не рабочий запрос метода."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1842
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2059
msgid "No extension specified."
msgstr "Нет определённого расширения."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1894
msgid "You have no permissions to activate extensions"
msgstr "У вас не достаточно прав для активации расширений"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1914
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2132
#, php-format
msgid "Extension \"%s\" does not exist."
msgstr "Расширение \"%s\" не существует."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2109
msgid "You have no permissions to deactivate extensions"
msgstr "У вас не достаточно прав для деактивации расширений"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2297
msgid "You are not allowed to save extensions settings."
msgstr "У вас недостаточно прав для сохранения настроек расширения."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2304
msgid "Invalid extension."
msgstr "Не рабочее расширение."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2309
msgid "Extension does not have settings options."
msgstr "У расширения нет параметров настройки."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2341
msgid "Extensions settings successfully saved."
msgstr "Настройки расширений успешно сохранены."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2368
#, php-format
msgid "Extension \"%s\" has no download sources."
msgstr "Расширение \"%s\" не имеет источников загрузки."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2392
#, php-format
msgid "Cannot create temporary directory: %s"
msgstr "Не удается создать временную папку: %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2403
#, php-format
msgid "\"%s\" extension github source \"user_repo\" parameter is required"
msgstr "Расширению \"%s\" требуется параметр \"user_repo\""

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2454
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (%s)"
msgstr "Нет доступа к релизам в репозитории \"%s\" Github'а. (%s)"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2477
#, php-format
msgid "\"%s\" extension github repository \"%s\" has no releases."
msgstr "В репозитории \"%s\" расширения \"%s\" нет релизов."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2510
#, php-format
msgid "Cannot download the \"%s\" extension zip. (Response code: %d)"
msgstr "Не удается скачать zip расширения \"%s\". (Код ответа: %d)"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2517
#, php-format
msgid "Cannot download the \"%s\" extension zip. %s"
msgstr "Не удается скачать zip расширения \"%s\". %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2525
#, php-format
msgid "Cannot download the \"%s\" extension zip."
msgstr "Не удается скачать zip расширения \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2538
#, php-format
msgid "Cannot save the \"%s\" extension zip."
msgstr "Не удается сохранить zip расширения \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2557
#, php-format
msgid "Cannot remove the \"%s\" extension downloaded zip."
msgstr "Не удается удалить скачанный zip расширения \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2582
#, php-format
msgid "The unzipped \"%s\" extension directory not found."
msgstr "Распакованная папка расширения \"%s\" не найдена."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2589
#, php-format
msgid "Unknown \"%s\" extension download source \"%s\""
msgstr "Неизвестный источник загрузки расширения \"%s\" \"%s\""

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2615
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2634
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2697
#, php-format
msgid "Cannot read directory \"%s\"."
msgstr "Не удается прочитать папку \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2649
#, php-format
msgid "Cannot delete \"%s\"."
msgstr "Не удается удалить \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2660
#, php-format
msgid "Cannot create the \"%s\" directory."
msgstr "Не удается создать папку \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2678
#, php-format
msgid "Cannot move \"%s\" to \"%s\"."
msgstr "Не удается переместить \"%s\" в \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3103
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3150
#, php-format
msgid "Cannot activate the %s extension because it is not installed. %s"
msgstr "Не удается активировать расширение %s , так как оно не установлено. %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3107
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3154
msgid "Install"
msgstr "Установить"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3197
msgid "Install theme compatible extensions"
msgstr "Установить расширения совместимые с темой"

#: ../framework/core/components/extensions/manager/available-extensions.php:13
msgid ""
"Adds a sliders module to your website from where you'll be able to create "
"different built in jQuery sliders for your homepage and rest of the pages."
msgstr "Добавляет слайдер к вашему веб-сайту от того, из которого вы сможете создать различные сделанные в jQuery слайдеры для главной страницы и остальных страниц."

#: ../framework/core/components/extensions/manager/available-extensions.php:24
msgid "Media"
msgstr "Медиа"

#: ../framework/core/components/extensions/manager/available-extensions.php:36
msgid "Population method"
msgstr "Метод посещений"

#: ../framework/core/components/extensions/manager/available-extensions.php:85
msgid ""
"Let's you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr "Вы можете с лёгкостью создавать бесчисленное количество страниц с помощью перетаскивания в Visual Page Builder, который уже с большим количеством уже созданных шорткодов."

#: ../framework/core/components/extensions/manager/available-extensions.php:96
msgid "Shortcodes"
msgstr "Шорткоды"

#: ../framework/core/components/extensions/manager/available-extensions.php:157
msgid ""
"This extension lets you set up daily, weekly or monthly backup schedule. You"
" can choose between a full backup or a data base only backup."
msgstr "Это расширение позволяет настроить ежедневное, еженедельное или ежемесячное расписание резервного копирования. Вы можете выбрать между полной резервной копией сайта или копией только базы данных."

#: ../framework/core/components/extensions/manager/views/extension.php:89
#: ../framework/core/components/extensions/manager/views/extension-page-header.php:25
msgid "Install Instructions"
msgstr "Инструкции по установке"

#: ../framework/core/components/extensions/manager/views/extension.php:102
msgid "Compatible"
msgstr "Совместимый"

#: ../framework/core/components/extensions/manager/views/extension.php:102
msgid "with your current theme"
msgstr "с текущей темой"

#: ../framework/core/components/extensions/manager/views/extension.php:174
#, php-format
msgid "Parent extension \"%s\" is disabled"
msgstr "Родительское расширение \"%s\" отключено"

#: ../framework/core/components/extensions/manager/views/extension.php:199
#, php-format
msgid "You need to update WordPress to %s: %s"
msgstr "Вам нужно обновить WordPress в %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:201
msgid "Update WordPress"
msgstr "Обновить WordPress"

#: ../framework/core/components/extensions/manager/views/extension.php:205
#, php-format
msgid "WordPress needs to be updated to %s"
msgstr "WordPress должен быть обновлен к %s"

#: ../framework/core/components/extensions/manager/views/extension.php:215
#, php-format
msgid "Maximum supported WordPress version is %s"
msgstr "Максимально поддерживаемая версия WordPress %s"

#: ../framework/core/components/extensions/manager/views/extension.php:230
#, php-format
msgid "You need to update %s to %s: %s"
msgstr "Вам нужно обновить %s к %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:239
#, php-format
msgid "%s needs to be updated to %s"
msgstr "%s должен быть обновлен к %s"

#: ../framework/core/components/extensions/manager/views/extension.php:250
#, php-format
msgid "Maximum supported %s version is %s"
msgstr "Максимально поддерживаемая версия %s %s"

#: ../framework/core/components/extensions/manager/views/extension.php:268
#, php-format
msgid "You need to update the %s extension to %s: %s"
msgstr "Необходимо обновить расширение %s к %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:277
#, php-format
msgid "The %s extension needs to be updated to %s"
msgstr "Расширение %s необходимо обновить к %s"

#: ../framework/core/components/extensions/manager/views/extension.php:288
#, php-format
msgid "Maximum supported %s extension version is %s"
msgstr "Максимально поддерживаемая версия расширения %s %s"

#: ../framework/core/components/extensions/manager/views/extension.php:303
#, php-format
msgid "The %s extension is disabled"
msgstr "Расширение %s отключено"

#: ../framework/core/components/extensions/manager/views/extension.php:304
#, php-format
msgid "Activate %s"
msgstr "Активировать %s"

#: ../framework/core/components/extensions/manager/views/extension.php:313
#, php-format
msgid "The %s extension is not installed: %s"
msgstr "Расширение %s не установлено: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:316
#, php-format
msgid "Install %s"
msgstr "Установить %s"

#: ../framework/core/components/extensions/manager/views/extension.php:321
#, php-format
msgid "The %s extension is not installed"
msgstr "Расширение %s не установлено"

#: ../framework/core/components/extensions/manager/views/extension.php:342
msgid "View Requirements"
msgstr "Посмотреть требования"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:40
#, php-format
msgid "%s Settings"
msgstr "Настройки %s"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:43
#, php-format
msgid "%s Install Instructions"
msgstr "Инструкции по установке %s"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:46
msgid "Unknown tab:"
msgstr "Неизвестная вкладка:"

#: ../framework/core/components/extensions/manager/views/delete-form.php:42
#: ../framework/core/components/extensions/manager/views/install-form.php:51
msgid "No, Return me to the extension list"
msgstr "Нет, вернуться к списку расширений"

#: ../framework/core/components/extensions/manager/views/delete-form.php:45
msgid "Click to view entire list of directories which will be deleted"
msgstr "Кликните, чтобы открыть полный список папок, которые будут удалены"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:17
msgid "Active Extensions"
msgstr "Активные расширения"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:33
msgid "No extensions activated yet"
msgstr "Активных расширений пока нет"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:33
msgid "Check the available extensions below"
msgstr "Выберите доступные расширения ниже"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:59
msgid "Available Extensions"
msgstr "Доступные расширения"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:184
msgid "Show other extensions"
msgstr "Показать другие расширения"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:185
msgid "Hide other extensions"
msgstr "Скрыть другие расширения"

#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:14
#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:14
msgid "Go to extensions page"
msgstr "Перейти на страницу расширений"

#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:17
#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:17
msgid "Return to Extensions page"
msgstr "Вернуться на страницу расширений"

#: ../framework/views/backend-settings-form.php:48
msgid "Reset Options"
msgstr "Сбросить настройки"

#: ../framework/views/backend-settings-form.php:62
msgid "by"
msgstr "автор"

#: ../framework/views/backend-settings-form.php:155
msgid ""
"Click OK to reset.\n"
"All settings will be lost and replaced with default settings!"
msgstr "Нажмите OK чтобы сбросить.\nВсе настройки будут удалены и заменены настройками по умолчанию!"

#: ../framework/views/backend-settings-form.php:202
msgid "Resetting"
msgstr "Сброс"

#: ../framework/views/backend-settings-form.php:204
msgid "We are currently resetting your settings."
msgstr "В данный момент мы сбрасываем настройки."

#: ../framework/views/backend-settings-form.php:206
#: ../framework/views/backend-settings-form.php:212
msgid "This may take a few moments."
msgstr "Это может занять несколько минут."

#: ../framework/views/backend-settings-form.php:208
msgid "Saving"
msgstr "Сохранение"

#: ../framework/views/backend-settings-form.php:210
msgid "We are currently saving your settings."
msgstr "В данный момент мы сохраняем настройки."

#: ../framework/includes/option-types/class-fw-option-type-undefined.php:25
msgid "UNDEFINED OPTION TYPE"
msgstr "НЕ ОПРЕДЕЛЕННЫЙ ТИП ОПЦИИ"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:22
msgid "25%"
msgstr "25%"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:23
msgid "50%"
msgstr "50%"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:24
msgid "100%"
msgstr "100%"

#: ../framework/includes/option-types/map/views/view.php:12
msgid "Specify location"
msgstr "Укажите местоположение"

#: ../framework/includes/option-types/map/views/view.php:27
msgid "Location Venue"
msgstr "Место проведения"

#: ../framework/includes/option-types/map/views/view.php:42
msgid "Address"
msgstr "Адрес"

#: ../framework/includes/option-types/map/views/view.php:57
msgid "City"
msgstr "Город"

#: ../framework/includes/option-types/map/views/view.php:72
msgid "Country"
msgstr "Страна"

#: ../framework/includes/option-types/map/views/view.php:87
msgid "State"
msgstr "Штат"

#: ../framework/includes/option-types/map/views/view.php:103
msgid "Zip Code"
msgstr "Почтовый индекс"

#: ../framework/includes/option-types/map/views/view.php:138
msgid "Cannot find the location?"
msgstr "Не удалось найти адрес?"

#: ../framework/includes/option-types/map/views/view.php:150
msgid "Reset location"
msgstr "Сбросить адрес"

#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:150
msgid "Add Image"
msgstr "Добавить изображение"

#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:176
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:186
msgid "Upload"
msgstr "Загрузка"

#: ../framework/includes/option-types/typography-v2/view.php:39
msgid "Font face"
msgstr "Название шрифта"

#: ../framework/includes/option-types/typography-v2/view.php:48
msgid "Normal"
msgstr "Нормальный"

#: ../framework/includes/option-types/typography-v2/view.php:49
msgid "Italic"
msgstr "Курсив"

#: ../framework/includes/option-types/typography-v2/view.php:50
msgid "Oblique"
msgstr "Наклонный"

#: ../framework/includes/option-types/typography-v2/view.php:59
#: ../framework/includes/option-types/typography-v2/view.php:118
msgid "Style"
msgstr "Стиль"

#: ../framework/includes/option-types/typography-v2/view.php:85
msgid "Weight"
msgstr "Толщина"

#: ../framework/includes/option-types/typography-v2/view.php:101
msgid "Script"
msgstr "Скрипт"

#: ../framework/includes/option-types/typography-v2/view.php:128
msgid "Size"
msgstr "Размер"

#: ../framework/includes/option-types/typography-v2/view.php:139
msgid "Line height"
msgstr "Междустрочный интервал"

#: ../framework/includes/option-types/typography-v2/view.php:150
msgid "Letter spacing"
msgstr "Межбуквенный интервал"

#: ../framework/includes/option-types/typography-v2/view.php:173
msgid "Color"
msgstr "Цвет"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:156
msgid "Unknown Set"
msgstr "Неизвестная установка"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:186
msgid "Web Application Icons"
msgstr "Веб-приложения"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:187
msgid "Hand Icons"
msgstr "Жесты"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:188
msgid "Transportation Icons"
msgstr "Транспорт"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:189
msgid "Gender Icons"
msgstr "Гендерные иконки"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:190
msgid "File Type Icons"
msgstr "Типы файлов"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:193
msgid "Payment Icons"
msgstr "Платежные системы"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:195
msgid "Currency Icons"
msgstr "Валюты"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:196
msgid "Text Editor Icons"
msgstr "Редактирование"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:197
msgid "Directional Icons"
msgstr "Направление"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:198
msgid "Video Player Icons"
msgstr "Видео-плеер"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:199
msgid "Brand Icons"
msgstr "Бренды"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:200
msgid "Medical Icons"
msgstr "Медицина"

#: ../framework/includes/option-types/icon/view.php:39
msgid "All Categories"
msgstr "Все Категории"

#: ../framework/includes/option-types/datetime-range/view.php:41
#: ../framework/includes/option-types/gradient/view.php:46
msgid "to"
msgstr "в"

#: ../framework/includes/option-types/multi-picker/class-fw-option-type-multi-picker.php:179
#, php-format
msgid "No 'picker' key set for multi-picker option: %s"
msgstr "Не задан ключ 'picker' для опции multi-picker: %s"

#: ../framework/includes/option-types/background-image/view.php:37
msgid "Predefined images"
msgstr "Предопределенные изображения"

#: ../framework/includes/option-types/background-image/view.php:38
msgid "Custom image"
msgstr "Пользовательские изображение"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:160
msgid "Add Images"
msgstr "Добавить изображения"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:188
msgid "1 File"
msgstr "1 файл"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:189
#, php-format
msgid "%u Files"
msgstr "%u файлов"
