<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

$axil_options           = Helper::axil_get_options();
$shop_notification      = Helper::axil_shop_notification_enable();
$shop_notification      = $shop_notification['shop_notification'];
$notification_contact   = $axil_options['axil_shop_notification_contact'];
$allowed_tags = wp_kses_allowed_html('post');
if ("no" !== $shop_notification && "0" !== $shop_notification): ?>
    <div class="header-top-campaign">
        <div class="container position-relative axilcoutdown3">  
            <div class="campaign-content">
                 <div data-time="<?php echo esc_html($axil_options["shop_notification_date"]);?>" class="campaign-countdown"></div>  
                <p><?php echo wp_kses($notification_contact, $allowed_tags); ?></p>
            </div> 
        </div>
         <button class="remove-campaign"><i class="fal fa-times"></i></button>
    </div>     
<?php endif ?>

 