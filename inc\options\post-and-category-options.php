<?php
if ( function_exists( 'acf_add_local_field_group' ) ):

    acf_add_local_field_group( array(
        'key'                   => 'group_5f9fe5ffd60db',
        'title'                 => 'Category Options',
        'fields'                => array(
            array(
                'key'               => 'field_5f9fe61390c4c',
                'label'             => 'Category Background Image',
                'name'              => 'etrade_category_background_image',
                'type'              => 'image',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'return_format'     => 'url',
                'preview_size'      => 'medium',
                'library'           => 'all',
                'min_width'         => '',
                'min_height'        => '',
                'min_size'          => '',
                'max_width'         => '',
                'max_height'        => '',
                'max_size'          => '',
                'mime_types'        => '',
            ),
        ),
        'location'              => array(
            array(
                array(
                    'param'    => 'taxonomy',
                    'operator' => '==',
                    'value'    => 'all',
                ),
            ),
        ),
        'menu_order'            => 0,
        'position'              => 'normal',
        'style'                 => 'default',
        'label_placement'       => 'left',
        'instruction_placement' => 'label',
        'hide_on_screen'        => '',
        'active'                => true,
        'description'           => '',
    ) );

    acf_add_local_field_group( array(
        'key'                   => 'group_5ffc20f3d9094',
        'title'                 => 'Post Layout Options',
        'fields'                => array(
            array(
                'key'               => 'field_5ffc2103339c4',
                'label'             => 'Select Banner Style',
                'name'              => 'select_banner_style',
                'type'              => 'select',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'choices'           => array(
                    0 => 'Select',
                    1 => 'Full Banner',
                    2 => 'Boxed Banner',
                ),
                'default_value'     => false,
                'allow_null'        => 0,
                'multiple'          => 0,
                'ui'                => 0,
                'return_format'     => 'value',
                'ajax'              => 0,
                'placeholder'       => '',
            ),
            array(
                'key'               => 'field_600aa9305741f',
                'label'             => 'Select Sidebar',
                'name'              => 'select_sidebar',
                'type'              => 'select',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'choices'           => array(
                    0       => 'Select',
                    'left'  => 'Left Sidebar',
                    'right' => 'Right Sidebar',
                    'full'  => 'No Sidebar',
                ),
                'default_value'     => false,
                'allow_null'        => 0,
                'multiple'          => 0,
                'ui'                => 0,
                'return_format'     => 'value',
                'ajax'              => 0,
                'placeholder'       => '',
            ),
        ),
        'location'              => array(
            array(
                array(
                    'param'    => 'post_type',
                    'operator' => '==',
                    'value'    => 'post',
                ),
            ),
        ),
        'menu_order'            => 0,
        'position'              => 'normal',
        'style'                 => 'default',
        'label_placement'       => 'left',
        'instruction_placement' => 'label',
        'hide_on_screen'        => '',
        'active'                => true,
        'description'           => '',
    ) );

    acf_add_local_field_group( array(
        'key'                   => 'group_6000636f948ec',
        'title'                 => 'Post Review Options',
        'fields'                => array(
            array(
                'key'               => 'field_600063878106a',
                'label'             => 'Review Box',
                'name'              => 'axil_post_review_box',
                'type'              => 'true_false',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'message'           => 'Enable review on this post',
                'default_value'     => 0,
                'ui'                => 0,
                'ui_on_text'        => '',
                'ui_off_text'       => '',
            ),
            array(
                'key'               => 'field_600064d89ac49',
                'label'             => 'Review Box Position',
                'name'              => 'axil_post_review_box_position',
                'type'              => 'select',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_600063878106a',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'choices'           => array(
                    'under' => 'Under the post content',
                    'above' => 'Above the post content',
                ),
                'default_value'     => false,
                'allow_null'        => 0,
                'multiple'          => 0,
                'ui'                => 0,
                'return_format'     => 'value',
                'ajax'              => 0,
                'placeholder'       => '',
            ),
            array(
                'key'               => 'field_600066372d9df',
                'label'             => 'Review Image',
                'name'              => 'axil_post_review_image',
                'type'              => 'image',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_600063878106a',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'return_format'     => 'array',
                'preview_size'      => 'medium',
                'library'           => 'all',
                'min_width'         => '',
                'min_height'        => '',
                'min_size'          => '',
                'max_width'         => '',
                'max_height'        => '',
                'max_size'          => '',
                'mime_types'        => '',
            ),
            array(
                'key'               => 'field_60006691face2',
                'label'             => 'Review Name',
                'name'              => 'axil_post_review_name',
                'type'              => 'text',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_600063878106a',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'default_value'     => '',
                'placeholder'       => '',
                'prepend'           => '',
                'append'            => '',
                'maxlength'         => '',
            ),
            array(
                'key'               => 'field_600066cdface3',
                'label'             => 'Description',
                'name'              => 'axil_post_review_description',
                'type'              => 'textarea',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_600063878106a',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'default_value'     => '',
                'placeholder'       => '',
                'maxlength'         => '',
                'rows'              => '',
                'new_lines'         => '',
            ),
            array(
                'key'               => 'field_6000673cb3a20',
                'label'             => 'Review Score',
                'name'              => 'axil_post_review_score',
                'type'              => 'range',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_600063878106a',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'default_value'     => '',
                'min'               => 0,
                'max'               => 10,
                'step'              => '.1',
                'prepend'           => '',
                'append'            => '',
            ),
            array(
                'key'               => 'field_60006777b3a21',
                'label'             => 'Summary',
                'name'              => 'axil_post_review_summary',
                'type'              => 'textarea',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_600063878106a',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'default_value'     => '',
                'placeholder'       => '',
                'maxlength'         => '',
                'rows'              => 5,
                'new_lines'         => '',
            ),
            array(
                'key'               => 'field_6001602866222',
                'label'             => 'Pors and Cons',
                'name'              => 'axil_post_review_pors_and_cons',
                'type'              => 'true_false',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_600063878106a',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'message'           => 'Enable Pors and Cons on this post',
                'default_value'     => 0,
                'ui'                => 0,
                'ui_on_text'        => '',
                'ui_off_text'       => '',
            ),
            array(
                'key'               => 'field_600067dab3a23',
                'label'             => 'Pors',
                'name'              => 'axil_post_review_pors',
                'type'              => 'repeater',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_6001602866222',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'collapsed'         => '',
                'min'               => 0,
                'max'               => 0,
                'layout'            => 'table',
                'button_label'      => 'Add new Pors',
                'sub_fields'        => array(
                    array(
                        'key'               => 'field_60006883b3a24',
                        'label'             => 'Add Pors',
                        'name'              => 'axil_post_review_add_pors',
                        'type'              => 'text',
                        'instructions'      => '',
                        'required'          => 0,
                        'conditional_logic' => 0,
                        'wrapper'           => array(
                            'width' => '',
                            'class' => '',
                            'id'    => '',
                        ),
                        'default_value'     => '',
                        'placeholder'       => '',
                        'prepend'           => '',
                        'append'            => '',
                        'maxlength'         => '',
                    ),
                ),
            ),
            array(
                'key'               => 'field_60006929564df',
                'label'             => 'Cons',
                'name'              => 'axil_post_review_cons',
                'type'              => 'repeater',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field'    => 'field_6001602866222',
                            'operator' => '==',
                            'value'    => '1',
                        ),
                    ),
                ),
                'wrapper'           => array(
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ),
                'collapsed'         => '',
                'min'               => 0,
                'max'               => 0,
                'layout'            => 'table',
                'button_label'      => 'Add New Cons',
                'sub_fields'        => array(
                    array(
                        'key'               => 'field_60006959564e0',
                        'label'             => 'Add Cons',
                        'name'              => 'axil_post_review_add_cons',
                        'type'              => 'text',
                        'instructions'      => '',
                        'required'          => 0,
                        'conditional_logic' => 0,
                        'wrapper'           => array(
                            'width' => '',
                            'class' => '',
                            'id'    => '',
                        ),
                        'default_value'     => '',
                        'placeholder'       => '',
                        'prepend'           => '',
                        'append'            => '',
                        'maxlength'         => '',
                    ),
                ),
            ),
        ),
        'location'              => array(
            array(
                array(
                    'param'    => 'post_type',
                    'operator' => '==',
                    'value'    => 'post',
                ),
            ),
        ),
        'menu_order'            => 0,
        'position'              => 'normal',
        'style'                 => 'default',
        'label_placement'       => 'left',
        'instruction_placement' => 'label',
        'hide_on_screen'        => '',
        'active'                => true,
        'description'           => '',
    ) );

endif;