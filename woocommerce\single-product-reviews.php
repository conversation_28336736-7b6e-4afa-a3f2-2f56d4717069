<?php
/**
 * Display single product reviews (comments)
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product-reviews.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce/Templates
 * @version 4.3.0
 */

if ( !defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

global $product;

if ( !comments_open() ) {
    return;
}
?>
<div id="axiltheme-wc-reviews" class="reviews-wrapper">
	<div  class="row">
		<div class="col-lg-6 mb--40">
			<div class="axil-comment-area pro-desc-commnet-area">
				<div id="comments"> 
				<h5 class="title mb--30 woocommerce-Reviews-title">
				<?php
				$count = $product->get_review_count();
				if ( $count && wc_review_ratings_enabled() ) {
					/* translators: 1: reviews count 2: product name */
					$reviews_title = sprintf( esc_html( _n( '%1$s review for %2$s', '%1$s reviews for %2$s', $count, 'etrade' ) ), esc_html( $count ), '<span>' . get_the_title() . '</span>' );
					echo apply_filters( 'woocommerce_reviews_title', $reviews_title, $count, $product ); // WPCS: XSS ok.
				} else {
					esc_html_e( 'Reviews', 'etrade' );
				}
				?>
				</h5>
				<?php if ( have_comments() ): ?>

					<ol class="comment-list">
						<?php wp_list_comments( apply_filters( 'woocommerce_product_review_list_args', array( 'callback' => 'woocommerce_comments' ) ) );?>
					</ol>

					<?php if ( get_comment_pages_count() > 1 && get_option( 'page_comments' ) ):
					echo '<nav class="woocommerce-pagination">';
					paginate_comments_links( apply_filters( 'woocommerce_comment_pagination_args', array(
						'prev_text' => '&larr;',
						'next_text' => '&rarr;',
						'type'      => 'list',
					) ) );
					echo '</nav>';
				endif;?> 
				<?php else: ?> 
					<p class="woocommerce-noreviews"><?php esc_html_e( 'There are no reviews yet.', 'etrade' );?></p> 
				<?php endif;?>
			</div>
		</div>
	</div>
	<div class="col-lg-6 mb--40 pro-des-commend-respond ">
	<?php if ( get_option( 'woocommerce_review_rating_verification_required' ) === 'no' || wc_customer_bought_product( '', get_current_user_id(), $product->get_id() ) ): ?>
		<?php
			$commenter = wp_get_current_commenter(); 
			$comment_form = array(
				'title_reply'         => have_comments() ? esc_html__( 'Add a review', 'etrade' ) : sprintf( esc_html__( 'Be the first to review &ldquo;%s&rdquo;', 'etrade' ), get_the_title() ),
				'title_reply_to'      => esc_html__( 'Leave a Reply to %s', 'etrade' ),
				'title_reply_before'  => '<h5 id="title" class="comment-reply-title title">',
				'title_reply_after'   => '</h5>',
				'comment_notes_after' => '',
				'fields'              => array(
					'author' => '<p class="comment-form-author">' . '<label for="author">' . esc_html__( 'Name', 'etrade' ) . '&nbsp;<span class="required">*</span></label> ' .
					'<input id="author" name="author" type="text" value="' . esc_attr( $commenter['comment_author'] ) . '" size="30" required /></p>',
					'email'  => '<p class="comment-form-email"><label for="email">' . esc_html__( 'Email', 'etrade' ) . '&nbsp;<span class="required">*</span></label> ' .
					'<input id="email" name="email" type="email" value="' . esc_attr( $commenter['comment_author_email'] ) . '" size="30" required /></p>',
				),
				'label_submit'        => esc_html__( 'Submit Comment', 'etrade' ),
				'logged_in_as'        => '',
				'comment_field'       => '',
			);

			if ( $account_page_url = wc_get_page_permalink( 'myaccount' ) ) {
				$comment_form['must_log_in'] = '<p class="must-log-in">' . sprintf( __( 'You must be <a href="%s">logged in</a> to post a review.', 'etrade' ), esc_url( $account_page_url ) ) . '</p>';
			}

			if ( wc_review_ratings_enabled() ) {
				$comment_form['comment_field'] = '<div class="comment-form-rating"><label for="rating">' . esc_html__( 'Your rating', 'etrade' ) . '</label><select name="rating" id="rating" required>
								<option value="">' . esc_html__( 'Rate&hellip;', 'etrade' ) . '</option>
								<option value="5">' . esc_html__( 'Perfect', 'etrade' ) . '</option>
								<option value="4">' . esc_html__( 'Good', 'etrade' ) . '</option>
								<option value="3">' . esc_html__( 'Average', 'etrade' ) . '</option>
								<option value="2">' . esc_html__( 'Not that bad', 'etrade' ) . '</option>
								<option value="1">' . esc_html__( 'Very poor', 'etrade' ) . '</option>
							</select></div>';
			}

			$comment_form['comment_field'] .= '<label>Other Notes (optional)</label><p class="comment-form-comment"><label for="comment">' . esc_html__( 'Your review', 'etrade' ) . '&nbsp;<span class="required">*</span></label><textarea id="comment" name="comment" cols="45" rows="8" required></textarea></p>';

			comment_form( apply_filters( 'woocommerce_product_review_comment_form_args', $comment_form ) );
			?> 
			<?php else: ?>
				<p class="woocommerce-verification-required"><?php esc_html_e( 'Only logged in customers who have purchased this product may leave a review.', 'etrade' );?></p>
			<?php endif;?>
			<div class="clear"></div>
		</div>
	</div>
</div>
