<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Controls_Manager;
use Elementor\Plugin;
use Elementor\Widget_Base;
use Elementor\Group_Control_Typography;
use Elementor\Scheme_Typography;
use Elementor\Group_Control_Image_Size;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly
class Title extends Widget_Base {  

 public function get_name() {
        return 'wooc-title';
    }    
    public function get_title() {
        return __( 'Section Title', 'etrade-elements' );
    }
    public function get_icon() {
        return 'eicon-post-title';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
    public function axil_get_img($img)
     {
         $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
         return $img;
    }

     private function axil_get_all_pages()
        {

            $page_list = get_posts(array(
                'post_type' => 'page',
                'orderby' => 'date',
                'order' => 'DESC',
                'posts_per_page' => -1,
            ));

            $pages = array();

            if (!empty($page_list) && !is_wp_error($page_list)) {
                foreach ($page_list as $page) {
                    $pages[$page->ID] = $page->post_title;
                }
            }

            return $pages;
        }
  protected function register_controls() {

     $this->start_controls_section(
            'title_section',
            [
                'label' => __( 'Section Title ', 'etrade-elements' ),
            ]
        );
            
        $this->add_control(
            'style',
            [
                'label' => __( 'Layout', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT,
                'default' => '1',
                'options' => [
                    '1'   => __( 'Style One', 'etrade-elements' ),
                    '2'   => __( 'Style Two', 'etrade-elements' ),                  
                    '3'   => __( 'Style Three', 'etrade-elements' ),

                ],
            ] 
        ); 
    

        $this->add_responsive_control(
            'title_align',
            [
                'label'   => __( 'Alignment', 'etrade-elements' ),
                'type'    => Controls_Manager::CHOOSE,                
                'options' => [
                    'left'    => [
                        'title' => __( 'Left', 'etrade-elements' ),
                        'icon'  => 'fa fa-align-left',
                    ],
                    'center' => [
                        'title' => __( 'Center', 'etrade-elements' ),
                        'icon'  => 'fa fa-align-center',
                    ],
                    'right' => [
                        'title' => __( 'Right', 'etrade-elements' ),
                        'icon'  => 'fa fa-align-right',
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .section-title-wrapper'   => 'text-align: {{VALUE}}',
                    
                ],
                'default' => 'center',
                'separator'     => 'after', 
                'condition'   => array( 'style' =>  array( '1','2' )), 
            ]
        ); 
        $this->add_control(
            'sub_title',
            [
                'label' => __( 'Before Title', 'etrade-elements' ),
                'type' => Controls_Manager::TEXTAREA,
                'placeholder' => __( 'Type your Description here.', 'etrade-elements' ),    
                'default' => 'Section sub title here',   
                'condition'   => array( 'style' =>  array( '1','2' )),          
            ]
        );
       $this->add_control(
            'beforetitlestyle',
            [
                'label' => esc_html__( 'Before Color', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT,
                'default' => 'primary',
                'condition'   => array( 'style' =>  array( '1','2' )), 
                'options' => [
                    'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                    'secondary'   => esc_html__( 'Secondary', 'etrade-elements' ),                                          
                    'primary2'   => esc_html__( 'Primary 2', 'etrade-elements' ),                                          
                                                              
                ],
            ] 
        );   
          $this->add_control(
            'icon',
            [
                'label' => __( 'Icons', 'etrade-elements' ),
                'type' => Controls_Manager::ICONS,
                'condition'   => array( 'style' =>  array( '1','2' )), 
                'separator'     => 'after', 
                'default' => [
                    'value' => 'fa fa-university',
                    'library' => 'solid',
                ],
                      
            ]
        );

     
        $this->add_control(
            'title',
            [
                'label' => __( 'Title', 'etrade-elements' ),
                'type' => Controls_Manager::TEXTAREA,
                'placeholder' => __( 'Type your title here...', 'etrade-elements' ),
                'default' => 'Section title here',
            ]
        );  
       $this->add_control(
         'image',
            [
                'label' => esc_html__('Product Image','etrade-elements'),
                'type'=>Controls_Manager::MEDIA,
                'default' => [
                    'url' =>  $this->axil_get_img( 'title-icon.png' ),
                ],
                'condition'   => array( 'style' =>  array( '3' )), 
                'dynamic' => [
                    'active' => true,
                ],
                    
            ]
        );       

        $this->add_group_control(
                Group_Control_Image_Size::get_type(),
                [
                    'name' => 'image_size',
                    'default' => 'full',
                    'separator' => 'none',
                    'condition'   => array( 'style' =>  array( '3' )), 
                       
                ]
            );

        $this->add_responsive_control(
            'sec_title_tag',
            [
                'label' => __( 'Title HTML Tag', 'etrade-elements' ),
                'type' => Controls_Manager::CHOOSE,
                'label_block'   => true,     
                 'options' => [
                      'h1'  => [
                        'title' => esc_html__( 'Layout One', 'etrade-elements' ),
                        'icon' => $this->axil_get_img( 'collection_6.png' ),
                    ],
                    'h2'  => [
                        'title' => esc_html__( 'H2', 'etrade-elements' ),
                        'icon' => 'eicon-editor-h2'
                    ],
                    'h3'  => [
                        'title' => esc_html__( 'H3', 'etrade-elements' ),
                        'icon' => 'eicon-editor-h3'
                    ],
                    'h4'  => [
                        'title' => esc_html__( 'H4', 'etrade-elements' ),
                        'icon' => 'eicon-editor-h4'
                    ],
                    'h5'  => [
                        'title' => esc_html__( 'H5', 'etrade-elements' ),
                        'icon' => 'eicon-editor-h5'
                    ],
                    'h6'  => [
                        'title' => esc_html__( 'H6', 'etrade-elements' ),
                        'icon' => 'eicon-editor-h6'
                    ],
                    'div'  => [
                        'title' => esc_html__( 'div', 'etrade-elements' ),
                        'icon' => 'eicon-font'
                    ]
                ],
                'default' => 'h3', 


            ]
        );

        $this->add_responsive_control(
            'bottom_space',
            [
                'label' => esc_html__( 'Bottom Space', 'etrade-elements' ),
                'type' => Controls_Manager::SLIDER,
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 1000,
                    ],
                ],
                'devices' => [ 'desktop', 'tablet', 'mobile' ],
                'desktop_default' => [
                    'size' => 0,
                    'unit' => 'px',
                ],
                'tablet_default' => [
                    'size' => 0,
                    'unit' => 'px',
                ],
                'mobile_default' => [
                    'size' => 0,
                    'unit' => 'px',
                ],
                'selectors' => [
                    '{{WRAPPER}} .section-title-wrapper' => 'margin-bottom: {{SIZE}}{{UNIT}};',
                ],
            ]
        );
        $this->end_controls_section(); 
       

        $this->start_controls_section(
            'link_style_section',
            [
                'label' => __( 'View All Link', 'etrade-elements' ), 
                'condition'   => array( 'style' =>  array( '3' )),          
            ]
        );

        $this->add_control(
            'title_link_text',
            [
                'label' => esc_html__('Link Text','etrade-elements'),
                'type' => Controls_Manager::TEXT,
                'default' => 'View All Products',
                'title' => esc_html__('Enter button text','etrade-elements'),
            ]
        );

          $this->add_control(
            'title_link_type',
            [
                'label' => esc_html__('Link Type','etrade-elements'),
                'type' => Controls_Manager::SELECT,
                'options' => [
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ],
                'default' => '1',
            ]
        );
          $this->add_control(
            'title_link',
            [
                'label' => esc_html__('Link link','etrade-elements'),
                'type' => Controls_Manager::URL,
                'dynamic' => [
                    'active' => true,
                ],
                'placeholder' => esc_html__('https://your-link.com','etrade-elements'),
                'show_external' => true,
                'default' => [
                    'url' => '#',
                    'is_external' => true,
                    'nofollow' => true,
                ],
                'condition' => [
                    'title_link_type' => '1'
                ]
            ]
        );
          $this->add_control(
            'title_page_link',
            [
                'label' => esc_html__('Select Link Page','etrade-elements'),
                'type' => Controls_Manager::SELECT2,
                'label_block' => true,
                'options' =>  $this-> axil_get_all_pages(),
                'condition' => [
                    'title_link_type' => '2'
                ]
            ]
        );

         $this->end_controls_section(); 


         $this->start_controls_section(
            'axil_sub_title_style_section',
            [
                'label' => __( 'Sub Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );

        
        $this->add_control(
            'sub_title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .section-title-wrapper .sub-title' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .section-title-wrapper .title-highlighter i' => 'background-color: {{VALUE}}',
                ),
            ]
        );
         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'sub_title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .sub-title',
            ]
        );
       
        $this->add_responsive_control(
            'sub_title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .sub-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
       
        $this->end_controls_section();

        $this->start_controls_section(
            'title_style_section',
            [
                'label' => __( 'Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
            $this->add_control(
            'title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .sec-title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),   
                'selector' => '{{WRAPPER}} .sec-title',
            ]
        );
       
        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .sec-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
       
    $this->end_controls_section();



  }


    protected function render() {
        $settings = $this->get_settings();
        $template   = 'title-' . str_replace("style", "", $settings['style']);                 
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
    }
}
