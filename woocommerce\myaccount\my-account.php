<?php
/**
 * My Account page
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/my-account.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.5.0
 */

defined( 'ABSPATH' ) || exit;
?>
<div class="axil-dashboard-warp">
	<?php
	if ( is_user_logged_in() ) {
		$current_user = wp_get_current_user();
		if (  ( $current_user instanceof WP_User ) ) {?>
			<div class="axil-dashboard-author">
				<div class="media">
					<div class="thumbnail">
						<?php echo get_avatar( $current_user->ID, 70 ); ?>
					</div>
					<div class="media-body ml--10">
						<h5 class="title mb-0"><?php echo esc_html( $current_user->display_name ); ?></h5>
						<span class="joining-date">
						<?php
						$udata = get_userdata( $current_user->ID );
								$registered = $udata->user_registered;
								//printf( 'etrade Member Since %s<br>', date( "M Y", strtotime( $registered ) ) );
								?>
						</span>
					</div>
				</div>
			</div>
			<?php
		}
	}
	?>
	<div class="row row--30">
		<div class="col-xl-3 col-md-4">
				<div class="axil-dashboard-aside">
					<?php do_action( 'woocommerce_account_navigation' );?>
				</div>
			</div>
		<div class="col-xl-9 col-md-8"><div class="axil-dashboard-account woocommerce-MyAccount-content tab-content"><?php do_action( 'woocommerce_account_content' );?></div></div>
	</div>
</div>