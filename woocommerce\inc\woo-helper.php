<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

/* Helper Functions
=============================================================== */

global $wooc_woocommerce_enabled;
$wooc_woocommerce_enabled = ( class_exists( 'WooCommerce' ) ) ? true : false;

/* Check if WooCommerce is activated */
function wooc_woocommerce_activated() {
    global $wooc_woocommerce_enabled;
    return $wooc_woocommerce_enabled;
}

/* Check if the current page is a WooCommmerce page */
function wooc_is_woocommerce_page() {
    // Get the current body class
    $body_classes = get_body_class();

    foreach ( $body_classes as $body_class ) {
        // Check if the class contains the word "woocommerce"
        if ( strpos( $body_class, 'etrade' ) !== false ) {
            return true;
        }
    }

    return false;
}

/* Add page include slug */
function wooc_add_page_include( $slug ) {
    global $wooc_page_includes;
    $wooc_page_includes[$slug] = true;
}

/* Get post categories */
function wooc_get_post_categories() {
    $args = array(
        'type'         => 'post',
        'child_of'     => 0,
        'parent'       => '',
        'orderby'      => 'name',
        'order'        => 'ASC',
        'hide_empty'   => 1,
        'hierarchical' => 1,
        'exclude'      => '',
        'include'      => '',
        'number'       => '',
        'taxonomy'     => 'category',
        'pad_counts'   => false,
    );

    $categories = get_categories( $args );

    $return = array( 'All' => '' );

    foreach ( $categories as $category ) {
        $return[wp_specialchars_decode( $category->name )] = $category->slug;
    }

    return $return;
};