<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */
$shop_permalink ='';
$axil_options           = Helper::axil_get_options();
$search      = isset( $_GET['s'] ) ? $_GET['s'] : '';
$product_cat = isset( $_GET['product_cat'] ) ? $_GET['product_cat'] : '';
$all_label = $label = esc_html__( 'All Categories', 'etrade' );

if ( isset( $_GET['product_cat'] ) ) {
    $pcat = $_GET['product_cat'];
    if ( isset( $category_dropdown[$pcat] ) ) {
        $label = $category_dropdown[$pcat]['name'];
    }
}
if( WOOC_WOO_ACTIVED ): 
    $shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
endif ; 
 ?> 
<div class="header-action">
    <ul class="action-list"> 
        <?php if( WOOC_WOO_ACTIVED ): ?>  
            <?php get_template_part('template-parts/header/header-right/header', 'search2'); ?>  
        <?php endif ; ?>   
        <?php get_template_part('template-parts/header/header-right/header-right', 'wishlist'); ?>  
        <?php if( WOOC_WOO_ACTIVED ): ?>  
            <?php get_template_part('template-parts/header/header-right/header-right', 'cart'); ?>  
            <?php get_template_part('template-parts/header/header-right/header-right', 'account'); ?> 
        <?php endif ; ?>  
        <?php get_template_part('template-parts/header/header-right/header-right', 'mobile-nav-toggler'); ?>   
    </ul>
</div>  
<div class="header-search-modal" id="header-search-modal">
    <button class="card-close sidebar-close"><i class="fas fa-times"></i></button>
    <div class="header-search-wrap">
        <div class="card-header">
            <form action="#">
                <div class="input-group">
                    <input type="search" class="form-control" name="prod-search" id="prod-search" placeholder="<?php echo esc_html__('Write Something....', 'etrade'); ?>">
                    <button type="submit" class="axil-btn btn-bg-primary"><i class="fal fa-search"></i></button>
                </div>
            </form>
        </div>
        <div class="search_input_loader loader1"></div>
            <div class="card-body search-results-body">
            <div class="recent-product">
                <div class="search-result-header">
                    <h6 class="title"><?php echo esc_html__('Recent Product', 'etrade'); ?></h6>
                    <a href="<?php echo esc_url($shop_permalink);?>" class="view-all"><?php echo esc_html__('View All', 'etrade'); ?></a>
                </div>
                <div class="psearch-results"> 
                    <?php axil_is_recent_product();?>  
                </div>
            </div>
        </div> 
    </div>
</div>