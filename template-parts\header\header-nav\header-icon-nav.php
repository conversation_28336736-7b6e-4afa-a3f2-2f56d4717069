<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

$axil_options = Helper::axil_get_options();
$mobile_logo  = empty($axil_options['axil_head_logo']['url'] ) ? Helper::get_img( 'logo/logo.svg' ) :$axil_options['axil_head_logo']['url'];
?>    
<div class="header-main-nav">
    <nav class="mainmenu-nav">
    <button class="mobile-close-btn mobile-nav-toggler"><i class="fas fa-times"></i></button>
    <div class="mobile-nav-brand">
        <a href="index.html" class="logo">
            <img src="<?php echo esc_url( $mobile_logo ); ?>" alt="<?php echo esc_attr(get_bloginfo('name')); ?>">
        </a>
    </div>  
    <?php
        wp_nav_menu( array( 
            'theme_location'        => 'iconmenu',
            'container'             => 'nav',
            'container_class'       => 'department-nav-menu',                                    
            'menu_class'            => 'mainmenu nav-menu-list',
            'fallback_cb'           => false,
            'walker'                => new \AxilNavWalker( true, true)
            
        ) );
        ?>
    </nav>
</div>