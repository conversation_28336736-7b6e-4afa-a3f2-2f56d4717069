<?php
/**
 * etrade functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package etrade
 */

define( 'AXIL_THEME_URI', get_template_directory_uri() );
define( 'AXIL_THEME_DIR', get_template_directory() );
define( 'AXIL_CSS_URL', get_template_directory_uri() . '/assets/css/' );
define( 'AXIL_JS_URL', get_template_directory_uri() . '/assets/js/' );
define( 'AXIL_ADMIN_CSS_URL', get_template_directory_uri() . '/assets/admin/css/' );
define( 'AXIL_ADMIN_JS_URL', get_template_directory_uri() . '/assets/admin/js/' );
define( 'AXIL_FREAMWORK_DIRECTORY', AXIL_THEME_DIR . '/inc/' );
define( 'AXIL_FREAMWORK_HELPER', AXIL_THEME_DIR . '/inc/helper/' );
define( 'AXIL_FREAMWORK_OPTIONS', AXIL_THEME_DIR . '/inc/options/' );
define( 'AXIL_FREAMWORK_CUSTOMIZER', AXIL_THEME_DIR . '/inc/customizer/' );
define( 'AXIL_THEME_FIX', 'axil' );
define( 'AXIL_FREAMWORK_LAB', AXIL_THEME_DIR . '/inc/lab/' );
define( 'AXIL_FREAMWORK_TP', AXIL_THEME_DIR . '/template-parts/' );
define( 'AXIL_IMG_URL', AXIL_THEME_URI . '/assets/images/logo/' );
do_action( 'etrade_theme_init' );
/* user info */
defined( 'WOOC_CORE_USER_LOGGED' ) or define( 'WOOC_CORE_USER_LOGGED', is_user_logged_in() );

/* Check if WooCommerce active */
defined( 'WOOC_WOO_ACTIVED' ) or define( 'WOOC_WOO_ACTIVED', (bool) function_exists( 'WC' ) );

/* Check if DOKAN active */
defined( 'WOOC_DOKAN_ACTIVED' ) or define( 'WOOC_DOKAN_ACTIVED', (bool) function_exists( 'dokan' ) );

defined( 'WOOC_WISHLIST_ENABLE' ) or define( 'WOOC_WISHLIST_ENABLE', (bool) defined( 'YITH_WCWL' ) );

$axi_theme_data = wp_get_theme();
define( 'AXIL_VERSION', ( WP_DEBUG ) ? time() : $axi_theme_data->get( 'Version' ) );

global $wooc_globals;
$wooc_globals = array();

// Globals: WooCommerce - Custom variation controls

$wooc_globals['pa_color_slug'] = sanitize_title( apply_filters( 'wooc_color_attribute_slug', 'color' ) );
$wooc_globals['pa_variation_controls'] = array(
    'color' => esc_html__( 'Color', 'etrade' ),
    'image' => esc_html__( 'Image', 'etrade' ),
    'size'  => esc_html__( 'Label', 'etrade' ),
);
$wooc_globals['pa_cache'] = array();

if ( !function_exists( 'etrade_setup' ) ):
    /**
     * Sets up theme defaults and registers support for various WordPress features.
     *
     * Note that this function is hooked into the after_setup_theme hook, which
     * runs before the init hook. The init hook is too late for some features, such
     * as indicating support for post thumbnails.
     */
    function etrade_setup() {
        /*
         * Make theme available for translation.
         * Translations can be filed in the /languages/ directory.
         * If you're building a theme based on etrade, use a find and replace
         * to change 'etrade' to the name of your theme in all the template files.
         */
        load_theme_textdomain( 'etrade', get_template_directory() . '/languages' );

        // Add default posts and comments RSS feed links to head.
        add_theme_support( 'automatic-feed-links' );

        /*
         * Let WordPress manage the document title.
         * By adding theme support, we declare that this theme does not use a
         * hard-coded <title> tag in the document head, and expect WordPress to
         * provide it for us.
         */
        add_theme_support( 'title-tag' );

        /*
         * Enable support for Post Thumbnails on posts and pages.
         *
         * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
         */
        add_theme_support( 'post-thumbnails' );

        // This theme uses wp_nav_menu() in one location.
        register_nav_menus( array(
            'primary'          => esc_html__( 'Primary', 'etrade' ),
            'categoryiconmenu' => esc_html__( 'Category Icon Menu', 'etrade' ),
            'iconmenu'         => esc_html__( 'Icon Menu', 'etrade' ),
            'headertop'        => esc_html__( 'Header Top Menu (No depth supported)', 'etrade' ),
            'footerbottom'     => esc_html__( 'Footer Bottom Menu (No depth supported)', 'etrade' ),
            'myaccount'        => esc_html__( 'My Account', 'etrade' ),
             
        ) );

        /*
         * Switch default core markup for search form, comment form, and comments
         * to output valid HTML5.
         */
        add_theme_support( 'html5', array(
            'search-form',
            'comment-form',
            'comment-list',
            'gallery',
            'caption',
        ) );

        // Add theme support for selective refresh for widgets.
        add_theme_support( 'customize-selective-refresh-widgets' );

        /**
         * Post Format
         */
        add_theme_support( 'post-formats', array( 'gallery', 'link', 'quote', 'video', 'audio' ) );

        add_theme_support( 'editor-styles' );
        add_editor_style( 'style-editor.css' );
        add_editor_style( array( 'style-editor.css', axil_fonts_url() ) );
        add_theme_support( 'responsive-embeds' );
        add_theme_support( 'wp-block-styles' );
        add_theme_support( 'woocommerce' );

        // for gutenberg support
        add_theme_support( 'align-wide' );
        add_theme_support( 'editor-color-palette', array(
            array(
                'name'  => esc_html__( 'Primary', 'etrade' ),
                'slug'  => 'etrade-primary',
                'color' => '#3577f0',
            ),
            array(
                'name'  => esc_html__( 'Secondary', 'etrade' ),
                'slug'  => 'etrade-secondary',
                'color' => '#ff497c',
            ),
            array(
                'name'  => esc_html__( 'Tertiary', 'etrade' ),
                'slug'  => 'etrade-tertiary',
                'color' => '#FAB8C4',
            ),
            array(
                'name'  => esc_html__( 'White', 'etrade' ),
                'slug'  => 'etrade-white',
                'color' => '#ffffff',
            ),
            array(
                'name'  => esc_html__( 'Dark', 'etrade' ),
                'slug'  => 'etrade-dark',
                'color' => '#27272E',
            ),
        ) );

        add_theme_support( 'editor-font-sizes', array(
            array(
                'name' => esc_html__( 'Small', 'etrade' ),
                'size' => 12,
                'slug' => 'small',
            ),
            array(
                'name' => esc_html__( 'Normal', 'etrade' ),
                'size' => 16,
                'slug' => 'normal',
            ),
            array(
                'name' => esc_html__( 'Large', 'etrade' ),
                'size' => 36,
                'slug' => 'large',
            ),
            array(
                'name' => esc_html__( 'Huge', 'etrade' ),
                'size' => 58,
                'slug' => 'huge',
            ),
        ) );

        /**
         * Add Custom Image Size
         */
        add_image_size( 'axil-blog-thumb', 295, 250, true );
        add_image_size( 'axil-single-blog-thumb', 1300, 600, true ); // blog single
        add_image_size( 'axil-thumbnail-sm', 60, 60, true );
        add_image_size( 'axil-cat-thumbnail-sm', 260, 170, true );
        add_image_size( 'axil-blog-grid', 640, 360, true );
        add_image_size( 'axil-blog-list', 850, 450, true );

    }
endif;
add_action( 'after_setup_theme', 'etrade_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function etrade_content_width() {
    // This variable is intended to be overruled from themes.
    // Open WPCS issue: {@link https://github.com/WordPress-Coding-Standards/WordPress-Coding-Standards/issues/1043}.
    // phpcs:ignore WordPress.NamingConventions.PrefixAllGlobals.NonPrefixedVariableFound
    $GLOBALS['content_width'] = apply_filters( 'etrade_content_width', 640 );
}

add_action( 'after_setup_theme', 'etrade_content_width', 0 );

/**
 * Enqueue scripts and styles.
 */
require_once AXIL_FREAMWORK_DIRECTORY . "scripts.php";

/**
 * Global Functions
 */
require_once AXIL_FREAMWORK_DIRECTORY . "global-functions.php";

/**
 * Register Custom Widget Area
 */
require_once AXIL_FREAMWORK_DIRECTORY . "widget-area-register.php";

/**
 * Register Custom Fonts
 */
require_once AXIL_FREAMWORK_DIRECTORY . "register-custom-fonts.php";

/**
 * TGM
 */
require_once AXIL_FREAMWORK_DIRECTORY . "tgm-config.php";

require_once AXIL_FREAMWORK_DIRECTORY . "loadmore.php";

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/underscore/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/underscore/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/underscore/template-functions.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
    require get_template_directory() . '/inc/underscore/jetpack.php';
}

/**
 * Helper Template
 */
require_once AXIL_FREAMWORK_HELPER . "menu-area-trait.php";
require_once AXIL_FREAMWORK_HELPER . "layout-trait.php";
require_once AXIL_FREAMWORK_HELPER . "option-trait.php";
require_once AXIL_FREAMWORK_HELPER . "meta-trait.php";
require_once AXIL_FREAMWORK_HELPER . "banner-trait.php";

// Helper
require_once AXIL_FREAMWORK_HELPER . "helper.php";

/**
 * Options
 */
require_once AXIL_FREAMWORK_OPTIONS . "theme/option-framework.php";
require_once AXIL_FREAMWORK_OPTIONS . "page-options.php";
require_once AXIL_FREAMWORK_OPTIONS . "post-format-options.php";
require_once AXIL_FREAMWORK_OPTIONS . "user-extra-meta.php";
require_once AXIL_FREAMWORK_OPTIONS . "menu-options.php";
require_once AXIL_FREAMWORK_OPTIONS . "single-product.php";

/**
 * Customizer
 */
require_once AXIL_FREAMWORK_CUSTOMIZER . "color.php";

/**
 * Lab
 */
require_once AXIL_FREAMWORK_LAB . "class-tgm-plugin-activation.php";
require_once AXIL_FREAMWORK_LAB . "aw-nav-menu-walker.php";
require_once AXIL_FREAMWORK_LAB . "aw-mobile-menu-walker.php";
require_once AXIL_FREAMWORK_TP . "title/breadcrumb.php";
require_once AXIL_FREAMWORK_DIRECTORY . "ajax-search.php";

if ( class_exists( 'WooCommerce' ) ) {
    require_once AXIL_THEME_DIR . "/woocommerce/inc/woo-helper.php";
    require_once AXIL_THEME_DIR . "/woocommerce/custom/functions.php";
    require_once AXIL_THEME_DIR . "/woocommerce/custom/single-functions.php";
    //require_once AXIL_THEME_DIR . "/woocommerce/inc/woocommerce-attribute-functions.php";
    require_once AXIL_THEME_DIR . "/woocommerce/inc/admin/admin-product-attributes.php";
    require_once AXIL_THEME_DIR . "/woocommerce/inc/admin/admin-product-data.php";
    require_once AXIL_THEME_DIR . "/woocommerce/inc/modules/aska-question.php"; 

}
if ( function_exists( 'dokan' ) ) { 
    require_once AXIL_THEME_DIR . "/dokan/custom/functions.php";
}

add_action( 'init', 'axil_remove_wc_noscript' );
function axil_remove_wc_noscript(){
  remove_action( 'wp_head', 'wc_gallery_noscript' );
};
add_filter( 'woocommerce_dropdown_variation_attribute_options_args', static function( $args ) {
    $args['class'] = 'dropdown-set-attribute';
    return $args;
}, 2 );

// update Version: 1.3.2
add_filter('admin_body_class', function($classes) {
    return is_string($classes) ? $classes : '';
});
