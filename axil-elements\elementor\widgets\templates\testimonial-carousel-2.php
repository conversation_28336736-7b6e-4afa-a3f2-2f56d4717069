<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;
?>
<div class="axil-testimoial-area testimonial-slick-activation-wrapper">
	<?php if ( $settings['section_title_display'] ): ?>
		<div class="section-title-wrapper">
			<?php if ( $settings['sub_title'] ): ?>
		    <span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?>"><?php Icons_Manager::render_icon( $settings['icon'] ); ?> <?php echo wp_kses_post( $settings['sub_title'] );?></span>
		    	<?php endif; ?>	

		    <?php  if($settings['title']){ ?>
                <<?php echo esc_html( $settings['sec_title_tag'] );?> class="title sec-title">
                <?php echo wp_kses_post( $settings['title'] );?></<?php echo esc_html( $settings['sec_title_tag'] );?>>   
            <?php  } ?>   
		</div>
	<?php endif; ?> 
    <div class="testimonial-slick-activation testimonial-style-one testimonial-style-one-wrapper slick-layout-wrapper--20 axil-slick-arrow arrow-top-slide">
		<?php foreach ( $settings['list_testimonial'] as $testimonial ): 					
			$has_designation  = $testimonial['designation'] == 'yes' ? true : false;			
			$designation  	  = $testimonial['designation'];
			$title  			= $testimonial['title'];	
			$content  			= $testimonial['content'];	
			$size 				= 'axil-thumbnail-sm';
			$img 				= wp_get_attachment_image( $testimonial['testimonial_image']['id'], $size );
			?>
	        <div class="slick-single-layout slick-slide ltestimonial-style-one">
	            <div class="review-speech">
	                <p>“<?php echo esc_html($content);?>”</p>
	            </div>
	            <div class="media"> 
					<?php if ($img) { ?>
						<div class="thumbnail">								
							<?php echo wp_kses_post($img);?>	            			
						</div>
					<?php } ?>	 
	                <div class="media-body">
	                    <span class="designation"><?php echo esc_html($designation);?></span>
	                    <h6 class="title"><?php echo esc_html($title);?></h6>
	                </div>
	            </div> 
	        </div> 
         <?php  endforeach;?>
        </div> 
</div> 