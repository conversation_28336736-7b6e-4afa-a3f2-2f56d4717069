<?php
/**
 * Cart Page
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/cart.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 7.9.0
 */


defined( 'ABSPATH' ) || exit;

do_action( 'woocommerce_before_cart' );?>
<div class="axil-product-cart-wrap">
	<div class="product-table-heading">
		<h1 class="page-title h4 title"><?php wp_title( '' );?></h1>

		<?php do_action( 'cart_clear_woocommerce_empty_cart_action' );?>
	</div>
	<form class="woocommerce-cart-form" action="<?php echo esc_url( wc_get_cart_url() ); ?>" method="post">
	<?php do_action( 'woocommerce_before_cart_table' );?>

<div class="table-responsive">
	<table class="shop_table axil-product-table axil-cart-table shop_table_responsive cart woocommerce-cart-form__contents">
		<thead>
			<tr>
				<th class="product-remove">&nbsp;</th>
				<th class="product-thumbnail">&nbsp;</th>
				<th class="product-title"><?php esc_html_e( 'Product Title', 'etrade' );?></th>
				<th class="product-price"><?php esc_html_e( 'Price', 'etrade' );?></th>
				<th class="product-quantity"><?php esc_html_e( 'Quantity', 'etrade' );?></th>
				<th class="product-subtotal"><?php esc_html_e( 'Subtotal', 'etrade' );?></th>
			</tr>
		</thead>
		<tbody>
			<?php do_action( 'woocommerce_before_cart_contents' );?>
			<?php
		foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
		$_product = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
		$product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );

    if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
        $product_permalink = apply_filters( 'woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '', $cart_item, $cart_item_key );
        ?>
					<tr class="woocommerce-cart-form__cart-item <?php echo esc_attr( apply_filters( 'woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key ) ); ?>">
						<td class="product-remove">
							<?php
						echo apply_filters(  // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
									'woocommerce_cart_item_remove_link',
									sprintf(
										'<a href="%s" class="remove remove-wishlist" aria-label="%s" data-product_id="%s" data-product_sku="%s"><i class="fal fa-times"></i></a>',
										esc_url( wc_get_cart_remove_url( $cart_item_key ) ),
										esc_html__( 'Remove this item', 'etrade' ),
										esc_attr( $product_id ),
										esc_attr( $_product->get_sku() )
									),
									$cart_item_key
								);
								?>
						</td>

						<td class="product-thumbnail" data-title="<?php esc_attr_e( 'Product', 'etrade' );?>">
						<?php

						$thumbnail = apply_filters( 'woocommerce_cart_item_thumbnail', $_product->get_image(), $cart_item, $cart_item_key );

						if ( !$product_permalink ) {
							echo WooC_Functions::kses_img( $thumbnail ); // PHPCS: XSS ok.
						} else {
							printf( '<a href="%s">%s</a>', esc_url( $product_permalink ), $thumbnail ); // PHPCS: XSS ok.
						}

						?>
						</td>

						<td class="product-title">
							<?php
			if ( !$product_permalink ) {

					echo wp_kses( apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key ) . '&nbsp;', 'alltext_allow' );

				} else {

					echo wp_kses( apply_filters( 'woocommerce_cart_item_name', sprintf( '<h6 class="product-title"><a href="%s">%s</a></h6>', esc_url( $product_permalink ), $_product->get_name() ), $cart_item, $cart_item_key ), 'alltext_allow' );
				}

				do_action( 'woocommerce_after_cart_item_name', $cart_item, $cart_item_key );

				// Meta data.
				echo wc_get_formatted_cart_item_data( $cart_item ); // PHPCS: XSS ok.

				// Backorder notification.
				if ( $_product->backorders_require_notification() && $_product->is_on_backorder( $cart_item['quantity'] ) ) {

					echo wp_kses( apply_filters( 'woocommerce_cart_item_backorder_notification', '<p class="backorder_notification">' . esc_html__( 'Available on backorder', 'etrade' ) . '</p>', $product_id ), 'alltext_allow' );
				}
        		?>
						</td>

						<td class="product-price" data-title="<?php esc_attr_e( 'Price', 'etrade' );?>">
							<?php
							echo apply_filters( 'woocommerce_cart_item_price', WC()->cart->get_product_price( $_product ), $cart_item, $cart_item_key ); // PHPCS: XSS ok.
									?>
							  </td>

						<td class="product-quantity" data-title="<?php esc_attr_e( 'Quantity', 'etrade' );?>">
						<?php
					if ( $_product->is_sold_individually() ) {
								$product_quantity = sprintf( '1 <input type="hidden" name="cart[%s][qty]" value="1" />', $cart_item_key );
							} else {
								$product_quantity = woocommerce_quantity_input(
									array(
										'input_name'   => "cart[{$cart_item_key}][qty]",
										'input_value'  => $cart_item['quantity'],
										'max_value'    => $_product->get_max_purchase_quantity(),
										'min_value'    => '0',
										'product_name' => $_product->get_name(),
									),
									$_product,
									false
								);
							}

										echo apply_filters( 'woocommerce_cart_item_quantity', $product_quantity, $cart_item_key, $cart_item ); // PHPCS: XSS ok.
										?>
										</td>

										<td class="product-subtotal" data-title="<?php esc_attr_e( 'Subtotal', 'etrade' );?>">
											<?php
										echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key ); // PHPCS: XSS ok.
												?>
										</td>
									</tr>
									<?php
						}
					}
				?>

			<?php do_action( 'woocommerce_cart_contents' );?>
			<tr class="cart-action">
				<td colspan="6" class="actions">
						<div class="cart-update-btn-area pt--40">
							<?php if ( wc_coupons_enabled() ) {?>
								<div class="input-group product-cupon coupon">
									<label class="d-none" for="coupon_code"><?php esc_html_e( 'Coupon:', 'etrade' );?></label>
									<input type="text" name="coupon_code" class="input-text" id="coupon_code" value="" placeholder="<?php esc_attr_e( 'Enter coupon code', 'etrade' );?>" />

								<div class="product-cupon-btn">
									<button type="submit" class="button axil-btn btn-outline" name="apply_coupon" value="<?php esc_attr_e( 'Apply coupon', 'etrade' );?>">
										<?php esc_attr_e( 'Apply', 'etrade' );?>
										</button>
								</div>

									<?php do_action( 'woocommerce_cart_coupon' );?>
								</div>

						<?php }?>
						<div class="update-btn">
						<button type="submit" class="button axil-btn btn-outline" name="update_cart" value="<?php esc_attr_e( 'Update cart', 'etrade' );?>"><?php esc_html_e( 'Update cart', 'etrade' );?></button>
					</div>
					</div>
						<?php do_action( 'woocommerce_cart_actions' );?>

						<?php wp_nonce_field( 'woocommerce-cart', 'woocommerce-cart-nonce' );?>
					</td>
				</tr>

			<?php do_action( 'woocommerce_after_cart_contents' );?>
		</tbody>
	</table>
</div>
	<?php do_action( 'woocommerce_after_cart_table' );?>
</form>
<?php do_action( 'woocommerce_before_cart_collaterals' );?>
<div class="row">
	<div class="col-xl-5 col-lg-7 offset-xl-7 offset-lg-5">
	    <div class="axil-order-summery mt--80">
			<div class="cart-collaterals">
				<?php
				/**
				 * Cart collaterals hook.
				 *
				 * @hooked woocommerce_cross_sell_display
				 * @hooked woocommerce_cart_totals - 10
				 */
				do_action( 'woocommerce_cart_collaterals' );
				?>
			</div>
		</div>
	</div>
</div>
<?php do_action( 'woocommerce_after_cart' );?>
</div>