<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */


if ( ! function_exists( 'axil_product_active_button_popup_groups' ) ) {
    function axil_product_active_button_popup_groups( $product_id ) {
        $active = false;
        $axil_options = Helper::axil_get_options();
        $aska_question          = true;

        if( !empty($aska_question) ) return true;

        return $active;
    }
}

if ( !function_exists( 'axil_product_popup_group_buttons' ) ) {
  add_action( 'woocommerce_single_product_summary', 'axil_product_popup_group_buttons', 35 );
  function axil_product_popup_group_buttons()
  {
      global $product; 
      
      $product_id = method_exists($product, 'get_id') === true ? $product->get_id() : $product->ID;

     if( !axil_product_active_button_popup_groups($product_id) ) return; 
      ?>
      <ul class="tbay-button-popup-wrap">
      
        <?php 
  
           axil_the_aska_question($product_id);
        ?>
      </ul>
    <?php
  }
}


 


if (!function_exists('axil_elementor_is_activated')) {
    function axil_elementor_is_activated() {
        return function_exists('elementor_load_plugin_textdomain');
    }
}


if (!function_exists('axil_is_woocommerce_activated')) {
    function axil_is_woocommerce_activated() {
        return class_exists('WooCommerce') ? true : false;
    }
}
/*
 * Product: Featured video button
 */
if ( ! function_exists( 'axil_single_product_featured_video_button' ) ) {
    function axil_single_product_featured_video_button() {
        global $product;
        
        $featured_video_url = get_post_meta( $product->get_id(), 'axil_featured_product_video', true );
        
        if ( ! empty( $featured_video_url ) ) {
            $button_icon_class = apply_filters( 'axil_featured_video_button_icon_class', 'axil-font-media-play' );
            
            echo apply_filters( 'axil_featured_product_video_button', '<a href="#" id="axil-featured-video-link" class="nm-featured-video-link" data-mfp-src="' . esc_url( $featured_video_url ) . '"><span class="axil-featured-video-icon ' . esc_attr( $button_icon_class ) . '"></span><span class="axil-featured-video-label"><i class="fas fa-play"></i> ' . esc_html__( 'Watch Video', 'etrade' ) . '</span></a>', $featured_video_url, $button_icon_class );
        }
    }
}
add_action( 'axil_woocommerce_after_product_thumbnails', 'axil_single_product_featured_video_button', 5 );




add_action( 'cart_clear_woocommerce_empty_cart_action', 'custom_woocommerce_empty_cart_button' );
function custom_woocommerce_empty_cart_button() {
    echo '<a href="' . esc_url( add_query_arg( 'empty_cart', 'yes' ) ) . '" class="cart-clear" title="' . esc_attr( 'Clear Shoping Cart', 'etrade' ) . '">' . esc_html( 'Clear Shopping Cart', 'etrade' ) . '</a>';
}
add_action( 'wp_loaded', 'custom_woocommerce_empty_cart_action', 20 );
function custom_woocommerce_empty_cart_action() {
    if ( isset( $_GET['empty_cart'] ) && 'yes' === esc_html( $_GET['empty_cart'] ) ) {
        WC()->cart->empty_cart();

        $referer = wp_get_referer() ? esc_url( remove_query_arg( 'empty_cart' ) ) : wc_get_cart_url();
        wp_safe_redirect( $referer );
    }
}
function axil_product_block_data() {

    $axil_options = Helper::axil_get_options();

    if ( !isset( $block_data ) ) {
        $block_data = array();
    }

    $block_data_defaults = array(
        'layout'                    => $axil_options['wooc_product_layout'],
        'rating_display'            => $axil_options['wooc_shop_review'] ? true : false,
        'display_attributes'        => $axil_options['product_display_attributes'] ? true : false,
        'product_display_hover'     => $axil_options['product_display_hover'] ? true : false,
        'wishlist'                  => $axil_options['wishlist'] ? true : false,
        'quickview'                 => $axil_options['quickview'] ? true : false,
        'compare'                   => $axil_options['display_shop_compare'] ? true : false,
        'display_title_badge_check' => $axil_options['display_title_badge_check'] ? true : false,
        'sale_price_only'           => $axil_options['sale_price_only'] ? true : false,
    );

    //$block_data_defaults['type'] = apply_filters('axiltheme_shop_view_type', $block_data_defaults['type']);

    $block_data = wp_parse_args( $block_data, $block_data_defaults );

    return $block_data;
}

function axil_product_col_class() {
    $axil_options = Helper::axil_get_options();
    $wooc_mobile_product_columns = $axil_options['wooc_mobile_product_columns'];
    $wooc_tab_product_columns = $axil_options['wooc_tab_product_columns'];
    $wooc_desktop_product_columns = $axil_options['wooc_desktop_product_columns'];

    if ( isset( $_GET['sidebar'] ) ) {
        if ( $_GET['sidebar'] == 'left' ) {
            $layout = 'left-sidebar';
        }
        if ( $_GET['sidebar'] == 'right' ) {
            $layout = 'right-sidebar';
        }
        if ( $_GET['sidebar'] == 'full' ) {
            $layout = 'full-width';
        }
    } else {
        $layout = $axil_options['shop_layout'];
    }

    $product_col_class = ( $layout == 'full-width' ) ? "col-xl-3 col-lg-3 col-md-{$wooc_tab_product_columns} col-sm-{$wooc_tab_product_columns} col-{$wooc_mobile_product_columns}" : "col-xl-{$wooc_desktop_product_columns} col-lg-{$wooc_desktop_product_columns} col-md-{$wooc_tab_product_columns} col-sm-{$wooc_tab_product_columns} col-{$wooc_mobile_product_columns}";

    $product_class = '';
    if ( is_shop() || is_product_taxonomy() || isset( $_REQUEST['ajax_product_loadmore'] ) ) {
        $product_class = $product_col_class;
    }

    if (function_exists('dokan')) {

         $product_col_class = "col-xl-3 col-lg-3 col-md-{$wooc_tab_product_columns} col-sm-{$wooc_tab_product_columns} col-{$wooc_mobile_product_columns}";


        if (function_exists('dokan_is_store_page') && dokan_is_store_page()) {
            $product_class = $product_col_class;
        }

        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5);
        foreach ($backtrace as $trace) {
            if ($trace['function'] == 'dokan_get_more_products_from_seller') {
                $product_class = $product_col_class;
                break;
            }
        }
    }



    // Product variation attributes
    $attributes_escaped = function_exists( 'wooc_template_loop_attributes' ) ? wooc_template_loop_attributes() : null;
    $product_class .= ( $attributes_escaped ) ? ' wooc-has-attributes' : '';
    //$product_class .= ' columns-'. esc_attr( wc_get_loop_prop( 'columns' ) );

    if ( !empty( $isloadmore ) ) {
        $product_class .= ' product_loaded';
    }

    return $product_class;
}

function get_stock_status() {
    global $product;
    return $product->is_in_stock() ? esc_html__( 'In Stock', 'etrade' ) : esc_html__( 'Out of Stock', 'etrade' );
}

function axil_single_stock() {
    ?>
		<ul class="product-meta">
		    <li><i class="fal fa-check"></i><div class="stock in-stock"><?php echo esc_html( get_stock_status() ); ?></div></li>
		</ul>
		<?php
}

function delete_tab( $tabs ) {
    global $product;
    unset( $tabs['reviews'] );
    return $tabs;
}

// Single Product Layout
add_action( 'template_redirect', 'single_product_layout_hooks' );

function single_product_layout_hooks() {

    $axil_options = Helper::axil_get_options();
    $layout = Helper::axil_product_layout_style();

    switch ( $layout ) {
    case '2':
        remove_action( 'woocommerce_archive_description', 'woocommerce_breadcrumb', 10 );

        add_filter( 'woocommerce_product_tabs', 'delete_tab', 98 );
        remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );
        add_action( 'wooc_sale_flash', 'woocommerce_show_product_sale_flash', 15 );

        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 15 );

        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_output_product_data_tabs', 40 );

        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );

        if ( $axil_options['wooc_related'] ) {
            add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 40 );
        }
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 15 );
        if ( $axil_options['wooc_up_sell'] ) {
            add_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 25 );
        }


        add_action( 'woocommerce_before_add_to_cart_button', 'add_to_cart_button_wrapper_start', 30 );
        add_action( 'woocommerce_after_add_to_cart_button', 'single_add_to_cart_button', 1 );
        add_action( 'woocommerce_after_add_to_cart_button', 'add_to_cart_button_wrapper_end', 5 );

        break;
    case '3':

        remove_action( 'woocommerce_archive_description', 'woocommerce_breadcrumb', 10 );
        add_filter( 'woocommerce_product_tabs', 'delete_tab', 98 );

        remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );
        add_action( 'axil_sale_flash', 'woocommerce_show_product_sale_flash', 15 );

        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 11 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_output_product_data_tabs', 40 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );

        if ( $axil_options['wooc_related'] ) {
            add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 40 );
        }
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 15 );
        if ( $axil_options['wooc_up_sell'] ) {
            add_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 25 );
        }
 
        add_action( 'woocommerce_before_add_to_cart_button', 'add_to_cart_button_wrapper_start', 30 );
        add_action( 'woocommerce_after_add_to_cart_button', 'single_add_to_cart_button', 1 );
        add_action( 'woocommerce_after_add_to_cart_button', 'add_to_cart_button_wrapper_end', 5 );



        break;
    case '4':
        remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );
        add_action( 'wooc_sale_flash', 'woocommerce_show_product_sale_flash', 10 );
        remove_action( 'woocommerce_archive_description', 'woocommerce_breadcrumb', 10 );
        add_filter( 'woocommerce_product_tabs', 'delete_tab', 98 );
        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 15 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_output_product_data_tabs', 40 );
 
    
        add_action( 'woocommerce_before_add_to_cart_button', 'add_to_cart_button_wrapper_start', 30 );
        add_action( 'woocommerce_after_add_to_cart_button', 'single_add_to_cart_button', 1 );
        add_action( 'woocommerce_after_add_to_cart_button', 'add_to_cart_button_wrapper_end', 5 );

        break;
    case '5':

        remove_action( 'woocommerce_archive_description', 'woocommerce_breadcrumb', 10 );
        remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );
        add_action( 'axil_sale_flash', 'woocommerce_show_product_sale_flash', 10 );
        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 12 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 10 );

        add_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_start', 10 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 15 );
        add_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_end', 39 );

        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 40 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 15 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 25 );

   
        add_action( 'woocommerce_before_add_to_cart_button', 'add_to_cart_button_wrapper_start', 30 );
        add_action( 'woocommerce_after_add_to_cart_button', 'single_add_to_cart_button', 1 );
        add_action( 'woocommerce_after_add_to_cart_button', 'add_to_cart_button_wrapper_end', 5 );

        break;
    case '6':
        remove_action( 'woocommerce_archive_description', 'woocommerce_breadcrumb', 10 );
        remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );
        add_action( 'wooc_sale_flash', 'woocommerce_show_product_sale_flash', 10 );
        add_action( 'woocommerce_single_product_summary', 'axil_single_stock', 20 );

        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 10 );

        add_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_start', 10 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 15 );
        add_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_end', 20 );

        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 40 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 15 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 25 );

        add_action( 'woocommerce_before_add_to_cart_button', 'add_to_cart_button_wrapper_start', 30 );
           add_action( 'woocommerce_after_add_to_cart_button', 'single_add_to_cart_button', 1 );
       add_action( 'woocommerce_after_add_to_cart_button', 'add_to_cart_button_wrapper_end', 5 );

        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 15 );

        break;

    case '7':
        remove_action( 'woocommerce_archive_description', 'woocommerce_breadcrumb', 10 );
        remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );

        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_meta', 40 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_meta', 30 );

        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_output_product_data_tabs', 31 );

        add_filter( 'woocommerce_product_tabs', 'delete_tab', 98 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 40 );

        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 15 );

  
        add_action( 'woocommerce_before_add_to_cart_button', 'add_to_cart_button_wrapper_start', 30 );
        add_action( 'woocommerce_after_add_to_cart_button', 'single_add_to_cart_button', 1 );
        add_action( 'woocommerce_after_add_to_cart_button', 'add_to_cart_button_wrapper_end', 5 );

        break;

    case '1':
        remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );
        add_action( 'wooc_sale_flash', 'woocommerce_show_product_sale_flash', 15 );

        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 15 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 10 );
        add_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_start', 10 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 15 );
        add_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_end', 20 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );

        if ( $axil_options['wooc_related'] ) {
            add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 40 );
        }
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 15 );
        if ( $axil_options['wooc_up_sell'] ) {
            add_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 25 );
        }
 
        add_action( 'woocommerce_before_add_to_cart_button', 'add_to_cart_button_wrapper_start', 30 );
        add_action( 'woocommerce_after_add_to_cart_button', 'single_add_to_cart_button', 1 );
        add_action( 'woocommerce_after_add_to_cart_button', 'add_to_cart_button_wrapper_end', 5 );

        break;
    default:

        remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );
        add_action( 'wooc_sale_flash', 'woocommerce_show_product_sale_flash', 15 );

        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
        add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 15 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 10 );
        add_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_start', 10 );
        add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_product_data_tabs', 15 );
        add_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_end', 20 );
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );

        if ( $axil_options['wooc_related'] ) {
            add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 40 );
        }
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 15 );
        if ( $axil_options['wooc_up_sell'] ) {
            add_action( 'woocommerce_after_single_product_summary', 'woocommerce_upsell_display', 25 );
        }
 
 
        add_action( 'woocommerce_before_add_to_cart_button', 'add_to_cart_button_wrapper_start', 30 );
        add_action( 'woocommerce_after_add_to_cart_button', 'single_add_to_cart_button', 1 );
        add_action( 'woocommerce_after_add_to_cart_button', 'add_to_cart_button_wrapper_end', 5 );
        add_filter( 'woocommerce_single_product_image_gallery_classes', 'bbloomer_5_columns_product_gallery' );

        break;
    }
}


 
function bbloomer_5_columns_product_gallery( $wrapper_classes ) {
   $columns = 6; // change this to 2, 3, 5, etc. Default is 4.
   $wrapper_classes[2] = 'woocommerce-product-gallery--columns-' . absint( $columns );
   return $wrapper_classes;
}
function add_to_cart_button_wrapper_start() {
    echo '<div class="product-action-wrapper d-flex">';
}

function add_to_cart_button_wrapper_end() {
    echo '</div>';
}

function add_wc_button_wrapper_start() {
    echo '<div class="single-woocwc-wrapper">';
}

function add_wc_button_wrapper_end() {
    echo '</div>';
}

function single_add_to_cart_button() {

    echo '<ul class="product-action d-flex-center mb--0">';
    echo '<li class="add-to-cart wishlist-compare">';
    WooC_Functions::axil_add_to_wishlist_icon();
    WooC_Functions::wooc_print_compare_icon();
    echo '</li>';
    echo '</ul>';
}

function single_3_tabs_wrp_start() {
    echo '<div class="woocommerce-tabs wc-tabs-wrapper bg-vista-white">';
    echo '<div class="container">';
}

function single_3_tabs_wrp_end() {
    echo '</div>';
    echo '</div>';
}

function single_7_tabs_wrp_start() {
    echo '<div class="woocommerce-tabs wc-tabs-wrapper bg-vista-white nft-info-tabs">';

}
function single_7_tabs_wrp_end() {

    echo '</div>';
}

//add_filter( 'woocommerce_account_menu_items', 'axil_woocommerce_account_menu_items', 10, 2 );

function axil_woocommerce_account_menu_items( $items, $endpoints ) {
    $items['orders'] = 'Recent Orders';
    $items['edit-address'] = 'Manage Addresses';
    unset( $items['payment-methods'] );
    return $items;
}

if ( defined( 'YITH_WCWL' ) ) {
    function yith_wcwl_dequeue_font_awesome_styles() {
        wp_deregister_style( 'woocommerce_prettyPhoto_css' );
        wp_deregister_style( 'jquery-selectBox' );
        wp_deregister_style( 'yith-wcwl-font-awesome' );
        wp_deregister_style( 'yith-wcwl-main' );

        $assets_path = str_replace( array( 'http:', 'https:' ), '', WC()->plugin_url() ) . '/assets/';
        wp_register_style( 'jquery-selectBox', YITH_WCWL_URL . 'assets/css/jquery.selectBox.css', array(), '1.2.0' );
        wp_register_style( 'woocommerce_prettyPhoto_css', $assets_path . 'css/prettyPhoto.css' );
        wp_register_style( 'yith-wcwl-main', YITH_WCWL_URL . 'assets/css/style.css', array( 'jquery-selectBox' ), '1.0.0' );
    }
    add_action( 'wp_enqueue_scripts', 'yith_wcwl_dequeue_font_awesome_styles', 11 );
}

function singletabs_wrp_start() {
    $axil_options = Helper::axil_get_options();
    if ( !$axil_options['wooc_additional_info'] ) {
        remove_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_start', 10 );
        remove_action( 'woocommerce_after_single_product_summary', 'single_3_tabs_wrp_end', 20 );
    }

}
add_action( 'init', 'singletabs_wrp_start' );

if ( !function_exists( 'woocommerce_show_product_popup_images' ) ) {

    /**
     * Output the product image before the single product summary.
     */
    function woocommerce_show_product_popup_images() {
        wc_get_template( 'single-product/product-image-popup.php' );
    }
}
if ( !function_exists( 'woocommerce_show_product_popup_thumbnails' ) ) {

    /**
     * Output the product thumbnails.
     */
    function woocommerce_show_product_popup_thumbnails() {
        wc_get_template( 'single-product/product-thumbnails-popup.php' );
    }
}

function axil_is_recent_product() {
    $axil_options = Helper::axil_get_options();
    $number = $axil_options['recent_number'];
    $args = array(
        'post_type'           => 'product',
        'posts_per_page'      => $number ? $number : 3,
        'ignore_sticky_posts' => true,
        'post_status'         => 'publish',
        'suppress_filters'    => false,
        'orderby'             => 'date',
    );
    $query = new \WP_Query( $args );

    ?>
    <?php if ( $query->have_posts() ): ?>
        <?php while ( $query->have_posts() ): $query->the_post();?>

	            <?php
    $cat = '';
        $id = get_the_ID();
        $product = wc_get_product( $id );
        $price_html = $product->get_price_html();

        ?>
	        <div class="axil-product-list">
	    		<?php if ( has_post_thumbnail() ) {?>
		            <div class="thumbnail">
		                <a href="<?php the_permalink();?>">
		                    <?php the_post_thumbnail( 'thumbnail' );?>
		                </a>
		            </div>
		        <?php }?>
	            <div class="product-content">
	                 <?php wc_get_template( 'loop/rating4.php' );?>
	                <h6 class="product-title"><a href="<?php the_permalink();?>"><?php the_title();?></a></h6>
	                <div class="product-price-variant">
	                     <?php echo wp_kses( $price_html, 'alltext_allow' ); ?>
	                </div>
	                <div class="product-cart add-to-cart">
	                    <?php WooC_Functions::wooc_print_add_to_cart_icon( true, false, false );?>
	                    <?php WooC_Functions::axil_add_to_wishlist_icon();?>
	                </div>
	            </div>
	        </div>
	    <?php endwhile;?>
    <?php else: ?>
    <div><?php esc_html_e( 'No products available', 'etrade' );?></div>
<?php endif;
}

add_filter( 'woocommerce_reset_variations_link', 'wp_kama_woocommerce_reset_variations_link_filter' );

/**
 * Function for `woocommerce_reset_variations_link` filter-hook.
 *
 * @param  $html
 *
 * @return
 */
function wp_kama_woocommerce_reset_variations_link_filter( $html ) {

    $html = '<a class="reset_variations" href="#"><i class="fas fa-times-circle"></i> ' . esc_html__( 'Clear', 'etrade' ) . '</a>';
    return $html;
}


