<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

$images         = axil_get_acf_data("axil_gallery_image");
$axil_options   = Helper::axil_get_options();
$thumb_size     = 'axil-blog-grid';
$readmore       = $axil_options['read_more_btn_txt'];
$thumb_size         = ($axil_options['axil_blog_sidebar'] === 'no') ? 'axil-single-blog-thumb':'axil-blog-grid';
?>
<div  id="post-<?php the_ID(); ?>" <?php post_class('content-blog format-gallery'); ?>>
     <div class="inner">  
        <?php
        if($images){ ?>
            <div class="post-thumbnail post-gallery-activation axil-slick-arrow arrow-between-side"> 
                <?php foreach( $images as $image ): ?>
                     <div class="thumbnail">
                        <a href="<?php the_permalink(); ?>">
                            <img class="w-100"  src="<?php echo esc_url($image['sizes'][$thumb_size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
            <?php } else { ?>
            <?php if(has_post_thumbnail()){ ?>
                <div class="thumbnail">
                    <a href="<?php the_permalink(); ?>">
                        <?php the_post_thumbnail($thumb_size) ?>
                    </a>
                </div>
            <?php } ?>
        <?php } ?> 
        <div class="content">
            <h5 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h5>  
            <?php if( !empty($readmore)  ){ ?>
                <div class="read-more-btn">
                    <a class="axil-btn right-icon" href="<?php the_permalink(); ?>"><?php echo esc_html($readmore); ?> <i class="fal fa-long-arrow-right"></i></a>
                </div>
              <?php } ?>  
        </div>
    </div>
</div>
 
 