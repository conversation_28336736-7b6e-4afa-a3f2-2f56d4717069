msgid ""
msgstr ""
"Project-Id-Version: Unyson\n"
"POT-Creation-Date: 2016-02-02 15:48+0300\n"
"PO-Revision-Date: 2016-02-02 15:57+0300\n"
"Last-Translator: \n"
"Language-Team: ThemeFuse <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.5.4\n"
"X-Poedit-KeywordsList: _:1;gettext:1;dgettext:2;ngettext:1,2;dngettext:2,3;"
"__:1;_e:1;_c:1;_n:1,2;_n_noop:1,2;_nc:1,2;__ngettext:1,2;__ngettext_noop:1,2;"
"_x:1,2c;_ex:1,2c;_nx:1,2,4c;_nx_noop:1,2,3c;_n_js:1,2;_nx_js:1,2,3c;"
"esc_attr__:1;esc_html__:1;esc_attr_e:1;esc_html_e:1;esc_attr_x:1,2c;"
"esc_html_x:1,2c;comments_number_link:2,3;t:1;st:1;trans:1;transChoice:1,2\n"
"X-Poedit-Basepath: .\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ..\n"

#: ../manifest.php:5
msgid "Unyson"
msgstr ""

#: ../helpers/class-fw-wp-filesystem.php:43
msgid "Cannot connect to Filesystem directly"
msgstr ""

#: ../helpers/class-fw-wp-filesystem.php:271
#, php-format
msgid "Cannot create directory \"%s\". It must be inside \"%s\""
msgstr ""

#: ../helpers/class-fw-wp-filesystem.php:273
msgid "\" or \""
msgstr ""

#: ../helpers/class-fw-flash-messages.php:95
#, php-format
msgid "Invalid flash message type: %s"
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:185
msgid "No items found."
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:309
msgid "Bulk Actions"
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:319
msgid "Apply"
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:403
msgid "All dates"
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:416
#, php-format
msgid "%1$s %2$d"
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:432
msgid "List View"
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:433
msgid "Excerpt View"
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:459
#, php-format
msgid "%s pending"
msgstr ""

#: ../helpers/class-fw-wp-list-table.php:714
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:33
msgid "Select All"
msgstr ""

#: ../helpers/general.php:1150
msgid "year"
msgstr ""

#: ../helpers/general.php:1151
msgid "years"
msgstr ""

#: ../helpers/general.php:1153
msgid "month"
msgstr ""

#: ../helpers/general.php:1154
msgid "months"
msgstr ""

#: ../helpers/general.php:1156
msgid "week"
msgstr ""

#: ../helpers/general.php:1157
msgid "weeks"
msgstr ""

#: ../helpers/general.php:1159
msgid "day"
msgstr ""

#: ../helpers/general.php:1160
msgid "days"
msgstr ""

#: ../helpers/general.php:1162
msgid "hour"
msgstr ""

#: ../helpers/general.php:1163
msgid "hours"
msgstr ""

#: ../helpers/general.php:1165
msgid "minute"
msgstr ""

#: ../helpers/general.php:1166
msgid "minutes"
msgstr ""

#: ../helpers/general.php:1168
msgid "second"
msgstr ""

#: ../helpers/general.php:1169
msgid "seconds"
msgstr ""

#: ../helpers/general.php:1558
msgid "Maximum stack depth exceeded"
msgstr ""

#: ../helpers/general.php:1561
msgid "Underflow or the modes mismatch"
msgstr ""

#: ../helpers/general.php:1564
msgid "Unexpected control character found"
msgstr ""

#: ../helpers/general.php:1567
msgid "Syntax error, malformed JSON"
msgstr ""

#: ../helpers/general.php:1570
msgid "Malformed UTF-8 characters, possibly incorrectly encoded"
msgstr ""

#: ../helpers/general.php:1573
#: ../extensions/backups/class-fw-extension-backups.php:550
msgid "Unknown error"
msgstr ""

#: ../helpers/class-fw-form.php:80
#, php-format
msgid "Form with id \"%s\" was already defined"
msgstr ""

#: ../helpers/class-fw-form.php:168
msgid "Nonce verification failed"
msgstr ""

#: ../helpers/class-fw-form.php:331
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/views/form.php:13
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:66
msgid "Submit"
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:285
#: ../extensions/update/class-fw-extension-update.php:602
msgid "Cannot delete: "
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:292
msgid "Cannot create: "
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:370
msgid "Cannot remove old temporary directory: "
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:376
#: ../extensions/update/class-fw-extension-update.php:572
msgid "Cannot create directory: "
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:381
#, php-format
msgid "Downloading the %s..."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:386
#, php-format
msgid "Cannot download the %s."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:396
#, php-format
msgid "Installing the %s..."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:402
#: ../extensions/update/class-fw-extension-update.php:431
#: ../extensions/update/class-fw-extension-update.php:531
#: ../extensions/update/class-fw-extension-update.php:552
#: ../extensions/update/class-fw-extension-update.php:582
msgid "Cannot access directory: "
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:421
msgid "Cannot remove: "
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:456
#: ../extensions/update/class-fw-extension-update.php:522
#: ../extensions/update/class-fw-extension-update.php:628
#: ../extensions/update/class-fw-extension-update.php:652
#, php-format
msgid "Cannot move \"%s\" to \"%s\""
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:472
#, php-format
msgid "Cannot merge \"%s\" with \"%s\""
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:485
#, php-format
msgid "The %s has been successfully updated."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:492
#, php-format
msgid "Cannot remove temporary directory \"%s\"."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:672
#: ../extensions/update/class-fw-extension-update.php:740
#: ../extensions/update/class-fw-extension-update.php:808
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:930
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1427
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1837
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2054
msgid "Invalid nonce."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:683
msgid "Framework Update"
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:699
msgid "Failed to get framework latest version."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:716
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:360
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:374
#: ../extensions/update/views/updates-list.php:10
#: ../core/class-fw-manifest.php:353
msgid "Framework"
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:751
msgid "Theme Update"
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:767
msgid "Failed to get theme latest version."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:784
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:393
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:407
msgid "Theme"
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:817
msgid "Please check the extensions you want to update."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:841
msgid "Extensions Update"
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:879
msgid "No extensions updates found."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:886
#, php-format
msgid "Extension \"%s\" does not exist or is disabled."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:893
#, php-format
msgid "No update found for the \"%s\" extension."
msgstr ""

#: ../extensions/update/class-fw-extension-update.php:915
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:426
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:440
#, php-format
msgid "%s extension"
msgstr ""

#: ../extensions/update/manifest.php:5
#: ../extensions/builder/includes/option-types/builder/includes/fullscreen.php:82
msgid "Update"
msgstr ""

#: ../extensions/update/manifest.php:6
msgid "Keep you framework, extensions and theme up to date."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:91
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:232
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2439
msgid "Github error:"
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:100
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:241
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2446
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (Response code: %d)"
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:108
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:249
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2462
#, php-format
msgid "Failed to access Github repository \"%s\" releases."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:122
#, php-format
msgid "No releases found in repository \"%s\"."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:143
#, php-format
msgid ""
"%s manifest has invalid \"github_update\" parameter. Please use \"user/repo"
"\" format."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:171
#, php-format
msgid "Failed to fetch %s latest version from github \"%s\"."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:264
#, php-format
msgid "%s github repository \"%s\" does not have the \"%s\" release."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:284
#, php-format
msgid "Cannot download %s zip."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:297
#, php-format
msgid "Cannot save %s zip."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:316
#, php-format
msgid "Cannot remove %s zip."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:325
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2566
msgid "Cannot access the unzipped directory files."
msgstr ""

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:341
#, php-format
msgid "The unzipped %s directory not found."
msgstr ""

#: ../extensions/update/views/updates-list.php:12
#, php-format
msgid "You have the latest version of %s."
msgstr ""

#: ../extensions/update/views/updates-list.php:25
msgid "Update Framework"
msgstr ""

#: ../extensions/update/views/updates-list.php:37
msgid "Your theme is up to date."
msgstr ""

#: ../extensions/update/views/updates-list.php:50
msgid "Update Theme"
msgstr ""

#: ../extensions/update/views/updates-list.php:60
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:844
#, php-format
msgid "%s Extensions"
msgstr ""

#: ../extensions/update/views/updates-list.php:62
#, php-format
msgid "You have the latest version of %s Extensions."
msgstr ""

#: ../extensions/update/views/updates-list.php:80
#: ../extensions/update/views/updates-list.php:95
#: ../extensions/update/views/updates-list.php:100
msgid "Update Extensions"
msgstr ""

#: ../extensions/update/views/updates-list.php:99
msgid "New extensions updates available."
msgstr ""

#: ../extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:16
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:14
#: ../extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:16
msgid "Go to updates page"
msgstr ""

#: ../extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:19
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:17
#: ../extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:19
msgid "Return to Updates page"
msgstr ""

#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:84
#, php-format
msgid "You have version %s installed. Update to %s."
msgstr ""

#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:126
msgid "No Extensions for update."
msgstr ""

#: ../extensions/portfolio/manifest.php:7
#: ../extensions/portfolio/class-fw-extension-portfolio.php:260
#: ../core/components/extensions/manager/available-extensions.php:72
msgid "Portfolio"
msgstr ""

#: ../extensions/portfolio/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:73
msgid ""
"This extension will add a fully fledged portfolio module that will let you "
"display your projects using the built in portfolio pages."
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:115
#: ../extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Project"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:116
msgid "Projects"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:123
#: ../extensions/learning/class-fw-extension-learning.php:63
#: ../extensions/learning/class-fw-extension-learning.php:127
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:57
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:58
#: ../extensions/events/class-fw-extension-events.php:76
#: ../extensions/media/extensions/slider/posts.php:8
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:32
msgid "Add New"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:124
#: ../extensions/portfolio/class-fw-extension-portfolio.php:198
#: ../extensions/learning/class-fw-extension-learning.php:64
#: ../extensions/learning/class-fw-extension-learning.php:128
#: ../extensions/events/class-fw-extension-events.php:77
#: ../extensions/events/class-fw-extension-events.php:131
#, php-format
msgid "Add New %s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:125
#: ../extensions/learning/class-fw-extension-learning.php:129
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:89
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:76
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:88
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:86
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:68
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:34
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:50
#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:110
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:45
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:37
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:73
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:81
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:57
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:50
#: ../extensions/events/class-fw-extension-events.php:78
#: ../includes/option-types/popup/class-fw-option-type-popup.php:158
#: ../includes/option-types/upload/class-fw-option-type-upload.php:151
#: ../includes/option-types/upload/class-fw-option-type-upload.php:177
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:161
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:187
msgid "Edit"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:126
#: ../extensions/portfolio/class-fw-extension-portfolio.php:196
#: ../extensions/learning/class-fw-extension-learning.php:65
#: ../extensions/learning/class-fw-extension-learning.php:66
#: ../extensions/learning/class-fw-extension-learning.php:130
#: ../extensions/learning/class-fw-extension-learning.php:192
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:59
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:60
#: ../extensions/events/class-fw-extension-events.php:79
#: ../extensions/events/class-fw-extension-events.php:129
#, php-format
msgid "Edit %s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:127
#: ../extensions/learning/class-fw-extension-learning.php:67
#: ../extensions/learning/class-fw-extension-learning.php:131
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:61
#: ../extensions/events/class-fw-extension-events.php:80
#, php-format
msgid "New %s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:128
#: ../extensions/portfolio/class-fw-extension-portfolio.php:193
#: ../extensions/learning/class-fw-extension-learning.php:68
#: ../extensions/learning/class-fw-extension-learning.php:132
#: ../extensions/learning/class-fw-extension-learning.php:189
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:62
#: ../extensions/events/class-fw-extension-events.php:81
#: ../extensions/events/class-fw-extension-events.php:126
#, php-format
msgid "All %s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:129
#: ../extensions/portfolio/class-fw-extension-portfolio.php:130
#: ../extensions/learning/class-fw-extension-learning.php:69
#: ../extensions/learning/class-fw-extension-learning.php:70
#: ../extensions/learning/class-fw-extension-learning.php:133
#: ../extensions/learning/class-fw-extension-learning.php:134
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:63
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:64
#: ../extensions/events/class-fw-extension-events.php:82
#: ../extensions/events/class-fw-extension-events.php:83
#, php-format
msgid "View %s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:131
#: ../extensions/portfolio/class-fw-extension-portfolio.php:192
#: ../extensions/learning/class-fw-extension-learning.php:71
#: ../extensions/learning/class-fw-extension-learning.php:135
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:65
#: ../extensions/events/class-fw-extension-events.php:84
#: ../extensions/events/class-fw-extension-events.php:125
#, php-format
msgid "Search %s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:132
#: ../extensions/learning/class-fw-extension-learning.php:72
#: ../extensions/learning/class-fw-extension-learning.php:136
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:66
#: ../extensions/events/class-fw-extension-events.php:85
#, php-format
msgid "No %s Found"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:133
#: ../extensions/learning/class-fw-extension-learning.php:73
#: ../extensions/learning/class-fw-extension-learning.php:137
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:67
#: ../extensions/events/class-fw-extension-events.php:86
#, php-format
msgid "No %s Found In Trash"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:136
msgid "Create a portfolio item"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:183
#: ../extensions/events/class-fw-extension-events.php:116
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:268
msgid "Category"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:184
#: ../extensions/events/class-fw-extension-events.php:117
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:168
msgid "Categories"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:194
#: ../extensions/learning/class-fw-extension-learning.php:190
#: ../extensions/events/class-fw-extension-events.php:127
#, php-format
msgid "Parent %s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:195
#: ../extensions/learning/class-fw-extension-learning.php:191
#: ../extensions/events/class-fw-extension-events.php:128
#, php-format
msgid "Parent %s:"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:197
#: ../extensions/learning/class-fw-extension-learning.php:193
#: ../extensions/events/class-fw-extension-events.php:130
#: ../core/components/extensions/manager/views/extension.php:234
#: ../core/components/extensions/manager/views/extension.php:272
#, php-format
msgid "Update %s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:199
#: ../extensions/learning/class-fw-extension-learning.php:195
#: ../extensions/events/class-fw-extension-events.php:132
#, php-format
msgid "New %s Name"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:200
#: ../extensions/learning/class-fw-extension-learning.php:196
#: ../extensions/events/class-fw-extension-events.php:133
#, php-format
msgid "%s"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Gallery"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:240
msgid "Set project gallery"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:241
msgid "Edit project gallery"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:271
msgid "Project Cover Image"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:287
msgid "Edit this item"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:348
#: ../extensions/portfolio/class-fw-extension-portfolio.php:361
#: ../extensions/learning/class-fw-extension-learning.php:320
#: ../extensions/learning/class-fw-extension-learning.php:333
msgid "View all categories"
msgstr ""

#: ../extensions/portfolio/class-fw-extension-portfolio.php:381
msgid "Cover Image"
msgstr ""

#: ../extensions/seo/settings-options.php:17
#: ../extensions/social/settings-options.php:11
msgid "General"
msgstr ""

#: ../extensions/seo/settings-options.php:21
#: ../extensions/social/settings-options.php:15
msgid "General Settings"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:89
msgid "Site name"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:95
msgid "Site description"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:101
msgid "Current time"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:107
msgid "Current date"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:113
msgid "Current month"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:119
msgid "Current year"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:125
msgid "Date of the post/page"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:131
msgid "Title of the post/page/term"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:137
msgid "Excerpt of the current post, of auto-generate if it is not set"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:143
msgid "Excerpt of the current post, without auto-generation"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:149
msgid "Post tags, separated by coma"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:155
msgid "Post categories, separated by coma"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:161
msgid "Category/tag/term description"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:167
msgid "Term title"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:173
msgid "Post modified time"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:179
msgid "Post/page id"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:185
msgid "Post/page author \"nicename\""
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:191
msgid "Post/page author id"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:197
msgid "Search phrase in search page"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:203
#: ../extensions/seo/class-fw-extension-seo.php:209
msgid "Page number"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:215
msgid "Attachment caption"
msgstr ""

#: ../extensions/seo/class-fw-extension-seo.php:435
#: ../extensions/seo/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:120
msgid "SEO"
msgstr ""

#: ../extensions/seo/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:121
msgid ""
"This extension will enable you to have a fully optimized WordPress website "
"by adding optimized meta titles, keywords and descriptions."
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:22
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:310
msgid "Titles & Meta"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:26
#: ../extensions/breadcrumbs/settings-options.php:6
#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:9
msgid "Homepage"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:30
msgid "Homepage Title"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:31
msgid "Set homepage title format"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:36
msgid "Homepage Description"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:37
msgid "Set homepage description"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:44
msgid "Homepage Meta Keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:45
msgid "Set homepage meta keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:54
msgid "Custom Posts"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:59
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:111
#: ../extensions/learning/includes/class-fw-widget-learning.php:120
#: ../extensions/shortcodes/shortcodes/testimonials/options.php:7
#: ../extensions/shortcodes/shortcodes/accordion/options.php:15
#: ../extensions/shortcodes/shortcodes/icon/options.php:12
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:8
#: ../extensions/shortcodes/shortcodes/tabs/options.php:15
#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:56
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:480
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:62
msgid "Title"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:60
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:112
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:287
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:315
msgid "Set title format"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:63
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:74
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:87
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:115
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:126
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:139
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:247
msgid "Here are some tags examples:"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:70
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:122
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:320
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:66
msgid "Description"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:71
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:123
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:293
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:321
msgid "Set description format"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:83
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:135
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:328
msgid "Meta Keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:84
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:136
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:329
msgid "Set meta keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:96
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:148
msgid "Meta Robots"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:97
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:149
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:188
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:227
msgid "noindex, follow"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:105
msgid "Taxonomies"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:158
msgid "Other"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:165
msgid "Author Page Title"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:166
msgid "Set author page title format"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:171
msgid "Author Page Description"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:172
msgid "Set author page description"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:179
msgid "Author Meta Keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:180
msgid "Set author page meta keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:187
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:226
msgid "Metarobots"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:193
msgid "Disable Author Archives"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:194
msgid "Disable Author archives SEO settings"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:204
msgid "Date Achieves Title"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:205
msgid "Set date achieves title format"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:210
msgid "Date Achieves Description"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:211
msgid "Set date achieves description"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:218
msgid "Date achieves Meta Keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:219
msgid "Set date achieves meta keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:232
msgid "Disable Date Archives"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:233
msgid "Disable date archives SEO settings"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:243
msgid "Search Page Title"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:244
msgid "Set search page title format"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:259
msgid "404 Page Title"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:260
msgid "Set 404 page title format"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:262
#: ../extensions/breadcrumbs/settings-options.php:8
msgid "404 Not Found"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:286
msgid "SEO Title"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:292
msgid "SEO Description"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:314
msgid "Page Title"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:356
msgid "Use Meta Keywords"
msgstr ""

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:357
msgid "Allow the use of meta keywords in posts and taxonomies"
msgstr ""

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:33
msgid "Google Webmasters"
msgstr ""

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:34
msgid "Insert Google Webmasters verification code"
msgstr ""

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:41
msgid "Bing Webmasters"
msgstr ""

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:42
msgid "Insert Bing Webmasters verification code"
msgstr ""

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:80
msgid "Webmasters"
msgstr ""

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:89
#, php-format
msgid "Webmaster %s already exists"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:634
msgid "Google"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:638
msgid "Bing"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:19
msgid "Check if you want to exclude this page"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:40
msgid "Check if you want to exclude this category"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:57
msgid "Sitemap"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:68
msgid "View Sitemap"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:69
msgid "Press button to view sitemap file"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:71
msgid "XML Sitemap"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:81
msgid "Search Engines"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:83
msgid "After adding content the extension will automatically ping to:"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:93
msgid "Exclude Pages"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:95
msgid "Please check the pages you do not want to include in sitemap"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:106
msgid "Exclude Categories"
msgstr ""

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:108
msgid "Please check the categories you do not want to include in sitemap"
msgstr ""

#: ../extensions/mailer/manifest.php:5
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:187
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:188
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:103
#: ../core/components/extensions/manager/available-extensions.php:240
msgid "Mailer"
msgstr ""

#: ../extensions/mailer/manifest.php:6
#: ../core/components/extensions/manager/available-extensions.php:241
msgid ""
"This extension will let you set some global email options and it is used by "
"other extensions (like Forms) to send emails."
msgstr ""

#: ../extensions/mailer/class-fw-extension-mailer.php:48
msgid "Invalid send method"
msgstr ""

#: ../extensions/mailer/class-fw-extension-mailer.php:81
msgid "The message has been successfully sent!"
msgstr ""

#: ../extensions/mailer/includes/class-mailer-sender.php:27
msgid "Invalid email configuration"
msgstr ""

#: ../extensions/mailer/includes/class-mailer-sender.php:145
#: ../extensions/mailer/includes/class-mailer-sender.php:161
msgid "Email sent"
msgstr ""

#: ../extensions/mailer/includes/class-mailer-sender.php:146
msgid "Could not send via smtp"
msgstr ""

#: ../extensions/mailer/includes/class-mailer-sender.php:162
msgid "Could not send via wp_mail"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:25
msgid "Server Address"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:26
msgid "Enter your email server"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:31
msgid "Username"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:32
msgid "Enter your username"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:37
msgid "Password"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:38
msgid "Enter your password"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:43
msgid "Secure Connection"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:54
msgid "Custom Port"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:55
msgid "Optional - SMTP port number to use."
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:56
msgid "Leave blank for default (SMTP - 25, SMTPS - 465)"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:82
msgid "Username cannot be empty"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:89
msgid "Password cannot be empty"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:96
msgid "Invalid host"
msgstr ""

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:176
#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-wpmail.php:56
msgid "Could not send the email"
msgstr ""

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:76
msgid "Send Method"
msgstr ""

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:77
msgid "Select the send form method"
msgstr ""

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:94
msgid "From Name"
msgstr ""

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:95
msgid "The name you'll see in the From filed in your email client."
msgstr ""

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:102
msgid "From Address"
msgstr ""

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:103
msgid "The form will look like was sent from this email address."
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:56
msgid "Lesson"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:57
#: ../extensions/learning/views/content-course.php:14
msgid "Lessons"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:78
msgid "Create a lesson"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:120
#: ../extensions/learning/hooks.php:53
msgid "Course"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:121
#: ../extensions/learning/includes/class-fw-widget-learning.php:108
msgid "Courses"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:142
msgid "Create a course"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:181
#: ../extensions/learning/class-fw-extension-learning.php:222
msgid "Course Category"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:182
#: ../extensions/learning/class-fw-extension-learning.php:223
msgid "Course Categories"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:188
msgid "Search categories"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:194
msgid "Add New category"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:285
msgid "View all courses"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:511
msgid "No courses available"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:513
msgid "Without Course"
msgstr ""

#: ../extensions/learning/class-fw-extension-learning.php:520
msgid "Select Course"
msgstr ""

#: ../extensions/learning/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:216
msgid "Learning"
msgstr ""

#: ../extensions/learning/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:217
msgid ""
"This extension adds a Learning module to your theme. Using this extension "
"you can add courses, lessons and tests for your users to take."
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:118
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:145
msgid "Quiz"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:259
msgid "Quiz Elements"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:272
msgid "Quiz settings"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:276
msgid "Quiz Passmark Points"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:278
msgid "The points number at which the test will be passed."
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:286
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:296
msgid "Lesson Quiz"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:535
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:544
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:552
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:559
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:568
msgid "Invalid Quiz"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/views/content.php:21
#, php-format
msgid "You require %d points in oder to pass the test"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/views/content.php:27
msgid "Sorry, you did not pass the test"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/views/content.php:29
msgid "Congratulation, you passed the test"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/views/content.php:43
#, php-format
msgid "You answered correctly %s questions from %s"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/views/content.php:69
#: ../extensions/learning/views/content-lesson.php:18
msgid "Back to"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/views/start-quiz.php:11
msgid "Start Quiz"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:20
msgid "Correct answers"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:21
msgid "Add correct answer variants"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:24
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:23
msgid "Set Correct Answer"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:34
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:31
msgid "Wrong answers"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:35
msgid "Add wrong answer variants"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:38
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:35
msgid "Set Wrong Answer"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:42
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:55
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:53
msgid "Creates a"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:59
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:91
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:82
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:180
msgid "Multiple Choice"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
msgid "item"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:87
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:73
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:86
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:84
#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:76
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:75
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:80
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:80
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:48
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:75
msgid "Label"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:88
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:75
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:87
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:85
msgid "Add/Edit Question"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:90
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:77
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:90
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:87
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:58
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:51
msgid "Delete"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:92
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:90
msgid "More"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:93
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:91
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:57
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:57
msgid "Close"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:94
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:78
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:91
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:56
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:52
msgid "Edit Label"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:96
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:80
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:93
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:93
msgid "The question label is empty"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:97
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:81
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:94
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:94
msgid "Invalid mark point number"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:98
msgid "There needs to be at least one correct answer"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:18
msgid "Correct answer"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:19
msgid "The question answer will be true or false"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:22
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:20
msgid "True"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:26
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:24
msgid "False"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:45
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:74
msgid "True/False"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:19
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:21
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:97
msgid "Text before gap"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:28
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:30
msgid "Gap"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:37
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:39
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:98
msgid "Text after gap"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:58
msgid "Gap Fill"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:89
msgid "Gap _____ Fill"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:96
#, php-format
msgid "At least one of the fields ( %s or %s ) has to ve filled with text"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:19
msgid "Correct Answer"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:20
msgid "Write the correct answer text"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:32
msgid "Add wrong answers variants"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:82
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:180
msgid "Single Choice"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:95
msgid "Correct answer cannot be empty"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:96
msgid "There are not any wrong answers set"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:51
msgid "Question"
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:53
msgid "Type the question..."
msgstr ""

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:58
msgid "Points"
msgstr ""

#: ../extensions/learning/includes/class-fw-widget-learning.php:18
msgid "Get list of courses"
msgstr ""

#: ../extensions/learning/includes/class-fw-widget-learning.php:20
msgid "Lesson Courses"
msgstr ""

#: ../extensions/learning/includes/class-fw-widget-learning.php:126
msgid "Number of courses"
msgstr ""

#: ../extensions/analytics/settings-options.php:11
msgid "Google Analytics"
msgstr ""

#: ../extensions/analytics/settings-options.php:12
msgid "Enter your Google Analytics code (Ex: UA-XXXXX-X)"
msgstr ""

#: ../extensions/analytics/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:192
msgid "Analytics"
msgstr ""

#: ../extensions/analytics/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:193
msgid ""
"Enables the possibility to add the Google Analytics tracking code that will "
"let you get all the analytics about visitors, page views and more."
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:36
#: ../extensions/blog/class-fw-extension-blog.php:37
#: ../extensions/breadcrumbs/settings-options.php:7
#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:10
msgid "Blog"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:38
msgid "Add blog post"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:39
msgid "Add new blog post"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:40
msgid "All blog posts"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:41
msgid "Edit blog post"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:42
#: ../extensions/blog/class-fw-extension-blog.php:43
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:49
msgid "Blog Post"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:44
msgid "New blog post"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:45
msgid "No blog posts found"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:46
msgid "No blog posts found in trash"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:47
msgid "Search blog posts"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:48
msgid "View blog post"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:67
#: ../extensions/blog/class-fw-extension-blog.php:87
#: ../extensions/blog/manifest.php:7
#: ../extensions/blog/manifest.php:8
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:50
msgid "Blog Posts"
msgstr ""

#: ../extensions/blog/class-fw-extension-blog.php:76
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:74
msgid "Blog Categories"
msgstr ""

#: ../extensions/styling/class-fw-extension-styling.php:60
#: ../extensions/styling/class-fw-extension-styling.php:61
#: ../extensions/styling/class-fw-extension-styling.php:78
#: ../extensions/styling/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:48
msgid "Styling"
msgstr ""

#: ../extensions/styling/class-fw-extension-styling.php:104
#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:278
#: ../core/components/backend.php:357
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2277
msgid "Save"
msgstr ""

#: ../extensions/styling/class-fw-extension-styling.php:118
msgid "You have no permission to change Styling options"
msgstr ""

#: ../extensions/styling/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:49
msgid ""
"This extension lets you control the website visual style. Starting from "
"predefined styles to changing specific fonts and colors across the website."
msgstr ""

#: ../extensions/styling/extensions/switch-style-panel/manifest.php:7
msgid "Switch Style Panel"
msgstr ""

#: ../extensions/styling/extensions/switch-style-panel/manifest.php:8
msgid ""
"Show on the front-end a panel that allows the user to make the switch "
"between predefined styles."
msgstr ""

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:9
msgid "Frontend Style Switcher"
msgstr ""

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:10
msgid "Enable frontend style switcher"
msgstr ""

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:13
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:274
#: ../extensions/shortcodes/shortcodes/map/options.php:45
#: ../extensions/shortcodes/shortcodes/button/options.php:24
#: ../extensions/shortcodes/shortcodes/media-image/options.php:42
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:34
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:48
#: ../includes/option-types/simple.php:454
#: ../includes/option-types/switch/class-fw-option-type-switch.php:151
msgid "Yes"
msgstr ""

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:17
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:278
#: ../extensions/shortcodes/shortcodes/map/options.php:49
#: ../extensions/shortcodes/shortcodes/button/options.php:28
#: ../extensions/shortcodes/shortcodes/media-image/options.php:46
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:38
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:52
#: ../includes/option-types/switch/class-fw-option-type-switch.php:147
msgid "No"
msgstr ""

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:24
msgid "The text that will be displayed at the top of the panel."
msgstr ""

#: ../extensions/styling/includes/option-types/style/views/settings.php:119
msgid "Background"
msgstr ""

#: ../extensions/styling/includes/option-types/style/views/predefined.php:26
msgid "Predefined Styles"
msgstr ""

#: ../extensions/styling/includes/option-types/style/views/preview.php:41
msgid "This is a simplified preview, not changes are reflected."
msgstr ""

#: ../extensions/feedback/class-fw-extension-feedback.php:64
#: ../core/components/extensions/manager/available-extensions.php:144
msgid "Feedback"
msgstr ""

#: ../extensions/feedback/class-fw-extension-feedback.php:111
msgid "Reviews"
msgstr ""

#: ../extensions/feedback/settings-options.php:10
#: ../extensions/shortcodes/extensions/page-builder/settings-options.php:11
msgid "Activate for"
msgstr ""

#: ../extensions/feedback/settings-options.php:16
msgid "Select the options you want the Feedback extension to be activated for"
msgstr ""

#: ../extensions/feedback/manifest.php:7
msgid "FeedBack"
msgstr ""

#: ../extensions/feedback/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:145
msgid ""
"Adds the possibility to leave feedback (comments, reviews and rating) about "
"your products, articles, etc. This replaces the default comments system."
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:84
msgid "Rating:"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:195
msgid "Feedback Stars"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:221
#: ../extensions/feedback/extensions/feedback-stars/views/rate.php:12
msgid "Rating"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "ERROR"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "please rate the post."
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/manifest.php:7
msgid "FeedBack Stars"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/manifest.php:8
msgid "Allows visitors to appreciate a post using star rating"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
msgid "Pingback:"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
msgid "(Edit)"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:37
msgid "Post author"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:57
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
#, php-format
msgid "%1$s at %2$s"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:63
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:53
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:56
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:122
msgid "Your comment is awaiting moderation."
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:24
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:40
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:119
msgid "says"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/views/view-rates.php:24
#, php-format
msgid "Based on %s Votes"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:6
msgid "Rating System"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:9
msgid "Enter the number of stars you want in the rating system"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:11
msgid "5 stars"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:12
msgid "7 stars"
msgstr ""

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:13
msgid "10 stars"
msgstr ""

#: ../extensions/feedback/views/reviews.php:32
#: ../extensions/feedback/views/reviews.php:53
msgid "Comment navigation"
msgstr ""

#: ../extensions/feedback/views/reviews.php:35
#: ../extensions/feedback/views/reviews.php:56
msgid "&larr; Older Comments"
msgstr ""

#: ../extensions/feedback/views/reviews.php:36
#: ../extensions/feedback/views/reviews.php:57
msgid "Newer Comments &rarr;"
msgstr ""

#: ../extensions/feedback/views/reviews.php:62
msgid "Comments are closed."
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/settings-options.php:18
msgid ""
"Select the posts you want the Page Builder extension to be activated for"
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:84
msgid "Page Builder"
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/manifest.php:9
msgid ""
"Lets you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:51
msgid ""
"There must not be more than one page Editor integrated with the wp post "
"editor per page"
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:96
msgid "Visual Page Builder"
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:97
msgid "Default Editor"
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:126
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:117
#: ../extensions/shortcodes/shortcodes/section/config.php:5
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:22
msgid "Layout Elements"
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:99
#, php-format
msgid "No Page Builder tab specified for shortcode: %s"
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:111
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:75
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:83
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:57
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:238
#: ../core/components/extensions/manager/views/extension.php:141
#: ../core/components/extensions/manager/views/extension.php:345
msgid "Remove"
msgstr ""

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:112
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:74
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:82
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:56
msgid "Duplicate"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:8
#: ../extensions/shortcodes/shortcodes/testimonials/options.php:12
msgid "Testimonials"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:9
msgid "Add some Testimonials"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:10
#: ../extensions/shortcodes/shortcodes/accordion/config.php:10
#: ../extensions/shortcodes/shortcodes/table/config.php:10
#: ../extensions/shortcodes/shortcodes/map/config.php:10
#: ../extensions/shortcodes/shortcodes/special-heading/config.php:8
#: ../extensions/shortcodes/shortcodes/team-member/config.php:10
#: ../extensions/shortcodes/shortcodes/icon/config.php:8
#: ../extensions/shortcodes/shortcodes/icon-box/config.php:8
#: ../extensions/shortcodes/shortcodes/button/config.php:10
#: ../extensions/shortcodes/shortcodes/calendar/config.php:10
#: ../extensions/shortcodes/shortcodes/notification/config.php:10
#: ../extensions/shortcodes/shortcodes/widget-area/config.php:10
#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:10
#: ../extensions/shortcodes/shortcodes/text-block/config.php:10
#: ../extensions/shortcodes/shortcodes/divider/config.php:8
#: ../extensions/shortcodes/shortcodes/tabs/config.php:10
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:10
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:106
msgid "Content Elements"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:8
msgid "Option Testimonials Title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:13
msgid "Add/Edit Testimonial"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:14
msgid "Here you can add, remove and edit your Testimonials."
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:19
msgid "Quote"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:20
msgid "Enter the testimonial here"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:25
#: ../extensions/shortcodes/shortcodes/media-image/config.php:6
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:102
msgid "Image"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:26
#: ../extensions/shortcodes/shortcodes/team-member/options.php:8
#: ../extensions/shortcodes/shortcodes/media-image/options.php:9
msgid ""
"Either upload a new, or choose an existing image from your media library"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:30
msgid "Name"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:31
msgid "Enter the Name of the Person to quote"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:35
msgid "Position"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:36
msgid "Can be used for a job description"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:40
msgid "Website Name"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:41
msgid "Linktext for the above Link"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:45
msgid "Website Link"
msgstr ""

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:46
msgid "Link to the Persons website"
msgstr ""

#: ../extensions/shortcodes/shortcodes/accordion/config.php:8
msgid "Accordion"
msgstr ""

#: ../extensions/shortcodes/shortcodes/accordion/config.php:9
msgid "Add an Accordion"
msgstr ""

#: ../extensions/shortcodes/shortcodes/accordion/options.php:8
#: ../extensions/shortcodes/shortcodes/tabs/config.php:8
#: ../extensions/shortcodes/shortcodes/tabs/options.php:8
msgid "Tabs"
msgstr ""

#: ../extensions/shortcodes/shortcodes/accordion/options.php:9
msgid "Add/Edit Tabs"
msgstr ""

#: ../extensions/shortcodes/shortcodes/accordion/options.php:10
#: ../extensions/shortcodes/shortcodes/tabs/options.php:10
msgid "Create your tabs"
msgstr ""

#: ../extensions/shortcodes/shortcodes/accordion/options.php:19
#: ../extensions/shortcodes/shortcodes/icon-box/options.php:24
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:13
#: ../extensions/shortcodes/shortcodes/text-block/options.php:10
#: ../extensions/shortcodes/shortcodes/tabs/options.php:19
msgid "Content"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/config.php:8
msgid "Table"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/config.php:9
msgid "Add a Table"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:64
msgid "table-builder option type must be inside the table shortcode"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:171
msgid "Table Styling"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:172
msgid "Choose the table styling options"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:174
msgid "Use the table as a pricing table"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:175
msgid "Use the table to display tabular data"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:197
msgid "Default row"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:198
msgid "Heading row"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:199
msgid "Pricing row"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:200
msgid "Button row"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:201
msgid "Row switch"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:211
msgid "Default column"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:212
msgid "Description column"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:213
msgid "Highlight column"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:214
msgid "Center text column"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:251
msgid "per month"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:262
#: ../extensions/shortcodes/shortcodes/button/config.php:8
msgid "Button"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:263
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:39
#: ../extensions/sidebars/class-fw-extension-sidebars.php:148
#: ../includes/option-types/addable-option/class-fw-option-type-addable-option.php:20
#: ../includes/option-types/addable-popup/class-fw-option-type-addable-popup.php:153
#: ../includes/option-types/addable-box/class-fw-option-type-addable-box.php:186
msgid "Add"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-head-template.php:27
msgid "Add Column"
msgstr ""

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/view.php:161
msgid "Add Row"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:24
#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:25
msgid "Custom"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:27
msgid "Locations"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:28
msgid "Add/Edit Location"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:31
msgid "Note: Please set location"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:35
#: ../extensions/events/class-fw-extension-events.php:187
msgid "Location"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:39
msgid "Location Title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:40
msgid "Set location title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:44
msgid "Location Description"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:45
msgid "Set location description"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:49
msgid "Location Url"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:50
msgid "Set page url (Ex: http://example.com)"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:53
msgid "Location Image"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:54
msgid "Add location image"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:113
msgid "No location provider specified for map shortcode"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:115
msgid "Map Placeholder"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:122
#, php-format
msgid "Unknown location provider \"%s\" specified for map shortcode"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/config.php:8
msgid "Map"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/config.php:9
msgid "Add a Map"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:13
#: ../extensions/shortcodes/shortcodes/calendar/options.php:13
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:214
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:473
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:11
msgid "Population Method"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:14
msgid "Select map population method (Ex: events, custom)"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:24
msgid "Map Type"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:25
msgid "Select map type"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:27
msgid "Roadmap"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:28
msgid "Terrain"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:29
msgid "Satellite"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:30
msgid "Hybrid"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:34
msgid "Map Height"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:35
msgid "Set map height (Ex: 300)"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:41
msgid "Disable zoom on scroll"
msgstr ""

#: ../extensions/shortcodes/shortcodes/map/options.php:42
msgid "Prevent the map from zooming when scrolling until clicking on the map"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:76
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:65
msgid "Column"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:120
#, php-format
msgid "Add a %s column"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:12
msgid "Columns"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:47
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:47
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:51
msgid "No Templates Saved"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:50
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:50
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:54
msgid "Load Template"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:91
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:91
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:94
msgid "Template Name"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:92
msgid "Save Column"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:93
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:93
msgid "Save as Template"
msgstr ""

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:174
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:174
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:206
msgid "No Title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/special-heading/config.php:6
msgid "Special Heading"
msgstr ""

#: ../extensions/shortcodes/shortcodes/special-heading/config.php:7
msgid "Add a Special Heading"
msgstr ""

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:8
msgid "Heading Title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:9
msgid "Write the heading title content"
msgstr ""

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:13
msgid "Heading Subtitle"
msgstr ""

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:14
msgid "Write the heading subtitle content"
msgstr ""

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:18
msgid "Heading Size"
msgstr ""

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:30
msgid "Centered"
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/config.php:8
msgid "Team Member"
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/config.php:9
msgid "Add a Team Member"
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/options.php:7
msgid "Team Member Image"
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/options.php:12
msgid "Team Member Name"
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/options.php:13
msgid "Name of the person"
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/options.php:18
msgid "Team Member Job Title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/options.php:19
msgid "Job title of the person."
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/options.php:24
msgid "Team Member Description"
msgstr ""

#: ../extensions/shortcodes/shortcodes/team-member/options.php:25
msgid "Enter a few words that describe the person"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon/config.php:6
#: ../extensions/shortcodes/shortcodes/icon/options.php:8
#: ../extensions/megamenu/class-fw-extension-megamenu.php:115
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:188
msgid "Icon"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon/config.php:7
msgid "Add an Icon"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon/options.php:13
msgid "Icon title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon-box/config.php:6
msgid "Icon Box"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon-box/config.php:7
msgid "Add an Icon Box"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:8
msgid "Box Style"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:10
msgid "Icon above title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:11
msgid "Icon in line with title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:16
msgid "Choose an Icon"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:20
msgid "Title of the Box"
msgstr ""

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:25
msgid "Enter the desired content"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/config.php:9
msgid "Add a Button"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:7
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:17
msgid "Button Label"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:8
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:18
msgid "This is the text that appears on your button"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:13
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:23
msgid "Button Link"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:14
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:24
msgid "Where should your button link to"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:20
#: ../extensions/shortcodes/shortcodes/media-image/options.php:38
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:30
msgid "Open Link in New Window"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:21
#: ../extensions/shortcodes/shortcodes/media-image/options.php:39
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:31
msgid "Select here if you want to open the linked page in a new window"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:32
msgid "Button Color"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:33
msgid "Choose a color for your button"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:36
msgid "Default"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:37
msgid "Black"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:38
msgid "Blue"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:39
msgid "Green"
msgstr ""

#: ../extensions/shortcodes/shortcodes/button/options.php:40
msgid "Red"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/config.php:6
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:108
msgid "Video"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/config.php:7
msgid "Add a Video"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/config.php:8
#: ../extensions/shortcodes/shortcodes/media-image/config.php:8
#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:10
msgid "Media Elements"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/options.php:8
msgid "Insert Video URL"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/options.php:9
#: ../extensions/shortcodes/shortcodes/section/options.php:24
msgid "Insert Video URL to embed this video"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/options.php:13
msgid "Video Width"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/options.php:14
msgid "Enter a value for the width"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/options.php:19
msgid "Video Height"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-video/options.php:20
msgid "Enter a value for the height"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/config.php:8
#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:16
msgid "Calendar"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/config.php:9
msgid "Add a Calendar"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:14
msgid "Select calendar population method (Ex: events, custom)"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:23
msgid "Calendar Type"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:24
msgid "Select calendar type"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:27
#: ../extensions/backups/includes/module/schedule/settings-options.php:23
msgid "Daily"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:28
#: ../extensions/backups/includes/module/schedule/settings-options.php:22
msgid "Weekly"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:29
#: ../extensions/backups/includes/module/schedule/settings-options.php:21
msgid "Monthly"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:33
msgid "Start Week On"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:34
msgid "Select first day of week"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:37
#: ../extensions/shortcodes/shortcodes/calendar/static.php:110
msgid "Monday"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/options.php:38
#: ../extensions/shortcodes/shortcodes/calendar/static.php:109
msgid "Sunday"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:29
#: ../extensions/shortcodes/shortcodes/calendar/static.php:56
#: ../extensions/shortcodes/shortcodes/calendar/static.php:82
#: ../extensions/events/class-fw-extension-events.php:69
#: ../extensions/events/class-fw-extension-events.php:74
#: ../extensions/events/manifest.php:7
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:77
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:185
#: ../core/components/extensions/manager/available-extensions.php:180
msgid "Events"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:30
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:59
msgid "Add/Edit Date & Time"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:33
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:63
msgid "Note: Please set start & end event datetime"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:37
msgid "Event Title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:38
msgid "Enter the event title"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:42
msgid "Event URL"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:43
msgid "Enter the event URL (Ex: http://your-domain.com/event)"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:47
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:58
msgid "Date & Time"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:48
msgid "Enter the event date & time"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:129
msgid "No events provider specified for calendar shortcode"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:138
#, php-format
msgid "Unknown events provider \"%s\" specified for calendar shortcode"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:55
#: ../extensions/events/class-fw-extension-events.php:68
#: ../extensions/events/class-fw-extension-events.php:75
msgid "Event"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:57
#: ../extensions/shortcodes/shortcodes/calendar/views/view.php:23
msgid "Today"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:66
#, php-format
msgid "Calendar: View %s not found"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:67
#, php-format
msgid ""
"Calendar: Wrong date format %s. Should be either \"now\" or \"yyyy-mm-dd\""
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:69
msgid "Calendar: Event URL is not set"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:70
#, php-format
msgid ""
"Calendar: Wrong navigation direction %s. Can be only \"next\" or \"prev\" or "
"\"today\""
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:72
msgid ""
"Calendar: Time split parameter should divide 60 without decimals. Something "
"like 10, 15, 30"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:74
msgid "No events in this day."
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:77
#, php-format
msgid "week %s of %s"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:79
msgid "Week "
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:80
msgid "All day"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:81
msgid "Time"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:83
msgid "Ends before timeline"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:84
msgid "Starts after timeline"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:85
msgid "January"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:86
msgid "February"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:87
msgid "March"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:88
msgid "April"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:89
#: ../extensions/shortcodes/shortcodes/calendar/static.php:101
msgid "May"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:90
msgid "June"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:91
msgid "July"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:92
msgid "August"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:93
msgid "September"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:94
msgid "October"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:95
msgid "November"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:96
msgid "December"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:97
msgid "Jan"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:98
msgid "Feb"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:99
msgid "Mar"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:100
msgid "Apr"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:102
msgid "Jun"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:103
msgid "Jul"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:104
msgid "Aug"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:105
msgid "Sep"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:106
msgid "Oct"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:107
msgid "Nov"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:108
msgid "Dec"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:111
msgid "Tuesday"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:112
msgid "Wednesday"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:113
msgid "Thursday"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:114
msgid "Friday"
msgstr ""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:115
msgid "Saturday"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-image/config.php:7
msgid "Add an Image"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-image/options.php:8
msgid "Choose Image"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-image/options.php:16
msgid "Width"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-image/options.php:17
msgid "Set image width"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-image/options.php:22
#: ../extensions/shortcodes/shortcodes/divider/options.php:24
msgid "Height"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-image/options.php:23
msgid "Set image height"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-image/options.php:33
msgid "Image Link"
msgstr ""

#: ../extensions/shortcodes/shortcodes/media-image/options.php:34
msgid "Where should your image link to?"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/config.php:8
msgid "Notification"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/config.php:9
msgid "Add a Notification Box"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:7
msgid "Message"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:8
msgid "Notification message"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:10
msgid "Message!"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:13
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:444
msgid "Type"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:14
msgid "Notification type"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:17
msgid "Congratulations"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:18
msgid "Information"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:19
msgid "Alert"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/options.php:20
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:456
msgid "Error"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:9
msgid "Congratulations!"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:13
msgid "Information!"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:17
msgid "Alert!"
msgstr ""

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:21
msgid "Error!"
msgstr ""

#: ../extensions/shortcodes/shortcodes/widget-area/config.php:8
msgid "Widget Area"
msgstr ""

#: ../extensions/shortcodes/shortcodes/widget-area/config.php:9
msgid "Add a Widget Area"
msgstr ""

#: ../extensions/shortcodes/shortcodes/widget-area/options.php:5
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:5
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:7
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:40
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:91
msgid "Sidebar"
msgstr ""

#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:8
msgid "Call To Action"
msgstr ""

#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:9
msgid "Add a Call to Action"
msgstr ""

#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:9
msgid "This can be left blank"
msgstr ""

#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:14
msgid "Enter some content for this Info Box"
msgstr ""

#: ../extensions/shortcodes/shortcodes/text-block/config.php:8
msgid "Text Block"
msgstr ""

#: ../extensions/shortcodes/shortcodes/text-block/config.php:9
msgid "Add a Text Block"
msgstr ""

#: ../extensions/shortcodes/shortcodes/text-block/options.php:11
msgid "Enter some content for this texblock"
msgstr ""

#: ../extensions/shortcodes/shortcodes/divider/config.php:6
msgid "Divider"
msgstr ""

#: ../extensions/shortcodes/shortcodes/divider/config.php:7
msgid "Add a Divider"
msgstr ""

#: ../extensions/shortcodes/shortcodes/divider/options.php:13
msgid "Ruler Type"
msgstr ""

#: ../extensions/shortcodes/shortcodes/divider/options.php:14
msgid "Here you can set the styling and size of the HR element"
msgstr ""

#: ../extensions/shortcodes/shortcodes/divider/options.php:16
msgid "Line"
msgstr ""

#: ../extensions/shortcodes/shortcodes/divider/options.php:17
msgid "Whitespace"
msgstr ""

#: ../extensions/shortcodes/shortcodes/divider/options.php:25
msgid ""
"How much whitespace do you need? Enter a pixel value. Positive value will "
"increase the whitespace, negative value will reduce it. eg: '50', '-25', "
"'200'"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/config.php:6
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:23
msgid "Section"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/config.php:7
msgid "Add a Section"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/options.php:7
msgid "Full Width"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/options.php:11
msgid "Background Color"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/options.php:12
msgid "Please select the background color"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/options.php:16
msgid "Background Image"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/options.php:17
msgid "Please select the background image"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/options.php:23
msgid "Background Video"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:12
msgid "Sections"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:92
msgid "Save Section"
msgstr ""

#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:24
msgid "Creates a section"
msgstr ""

#: ../extensions/shortcodes/shortcodes/tabs/config.php:9
msgid "Add some Tabs"
msgstr ""

#: ../extensions/shortcodes/shortcodes/tabs/options.php:9
msgid "Add/Edit Tab"
msgstr ""

#: ../extensions/shortcodes/includes/class-fw-shortcode.php:194
#, php-format
msgid "No default view (views/view.php) found for shortcode: %s"
msgstr ""

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:203
#, php-format
msgid "Shortcode \"%s\" from %s was already defined at %s"
msgstr ""

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:264
#, php-format
msgid "Class file found for shortcode %s but no class %s found"
msgstr ""

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:269
#, php-format
msgid "The class %s must extend from FW_Shortcode"
msgstr ""

#: ../extensions/builder/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:204
msgid "Builder"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:306
msgid "Full Screen"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:307
msgid "Exit Full Screen"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:334
msgid "Undo"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:335
msgid "Redo"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/includes/fullscreen.php:81
msgid "Preview Changes"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/includes/templates/class-fw-ext-builder-templates.php:119
msgid "Templates"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:12
msgid "Full Templates"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:61
msgid "Save Full Template"
msgstr ""

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:95
msgid "Save Builder Template"
msgstr ""

#: ../extensions/social/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:252
msgid "Social"
msgstr ""

#: ../extensions/social/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:253
msgid ""
"Use this extension to configure all your social related APIs. Other "
"extensions will use the Social extension to connect to your social accounts."
msgstr ""

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:62
#: ../core/components/backend.php:584
msgid "Facebook"
msgstr ""

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:66
#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:33
msgid "API Settings"
msgstr ""

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:72
msgid "App ID/API Key:"
msgstr ""

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:73
msgid "Enter Facebook App ID / API Key."
msgstr ""

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:78
msgid "App Secret:"
msgstr ""

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:79
msgid "Enter Facebook App Secret."
msgstr ""

#: ../extensions/social/extensions/social-facebook/manifest.php:7
#: ../extensions/social/extensions/social-facebook/manifest.php:8
msgid "Social Facebook"
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:29
#: ../core/components/backend.php:592
msgid "Twitter"
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:39
msgid "Consumer Key"
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:40
msgid "Enter Twitter Consumer Key."
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:45
msgid "Consumer Secret"
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:46
msgid "Enter Twitter App Secret."
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:51
msgid "Access Token"
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:52
msgid "Enter Twitter Access Token."
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:57
msgid "Access Token Secret"
msgstr ""

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:58
msgid "Enter Twitter Access Token Secret."
msgstr ""

#: ../extensions/social/extensions/social-twitter/manifest.php:7
#: ../extensions/social/extensions/social-twitter/manifest.php:8
msgid "Social Twitter"
msgstr ""

#: ../extensions/forms/class-fw-extension-forms.php:112
#: ../extensions/forms/class-fw-extension-forms.php:123
#: ../extensions/forms/class-fw-extension-forms.php:131
#: ../extensions/forms/class-fw-extension-forms.php:142
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:102
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:114
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:124
msgid "Unable to process the form"
msgstr ""

#: ../extensions/forms/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:228
msgid "Forms"
msgstr ""

#: ../extensions/forms/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:229
msgid ""
"This extension adds the possibility to create a contact form. Use the drag & "
"drop form builder to create any contact form you'll ever want or need."
msgstr ""

#: ../extensions/forms/extensions/contact-forms/manifest.php:5
msgid "Contact Forms"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:134
msgid "Invalid destination email (please contact the site administrator)"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:158
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:77
msgid "Message sent!"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:164
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:85
msgid "Oops something went wrong."
msgstr ""

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:190
msgid "Please configure the {mailer_link} extension."
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:8
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:107
msgid "Contact form"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:9
msgid "Build contact forms"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:15
msgid "Form Fields"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:39
#: ../core/components/extensions/manager/views/extension.php:82
#: ../core/components/extensions/manager/views/extension-page-header.php:31
msgid "Settings"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:42
msgid "Options"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:53
msgid "Subject Message"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:54
msgid "This text will be used as subject message for the email"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:55
msgid "New message"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:64
msgid "Submit Button"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:65
msgid "This text will appear in submit button"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:66
msgid "Send"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:75
msgid "Success Message"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:76
msgid "This text will be displayed when the form will successfully send"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:83
msgid "Failure Message"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:84
msgid "This text will be displayed when the form will fail to be sent"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:94
msgid "Email To"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:95
msgid "We recommend you to use an email that you verify often"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:96
msgid "The form will be sent to this email address."
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:52
msgid "Contact Form"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:54
msgid "Configure Mailer"
msgstr ""

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:108
msgid "Add a Contact Form"
msgstr ""

#: ../extensions/forms/views/backend/submit-box-add.php:9
msgid "Note that the type can't be changed later."
msgstr ""

#: ../extensions/forms/views/backend/submit-box-add.php:11
msgid ""
"You will need to create a new form in order to have a different form type."
msgstr ""

#: ../extensions/forms/views/backend/submit-box-add.php:20
#: ../extensions/forms/views/backend/submit-box-edit.php:16
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:14
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:31
#: ../extensions/media/extensions/slider/views/backend/submit-box-error.php:8
msgid "Delete Permanently"
msgstr ""

#: ../extensions/forms/views/backend/submit-box-add.php:22
#: ../extensions/forms/views/backend/submit-box-edit.php:18
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:16
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:33
#: ../extensions/media/extensions/slider/views/backend/submit-box-error.php:10
msgid "Move to Trash"
msgstr ""

#: ../extensions/forms/views/backend/submit-box-add.php:33
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:35
msgid "Create"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:24
msgid "Add a Recaptcha field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:26
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:78
msgid "Recaptcha"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:59
msgid "Set site key"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:60
msgid "Set secret key"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:76
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:81
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:81
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:76
msgid "Enter field label (it will be displayed on the web site)"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:133
msgid "Security Code"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:147
msgid "Could not validate the form"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:148
msgid "Please fill the recaptcha"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:37
msgid "Site key"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:38
msgid "Your website key. More on how to configure ReCaptcha"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:42
msgid "Secret key"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:43
msgid "Your secret key. More on how to configure ReCaptcha"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:18
msgid "Add a Paragraph Text"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:20
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:48
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:77
msgid "Paragraph Text"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:49
msgid "Toggle mandatory field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:83
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:83
msgid "Mandatory Field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:84
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:84
msgid "Make this field mandatory?"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:98
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:98
msgid "Placeholder"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:99
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:99
msgid "This text will be used as field placeholder"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:105
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:107
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:107
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:105
msgid "Default Value"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:106
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:108
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:108
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:106
msgid "This text will be used as field default value"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:126
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:128
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:126
msgid "Restrictions"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:127
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:127
msgid "Set characters or words restrictions for this field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:131
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:131
msgid "Characters"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:132
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:132
msgid "Words"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:140
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:154
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:142
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:156
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:140
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:154
msgid "Min"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:141
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:155
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:143
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:157
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:141
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:155
msgid "Minim value"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:146
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:160
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:148
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:162
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:146
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:160
msgid "Max"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:147
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:161
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:149
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:163
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:147
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:161
msgid "Maxim value"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:178
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:180
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:121
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:137
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:137
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:114
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:121
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:178
msgid "Instructions for Users"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:179
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:181
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:122
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:138
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:138
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:115
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:122
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:179
msgid "The users will see these instructions in the tooltip near the field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:331
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:368
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:230
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:244
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:244
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:210
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:217
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:332
msgid "The {label} field is required"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:336
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:337
#, php-format
msgid "The {label} field must contain minimum %d character"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:341
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:342
#, php-format
msgid "The {label} field must contain minimum %d characters"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:346
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:347
#, php-format
msgid "The {label} field must contain maximum %d character"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:351
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:352
#, php-format
msgid "The {label} field must contain maximum %d characters"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:356
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:357
#, php-format
msgid "The {label} field must contain minimum %d word"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:361
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:362
#, php-format
msgid "The {label} field must contain minimum %d words"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:366
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:367
#, php-format
msgid "The {label} field must contain maximum %d word"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:371
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:372
#, php-format
msgid "The {label} field must contain maximum %d words"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:20
msgid "Add a Number field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:79
msgid "Number"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:129
msgid "Set digits or values restrictions of this field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:133
msgid "Digits"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:134
msgid "Value"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:363
msgid "The {label} field must be a valid number"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:373
#, php-format
msgid "The {label} field must have minimum %d digit"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:378
#, php-format
msgid "The {label} field must have minimum %d digits"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:383
#, php-format
msgid "The {label} field must have maximum %d digit"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:388
#, php-format
msgid "The {label} field must have maximum %d digits"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:393
#, php-format
msgid "The {label} field minimum value must be %s"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:398
#, php-format
msgid "The {label} field maximum value must be %s"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:18
msgid "Add a Dropdown"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:79
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:164
msgid "Dropdown"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:103
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:103
msgid "Choices"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:104
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:104
msgid "Add choice"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:110
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:113
msgid "Randomize"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:111
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:114
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:114
msgid "Do you want choices to be displayed in random order?"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:235
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:249
msgid "{label}: Submitted data contains not existing choice"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:38
msgid "Edit Title"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:39
msgid "Edit Subtitle"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:57
msgid "The title will be displayed on contact form header"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:62
#: ../extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:7
msgid "Subtitle"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:63
msgid "The form header subtitle text"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:18
msgid "Add a Single Choice field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:56
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:56
msgid "{x} More"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:113
msgid "Randomize?"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:124
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:124
msgid "Field Layout"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:125
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:125
msgid "Select choice display layout"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:127
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:127
msgid "One column"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:128
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:128
msgid "Two columns"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:129
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:129
msgid "Three columns"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:130
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:130
msgid "Side by side"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:18
msgid "Add a Multiple Choices field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:249
msgid "{label}: Submitted data contains not existing choices"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:18
msgid "Add an Email field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:79
msgid "Email"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:215
msgid "The {label} field must contain a valid email"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:18
msgid "Add a Website field"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:79
msgid "Website"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:222
msgid "The {label} field must be a valid website name"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:18
msgid "Add a Single Line Text"
msgstr ""

#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:20
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:47
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:77
msgid "Single Line Text"
msgstr ""

#: ../extensions/breadcrumbs/settings-options.php:17
msgid "Text for Homepage"
msgstr ""

#: ../extensions/breadcrumbs/settings-options.php:18
msgid "The homepage anchor will have this text"
msgstr ""

#: ../extensions/breadcrumbs/settings-options.php:23
msgid "Text for Blog Page"
msgstr ""

#: ../extensions/breadcrumbs/settings-options.php:24
msgid ""
"The blog page anchor will have this text. In case homepage will be set as "
"blog page, will be taken the homepage text"
msgstr ""

#: ../extensions/breadcrumbs/settings-options.php:29
msgid "Text for 404 Page"
msgstr ""

#: ../extensions/breadcrumbs/settings-options.php:30
msgid "The 404 anchor will have this text"
msgstr ""

#: ../extensions/breadcrumbs/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:108
msgid "Breadcrumbs"
msgstr ""

#: ../extensions/breadcrumbs/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:109
msgid ""
"Creates a simplified navigation menu for the pages that can be placed "
"anywhere in the theme. This will make navigating the website much easier."
msgstr ""

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:11
msgid "404 Not found"
msgstr ""

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:124
msgid "Searching for:"
msgstr ""

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:248
#: ../includes/option-types/multi-select/class-fw-option-type-multi-select.php:472
msgid "No title"
msgstr ""

#: ../extensions/events/class-fw-extension-events.php:89
msgid "Create a event item"
msgstr ""

#: ../extensions/events/class-fw-extension-events.php:186
msgid "Date"
msgstr ""

#: ../extensions/events/class-fw-extension-events.php:204
msgid "Event Options"
msgstr ""

#: ../extensions/events/class-fw-extension-events.php:310
msgid "Multi Interval Event"
msgstr ""

#: ../extensions/events/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:181
msgid ""
"This extension adds a fully fledged Events module to your theme. It comes "
"with built in pages that contain a calendar where events can be added."
msgstr ""

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:81
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:189
msgid "Event Categories"
msgstr ""

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:82
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:190
msgid "Select an event category"
msgstr ""

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:84
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:192
msgid "All Events"
msgstr ""

#: ../extensions/events/extensions/events-tags/manifest.php:7
msgid "Event-search-tags"
msgstr ""

#: ../extensions/events/extensions/events-tags/manifest.php:8
msgid "Connect extension event with shortcodes map & calendar"
msgstr ""

#: ../extensions/events/views/content.php:16
msgid "Google Calendar"
msgstr ""

#: ../extensions/events/views/content.php:17
msgid "Ical Export"
msgstr ""

#: ../extensions/events/views/content.php:20
msgid "Start"
msgstr ""

#: ../extensions/events/views/content.php:21
msgid "End"
msgstr ""

#: ../extensions/events/views/content.php:25
msgid "Speakers"
msgstr ""

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:37
msgid "Event Location"
msgstr ""

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:39
msgid "Where does the event take place?"
msgstr ""

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:43
msgid "All Day Event?"
msgstr ""

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:44
msgid "Is your event an all day event?"
msgstr ""

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:67
msgid "Start & End of Event"
msgstr ""

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:68
msgid "Set start and end events datetime"
msgstr ""

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:97
msgid "Associated User"
msgstr ""

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:99
msgid "Link this event to a specific user"
msgstr ""

#: ../extensions/sidebars/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:132
msgid "Sidebars"
msgstr ""

#: ../extensions/sidebars/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:133
msgid ""
"Brings a new layer of customization freedom to your website by letting you "
"add more than one sidebar to a page, or different sidebars on different "
"pages."
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:131
msgid "No matches found"
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:139
msgid "Do you realy want to change without saving?"
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:141
msgid "Missing ID. Check that you provided all mandatory data."
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:142
#: ../extensions/sidebars/views/backend-main-view.php:27
msgid "Created"
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:143
msgid "(For Grouped Pages)"
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:144
msgid "(For Specific Pages)"
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:145
msgid "No sidebar name specified"
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:146
msgid "Sidebar Name"
msgstr ""

#: ../extensions/sidebars/class-fw-extension-sidebars.php:147
msgid "New Sidebar"
msgstr ""

#: ../extensions/sidebars/views/frontend-no-widgets.php:5
msgid "Widgets Page"
msgstr ""

#: ../extensions/sidebars/views/backend-tab-specific.php:5
msgid "For specific"
msgstr ""

#: ../extensions/sidebars/views/backend-tab-specific.php:23
msgid "Type to search ..."
msgstr ""

#: ../extensions/sidebars/views/backend-tab-specific.php:30
msgid "Search for a specific page you want to set a sidebar for"
msgstr ""

#: ../extensions/sidebars/views/backend-tab-grouped.php:6
msgid "For group"
msgstr ""

#: ../extensions/sidebars/views/backend-tab-grouped.php:25
msgid "Select group of pages you want to set a sidebar for."
msgstr ""

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:18
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:60
msgid "Choose the position for your sidebar(s)"
msgstr ""

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:43
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:40
msgid "Add Sidebar"
msgstr ""

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:4
msgid "Sidebars for"
msgstr ""

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Specific Page"
msgstr ""

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Grouped Page"
msgstr ""

#: ../extensions/sidebars/views/backend-main-view.php:11
#: ../extensions/sidebars/views/backend-main-view.php:15
msgid "Manage Sidebars"
msgstr ""

#: ../extensions/sidebars/views/backend-main-view.php:18
msgid ""
"Use this section to create and/or set different sidebar(s) for different "
"page(s)"
msgstr ""

#: ../extensions/sidebars/views/backend-main-view.php:24
msgid "For Grouped Pages"
msgstr ""

#: ../extensions/sidebars/views/backend-main-view.php:25
msgid "For Specific Pages"
msgstr ""

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:32
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:121
msgid "Select sidebar you wish to replace."
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:241
msgid "No sidebar name specified."
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:270
msgid "Dynamic sidebar doesn't exixt"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:277
msgid ""
"The placeholder can't be deleted because it is used in one of sidebars below."
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:280
msgid ""
"Please replace it first so that you will not have visual gaps in your layout."
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:293
msgid "Successfully removed"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:543
msgid "Default for all pages"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:613
msgid " (no title)"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:780
msgid "Error: Type or sub_type error"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:838
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:869
msgid "Error: Sidebars not set"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:865
msgid "Error: Position doesn't exists. Please check config file."
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:55
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:253
msgid "Page"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:56
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:153
msgid "Pages"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:61
msgid "Portfolio Project"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:62
msgid "Portfolio Projects"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:73
msgid "Blog Category"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:79
msgid "Portfolio Category"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:85
msgid "Home Page"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:90
msgid "Search Page"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:95
msgid "404 Page"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:100
msgid "Author Page"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:105
msgid "Archive Page"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:149
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:308
msgid "All Pages"
msgstr ""

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:184
msgid "Others"
msgstr ""

#: ../extensions/megamenu/manifest.php:7
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:64
#: ../core/components/extensions/manager/available-extensions.php:60
msgid "Mega Menu"
msgstr ""

#: ../extensions/megamenu/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:61
msgid ""
"The Mega Menu extension adds a user-friendly drop down menu that will let "
"you easily create highly customized menu configurations."
msgstr ""

#: ../extensions/megamenu/class-fw-extension-megamenu.php:58
msgid "Select Icon"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:44
#, php-format
msgid "%s (Invalid)"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:48
#, php-format
msgid "%s (Pending)"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:61
msgid "sub item"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:97
msgid "Edit Menu Item"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:108
msgid "URL"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:117
msgid "Navigation Label"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:125
msgid "Title Attribute"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:134
msgid "Open link in a new window/tab"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:141
msgid "CSS Classes (optional)"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:149
msgid "Link Relationship (XFN)"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:157
msgid "Mega Menu Column Title"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:158
msgid "Item Title"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:166
msgid "Hide"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:172
msgid "This column should start a new row"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:178
msgid "Description (HTML)"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:181
msgid ""
"The description will be displayed in the menu if the current theme supports "
"it."
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:190
msgid "Add Icon"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:191
msgid "Edit Icon"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:205
msgid "Use as Mega Menu"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:211
msgid "Move"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:212
msgid "Up one"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:213
msgid "Down one"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:216
msgid "To the top"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:225
#, php-format
msgid "Original: %s"
msgstr ""

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:239
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:60
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2282
msgid "Cancel"
msgstr ""

#: ../extensions/backups/class-fw-extension-backups.php:299
#: ../extensions/backups/class-fw-extension-backups.php:366
msgid "File not specified"
msgstr ""

#: ../extensions/backups/class-fw-extension-backups.php:399
#: ../extensions/backups/class-fw-extension-backups.php:400
#: ../core/components/extensions/manager/available-extensions.php:156
msgid "Backup"
msgstr ""

#: ../extensions/backups/class-fw-extension-backups.php:554
#: ../extensions/backups/class-fw-extension-backups.php:568
msgid "Access Denied"
msgstr ""

#: ../extensions/backups/class-fw-extension-backups.php:561
msgid "Archive not found"
msgstr ""

#: ../extensions/backups/class-fw-extension-backups.php:575
#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:202
msgid "Failed to open file"
msgstr ""

#: ../extensions/backups/helpers.php:37
msgid "Invalid JSON response"
msgstr ""

#: ../extensions/backups/helpers.php:44
msgid ""
"HTTP Loopback Connections are not enabled on this server. If you need to "
"contact your web host, tell them that when PHP tries to connect back to the "
"site at the URL `{url}` and it gets the error `{error}`. There may be a "
"problem with the server configuration (eg local DNS problems, mod_security, "
"etc) preventing connections from working properly."
msgstr ""

#: ../extensions/backups/helpers.php:123
#: ../extensions/backups/helpers.php:145
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:363
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:431
#, php-format
msgid "Failed to create dir: %s"
msgstr ""

#: ../extensions/backups/helpers.php:152
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:137
#, php-format
msgid "Failed to copy: %s"
msgstr ""

#: ../extensions/backups/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:168
msgid "Backup & Demo Content"
msgstr ""

#: ../extensions/backups/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:169
msgid ""
"This extension lets you create an automated backup schedule, import demo "
"content or even create a demo content archive for migration purposes."
msgstr ""

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:97
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:98
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:385
msgid "Demo Content Install"
msgstr ""

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:282
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:329
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:362
msgid "Forbidden"
msgstr ""

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:342
msgid "Invalid demo"
msgstr ""

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:349
msgid "A content install is currently running"
msgstr ""

#: ../extensions/backups/extensions/backups-demo/views/page.php:28
#: ../extensions/backups/extensions/backups-demo/views/page.php:39
#: ../extensions/backups/views/page.php:17
#: ../extensions/backups/views/page.php:28
msgid "Important"
msgstr ""

#: ../extensions/backups/extensions/backups-demo/views/page.php:30
#: ../extensions/backups/views/page.php:19
#, php-format
msgid "You need to activate %s."
msgstr ""

#: ../extensions/backups/extensions/backups-demo/views/page.php:31
#: ../extensions/backups/views/page.php:20
msgid "zip extension"
msgstr ""

#: ../extensions/backups/views/page.php:70
msgid "Archives"
msgstr ""

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../extensions/backups/includes/module/schedule/settings-options.php:76
msgid "Full Backup"
msgstr ""

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../extensions/backups/includes/module/schedule/settings-options.php:85
msgid "Content Backup"
msgstr ""

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:88
msgid ""
"Warning! \n"
"You are about to delete a backup, it will be lost forever. \n"
"Are you sure?"
msgstr ""

#: ../extensions/backups/includes/module/schedule/settings-options.php:16
msgid "Interval"
msgstr ""

#: ../extensions/backups/includes/module/schedule/settings-options.php:20
#: ../core/components/extensions/manager/views/extension.php:180
msgid "Disabled"
msgstr ""

#: ../extensions/backups/includes/module/schedule/settings-options.php:25
msgid "Select how often do you want to backup your website."
msgstr ""

#: ../extensions/backups/includes/module/schedule/settings-options.php:32
#: ../extensions/backups/includes/module/schedule/settings-options.php:45
#: ../extensions/backups/includes/module/schedule/settings-options.php:58
msgid "Age Limit"
msgstr ""

#: ../extensions/backups/includes/module/schedule/settings-options.php:33
msgid "Age limit of backups in months"
msgstr ""

#: ../extensions/backups/includes/module/schedule/settings-options.php:46
msgid "Age limit of backups in weeks"
msgstr ""

#: ../extensions/backups/includes/module/schedule/settings-options.php:59
msgid "Age limit of backups in days"
msgstr ""

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:186
msgid "Backup Schedule"
msgstr ""

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:288
msgid "Once Weekly"
msgstr ""

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:292
msgid "Once a month"
msgstr ""

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:139
msgid "undefined"
msgstr ""

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:279
msgid "Task type not registered"
msgstr ""

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:286
msgid "Execution stopped (next step did not started)"
msgstr ""

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:306
msgid "Timed out"
msgstr ""

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:366
msgid "Invalid execution end time"
msgstr ""

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:537
msgid "Invalid execution result"
msgstr ""

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:639
msgid "Invalid token"
msgstr ""

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:658
msgid "Invalid tasks hash"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-restore.php:9
msgid "Image Sizes Restore"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:12
msgid "Archive Unzip"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:39
msgid "Zip file not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:47
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:50
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:49
#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:114
msgid "Destination dir not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:56
msgid "Destination dir is not empty"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:63
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:59
#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:50
msgid "Zip extension missing"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:71
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:70
#, php-format
msgid "Cannot open zip (Error code: %s)"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:38
msgid "Database export"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:71
msgid "Database table disappeared"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:91
msgid "Cannot create file"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:97
msgid "Cannot reopen file"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:141
msgid "Cannot export CREATE TABLE sql"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:211
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:267
msgid "Cannot get next database table"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:9
msgid "Files Restore"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:32
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:41
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:45
msgid "Source dir not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:39
msgid "Invalid source dir"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:46
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:25
msgid "Source dirs not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:116
msgid "No filesystem access, credentials required"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:120
msgid "No filesystem access, invalid credentials"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:126
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:135
msgid "Filesystem init failed"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:192
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:317
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:341
#, php-format
msgid "Cannot convert Filesystem path: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:197
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:322
#, php-format
msgid "Failed to list dir: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:227
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:281
#, php-format
msgid "Failed to remove dir: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:234
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:288
#, php-format
msgid "Failed to remove file: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:376
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:444
#, php-format
msgid "Failed to copy file: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:12
msgid "Archive Zip"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:97
msgid "Cannot close the zip file"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:103
msgid "Cannot move zip in destination dir"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:9
msgid "Files Export"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:33
msgid "Destination not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:74
#, php-format
msgid "Source dir %s is empty"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:116
msgid "Failed to get dir chmod"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:123
msgid "Failed to create destination dir"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:214
#, php-format
msgid "Failed to restore dir listing from: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:12
msgid "Database restore"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:63
msgid "Database file not found"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:111
#, php-format
msgid "Cannot drop temporary table: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:129
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:285
msgid "Cannot open db file"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:138
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:294
msgid "Cannot move cursor in db file"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:154
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:377
#, php-format
msgid "Failed to decode line %d from db file."
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:186
#, php-format
msgid "Cannot read line %d from db file"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:195
msgid "Required params not found"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:232
msgid "Cannot do full db restore because backup is missing some tables"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:396
#, php-format
msgid "Failed to drop tmp table %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:435
#, php-format
msgid "Failed to create tmp table %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:447
#, php-format
msgid "Tried to insert data in table that was not imported %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:606
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:628
#, php-format
msgid "Failed insert row from line %d"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:642
#, php-format
msgid "Invalid json type %s in db file"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:649
msgid "Cannot read line from db file"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:688
msgid "Failed to restore options keeping step"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:723
#, php-format
msgid "Failed to keep option: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:774
msgid "Tables drop failed"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:787
msgid "Tables rename failed."
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:799
#, php-format
msgid "Invalid sub task %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:12
msgid "Directory Cleanup"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:21
msgid "Dir not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:30
msgid "Cannot remove directory"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:37
msgid "Cannot create directory"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:55
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:70
#, php-format
msgid "Cannot create file: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:9
msgid "Image Sizes Remove"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:23
msgid "Uploads dir not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:18
msgid "Download"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:119
msgid "Invalid destination dir"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:132
#, php-format
msgid "Invalid type: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:141
#, php-format
msgid "Args not specified for type: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:9
msgid "Local Download"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:24
msgid "Source not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:29
msgid "Invalid source"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:38
msgid "Invalid source type"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:59
#, php-format
msgid "Cannot open zip: %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:70
msgid "Unhandled type"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:13
msgid "Downloading..."
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:16
msgid "Download finished. Doing unzip..."
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:19
#, php-format
msgid "Downloading... %s of %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:24
#, php-format
msgid "Downloading... %s"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:67
msgid "Url not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:72
msgid "Invalid url"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:79
msgid "File id not specified"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:84
msgid "Invalid file id"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:105
#, php-format
msgid "Zip open failed (code %d). Please try again"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:112
msgid "Zip extract failed"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:119
msgid "Failed to close the zip after extract"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:152
#, php-format
msgid "Request failed. Error code: %d"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:163
msgid "Invalid byte position"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:168
msgid "Empty response body"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:188
msgid "File ended without content"
msgstr ""

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:213
msgid "Failed to write data to file"
msgstr ""

#: ../extensions/translation/settings-options.php:11
msgid "Default Language"
msgstr ""

#: ../extensions/translation/settings-options.php:12
msgid "This is the default language of your website."
msgstr ""

#: ../extensions/translation/settings-options.php:19
msgid "Translate to"
msgstr ""

#: ../extensions/translation/settings-options.php:20
msgid "Choose the languages you want your website translated to."
msgstr ""

#: ../extensions/translation/settings-options.php:27
msgid "Convert data"
msgstr ""

#: ../extensions/translation/settings-options.php:28
msgid ""
"Set to default language the posts, pages categories or tags that don't have "
"a language set ?"
msgstr ""

#: ../extensions/translation/manifest.php:7
#: ../extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:336
#: ../core/components/extensions/manager/available-extensions.php:264
msgid "Translations"
msgstr ""

#: ../extensions/translation/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:265
msgid ""
"This extension lets you translate your website in any language or even add "
"multiple languages for your users to change at their will from the front-end."
msgstr ""

#: ../extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:114
msgid "All Languages"
msgstr ""

#: ../extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:332
msgid "The term translation does already exists.ACTION +++ "
msgstr ""

#: ../extensions/translation/extensions/translate-terms/manifest.php:7
msgid "Translate Terms"
msgstr ""

#: ../extensions/translation/extensions/translate-terms/manifest.php:8
msgid "This extension translate terms"
msgstr ""

#: ../extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:341
msgid "Language of this post"
msgstr ""

#: ../extensions/translation/extensions/translate-posts/manifest.php:7
msgid "Translate Posts"
msgstr ""

#: ../extensions/translation/extensions/translate-posts/manifest.php:8
msgid "This extension translate posts"
msgstr ""

#: ../extensions/translation/extensions/translate-widgets/manifest.php:7
msgid "Translate Widgets"
msgstr ""

#: ../extensions/translation/extensions/translate-widgets/manifest.php:8
msgid "This extension translate Widgets"
msgstr ""

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:10
msgid "Language Switcher"
msgstr ""

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:11
msgid "A Language Switcher Widget"
msgstr ""

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:42
msgid "New title"
msgstr ""

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:45
#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:49
msgid "Title:"
msgstr ""

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:14
msgid "A calendar of your site&#8217;s Posts."
msgstr ""

#: ../extensions/translation/extensions/translate-menus/manifest.php:7
msgid "Translate Menus"
msgstr ""

#: ../extensions/translation/extensions/translate-menus/manifest.php:8
msgid "This extension translate menus"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:211
msgid "Slider Design"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:213
msgid "Number of Images"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:231
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:234
#, php-format
msgid "%s updated."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:232
msgid "Custom field updated."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:233
msgid "Custom field deleted."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:235
#, php-format
msgid "%s restored to revision from %s"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:237
#, php-format
msgid "%s published."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:238
msgid "Page saved."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:239
#, php-format
msgid "%s submitted."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:240
#, php-format
msgid "%s scheduled for: %s."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:242
#, php-format
msgid "%s draft updated."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:272
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:52
msgid "Publish"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:342
msgid ""
"This slider was created correctly, but the code implementation was delete "
"from source code or blocked from filter.Delete this post or recovery slider "
"implementation"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:351
msgid ""
"This slider was created correctly, but the multimedia_types from config.php "
"file was deleted, please set multimedia_types for this slider type."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:361
msgid "Slider Configuration"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:375
msgid "Slider Title"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:377
msgid "Choose a title for your slider only for internal use: Ex: \"Homepage\"."
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:433
msgid "Slider Settings"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:461
msgid ""
"You don't have slider extensions, please create at least one extension for "
"properly work"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:474
msgid "Choose the population method for your slider"
msgstr ""

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:542
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:94
msgid "(no title)"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:6
#: ../extensions/media/extensions/slider/posts.php:12
#: ../extensions/media/extensions/slider/posts.php:18
#: ../extensions/media/extensions/slider/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:12
msgid "Sliders"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:7
#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:8
msgid "Slider"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:9
msgid "Add New Slider"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:10
msgid "Edit Slider"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:11
msgid "New Slider"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:13
msgid "View Slider"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:14
msgid "Search Sliders"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:15
msgid "No Sliders found"
msgstr ""

#: ../extensions/media/extensions/slider/posts.php:16
msgid "No Sliders found in Trash"
msgstr ""

#: ../extensions/media/extensions/slider/manifest.php:6
msgid ""
"Adds the Sliders extension to your website. You'll be able to create "
"different built in jQuery sliders for your homepage and all the other "
"website pages."
msgstr ""

#: ../extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:6
msgid "Nivo Slider"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:6
msgid "Owl Slider"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/bx-slider/manifest.php:6
msgid "Bx-Slider"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:4
msgid "Population Method Categories opt 1"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:5
#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:11
msgid "Option description"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:10
msgid "Population Method Categories opt 2"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:5
msgid "Type of Transition"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:6
msgid "Type of transition between slides"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:9
msgid "Horizontal"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:10
msgid "Vertical"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:11
msgid "Fade"
msgstr ""

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:9
msgid "Choose a subtitle for your slide."
msgstr ""

#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:9
msgid "Add a Slider"
msgstr ""

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:9
msgid "Select Slider"
msgstr ""

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:14
msgid "Set width"
msgstr ""

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:19
msgid "Set height"
msgstr ""

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:34
msgid "No Sliders Available"
msgstr ""

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:47
msgid "create a new Slider"
msgstr ""

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:49
msgid ""
"No Sliders created yet. Please go to the {br}Sliders page and "
"{add_slider_link}."
msgstr ""

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:5
msgid ""
"Note that the type and population can't be changed later. You'll need to "
"create a new slider to have a different slider type or population method."
msgstr ""

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:31
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:48
msgid "Schedule"
msgstr ""

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:40
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:57
msgid "Submit for Review"
msgstr ""

#: ../extensions/media/extensions/population-method/class-fw-extension-population-method.php:36
#, php-format
msgid "Specified population method does not exists: %s"
msgstr ""

#: ../extensions/media/extensions/population-method/class-fw-extension-population-method.php:55
#, php-format
msgid "Population method %s does not exist"
msgstr ""

#: ../extensions/media/extensions/population-method/manifest.php:3
msgid "Population Methods"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:21
msgid "Automatically, fetch images from categories"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:29
#, php-format
msgid "%s extension needs configured categories in post types"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:34
msgid "Categories Population Method"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:44
msgid "Choose Category"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:53
#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:53
msgid "Number of Images in the slider"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:107
msgid "Select Specific Categories"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:4
msgid "Population Method - Categories"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:21
msgid "Automatically, fetch images from tags"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:29
#, php-format
msgid "%s extension needs configured tags in post types"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:34
msgid "Tags Population Method"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:44
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:44
msgid "Choose Tag"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:107
msgid "Select Specific tags"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:4
msgid "Population Method - Tags"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:21
msgid "Automatically, fetch images from posts"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:29
#, php-format
msgid "%s extension needs configured post categories in post types"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:34
msgid "Posts Population Method"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:101
msgid "Select Specific posts"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:4
msgid "Population Method - Posts"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:22
msgid "Manually, I'll upload the images myself"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:33
msgid "Click to edit / Drag to reorder"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:54
msgid "Choose"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:4
msgid "Population Method - Custom"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:57
msgid "Add Slide"
msgstr ""

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:59
#: ../views/backend-settings-form.php:47
msgid "Save Changes"
msgstr ""

#: ../core/Fw.php:73
msgid "Framework requirements not met:"
msgstr ""

#: ../core/class-fw-manifest.php:293
msgid "minimum required version is"
msgstr ""

#: ../core/class-fw-manifest.php:296
msgid "maximum required version is"
msgstr ""

#: ../core/class-fw-manifest.php:301
msgid "and"
msgstr ""

#: ../core/class-fw-manifest.php:308
#, php-format
msgid "Current WordPress version is %s, %s"
msgstr ""

#: ../core/class-fw-manifest.php:314
#, php-format
msgid "Current Framework version is %s, %s"
msgstr ""

#: ../core/class-fw-manifest.php:323
#, php-format
msgid "Current version of the %s extension is %s, %s"
msgstr ""

#: ../core/class-fw-manifest.php:329
#, php-format
msgid "%s extension is required"
msgstr ""

#: ../core/class-fw-manifest.php:334
#, php-format
msgid "%s extension is required (%s)"
msgstr ""

#: ../core/extends/class-fw-option-type.php:283
#, php-format
msgid "Option type %s has no default value"
msgstr ""

#: ../core/components/backend.php:355
msgid "Done"
msgstr ""

#: ../core/components/backend.php:356
msgid "Ah, Sorry"
msgstr ""

#: ../core/components/backend.php:358
#: ../includes/option-types/color-picker/class-fw-option-type-color-picker.php:39
#: ../includes/option-types/color-picker/class-fw-option-type-color-picker.php:40
#: ../includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:41
#: ../includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:42
msgid "Reset"
msgstr ""

#: ../core/components/backend.php:541
#: ../core/components/backend.php:542
#: ../core/components/backend.php:650
msgid "Theme Settings"
msgstr ""

#: ../core/components/backend.php:577
msgid "leave a review"
msgstr ""

#: ../core/components/backend.php:588
msgid ""
"Unyson WordPress Framework is the fastest and easiest way to develop a "
"premium theme. I highly recommend it"
msgstr ""

#: ../core/components/backend.php:594
msgid ""
"If you like Unyson, {wp_review_link}, share on {facebook_share_link} or "
"{twitter_share_link}."
msgstr ""

#: ../core/components/backend.php:1303
msgid "You have no permissions to change settings options"
msgstr ""

#: ../core/components/backend.php:1316
msgid "The options were successfully reset"
msgstr ""

#: ../core/components/backend.php:1327
msgid "The options were successfully saved"
msgstr ""

#: ../core/components/backend.php:1440
msgid "Unknown collected group"
msgstr ""

#: ../core/components/backend.php:1779
#, php-format
msgid "Undefined option type: %s"
msgstr ""

#: ../core/components/backend.php:1826
#, php-format
msgid "Undefined container type: %s"
msgstr ""

#: ../core/components/extensions.php:447
#: ../core/components/extensions.php:525
#, php-format
msgid "Extension %s is invalid."
msgstr ""

#: ../core/components/theme.php:206
msgid "Theme requirements not met:"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:351
msgid "Cannot remove the old extensions backup dir"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:357
msgid "Cannot create the extensions backup dir"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:362
msgid "Cannot backup the extensions"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:408
msgid "Cannot clear the extensions directory"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:413
msgid "Cannot recreate the extensions directory"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:419
msgid "Cannot recover the extensions"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:645
#, php-format
msgid "Cannot activate hidden standalone extension %s"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:874
msgid "You are not allowed to install extensions."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:891
msgid "All supported extensions are already installed."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:967
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2384
#, php-format
msgid "Cannot remove temporary directory: %s"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1037
msgid "You have no permissions to install extensions"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1044
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1517
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1901
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2116
msgid "No extensions provided"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1053
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1527
msgid "WP Filesystem is not initialized"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1079
#, php-format
msgid "Extension \"%s\" is already installed."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1094
#, php-format
msgid "Extension \"%s\" is not available for install."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1122
#, php-format
msgid "Parent extension \"%s\" not available."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1156
#, php-format
msgid "Downloading the \"%s\" extension..."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1200
#, php-format
msgid "Installing the \"%s\" extension..."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1238
#, php-format
msgid "The %s extension has been successfully installed."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1399
msgid "You are not allowed to delete extensions."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1510
msgid "You have no permissions to uninstall extensions"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1578
#, php-format
msgid "Deleting the \"%s\" extension..."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1590
#, php-format
msgid "Cannot delete the \"%s\" extension."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1602
#, php-format
msgid "The %s extension has been successfully deleted."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1703
msgid "Extension not specified."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1708
#, php-format
msgid "Extension \"%s\" is not installed."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1776
#, php-format
msgid "Extension \"%s\" does not exist or is not active."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1785
#, php-format
msgid "%s extension does not have settings."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1806
msgid "Extension has no Install Instructions"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1830
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2047
msgid "Invalid request method."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1842
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2059
msgid "No extension specified."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1894
msgid "You have no permissions to activate extensions"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1914
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2132
#, php-format
msgid "Extension \"%s\" does not exist."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2109
msgid "You have no permissions to deactivate extensions"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2297
msgid "You are not allowed to save extensions settings."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2304
msgid "Invalid extension."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2309
msgid "Extension does not have settings options."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2341
msgid "Extensions settings successfully saved."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2368
#, php-format
msgid "Extension \"%s\" has no download sources."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2392
#, php-format
msgid "Cannot create temporary directory: %s"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2403
#, php-format
msgid "\"%s\" extension github source \"user_repo\" parameter is required"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2454
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (%s)"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2477
#, php-format
msgid "\"%s\" extension github repository \"%s\" has no releases."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2510
#, php-format
msgid "Cannot download the \"%s\" extension zip. (Response code: %d)"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2517
#, php-format
msgid "Cannot download the \"%s\" extension zip. %s"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2525
#, php-format
msgid "Cannot download the \"%s\" extension zip."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2538
#, php-format
msgid "Cannot save the \"%s\" extension zip."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2557
#, php-format
msgid "Cannot remove the \"%s\" extension downloaded zip."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2582
#, php-format
msgid "The unzipped \"%s\" extension directory not found."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2589
#, php-format
msgid "Unknown \"%s\" extension download source \"%s\""
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2615
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2634
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2697
#, php-format
msgid "Cannot read directory \"%s\"."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2649
#, php-format
msgid "Cannot delete \"%s\"."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2660
#, php-format
msgid "Cannot create the \"%s\" directory."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2678
#, php-format
msgid "Cannot move \"%s\" to \"%s\"."
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3103
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3150
#, php-format
msgid "Cannot activate the %s extension because it is not installed. %s"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3107
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3154
msgid "Install"
msgstr ""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3197
msgid "Install theme compatible extensions"
msgstr ""

#: ../core/components/extensions/manager/available-extensions.php:13
msgid ""
"Adds a sliders module to your website from where you'll be able to create "
"different built in jQuery sliders for your homepage and rest of the pages."
msgstr ""

#: ../core/components/extensions/manager/available-extensions.php:24
msgid "Media"
msgstr ""

#: ../core/components/extensions/manager/available-extensions.php:36
msgid "Population method"
msgstr ""

#: ../core/components/extensions/manager/available-extensions.php:85
msgid ""
"Let's you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr ""

#: ../core/components/extensions/manager/available-extensions.php:96
msgid "Shortcodes"
msgstr ""

#: ../core/components/extensions/manager/available-extensions.php:157
msgid ""
"This extension lets you set up daily, weekly or monthly backup schedule. You "
"can choose between a full backup or a data base only backup."
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:89
#: ../core/components/extensions/manager/views/extension-page-header.php:25
msgid "Install Instructions"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:102
msgid "Compatible"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:102
msgid "with your current theme"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:174
#, php-format
msgid "Parent extension \"%s\" is disabled"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:199
#, php-format
msgid "You need to update WordPress to %s: %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:201
msgid "Update WordPress"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:205
#, php-format
msgid "WordPress needs to be updated to %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:215
#, php-format
msgid "Maximum supported WordPress version is %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:230
#, php-format
msgid "You need to update %s to %s: %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:239
#, php-format
msgid "%s needs to be updated to %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:250
#, php-format
msgid "Maximum supported %s version is %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:268
#, php-format
msgid "You need to update the %s extension to %s: %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:277
#, php-format
msgid "The %s extension needs to be updated to %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:288
#, php-format
msgid "Maximum supported %s extension version is %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:303
#, php-format
msgid "The %s extension is disabled"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:304
#, php-format
msgid "Activate %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:313
#, php-format
msgid "The %s extension is not installed: %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:316
#, php-format
msgid "Install %s"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:321
#, php-format
msgid "The %s extension is not installed"
msgstr ""

#: ../core/components/extensions/manager/views/extension.php:342
msgid "View Requirements"
msgstr ""

#: ../core/components/extensions/manager/views/extension-page-header.php:40
#, php-format
msgid "%s Settings"
msgstr ""

#: ../core/components/extensions/manager/views/extension-page-header.php:43
#, php-format
msgid "%s Install Instructions"
msgstr ""

#: ../core/components/extensions/manager/views/extension-page-header.php:46
msgid "Unknown tab:"
msgstr ""

#: ../core/components/extensions/manager/views/delete-form.php:42
#: ../core/components/extensions/manager/views/install-form.php:51
msgid "No, Return me to the extension list"
msgstr ""

#: ../core/components/extensions/manager/views/delete-form.php:45
msgid "Click to view entire list of directories which will be deleted"
msgstr ""

#: ../core/components/extensions/manager/views/extensions-page.php:17
msgid "Active Extensions"
msgstr ""

#: ../core/components/extensions/manager/views/extensions-page.php:33
msgid "No extensions activated yet"
msgstr ""

#: ../core/components/extensions/manager/views/extensions-page.php:33
msgid "Check the available extensions below"
msgstr ""

#: ../core/components/extensions/manager/views/extensions-page.php:59
msgid "Available Extensions"
msgstr ""

#: ../core/components/extensions/manager/views/extensions-page.php:184
msgid "Show other extensions"
msgstr ""

#: ../core/components/extensions/manager/views/extensions-page.php:185
msgid "Hide other extensions"
msgstr ""

#: ../core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:14
#: ../core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:14
msgid "Go to extensions page"
msgstr ""

#: ../core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:17
#: ../core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:17
msgid "Return to Extensions page"
msgstr ""

#: ../views/backend-settings-form.php:48
msgid "Reset Options"
msgstr ""

#: ../views/backend-settings-form.php:62
msgid "by"
msgstr ""

#: ../views/backend-settings-form.php:155
msgid ""
"Click OK to reset.\n"
"All settings will be lost and replaced with default settings!"
msgstr ""

#: ../views/backend-settings-form.php:202
msgid "Resetting"
msgstr ""

#: ../views/backend-settings-form.php:204
msgid "We are currently resetting your settings."
msgstr ""

#: ../views/backend-settings-form.php:206
#: ../views/backend-settings-form.php:212
msgid "This may take a few moments."
msgstr ""

#: ../views/backend-settings-form.php:208
msgid "Saving"
msgstr ""

#: ../views/backend-settings-form.php:210
msgid "We are currently saving your settings."
msgstr ""

#: ../includes/option-types/class-fw-option-type-undefined.php:25
msgid "UNDEFINED OPTION TYPE"
msgstr ""

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:22
msgid "25%"
msgstr ""

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:23
msgid "50%"
msgstr ""

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:24
msgid "100%"
msgstr ""

#: ../includes/option-types/map/views/view.php:12
msgid "Specify location"
msgstr ""

#: ../includes/option-types/map/views/view.php:27
msgid "Location Venue"
msgstr ""

#: ../includes/option-types/map/views/view.php:42
msgid "Address"
msgstr ""

#: ../includes/option-types/map/views/view.php:57
msgid "City"
msgstr ""

#: ../includes/option-types/map/views/view.php:72
msgid "Country"
msgstr ""

#: ../includes/option-types/map/views/view.php:87
msgid "State"
msgstr ""

#: ../includes/option-types/map/views/view.php:103
msgid "Zip Code"
msgstr ""

#: ../includes/option-types/map/views/view.php:138
msgid "Cannot find the location?"
msgstr ""

#: ../includes/option-types/map/views/view.php:150
msgid "Reset location"
msgstr ""

#: ../includes/option-types/upload/class-fw-option-type-upload.php:150
msgid "Add Image"
msgstr ""

#: ../includes/option-types/upload/class-fw-option-type-upload.php:176
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:186
msgid "Upload"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:39
msgid "Font face"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:48
msgid "Normal"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:49
msgid "Italic"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:50
msgid "Oblique"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:59
#: ../includes/option-types/typography-v2/view.php:118
msgid "Style"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:85
msgid "Weight"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:101
msgid "Script"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:128
msgid "Size"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:139
msgid "Line height"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:150
msgid "Letter spacing"
msgstr ""

#: ../includes/option-types/typography-v2/view.php:173
msgid "Color"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:156
msgid "Unknown Set"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:186
msgid "Web Application Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:187
msgid "Hand Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:188
msgid "Transportation Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:189
msgid "Gender Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:190
msgid "File Type Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:193
msgid "Payment Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:195
msgid "Currency Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:196
msgid "Text Editor Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:197
msgid "Directional Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:198
msgid "Video Player Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:199
msgid "Brand Icons"
msgstr ""

#: ../includes/option-types/icon/class-fw-option-type-icon.php:200
msgid "Medical Icons"
msgstr ""

#: ../includes/option-types/icon/view.php:39
msgid "All Categories"
msgstr ""

#: ../includes/option-types/datetime-range/view.php:41
#: ../includes/option-types/gradient/view.php:46
msgid "to"
msgstr ""

#: ../includes/option-types/multi-picker/class-fw-option-type-multi-picker.php:179
#, php-format
msgid "No 'picker' key set for multi-picker option: %s"
msgstr ""

#: ../includes/option-types/background-image/view.php:37
msgid "Predefined images"
msgstr ""

#: ../includes/option-types/background-image/view.php:38
msgid "Custom image"
msgstr ""

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:160
msgid "Add Images"
msgstr ""

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:188
msgid "1 File"
msgstr ""

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:189
#, php-format
msgid "%u Files"
msgstr ""
