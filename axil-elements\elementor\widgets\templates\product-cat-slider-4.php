<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Group_Control_Image_Size;
use Elementor\Icons_Manager;
 
  $thumb_size                = 'axil-cat-thumbnail-sm';
  $thumb_size2               = 'full';
  $shop_permalink            = get_permalink( wc_get_page_id( 'shop' ) );
  $hide_empty_category       = $settings['hide_empty_category'] ? $settings['hide_empty_category'] : 0;
  $sub_title                 = $settings['sub_title'] ? ' sub_title' : ' no-sub_title';
  $cate_list                 = $settings['select_categories'];
  $sub_cats_list             = $settings['sub_select_categories'];
  $ordering                  = $settings['cat_ordering'];
  $number                    = $settings['number']; 
  $sliderautoplay            = $settings['sliderautoplay'] ? true : false;   
  $sliderinfinite            = $settings['sliderinfinite'] ? true : false; 
  
   $carasolArray = json_encode([
      "infinite"        => $sliderinfinite,
      "slidesToShow"    => 1, 
      "arrows"          => true,
      "dots"            => false,
       "autoplay"       => $sliderautoplay,
      "speed"           => 1000,  
      "prevArrow"       => '<button class="slide-arrow prev-arrow slick-arrow"><i class="fal fa-angle-left"></i></button',
      "nextArrow"       => '<button class="slide-arrow next-arrow slick-arrow"><i class="fal fa-angle-right"></i></button>'
    ]);

    $number_of_items = $settings['itemsPerSlide'];
    $count = 1;
    $i = 0;

if ( $settings['show_only_sub_category'] ){
 
      $parent_cat = array(
        'parent' => 0,
      );

      $filter_cat_arg = array(
        'include'    => $cate_list,
      );

      $cat_arg = array(
        'taxonomy'   => 'product_cat',
        'hide_empty' => 1,
        'orderby'    => 'date',
        'order'      => $ordering,
        'number'     => $number,
        'hide_empty' => $hide_empty_category,

      );
      $cat_args    = array_merge( $cat_arg, $filter_cat_arg );
      $product_categories   = get_categories( $cat_args );
      
        $cat_count = count($product_categories);

$row_col_class  = "row-cols-xl-{$settings['col_xl']} row-cols-lg-{$settings['col_lg']} row-cols-md-{$settings['col_md']} row-cols-sm-{$settings['col_sm']} row-cols-{$settings['col_mobile']}";
 
?>

<div class="has-axil-categrie-area bg-color-white axil-section-gapcommo is-cat-slider title-border-style">  
    <?php if ( $settings['section_title_display'] ): ?>  
      <div class="section-title-wrapper section-title-border"> 
            <?php  if($settings['title']){ ?>
              <<?php echo esc_html( $settings['sec_title_tag'] );?> class="sec-title title">
              <span><?php echo wp_kses_post( $settings['title'] );?></span><?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?> </<?php echo esc_html( $settings['sec_title_tag'] );?>>   
            <?php  } ?> 
      </div>
    <?php endif; ?> 

     <div data-slick='<?= $carasolArray; ?>' class="categrie-product-activation slick-layout-wrapper--15 axil-slick-angle  arrow-top-slide">           
      <?php 
        foreach ($product_categories as $product_category) :
          $i++;
          $cat_thumb = get_term_meta($product_category->term_id, 'thumbnail_id', true);  
          $cat_thumnail_url  = wp_get_attachment_image(  $cat_thumb, $thumb_size2, " ", array( "class" => "img-responsive d-block m-auto" ) );  
          ?>
          <?php echo ($count == 1) ? '<div class="slick-single-layout slick-slide"><div class="row '. $row_col_class  .' justify-content-center">' : '' ?>
            <div class="col">          
              <div class="categrie-product-4">
                  <a href="<?php echo get_term_link($product_category->term_id);?>">
                     <?php if ( $settings['cat_image_show'] ): ?>
                      <?php echo wp_kses_post( $cat_thumnail_url );?>
                    <?php endif; ?>
                    <h5 class="cat-title text-center mt-4"><?php echo esc_html( $product_category->name );?></h5>
                  </a>
              </div>               
            </div>
            <?php if($count == $number_of_items){
              echo '</div></div>';
              $count = 1;
            }else if($cat_count == $i){
							echo "</div></div>";
							$count++;
						}
						else{
							$count++;
						}
        endforeach; ?>        
  </div>
</div>
<?php 

}else{

  $taxonomy = 'product_cat';
  $product_categories = get_terms( $taxonomy, array(
    'parent'     => $sub_cats_list,
    'orderby'    => 'name',
    'order'      => $ordering,
    'number'     => $number,
    'hide_empty' => $hide_empty_category,
  ) );
  $row_col_class  = "row-cols-xl-{$settings['col_xl']} row-cols-lg-{$settings['col_lg']} row-cols-md-{$settings['col_md']} row-cols-sm-{$settings['col_sm']} row-cols-{$settings['col_mobile']}";
 
  
  ?>  
  <div class="has-axil-categrie-area bg-color-white axil-section-gapcommo is-cat-slider">  
    <?php if ( $settings['section_title_display'] ): ?>  
      <div class="section-title-wrapper section-title-border"> 
            <?php  if($settings['title']){ ?>
              <<?php echo esc_html( $settings['sec_title_tag'] );?> class="sec-title title">
              <span><?php echo wp_kses_post( $settings['title'] );?></span> <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?> </<?php echo esc_html( $settings['sec_title_tag'] );?>>   
            <?php  } ?> 
      </div>
    <?php endif; ?> 
       <div data-slick='<?= $carasolArray; ?>' class="categrie-product-activation slick-layout-wrapper--15 axil-slick-angle arrow-top-slide">           
     <?php 
      $cat_count = count($product_categories);
        foreach ($product_categories as $product_category) :
          $i++;
          $cat_thumb = get_term_meta($product_category->term_id, 'thumbnail_id', true);  
          $cat_thumnail_url  = wp_get_attachment_image(  $cat_thumb, $thumb_size2, " ", array( "class" => "img-responsive d-block m-auto" ) );  
          ?>
          <?php echo ($count == 1) ? '<div class="slick-single-layout slick-slide"><div class="row  '. $row_col_class  .' justify-content-center">' : '' ?>
            <div class="col">          
              <div class="categrie-product-4">
                  <a href="<?php echo get_term_link($product_category->term_id);?>">
                     <?php if ( $settings['cat_image_show'] ): ?>
                      <?php echo wp_kses_post( $cat_thumnail_url );?>
                    <?php endif; ?>
                    <h5 class="cat-title text-center mt-4"><?php echo esc_html( $product_category->name );?></h5>
                  </a>
              </div>               
            </div>
            <?php if($count == $number_of_items){
              echo '</div></div>';
              $count = 1;
            }else if($cat_count == $i){
							echo "</div></div>";
							$count++;
						}
						else{
							$count++;
						}
        endforeach; ?>
    </div>
  </div>
<?php } ?>
  