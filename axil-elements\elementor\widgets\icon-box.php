<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Group_Control_Css_Filter;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Box_Shadow;
use Elementor\Group_Control_Image_Size;
use Elementor\Utils;
use Elementor\Group_Control_Typography;
use Elementor\Scheme_Typography;
use Elementor\Scheme_Color;
use Elementor\Icons_Manager;


if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Icon_Box extends Widget_Base {

 public function get_name() {
        return 'wooc-icon-box';
    }    
    public function get_title() {
        return __( 'Icon Box', 'etrade-elements' );
    }
    public function get_icon() {
        return 'eicon-icon-box';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }

    protected function register_controls() {
              
        $this->start_controls_section(
            'icon_layout',
            [
                'label' => __( 'General', 'etrade-elements' ),
            ]
        );
	    
       $this->add_control(
           'style',
           [
               'label' => __( 'Layouts', 'etrade-elements' ),
               'type' => Controls_Manager::SELECT,
               'default' => '1',
               'separator'     => 'after', 
               'options' => [
                   '1'   => __( 'Layout One', 'etrade-elements' ),
                   '2'   => __( 'Layout Two', 'etrade-elements' ),                  
                   '3'   => __( 'Layout Three', 'etrade-elements' ),    
               ],
           ] 
       );
  
		$this->add_control(
		    'title',
		    [
		        'label' => __( 'Title', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXTAREA,
		        'default' => __( 'Title', 'etrade-elements' ),
		        'placeholder' => __( 'Title', 'etrade-elements' ),
		    ]
		);
		$this->add_control(
		    'subtitle',
		    [
		        'label' => __( 'Sub Title', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXTAREA,
		        'default' => __( 'Sub title', 'etrade-elements' ),
		        'placeholder' => __( 'Sub title', 'etrade-elements' ),
		    ]
		);
		$this->add_control(
			    'icontype',
			    [
			        'label' => __( 'Style', 'etrade-elements' ),
			        'type' => Controls_Manager::SELECT,
			        'default' => 'icon',			       
			        'options' => [
						'icon'  => esc_html__( 'Icon', 'etrade-elements' ),
						'image' => esc_html__( 'Custom Image', 'etrade-elements' ),
			        ],
			    ] 
			);

			$this->add_control(
			    'icon',
			    [
			        'label' => __( 'Icons', 'etrade-elements' ),
			        'type' => Controls_Manager::ICONS,
			        'default' => [
			            'value' => 'fa fa-university',
			            'library' => 'solid',
			        ],
                    'condition' => [
                            'icontype' =>'icon',
                    ],      
			    ]
			);

        $this->add_control(
            'icon_size',
            [
                'label' => esc_html__( 'Icon Size', 'plugin-name' ),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'condition' => [
                            'icontype' =>'icon',
                    ], 
                'size_units' => [ 'px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 300,
                        'step' => 2,
                    ],
                   
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 50,
                ],
                'selectors' => [
                    '{{WRAPPER}} .service-box .icon i' => 'font-size: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

			$this->add_control(
			    'image',
			    [
			        'label' => __('Image','etrade-elements'),
			        'type'=>Controls_Manager::MEDIA,
			        'default' => [
			            'url' => Utils::get_placeholder_image_src(),
			        ],
			        'dynamic' => [
			            'active' => true,
			        ],
			        'condition' => [
				                'icontype' =>'image',
				            ],      
			    ]
			);
			

			$this->add_group_control(
                Group_Control_Image_Size::get_type(),
                [
                    'name' => 'image_size',
                    'default' => 'large',
                    'separator' => 'none',
                     'condition' => [
				                'icontype' =>'image',
				            ], 
                ]
            );
   	
			
		
       $this->end_controls_section();  


     $this->start_controls_section(
            'icon_style_section',
            [
                'label' => __( 'Icon', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
 
        $this->add_control(
            'icon_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
               
                'selectors' => array(
                    '{{WRAPPER}} .service-box .icon i' => 'color: {{VALUE}}',
                ),
            ]
        ); 
       
        $this->add_responsive_control(
            'icon_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                
                'selectors' => [
                    '{{WRAPPER}} .service-box .icon' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
        
    $this->end_controls_section();

	
     $this->start_controls_section(
            'title_style_section',
            [
                'label' => __( 'Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
 
 
          $this->add_control(
            'title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
               
                'selectors' => array(
                    '{{WRAPPER}} .service-box .title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                
                'selector' => '{{WRAPPER}} .service-box .title',
            ]
        );
       
        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                
                'selectors' => [
                    '{{WRAPPER}} .service-box .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
        
    $this->end_controls_section();

     $this->start_controls_section(
            'subtitle_style_section',
            [
                'label' => __( 'Content', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
 
 
          $this->add_control(
            'subtitle_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
               
                'selectors' => array(
                    '{{WRAPPER}} .item-subtitle' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'subtitle_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                
                'selector' => '{{WRAPPER}} .item-subtitle',
            ]
        );
       
        $this->add_responsive_control(
            'subtitle_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                
                'selectors' => [
                    '{{WRAPPER}} .item-subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    
                ],
            ]
        );
        
    $this->end_controls_section();  
    }
	protected function render() {
	 $settings = $this->get_settings();	
     $template   = 'icon-box-' . str_replace("style", "", $settings['style']);                 
     return wooc_Elements_Helper::wooc_element_template( $template, $settings );
		 
	}
}