<?php
/**
 * ReduxFramework Sample Config File
 * For full documentation, please visit: http://docs.reduxframework.com/
 */
if (!class_exists('Redux')) {
    return;
}
$opt_name = AXIL_THEME_FIX . '_options';
$theme = wp_get_theme();
$args = array(
    // TYPICAL -> Change these values as you need/desire
    'opt_name' => $opt_name,
    // This is where your data is stored in the database and also becomes your global variable name.
    'disable_tracking' => true,
    'display_name' => $theme->get('Name'),
    // Name that appears at the top of your panel
    'display_version' => $theme->get('Version'),
    // Version that appears at the top of your panel
    'menu_type' => 'submenu',
    //Specify if the admin menu should appear or not. Options: menu or submenu (Under appearance only)
    'allow_sub_menu' => true,
    // Show the sections below the admin menu item or not
    'menu_title' => esc_html__('Theme Options', 'etrade'),
    'page_title' => esc_html__('Theme Options', 'etrade'),
    // You will need to generate a Google API key to use this feature.
    // Please visit: https://developers.google.com/fonts/docs/developer_api#Auth
    //'google_api_key'       => 'AIzaSyC2GwbfJvi-WnYpScCPBGIUyFZF97LI0xs',
    // Set it you want google fonts to update weekly. A google_api_key value is required.
    'google_update_weekly' => false,
    // Must be defined to add google fonts to the typography module
    'async_typography' => false,
    // Use a asynchronous font on the front end or font string
    //'disable_google_fonts_link' => true,                    // Disable this in case you want to create your own google fonts loader
    'admin_bar' => true,
    // Show the panel pages on the admin bar
    'admin_bar_icon' => 'dashicons-menu',
    // Choose an icon for the admin bar menu
    'admin_bar_priority' => 50,
    // Choose an priority for the admin bar menu
    'global_variable' => '',
    // Set a different name for your global variable other than the opt_name
    'dev_mode' => false,
    'forced_dev_mode_off' => false,
    // Show the time the page took to load, etc
    'update_notice' => false,
    // If dev_mode is enabled, will notify developer of updated versions available in the GitHub Repo
    'customizer' => false,
    // Enable basic customizer support
    //'open_expanded'     => true,                    // Allow you to start the panel in an expanded way initially.
    //'disable_save_warn' => true,                    // Disable the save warning when a user changes a field

    // OPTIONAL -> Give you extra features
    'page_priority' => null,
    // Order where the menu appears in the admin area. If there is any conflict, something will not show. Warning.
    'page_parent' => 'themes.php',
    // For a full list of options, visit: http://codex.wordpress.org/Function_Reference/add_submenu_page#Parameters
    'page_permissions' => 'manage_options',
    // Permissions needed to access the options panel.
    'menu_icon' => '',
    // Specify a custom URL to an icon
    'last_tab' => '',
    // Force your panel to always open to a specific tab (by id)
    'page_icon' => 'icon-themes',
    // Icon displayed in the admin panel next to your menu_title
    'page_slug' => AXIL_THEME_FIX . '_options',
    // Page slug used to denote the panel, will be based off page title then menu title then opt_name if not provided
    'save_defaults' => true,
    // On load save the defaults to DB before user clicks save or not
    'default_show' => true,
    // If true, shows the default value next to each field that is not the default value.
    'default_mark' => '',
    // What to print by the field's title if the value shown is default. Suggested: *
    'show_import_export' => true,
    // Shows the Import/Export panel when not used as a field.

    // CAREFUL -> These options are for advanced use only
    'transient_time' => 60 * MINUTE_IN_SECONDS,
    'output' => true,
    // Global shut-off for dynamic CSS output by the framework. Will also disable google fonts output
    'output_tag' => true,
    // Allows dynamic CSS to be generated for customizer and google fonts, but stops the dynamic CSS from going to the head
    'footer_credit' => '&nbsp;',
    // Disable the footer credit of Redux. Please leave if you can help it.

    // FUTURE -> Not in use yet, but reserved or partially implemented. Use at your own risk.
    'database' => '',
    // possible: options, theme_mods, theme_mods_expanded, transient. Not fully functional, warning!
    'use_cdn' => true,
    // If you prefer not to use the CDN for Select2, Ace Editor, and others, you may download the Redux Vendor Support plugin yourself and run locally or embed it in your code.
    'hide_expand' => true,
    // This variable determines if the ‘Expand Options’ buttons is visible on the options panel.
);

Redux::setArgs($opt_name, $args);
 
/**
 * General
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('General', 'etrade'),
    'id' => 'axil_general',
    'icon' => 'el el-cog',
));
Redux::setSection($opt_name, array(
    'title' => esc_html__('General Setting', 'etrade'),
    'id' => 'axil-general-setting',
    'icon' => 'el el-adjust-alt',
    'subsection' => true,
    'fields' => array(
      
        array(
            'id' => 'axil_logo_type',
            'type' => 'button_set',
            'title' => esc_html__('Select Logo Type', 'etrade'),
            'subtitle' => esc_html__('Select logo type, if the image is chosen the existing options of  image below will work, or text option will work. (Note: Used when Transparent Header is enabled.)', 'etrade'),
            'options' => array(
                'image' => 'Image',
                'text' => 'Text',
            ),
            'default' => 'image',
        ),
        array(
            'id' => 'axil_head_logo',
            'title' => esc_html__('Default Logo', 'etrade'),
            'subtitle' => esc_html__('Upload the main logo of your site. ( Recommended size: Width 267px and Height: 70px )', 'etrade'),
            'type' => 'media',
            'default' => array(
                'url' => AXIL_IMG_URL . 'logo.svg'
            ),
            'required' => array('axil_logo_type', 'equals', 'image'),
        ),
        
        
        array(
            'id' => 'axil_logo_padding',
            'type' => 'spacing',
            'title' => esc_html__('Logo Padding', 'etrade'),
            'subtitle' => esc_html__('Controls the top, right, bottom and left padding of the logo. (Note: Used when Transparent Header is enabled.)', 'etrade'),
            'mode' => 'padding',
            'units' => array('em', 'px'),
            'default' => array(
                'padding-top' => 'px',
                'padding-right' => 'px',
                'padding-bottom' => 'px',
                'padding-left' => 'px',
                'units' => 'px',
            ),

            'output'         => array('.header-brand a, .menu-item.logo a'),

            'required' => array('axil_logo_type', 'equals', 'image'),
        ),
        array(
            'id' => 'axil_logo_text',
            'type' => 'text',
            'required' => array('axil_logo_type', 'equals', 'text'),
            'title' => esc_html__('Site Title', 'etrade'),
            'subtitle' => esc_html__('Enter your site title here. (Note: Used when Transparent Header is enabled.)', 'etrade'),
            'default' => get_bloginfo('name')
        ),
        array(
            'id' => 'axil_logo_text_font',
            'type' => 'typography',
            'title' => esc_html__('Site Title Font Settings', 'etrade'),
            'required' => array('axil_logo_type', 'equals', 'text'),
            'google' => true,
            'subsets' => false,
            'line-height' => false,
            'text-transform' => true,
            'transition' => false,
            'text-align' => false,
            'preview' => false,
            'all_styles' => true,
            'output' => array('.header-brand a, .haeder-default .header-brand a'),
            'units' => 'px',
            'subtitle' => esc_html__('Controls the font settings of the site title. (Note: Used when Transparent Header is enabled.)', 'etrade'),
            'default' => array(
                'google' => true,
            )
        ),
        // End logo
        array(
            'id' => 'axil_scroll_to_top_enable',
            'type' => 'button_set',
            'title' => esc_html__('Enable Back To Top', 'etrade'),
            'subtitle' => esc_html__('Enable the back to top button that appears in the bottom right corner of the screen.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Yes', 'etrade'),
                'no' => esc_html__('No', 'etrade'),
            ),
            'default' => 'yes'
        ),
       

         array(
            'id' => 'cd_days',
            'type' => 'text',            
            'title' => esc_html__('Countdown Days label', 'etrade'),            
            'default' => 'Days'
        ),
        array(
            'id' => 'cd_hour',
            'type' => 'text',            
            'title' => esc_html__('Countdown Hour label', 'etrade'),            
            'default' => 'Hour'
        ),
        array(
            'id' => 'cd_minute',
            'type' => 'text',            
            'title' => esc_html__('Countdown Minute label', 'etrade'),            
            'default' => 'Minute'
        ),
        array(
            'id' => 'cd_second',
            'type' => 'text',            
            'title' => esc_html__('Countdown Second label', 'etrade'),            
            'default' => 'Second'
        ),


    )
));


/**
 * Header Top
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Login / Acount', 'etrade'),
    'id' => 'axil_acount_section',
    'icon' => 'el el-credit-card',
    'fields' => array(
        
        array(
            'id' => 'acount_login_title',
            'type' => 'text',
            'title' => esc_html__('Login Title', 'etrade'),
            'default' => 'We Offer the Best Products',
            
        ),       

         array(
            'id' => 'acount_login_img',
            'title' => esc_html__('Banner Login Image', 'etrade'),           
            'type' => 'media',
            'default' => array(
                'url' => AXIL_IMG_URL . 'bg-image-9.jpg'
            ),
        ),   
          array(
            'id' => 'acount_register_title',
            'type' => 'text',
            'title' => esc_html__('Register Page Title', 'etrade'),
            'default' => 'We Offer the Best Products',
            
        ),   

         array(
            'id' => 'acount_register_img',
            'title' => esc_html__('Banner Register Image', 'etrade'),           
            'type' => 'media',
            'default' => array(
                'url' => AXIL_IMG_URL . 'bg-image-10.jpg'
            ),
        ),      
       
    )
));




/**
 * Page Banner/Title section
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Shop Notification', 'etrade'),
    'id' => 'axil_banner_top_section',
    'icon' => 'el el-info-circle',
    'fields' => array(
        array(
            'id' => 'axil_shop_notification_enable',
            'type' => 'switch',
            'title' => esc_html__('Enable Shop Notification', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the Notification area.', 'etrade'),
            'default' => false,
        ),  
        
        array(
            'id' => 'shop_notification_date_enable',
            'type' => 'switch',
            'title' => esc_html__('Enable Date Counter', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the Notification Date Counter.', 'etrade'),
            'default' => false,
           // 'required'   => array('axil_select_notification_template','equals', '2'),
        ), 

         array(
            'id'          => 'shop_notification_date',
            'type'        => 'date',
            'title'       => __('Date Option', 'etrade'),  
         //   'required'    => array('shop_notification_date_enable','equals', true),
            'placeholder' => 'Click to enter a date'

        ),

          array(
            'id' => 'axil_shop_notification_contact',
            'type' => 'textarea',
            'title' => esc_html__('Notification', 'etrade'),
             
            'default' => 'Open Doors To A World Of Fashion <a href="#" target="_blank" rel="noopener">Discover More</a>',
         //   'required' => array('axil_shop_notification_enable', 'equals', true),
        ),
           
         
    )
));
 
Redux::setSection($opt_name, array(
    'title' => esc_html__('Header Top', 'etrade'),
    'id' => 'axil_header_top_section',
    'icon' => 'el el-credit-card',
    'fields' => array(
        array(
            'id' => 'axil_header_top_enable',
            'type' => 'switch',
            'title' => esc_html__('Header Top', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the Header top area.', 'etrade'),
            'default' => false,
        ),

    
        // Header Custom Style
        array(
            'id' => 'header_top_left',
            'type' => 'text',
            'title' => esc_html__('Header Top Left', 'etrade'),
            'default' => '365 days to change your mind',
            'required' => array('axil_header_top_enable', 'equals', true),
        ),
        array(
            'id' => 'header_top_center',
            'type' => 'text',
            'title' => esc_html__('Header Top Center', 'etrade'),
            'default' => 'CHRISTMAS SALE - 25% OFF <span>added at the checkout</span>',
            'required' => array('axil_header_top_enable', 'equals', true),
        ),
         array(
            'id' => 'header_top_right',
            'type' => 'switch',
            'title' => esc_html__('Header Top right', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the Top Right.', 'etrade'),
            'default' => true,
             'required' => array('axil_header_top_enable', 'equals', true),
        ),
        
    )
));


/**
 * Header
 */

Redux::setSection($opt_name, array(
        'title' => esc_html__('Header', 'etrade'),
        'id' => 'header_id',
        'icon' => 'el el-minus',
        'fields' => array(
            array(
                'id' => 'axil_enable_header',
                'type' => 'switch',
                'title' => esc_html__('Header', 'etrade'),
                'subtitle' => esc_html__('Enable or disable the header area.', 'etrade'),
                'default' => true
            ),

            array(
                'id' => 'axil_header_sticky',
                'type' => 'switch',
                'title' => esc_html__('Enable Sticky Header ', 'etrade'),
                'subtitle' => esc_html__('Enable to activate the sticky header.', 'etrade'),
                'default' => false,
                'required' => array('axil_enable_header', 'equals', true),
            ),
          
            // Header Custom Style
            array(
                'id' => 'axil_select_header_template',
                'type' => 'image_select',
                'title' => esc_html__('Select Header Layout', 'etrade'),
                'options' => array(
                    '1' => array(
                        'alt' => esc_html__('Header Layout 1', 'etrade'),
                        'title' => esc_html__('Header Layout 1', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/header/1.png',
                    ),
                    '2' => array(
                        'alt' => esc_html__('Header Layout 2', 'etrade'),
                        'title' => esc_html__('Header Layout 2', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/header/2.png',
                    ),
                    '3' => array(
                        'alt' => esc_html__('Header Layout 3', 'etrade'),
                        'title' => esc_html__('Header Layout 3', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/header/3.png',
                    ),
                    '4' => array(
                        'alt' => esc_html__('Header Layout 4', 'etrade'),
                        'title' => esc_html__('Header Layout 4', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/header/2.png',
                    ),
                    '5' => array(
                        'alt' => esc_html__('Header Layout 5', 'etrade'),
                        'title' => esc_html__('Header Layout 5', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/header/3.png',
                    ),
                    '6' => array(
                        'alt' => esc_html__('Header Layout 6', 'etrade'),
                        'title' => esc_html__('Header Layout 6', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/header/3.png',
                    ),
                 
                ),
                'default' => '5',
                'required' => array('axil_enable_header', 'equals', true),
            ),
           
            array(
                'id' => 'axil_enable_wishlist',
                'type' => 'switch',
                'title' => esc_html__('Header wishlist Icon', 'etrade'),
                'subtitle' => esc_html__('Enable or disable header wishlist Icon.', 'etrade'),
                'default' => false,
                'required' => array('axil_enable_header', 'equals', true),
            ),   
            array(
                'id' => 'axil_enable_cart',
                'type' => 'switch',
                'title' => esc_html__('Header Cart Icon', 'etrade'),
                'subtitle' => esc_html__('Enable or disable header cart Icon.', 'etrade'),
                'default' => false,
                'required' => array('axil_enable_header', 'equals', true),
            ), 

            array(
                'id' => 'axil_enable_acount',
                'type' => 'switch',
                'title' => esc_html__('Header Acount Icon', 'etrade'),
                'subtitle' => esc_html__('Enable or disable header Acount Icon.', 'etrade'),
                'default' => false,
                'required' => array('axil_enable_header', 'equals', true),
            ),

            array(
                'id' => 'axil_enable_department_menu',
                'type' => 'switch',
                'title' => esc_html__('Disable Categories Menu', 'etrade'),
                'subtitle' => esc_html__('Enable or disable header Acount Icon.', 'etrade'),
                'default' => true,
                'required' => array('axil_enable_header', 'equals', true),

            ),

             array(
                'id' => 'axil_enable_header_search',
                'type' => 'switch',
                'title' => esc_html__('Header Search Icon', 'etrade'),
                'subtitle' => esc_html__('Enable or disable header search form.', 'etrade'),
                'default' => false,
                'required' => array('axil_enable_header', 'equals', true),
            ),
 

            array(
              'id' => 'recent_number',
              'type' => 'text',
              'title' => esc_html__('Recent Items', 'etrade'),
              'default' => '3',
              'required' => array('axil_enable_header_search', 'equals', true),
              
          ),   
 
        
          
        )
    ));



/**
 * Header Top
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Currency / Language', 'etrade'),
    'id' => 'axil_currency_language_section',
    'icon' => 'el el-credit-card',
    'fields' => array(  

        array(
                'id' => 'axil_enable_currency',
                'type' => 'switch',
                'title' => esc_html__('Enable Currency', 'etrade'),
                'subtitle' => esc_html__('Enable or disable the Currency area.', 'etrade'),
                'default' => true
            ), 
        
        array(
            'id' => 'axil_currency_shortcode',
            'type' => 'textarea',
            'title' => esc_html__('Add Shortcode Here', 'etrade'),
            'default' => '',
            'required' => array('axil_enable_currency', 'equals', true),
            
        ), 

        array(
                'id' => 'axil_enable_language',
                'type' => 'switch',
                'title' => esc_html__('Enable Language', 'etrade'),
                'subtitle' => esc_html__('Enable or disable the Language area.', 'etrade'),
                'default' => true
            ), 

        array(
            'id' => 'axil_language_shortcode',
            'type' => 'textarea',
            'title' => esc_html__('Add Shortcode Here', 'etrade'),
            'default' => '',
            'required' => array('axil_enable_language', 'equals', true),
            
        ), 

              
       
    )
));



/**
 * Page Banner/Title section
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Pages Default Banner', 'etrade'),
    'id' => 'axil_banner_section',
    'icon' => 'el el-website',
    'fields' => array(
         
        array(
            'id' => 'axil_banner_enable',
            'type' => 'switch',
            'title' => esc_html__('Banner', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the banner area.', 'etrade'),
            'default' => true,
        ),      
           
        array(
            'id' => 'axil_breadcrumb_enable',
            'type' => 'switch',
            'title' => esc_html__('Breadcrumb', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the breadcrumb area.', 'etrade'),
            'default' => true,
            'required' => array('axil_banner_enable', 'equals', true),
        ),
         array(
            'id' => 'axil_select_banner_img',
            'title' => esc_html__('Pages Banner Image', 'etrade'),           
            'type' => 'media',
            'default' => array(
               'url' => AXIL_IMG_URL . 'product-45.png'
            ),
            'required' => array('axil_banner_enable', 'equals', true),
        ),   
       
    )
));



// Woocommerce
if ( class_exists( 'WooCommerce' ) ) {

/**
 * Ads Management
 */
Redux::setSection($opt_name,
    array(
        'title' => esc_html__('WooCommerce', 'etrade'),
        'id' => 'ad_settings_section',
        'icon' => 'el el-shopping-cart-sign',
    )
);



Redux::setSection( $opt_name,
    array(
        'title'   => esc_html__( 'Shop Settings', 'etrade' ),
        'id'      => 'wc_sec_shop',
        'subsection' => true,
        'fields'  => array(         
               
        array(
            'id' => 'axil_shop_banner_enable',
            'type' => 'switch',
            'title' => esc_html__('Banner', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the banner area.', 'etrade'),
            'on'       => esc_html__( 'Show', 'etrade' ),
            'off'      => esc_html__( 'Hide', 'etrade' ),

            'default' => true,

        ),
        array(
            'id' => 'axil_shop_banner_title',
            'type' => 'text',
            'title' => esc_html__('Default Title', 'etrade'),
            'subtitle' => esc_html__('Controls the Default title of the page which is displayed on the page title are on the shop page.', 'etrade'),
            'default' => esc_html__('Explore All Products', 'etrade'),
            'required' => array('axil_shop_banner_enable', 'equals', true),
        ),           
        array(
            'id' => 'axil_shop_bannr_subtitle',
            'type' => 'text',
            'title' => esc_html__('Shop Banner Sub Title', 'etrade'),
            'default' => '',
            'required' => array('axil_shop_banner_enable', 'equals', true),
        ),

         array(
            'id' => 'axil_shop_banner_img',
            'title' => esc_html__('Shop Banner Image', 'etrade'),           
            'type' => 'media',      
            'default' => array(
                'url' => AXIL_IMG_URL . 'product-45.png'
            ),      
            'required' => array('axil_shop_banner_enable', 'equals', true),
        ),


           array(
            'id' => 'shop_layout',
            'type' => 'image_select',
            'title' => esc_html__('Select Shop Page Sidebar', 'etrade'),
            'subtitle' => esc_html__('Choose your favorite blog layout', 'etrade'),
            'options' => array(
                'left-sidebar' => array(
                    'alt' => esc_html__('Left Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/left-sidebar.png',
                    'title' => esc_html__('Left Sidebar', 'etrade'),
                ),
                'right-sidebar' => array(
                    'alt' => esc_html__('Right Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/right-sidebar.png',
                    'title' => esc_html__('Right Sidebar', 'etrade'),
                ),
                'full-width' => array(
                    'alt' => esc_html__('No Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/no-sidebar.png',
                    'title' => esc_html__('No Sidebar', 'etrade'),
                ),
            ),
            'default' => 'left-sidebar',
        ),
        array(
            'id'       => 'shop_sidebar',
            'type'     => 'select',
            'title'    => esc_html__( 'Select Shop Siebar', 'etrade'), 
             'options'  => Helper::get_custom_sidebar_fields(),
             'default'  => 'widgets-shop',
        ),            
  
            array(
                'id' => 'wooc_product_layout',
                'type' => 'image_select',
                'title' => esc_html__('Product Block Style', 'etrade'),
                'options' => array(
                    '1' => array(
                        'alt' => esc_html__('Product Layout 1', 'etrade'),
                        'title' => esc_html__('Product Layout 1', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/layout-1.png',
                    ),
                    '2' => array(
                        'alt' => esc_html__('Product Layout 2', 'etrade'),
                        'title' => esc_html__('Product Layout 2', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/layout-2.png',
                    ),
                    '3' => array(
                        'alt' => esc_html__('Product Layout 3', 'etrade'),
                        'title' => esc_html__('Product Layout 3', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/layout-3.png',
                    ),
                    '4' => array(
                        'alt' => esc_html__('Product Layout 4', 'etrade'),
                        'title' => esc_html__('Product Layout 4', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/layout-4.png',
                    ),
                    '5' => array(
                        'alt' => esc_html__('Product Layout 5', 'etrade'),
                        'title' => esc_html__('Product Layout 5', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/layout-5.png',
                    ),
                    '6' => array(
                        'alt' => esc_html__('Product Layout 6', 'etrade'),
                        'title' => esc_html__('Product Layout 6', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/layout-6.png',
                    ),
                    '7' => array(
                        'alt' => esc_html__('Product Layout 7', 'etrade'),
                        'title' => esc_html__('Product Layout 7', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/layout-7.png',
                    ),  
                     '8' => array(
                        'alt' => esc_html__('Product Layout 8', 'etrade'),
                        'title' => esc_html__('Product Layout 8', 'etrade'),
                        'img' => get_template_directory_uri() . '/assets/images/optionframework/archive/layout-8.png',
                    ),
                 
                ),
                'default' => '1',
                
            ),
 
            array(
                'id'       => 'wooc_desktop_product_columns',
                'type'     => 'select',
                'title'    => esc_html__( 'desktop Product Columns', 'etrade'), 
                'options'  => array(
                    '12' => esc_html__( '1 Col', 'etrade' ),
                    '6'  => esc_html__( '2 Col', 'etrade' ),
                    '4'  => esc_html__( '3 Col', 'etrade' ),
                    '3'  => esc_html__( '4 Col', 'etrade' ),
                    '2'  => esc_html__( '6 Col', 'etrade' ),
                ),
                'default'  => '4',
            ),   
            array(
                'id'       => 'wooc_tab_product_columns',
                'type'     => 'select',
                'title'    => esc_html__( 'Tablet Product Columns', 'etrade'), 
                'options'  => array(
                    '12' => esc_html__( '1 Col', 'etrade' ),
                    '6'  => esc_html__( '2 Col', 'etrade' ),
                    '4'  => esc_html__( '3 Col', 'etrade' ),
                    '3'  => esc_html__( '4 Col', 'etrade' ),
                    '2'  => esc_html__( '6 Col', 'etrade' ),
                ),
                'default'  => '6',
            ),
            array(
                'id'       => 'wooc_mobile_product_columns',
                'type'     => 'select',
                'title'    => esc_html__( 'Mobile Product Columns', 'etrade'), 
                'options'  => array(
                    '12' => esc_html__( '1 Col', 'etrade' ),
                    '6'  => esc_html__( '2 Col', 'etrade' ),
                    '4'  => esc_html__( '3 Col', 'etrade' ),
                    '3'  => esc_html__( '4 Col', 'etrade' ),
                    '2'  => esc_html__( '6 Col', 'etrade' ),
                ),
                'default'  => '12',
            ),  

            array(
                'id'       => 'wooc_num_product',
                'type'     => 'text',
                'title'    => esc_html__( 'Number of Products Per Page', 'etrade' ),
                'default'  => '9',
            ),

            array(
                'id'       => 'wooc_pagination',
                'type'     => 'button_set',
                'title'    => esc_html__( 'Pagination Type', 'etrade' ),
                'options'  => array(
                    'numbered'        => esc_html__( 'Numbered', 'etrade' ),
                    'load-more'       => esc_html__( 'Load More', 'etrade' ),
                    'infinity-scroll' => esc_html__( 'Infinity Scroll', 'etrade' ),
                ),
                'default'  => 'numbered'
            ),
 
            array(
                'id'       => 'wooc_shop_review',
                'type'     => 'switch',
                'title'    => esc_html__( 'Product Review Star', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
                 
            ), 
            array(
                'id'       => 'sale_price_only',
                'type'     => 'switch',
                'title'    => esc_html__( 'Display only sale price', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => false,
                 
            ),
  
              array(
                'id'        => 'product_display_attributes',
                'type'      => 'switch',
                'title'     => esc_html__( 'Color Attributes', 'etrade' ),
                'subtitle'  => esc_html__( 'Display color for product attributes.', 'etrade' ),
                'default'   => 0,
                'on'        => 'Enable',
                'off'       => 'Disable'
            ),

    
            
            array(
                'id'        => 'product_display_hover',
                'type'      => 'switch',
                'title'     => esc_html__( 'Hover Image', 'etrade' ),
                'subtitle'  => esc_html__( 'Display Hover image for product.', 'etrade' ),
                'default'   => 0,
                'on'        => 'Enable',
                'off'       => 'Disable'
            ),
            
            array(
                'id'        => 'wishlist',
                'type'      => 'switch',
                'title'     => esc_html__( 'Display Wishlist', 'etrade' ),
                'subtitle'  => esc_html__( 'Display Wishlist for product.', 'etrade' ),
                'default'   => 0,
                'on'        => 'Enable',
                'off'       => 'Disable'
            ),
        array(
            'id'        => 'quickview',
            'type'      => 'switch',
            'title'     => esc_html__( 'Display Quickview', 'etrade' ),
            'subtitle'  => esc_html__( 'Display Quickview for product.', 'etrade' ),
            'default'   => 0,
            'on'        => 'Enable',
            'off'       => 'Disable'
        ),   
        array(
            'id'        => 'display_shop_compare',
            'type'      => 'switch',
            'title'     => esc_html__( 'Display Compare', 'etrade' ),
            'subtitle'  => esc_html__( 'Display Compare for product.', 'etrade' ),
            'default'   => 0,
            'on'        => 'Enable',
            'off'       => 'Disable'
        ),
        array(
            'id'        => 'display_title_badge_check',
            'type'      => 'switch',
            'title'     => esc_html__( 'Display Title Badge Check', 'etrade' ),
            'subtitle'  => esc_html__( 'Display Icon for product Title.', 'etrade' ),
            'default'   => 0,
            'on'        => 'Enable',
            'off'       => 'Disable'
        ),

       array(
            'id'       => 'wooc_sale_label',
            'type'     => 'button_set',
            'title'    => esc_html__( 'Sale Product Label', 'etrade' ),
            'options'  => array(
                'percentage' => esc_html__( 'Percentage', 'etrade' ),
                'text'       => esc_html__( 'Text', 'etrade' ),
            ),
            'default'  => 'percentage'
        ),

         array(
            'id' => 'wooc_sale_label_txt',
            'type' => 'text',
            'title' => esc_html__('Label Text', 'etrade'),
            'default' => 'Sale! ',
            'required' => array('wooc_sale_label', 'equals', 'text'),
        ),

    )
)
);

Redux::setSection( $opt_name,
    array(
        'title'   => esc_html__( 'Product Settings', 'etrade' ),
        'id'      => 'wc_sec_product',
        'subsection' => true,
        'fields'  => array(

     
        array(
            'id' => 'product_wc_single_layout',
            'type' => 'image_select',
            'title' => esc_html__('Select Product Layout', 'etrade'),
            'options' => array(

                '1' => array(
                    'alt' => esc_html__('Product Layout 1', 'etrade'),
                    'title' => esc_html__('Product Layout 1', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/product/1.png',
                ),
                
                '2' => array(
                    'alt' => esc_html__('Product Layout 2', 'etrade'),
                    'title' => esc_html__('Product Layout 2', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/product/2.png',
                ),
                
                '3' => array(
                    'alt' => esc_html__('Product Layout 3', 'etrade'),
                    'title' => esc_html__('Product Layout 3', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/product/3.png',
                ),
                
                '4' => array(
                    'alt' => esc_html__('Product Layout 4', 'etrade'),
                    'title' => esc_html__('Product Layout 4', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/product/4.png',
                ),
                
                '5' => array(
                    'alt' => esc_html__('Product Layout 5', 'etrade'),
                    'title' => esc_html__('Product Layout 5', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/product/5.png',
                ),
                
                '6' => array(
                    'alt' => esc_html__('Product Layout 6', 'etrade'),
                    'title' => esc_html__('Product Layout 6', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/product/6.png',
                ),
                
                '7' => array(
                    'alt' => esc_html__('Product Layout 7', 'etrade'),
                    'title' => esc_html__('Product Layout 7 (NFT)', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/product/7.png',
                ),
                '8' => array(
                    'alt' => esc_html__('Woocommerce Default Layout', 'etrade'),
                    'title' => esc_html__('Woocommerce Default Layout', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/product/7.png',
                ),
                
                 
             
            ),
            'default' => '1',
            'required' => array('axil_enable_header', 'equals', true),
        ),
 


            array(
                'id'       => 'wc_social',
                'type'     => 'switch',
                'title'    => esc_html__( 'Display Social Sharing', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => false,
            ),
            array(
                'id'      => 'wc_share',
                'type'    => 'checkbox',
                'class'   => 'redux-custom-inline',
                'title'   => esc_html__( 'Social Sharing Icons', 'etrade'),
                'options' => array(
                    'facebook'  => 'Facebook',
                    'twitter'   => 'Twitter',
                    'linkedin'  => 'Linkedin',
                    'pinterest' => 'Pinterest',
                    'tumblr'    => 'Tumblr',
                    'reddit'    => 'Reddit',
                    'vk'        => 'Vk',
                ),
                'default' => array(
                    'facebook'  => '1',
                    'twitter'   => '1',
                    'linkedin'  => '1',
                    'pinterest' => '1',
                    'tumblr'    => '0',
                    'reddit'    => '1',
                    'vk'        => '0',
                ),
                'required' => array( 'wc_social', '=', true )
            ),
            array(
                'id'       => 'wooc_select_attribute',
                'type'     => 'switch',
                'title'    => esc_html__( "Enabled Select Attribute", 'etrade' ),
                'on'       => esc_html__( 'Enabled', 'etrade' ),
                'off'      => esc_html__( 'Disabled', 'etrade' ),
                'default'  => false,
            ),
            array(
                'id'       => 'wooc_show_excerpt',
                'type'     => 'switch',
                'title'    => esc_html__( "Show excerpt when short description doesn't exist", 'etrade' ),
                'on'       => esc_html__( 'Enabled', 'etrade' ),
                'off'      => esc_html__( 'Disabled', 'etrade' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wooc_cats',
                'type'     => 'switch',
                'title'    => esc_html__( 'Categories', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ),
             array(
                'id'       => 'wooc_tags',
                'type'     => 'switch',
                'title'    => esc_html__( 'Tags', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ),
            array(
                'id'       => 'in_stock_avaibility',
                'type'     => 'switch',
                'title'    => esc_html__( 'In stock Avaibility', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ),
                   
            array(
                'id'       => 'wooc_product_wishlist_icon',
                'type'     => 'switch',
                'title'    => esc_html__( 'Wishlist Icon', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
                'subtitle' => esc_html__( 'Plugin "YITH WooCommerce Wishlist" must be enabled to use this feature', 'etrade' ),
            ),       
            array(
                'id'       => 'wc_product_compare_icon',
                'type'     => 'switch',
                'title'    => esc_html__( 'Wishlist compare', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
                'subtitle' => esc_html__( 'Plugin "YITH WooCommerce compare" must be enabled to use this feature', 'etrade' ),
            ),
            array(
                'id'       => 'wooc_related',
                'type'     => 'switch',
                'title'    => esc_html__( 'Related Products', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ), 


            array(
                'id' => 'related_products_before_heading',
                'type' => 'text',
                'title' => esc_html__('Related Products Before Title', 'etrade'),
                'default' => 'Your Recently',
                'required' => array('wooc_related', 'equals', true),
            ), 
            array(
                'id' => 'related_products_heading',
                'type' => 'text',
                'title' => esc_html__('Related Products Title', 'etrade'),
                'default' => 'Recently Items',
                'required' => array('wooc_related', 'equals', true),
            ),


            array(
                'id'       => 'wooc_up_sell',
                'type'     => 'switch',
                'title'    => esc_html__( 'Up Sell Products', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ),  
            array(
                'id'       => 'wooc_cross_sell',
                'type'     => 'switch',
                'title'    => esc_html__( 'Cross Sell Products', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wooc_description',
                'type'     => 'switch',
                'title'    => esc_html__( 'Description Tab', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wooc_reviews',
                'type'     => 'switch',
                'title'    => esc_html__( 'Reviews Tab', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ),
            array(
                'id'       => 'wooc_additional_info',
                'type'     => 'switch',
                'title'    => esc_html__( 'Additional Information Tab', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ), 
            array(
               'id' => 'axil_nft_verifiy_enable',
                'type'     => 'switch',
                'title'    => esc_html__( 'NFTs Enable Verifiy', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ), 
 
            array(
                'id'            => 'axil_nft_verifiy_title',
                'type'          => 'text',
                'title'         => esc_html__('Default NFT Verifiy Title', 'etrade'),
                'subtitle'      => esc_html__('Controls the Default title of the page which is displayed on the Verifiy are on the Products page.', 'etrade'),
                'default'       => esc_html__('Is this item Authentic?', 'etrade'),
                'required'      => array('axil_nft_verifiy_enable', 'equals', true),
            ),   
            array(
                'id'            => 'axil_nft_btn_txt',
                'type'          => 'text',
                'title'         => esc_html__('Default NFT Button Text', 'etrade'),
                'subtitle'      => esc_html__('Controls the Default title of the page which is displayed on the Verifiy are on the Products page.', 'etrade'),
                'default'       => esc_html__('Verifiy', 'etrade'),
                'required'      => array('axil_nft_verifiy_enable', 'equals', true),
            ),

             array(
               'id' => 'axil_nft_verifiy_enable',
                'type'     => 'switch',
                'title'    => esc_html__( 'NFTs Enable Verifiy', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ), 
     
        array(
                'id'       => 'wooc_nft_sku',
                'type'     => 'switch',
                'title'    => esc_html__( 'NFT SKU', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => false,
            ),   
            array(
                'id'       => 'wooc_nft_cats',
                'type'     => 'switch',
                'title'    => esc_html__( 'NFT Categories', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => true,
            ),
             array(
                'id'       => 'wooc_nft_tags',
                'type'     => 'switch',
                'title'    => esc_html__( 'NFT Tags', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => false,
            ),    
             array(
                'id'       => 'wooc_popup_zoom',
                'type'     => 'switch',
                'title'    => esc_html__( 'Image Popup zoom', 'etrade' ),
                'on'       => esc_html__( 'Show', 'etrade' ),
                'off'      => esc_html__( 'Hide', 'etrade' ),
                'default'  => false,
            ),
            array(
                'id' => 'single_aska_question',
                'type' => 'textarea',
                'title' => esc_html__('Ask Product Question', 'etrade'),
                'default' => '', 

            ),

                array(
                'id' => 'single_delivery_return',
                'type' => 'editor',
                'title' => esc_html__('Ask Product Question', 'etrade'),
                'default' => '', 
                'args'   => array(
                    'teeny'            => true,
                    'textarea_rows'    => 10
                )

            ),
 
        )
    )
);

}



/**
 * Blog Panel
 */

Redux::setSection($opt_name, array(
    'title' => esc_html__('Blog', 'etrade'),
    'id' => 'axil_blog',
    'icon' => 'el el-file-edit',
));

/**
 * Blog Options
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Archive', 'etrade'),
    'id' => 'axil_blog_genaral',
    'icon' => 'el el-edit',
    'subsection' => true,
    'fields' => array(
        array(
            'id' => 'axil_enable_blog_title',
            'type' => 'button_set',
            'title' => esc_html__('Title', 'etrade'),
            'subtitle' => esc_html__('Enable or Disable the blog page title.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Enable', 'etrade'),
                'no' => esc_html__('Disable', 'etrade'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'axil_blog_text',
            'type' => 'text',
            'title' => esc_html__('Default Title', 'etrade'),
            'subtitle' => esc_html__('Controls the Default title of the page which is displayed on the page title are on the blog page.', 'etrade'),
            'default' => esc_html__('Blogs', 'etrade'),
            'required' => array('axil_enable_blog_title', 'equals', 'yes'),
        ),
        array(
            'id' => 'axil_select_blog_banner_img',
            'title' => esc_html__('Banner Image', 'etrade'),           
            'type' => 'media',            
            'default' => array(
               'url' => AXIL_IMG_URL . 'product-45.png'
            ),
            'required' => array('axil_enable_blog_title', 'equals', 'yes' ),
        ),

        array(
            'id' => 'axil_blog_sidebar',
            'type' => 'image_select',
            'title' => esc_html__('Select Blog Sidebar', 'etrade'),
            'subtitle' => esc_html__('Choose your favorite blog layout', 'etrade'),
            'options' => array(
                'left' => array(
                    'alt' => esc_html__('Left Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/left-sidebar.png',
                    'title' => esc_html__('Left Sidebar', 'etrade'),
                ),
                'right' => array(
                    'alt' => esc_html__('Right Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/right-sidebar.png',
                    'title' => esc_html__('Right Sidebar', 'etrade'),
                ),
                'no' => array(
                    'alt' => esc_html__('No Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/no-sidebar.png',
                    'title' => esc_html__('No Sidebar', 'etrade'),
                ),
            ),
            'default' => 'right',
        ), 

        array(
            'id' => 'axil_blog_layout',
            'type' => 'image_select',
            'title' => esc_html__('Select Blog Layout', 'etrade'),
            'subtitle' => esc_html__('Choose your favorite blog layout', 'etrade'),
            'options' => array(
                'list' => array(
                    'alt' => esc_html__('Left Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/blog-grid.png',
                    'title' => esc_html__('List Layout', 'etrade'),
                ),
                'grid' => array(
                    'alt' => esc_html__('Right Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/blog-list.png',
                    'title' => esc_html__('Grid Layout', 'etrade'),
                ),
                
            ),
            'default' => 'list',
        ),
         array(
            'id' => 'post_content_limit',
            'type' => 'text',
            'title' => esc_html__('Post Content Limit', 'etrade'),
            'default' => '30 ',
          
        ),    
         array(
            'id' => 'read_more_btn_txt',
            'type' => 'text',
            'title' => esc_html__('Read More Button Text', 'etrade'),
            'default' => esc_html__('Read More', 'etrade'), 
          
        ),

        array(
            'id' => 'axil_show_post_categories_meta',
            'type' => 'button_set',
            'title' => esc_html__('Categories', 'etrade'),
            'subtitle' => esc_html__('Show or hide the author of blog post.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Show', 'etrade'),
                'no' => esc_html__('Hide', 'etrade'),
            ),
            'default' => 'yes',
        ),  
        array(
            'id' => 'axil_show_post_author_meta',
            'type' => 'button_set',
            'title' => esc_html__('Author', 'etrade'),
            'subtitle' => esc_html__('Show or hide the author of blog post.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Show', 'etrade'),
                'no' => esc_html__('Hide', 'etrade'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'axil_show_post_publish_date_meta',
            'type' => 'button_set',
            'title' => esc_html__('Publish Date', 'etrade'),
            'subtitle' => esc_html__('Show or hide the publish date of blog post.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Show', 'etrade'),
                'no' => esc_html__('Hide', 'etrade'),
            ),
            'default' => 'yes',
        ),   
        array(
            'id' => 'axil_show_post_view',
            'type' => 'button_set',
            'title' => esc_html__('Post View', 'etrade'),
            'subtitle' => esc_html__('Show or hide the post view of blog post.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Show', 'etrade'),
                'no' => esc_html__('Hide', 'etrade'),
            ),
            'default' => 'no',
        ), 

    )
));
 
/**
 * Single Post
 */

Redux::setSection($opt_name, array(
    'title' => esc_html__('Single', 'etrade'),
    'id' => 'axil_blog_details_id',
    'icon' => 'el el-website',
    'subsection' => true,
    'fields' => array(
        array(
            'id' => 'axil_single_pos',
            'type' => 'image_select',
            'title' => esc_html__('Blog Details Sidebar', 'etrade'),
            'subtitle' => esc_html__('Choose your favorite layout', 'etrade'),
            'options' => array(
                'left' => array(
                    'alt' => esc_html__('Left Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/left-sidebar.png',
                    'title' => esc_html__('Left Sidebar', 'etrade'),
                ),
                'right' => array(
                    'alt' => esc_html__('Right Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/right-sidebar.png',
                    'title' => esc_html__('Right Sidebar', 'etrade'),
                ),
                'full' => array(
                    'alt' => esc_html__('No Sidebar', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/layout/no-sidebar.png',
                    'title' => esc_html__('No Sidebar', 'etrade'),
                ),
            ),
            'default' => 'full',
        ),
        
    

        array(
            'id' => 'axil_show_blog_details_author_meta',
            'type' => 'button_set',
            'title' => esc_html__('Author', 'etrade'),
            'subtitle' => esc_html__('Show or hide the author of blog post.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Show', 'etrade'),
                'no' => esc_html__('Hide', 'etrade'),
            ),
            'default' => 'yes',
        ),
        array(
            'id' => 'axil_show_blog_details_publish_date_meta',
            'type' => 'button_set',
            'title' => esc_html__('Publish Date', 'etrade'),
            'subtitle' => esc_html__('Show or hide the publish date of blog post.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Show', 'etrade'),
                'no' => esc_html__('Hide', 'etrade'),
            ),
            'default' => 'yes',
        ),

   

        array(
            'id' => 'axil_show_blog_details_tags_meta',
            'type' => 'button_set',
            'title' => esc_html__('Tags', 'etrade'),
            'subtitle' => esc_html__('Show or hide the tags of blog post.', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Show', 'etrade'),
                'no' => esc_html__('Hide', 'etrade'),
            ),
            'default' => 'yes',
        ),
       
        array(
            'id' => 'axil_blog_details_social_share',
            'type' => 'switch',
            'title' => esc_html__('Social Link', 'etrade'),
            'subtitle' => esc_html__('Show or hide the social share of single post.', 'etrade'),
            'default' => false,
        ), 

        array(
            'id' => 'axil_blog_details_show_author_info',
            'type' => 'switch',
            'title' => esc_html__('Show Author Info', 'etrade'),
            'subtitle' => esc_html__('Show or hide the Author Info box of single post.', 'etrade'),
            'default' => false,
        ),

        // Show Related post
        array(
            'id'      => 'show_related_post',
            'type'    => 'switch',
            'title'   => esc_html__( 'Show Related post', 'etrade' ),
            'on'      => esc_html__( 'On', 'etrade' ),
            'off'     => esc_html__( 'Off', 'etrade' ),
            'default' => false,
        ),
        array(
            'id'       => 'related_post_before_title',
            'type'     => 'text',
            'title'    => esc_html__( 'Related Post Before Title', 'etrade' ),
            'default'  => esc_html__( 'Hot News', 'etrade' ),
            'required' => array('show_related_post', 'equals', true),
        ),  
        array(
            'id'       => 'related_post_area_title',
            'type'     => 'text',
            'title'    => esc_html__( 'Related Post Area Title', 'etrade' ),
            'default'  => esc_html__( 'Related Posts', 'etrade' ),
            'required' => array('show_related_post', 'equals', true),
        ),

        array(
            'id'       => 'show_related_post_number',
            'type'     => 'text',
            'title'    => esc_html__( 'Show Related Post Number', 'etrade' ),
            'required' => array( 'show_related_post', 'equals', true ),
            'default'  =>  '4',
        ),

        array(
            'id'       => 'related_post_query',
            'type'     => 'radio',
            'title'    => esc_html__('Query Type', 'etrade'),
            'subtitle' => esc_html__('Post Query', 'etrade'),
            'required' => array( 'show_related_post', 'equals', true ),
            'options'  => array(
                'cat'       => esc_html__( 'Posts in the same Categories', 'etrade' ),
                'tag'       => esc_html__( 'Posts in the same Tags', 'etrade' ),
                'author'    => esc_html__( 'Posts by the same Author', 'etrade' ),
            ),
            'default'   => 'cat'
        ),

        array(
            'id'       => 'related_post_sort',
            'type'     => 'radio',
            'title'    => esc_html__('Sort Order', 'etrade'),
            'subtitle' => esc_html__('Display post Order', 'etrade'),
            'required' => array( 'show_related_post', 'equals', true ),
            'options'  => array(
                'recent'    => esc_html__( 'Recent Posts', 'etrade' ),
                'rand'      => esc_html__( 'Random Posts', 'etrade' ),
                'modified'  => esc_html__( 'Last Modified Posts', 'etrade' ),
                'popular'   => esc_html__( 'Most Commented posts', 'etrade' ),
            ),
            'default'   => 'recent'
        ),
        array(
            'id'       => 'related_title_limit',
            'type'     => 'text',
            'required' => array( 'show_related_post', 'equals', true ),
            'title'    => esc_html__( 'Related Post Title Length', 'etrade' ),
            'default'  => '15',
        ),
        
       
    )
));

/**
 * footer Top
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Footer Top', 'etrade'),
    'id' => 'axil_footer_top_section',
    'icon' => 'el el-credit-card',
    'fields' => array( 

        array(
            'id' => 'axil_footer_top_enable',
            'type' => 'button_set',
            'title' => esc_html__('Footer Top', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the Default footer top area. Also Enable or disable page wise ', 'etrade'),
            'options' => array(
                'yes' => esc_html__('Show', 'etrade'),
                'no' => esc_html__('Hide', 'etrade'),
            ),
            'default' => 'no',
        ), 
        
        array(
            'id' => 'axil_ft_title',
            'type' => 'text',
            'title' => esc_html__('Title', 'etrade'),
            'default' => esc_html__('New Product Notifications', 'etrade'),
              'required' => array( 'axil_footer_top_enable', 'equals', 'yes' ),
            
        ), 
        array(
            'id' => 'axil_ft_sub_title',
            'type' => 'text',
            'title' => esc_html__('Sub Title', 'etrade'),
            'default' => esc_html__('Our Newsletter', 'etrade'),
              'required' => array( 'axil_footer_top_enable', 'equals', 'yes' ),
            
        ),
        array(
            'id' => 'axil_ft_shortcode',
            'type' => 'textarea',
            'title' => esc_html__('Add Shortcode Here', 'etrade'),
            'default' => '',
              'required' => array( 'axil_footer_top_enable', 'equals', 'yes' ),
            
        ),
        
        array(
            'id'       => 'axil_ft_area_background',
            'type'     => 'background',
            'title'    => esc_html__('Background', 'etrade'),
            'subtitle' => esc_html__('Footer Top Background', 'etrade'),
              'required' => array( 'axil_footer_top_enable', 'equals', 'yes' ),
            
        ),     

    )
));


/**
 * Service policies section
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Service Policies', 'etrade'),
    'id' => 'axil_service_policies_section',
    'icon' => 'el el-edit',
    'fields' => array(
        array(
            'id' => 'axil_service_policy_section_enable',
            'type' => 'switch',
            'title' => esc_html__('Service Policies', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the Default Service Policies area. Also Enable or disable page wise', 'etrade'),
            'default'  => false
        ), 
        array(
            'id' => 'service_policies_title_text1',
            'type' => 'text',
            'title' => esc_html__('Service Policy 1', 'etrade'),
            'default' => esc_html__('Fast & Secure Delivery', 'etrade'),
            'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),
        array(
            'id' => 'service_policies_subtitle_text1',
            'type' => 'text',
            'title' => esc_html__('Sub Title', 'etrade'),
            'default' => esc_html__('Tell about your service.', 'etrade'),
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),
        array(
            'id'       => 'service_policies_page_image1',
            'type'     => 'media',
            'title'    => esc_html__( 'Service Policy 1 Image', 'etrade' ),
            'default' => array('url' => get_template_directory_uri() . '/assets/images/optionframework/footer/service1.png'), 
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),

        array(
            'id' => 'service_policies_title_text2',
            'type' => 'text',
            'title' => esc_html__('Service Policy 2', 'etrade'),
            'default' => esc_html__('Money Back Guarantee', 'etrade'),
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),
        array(
            'id' => 'service_policies_subtitle_text2',
            'type' => 'text',
            'title' => esc_html__('Sub Title', 'etrade'),
            'default' => esc_html__('Within 10 days.', 'etrade'),
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),
        array(
            'id'       => 'service_policies_page_image2',
            'type'     => 'media',
            'title'    => esc_html__( 'Service Policy 2 Image', 'etrade' ),
            'default' => array('url' => get_template_directory_uri() . '/assets/images/optionframework/footer/service2.png'), 
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),

        array(
            'id' => 'service_policies_title_text3',
            'type' => 'text',
            'title' => esc_html__('Service Policy 3', 'etrade'),
            'default' => esc_html__('24 Hour Return Policy', 'etrade'),
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),
        array(
            'id' => 'service_policies_subtitle_text3',
            'type' => 'text',
            'title' => esc_html__('Sub Title', 'etrade'),
            'default' => esc_html__('No question ask.', 'etrade'),
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),
        array(
            'id'       => 'service_policies_page_image3',
            'type'     => 'media',
            'title'    => esc_html__( 'Service Policy 3 Image', 'etrade' ),
          'default' => array('url' => get_template_directory_uri() . '/assets/images/optionframework/footer/service3.png'), 
            'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),

        array(
            'id' => 'service_policies_title_text4',
            'type' => 'text',
            'title' => esc_html__('Service Policy 4', 'etrade'),
            'default' => esc_html__('Pro Quality Support', 'etrade'),
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),
        array(
            'id' => 'service_policies_subtitle_text4',
            'type' => 'text',
            'title' => esc_html__('Sub Title', 'etrade'),
            'default' => esc_html__('24/7 Live support.', 'etrade'),
              'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),

        array(
            'id'       => 'service_policies_page_image4',
            'type'     => 'media',
            'title'    => esc_html__( 'Service Policy 4 Image', 'etrade' ),
           'default' => array('url' => get_template_directory_uri() . '/assets/images/optionframework/footer/service4.png'), 
             'required' => array('axil_service_policy_section_enable', 'equals', true),
           
        ),
    )
));


/**
 * Footer section
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('Footer', 'etrade'),
    'id' => 'axil_footer_section',
    'icon' => 'el el-photo',
    'fields' => array(
        array(
            'id' => 'axil_footer_enable',
            'type' => 'switch',
            'title' => esc_html__('Footer', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the footer area.', 'etrade'),
            'default' => true,
        ),       
 
        array(
            'id' => 'axil_select_footer_template',
            'type' => 'image_select',
            'title' => esc_html__('Select Footer Layout', 'etrade'),
            'options' => array(
                '1' => array(
                    'alt' => esc_html__('Header Layout 1', 'etrade'),
                    'title' => esc_html__('Header Layout 1', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/1.png',
                ),
                '2' => array(
                    'alt' => esc_html__('Header Layout 2', 'etrade'),
                    'title' => esc_html__('Header Layout 2', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/2.png',
                ),
                '3' => array(
                    'alt' => esc_html__('Header Layout 3', 'etrade'),
                    'title' => esc_html__('Header Layout 3', 'etrade'),
                    'img' => get_template_directory_uri() . '/assets/images/optionframework/footer/3.png',
                ), 
                 
             
            ),
            'default' => '1',
            'required' => array('axil_enable_header', 'equals', true),
        ),
 

        array(
            'id' => 'axil_footer_footerbottom',
            'type' => 'switch',
            'title' => esc_html__('Footer Bottom Menu', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the footer Menu.', 'etrade'),
            'default' => false,
            'required' => array('axil_footer_enable', 'equals', true),            
        ),
         array(
            'id' => 'axil_copyright_contact',
            'type' => 'editor',
            'title' => esc_html__('Copyright Content', 'etrade'),
            'args' => array(
                'teeny' => true,
                'textarea_rows' => 5,
            ),
            'default' => '© 2023. All rights reserved by <a href="#" target="_blank" rel="noopener">Your Company.</a>',
            'required' => array('axil_footer_enable', 'equals', true),
        ),
       array(
            'id'       => 'payment_icons',
            'type'     => 'switch',
            'title'    => esc_html__( 'Display Payment Title', 'etrade' ),
            'on'       => esc_html__( 'Enabled', 'etrade' ),
            'off'      => esc_html__( 'Disabled', 'etrade' ),
            'default'  => false,     
            'required' => array('axil_footer_enable', 'equals', true),           
        ),
       array(
            'id' => 'display_Payment_title',
            'type' => 'text',
            'title' => esc_html__('Title', 'etrade'),
            'default' => 'Accept For',
            'required' => array('payment_icons', 'equals', true),
        ), 
        array(
            'id'       => 'payment_img',
            'type'     => 'gallery',
            'title'    => esc_html__( 'Payment Icons Gallery', 'etrade' ),
            'required' => array( 'payment_icons', 'equals', true )
        ),



            array(
                'id' => 'axil_footer_social_enable',
                'type' => 'switch',
                'title' => esc_html__('Social Enable', 'etrade'),
                'subtitle' => esc_html__('Enable or disable the footer top area.', 'etrade'),
                'default' => false,
            ),     
             array(
                'id' => 'social_title',
                'type' => 'text',
                'title' => esc_html__('Social Title', 'etrade'),
                'default' => esc_html__('Follow us', 'etrade'),
                'required' => array('axil_footer_social_enable', 'equals', true),
            ),    

            array(
                'id' => 'axil_social_icons',
                'type' => 'sortable',
                'title' => esc_html__('Social Icons', 'etrade'),
                'subtitle' => esc_html__('Enter social links to show the icons', 'etrade'),
                'mode' => 'text',
                'label' => true,
                'required' => array('axil_footer_social_enable', 'equals', true),
                'options' => array(
                    'facebook-f' => '',
                    'twitter' => '',
                    'pinterest-p' => '',
                    'linkedin-in' => '',
                    'instagram' => '',
                    'vimeo-v' => '',
                    'dribbble' => '',
                    'behance' => '',
                    'youtube' => '',
                    'tiktok' => '',
                     
                    'snapchat' => '',
                ),
                'default' => array(
                    'facebook-f' => 'https://www.facebook.com/',
                    'twitter' => 'https://twitter.com/',
                    'pinterest-p' => 'https://pinterest.com/',
                    'linkedin-in' => 'https://linkedin.com/',
                    'instagram' => 'https://instagram.com/',
                    'vimeo-v' => 'https://vimeo.com/',
                    'dribbble' => 'https://dribbble.com/',
                    'behance' => 'https://behance.com/',
                    'youtube' => '',
                    'tiktok' => '',
                    
                    'snapchat' => '',
                ),
            )
    )
));


Redux::setSection($opt_name,
    array(
        'title' => esc_html__('Under Construction', 'etrade'),
        'id' => 'under_construction_sttings_section',
        'heading' => esc_html__('Under Construction', 'etrade'),
        'icon' => 'el el-error-alt',
         
        'fields' => array(
            array(
                'id'                    => 'under_construction_mode_enable',
                'type'                  => 'button_set',
                'title'                 => esc_html__('Under Construction / Coming Soon Mode', 'etrade'),
                'subtitle'              => esc_html__('If enable, the frontend shows maintenance / coming soon mode page only.', 'etrade'),
                'options'               => array(
                    'on'                => 'Enable',
                    'off'               => 'Disable'    
                ),
                'default'               => 'off'
            ),
            array(
                'id' => 'under_construction_title_text',
                'type' => 'text',
                'title' => esc_html__('Page Title', 'etrade'),
                'default' => esc_html__('Our new website is on its way', 'etrade'),
                'required'              => array('under_construction_mode_enable','equals', 'on'),
            ),
            array(
                'id' => 'under_construction_subtitle_text',
                'type' => 'text',
                'title' => esc_html__('Sub Title', 'etrade'),
                'default' => esc_html__('Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.', 'etrade'),
                'required'              => array('under_construction_mode_enable','equals', 'on'),
            ),

            array(
                'id'       => 'under_construction_page_image',
                'type'     => 'media',
                'title'    => esc_html__( 'Under Construction Image', 'etrade' ),
                 'default' => array( 
                   'url' => AXIL_IMG_URL . 'bg-image-13.jpg'
            ), 
            'required'              => array('under_construction_mode_enable','equals', 'on'),
            ),


        array(
                'id'                    => 'under_construction_countdown_enable',
                'type'                  => 'button_set',
                'title'                 => esc_html__('Under Construction / Coming Soon Mode', 'etrade'),
                'subtitle'              => esc_html__('If enable, the frontend shows maintenance / coming soon mode page only.', 'etrade'),
                'options'               => array(
                    'on'                => 'Enable',
                    'off'               => 'Disable'    
                ),
                 'required'              => array('under_construction_mode_enable','equals', 'on'),
                'default'               => 'off'
            ),

             array(
                'id' => 'under_cd_days',
                'type' => 'text',            
                'title' => esc_html__('Countdown Days label', 'etrade'),            
                'default' => 'Days',
                'required'              => array('under_construction_countdown_enable','equals', 'on'),

            ),
            array(
                'id' => 'under_cd_hour',
                'type' => 'text',            
                'title' => esc_html__('Countdown Hour label', 'etrade'),    
                
                'default' => 'Hrs',
                'required'    => array('under_construction_countdown_enable','equals', 'on'),        
            ),
            array(
                'id' => 'under_cd_minute',
                'type' => 'text',            
                'title' => esc_html__('Countdown Minute label', 'etrade'),            
                'default' => 'Min',
                'required'              => array('under_construction_countdown_enable','equals', 'on'),
            ),
            array(
                'id' => 'under_cd_second',
                'type' => 'text',            
                'title' => esc_html__('Countdown Second label', 'etrade'),            
                'default' => 'Second',
                'required'  => array('under_construction_countdown_enable','equals', 'on'),

            ),

             array(
                'id'          => 'under_construction_date',
                'type'        => 'date',
                'title'       => esc_html__('Date Option', 'etrade'), 
                'subtitle'    => esc_html__('No validation can be done on this field type', 'etrade'),
                'desc'        => esc_html__('This is the description field, again good for additional info.', 'etrade'),
                'required'              => array('under_construction_countdown_enable','equals', 'on'),
                'placeholder' => 'Click to enter a date'

            ),
            
        )
    ) 
);


/**
 * 404 error page
 */
Redux::setSection($opt_name, array(
    'title' => esc_html__('404 Page', 'etrade'),
    'id' => 'axil_error_page',
    'icon' => 'el el-eye-close',
    'fields' => array(
        array(
            'id' => 'axil_404_before_title',
            'type' => 'text',
            'title' => esc_html__('Before Title', 'etrade'),
            'subtitle' => esc_html__('Add a text before title.', 'etrade'), 
            'default' => esc_html__('Oops! Somthings missing.', 'etrade'),
        ),

        array(
            'id' => 'axil_404_title',
            'type' => 'text',
            'title' => esc_html__('Title', 'etrade'),
            'subtitle' => esc_html__('Add your Default title.', 'etrade'),
            'value' => 'Page not Found',
            'default' => esc_html__('Page not Found', 'etrade'),
        ),

        array(
            'id' => 'axil_404_subtitle',
            'type' => 'text',
            'title' => esc_html__('Sub Title', 'etrade'),
            'subtitle' => esc_html__('Add your custom subtitle.', 'etrade'),
            'default' => esc_html__('It seems like we dont find what you searched. The page you were looking for doesn\'t exist, isn\'t available loading incorrectly.', 'etrade'),
        ),

        array(
            'id' => 'axil_enable_go_back_btn',
            'type' => 'button_set',
            'title' => esc_html__('Button', 'etrade'),
            'subtitle' => esc_html__('Enable or disable the go to home page button.', 'etrade'),
            'options' => array(
                'yes' => 'Enable',
                'no' => 'Disable'
            ),
            'default' => 'yes'
        ),
        array(
            'id' => 'axil_button_text',
            'type' => 'text',
            'title' => esc_html__('Button Text', 'etrade'),
            'subtitle' => esc_html__('Set the custom text of go to home page button.', 'etrade'),
            'default' => esc_html__('Back to Home', 'etrade'),
            'required' => array('axil_enable_go_back_btn', 'equals', 'yes'),
        ),
        array(
            'id' => 'axil_404_image',
            'title' => esc_html__('404 Image', 'etrade'),           
            'type' => 'media',
            'default' => array( 
                'url' => AXIL_IMG_URL . '404.png'
            ),
            
        )
    )
));


