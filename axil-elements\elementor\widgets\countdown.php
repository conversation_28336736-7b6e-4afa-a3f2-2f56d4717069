<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;
use Elementor\Icons_Manager;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Wooc_Product_Countdown extends Widget_Base {

 public function get_name() {
        return 'wooc-countdown-1';
    }    
    public function get_title() {
        return __( 'Product Countdown', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-countdown';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
 public function axil_get_img($img)
     {
         $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
         return $img;
    }
    protected function register_controls() {   

        $this->start_controls_section(
            'countdown_info',
            [
                'label' => __( 'Countdown Info', 'etrade-elements' ),
            ]
        );         
        
        $this->add_control(
            'subtitle',
            [
                'label' => esc_html__( 'Sub / Before Title', 'etrade-elements' ),
                'type' => Controls_Manager::TEXT,
                'default' => esc_html__( 'Don’t Miss!!', 'etrade-elements' ),
                'placeholder' => esc_html__( 'Sub title', 'etrade-elements' ),
            ]
        ); 

  
	    $this->add_control(
	        'beforetitlestyle',
	        [
	            'label' => esc_html__( 'Before Color', 'etrade-elements' ),
	            'type' => Controls_Manager::SELECT,
	            'default' => 'primary',
	            'options' => [
	                'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
	                'secondary'   => esc_html__( 'Secondary', 'etrade-elements' ),                                          
	                'primary2'   => esc_html__( 'Primary 2', 'etrade-elements' ),                                          
	                                                          
	            ],
	        ] 
	    );   
	    
		$this->add_control(
            'icon',
            [
                'label' => __( 'Icons', 'etrade-elements' ),
                'type' => Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-coffee',
                    'library' => 'solid',
                ],
                        
            ]
        );

		$this->add_control(
			'title',
			[
				'label' => esc_html__( 'Title', 'etrade-elements' ),
				'type' => Controls_Manager::TEXTAREA,
				'default' => esc_html__( 'Enhance Your Music Experience', 'etrade-elements' ),
				'placeholder' => esc_html__( 'Title Here', 'etrade-elements' ), 
                'separator'     => 'before',
            ]
        );  

		$this->add_control(
			'date',
			[
				'label' => __( 'Select Date', 'etrade-elements' ),
				'type' => Controls_Manager::DATE_TIME,
			]
		);
		$this->add_control(
		    'buttontext',
		    [
		        'label'   => esc_html__( 'Button Text', 'etrade-elements' ),
		        'type'    => Controls_Manager::TEXT,
		        'default' => esc_html__( 'Check it Out!', 'etrade-elements' ),

		    ]
		);
		$this->add_control(
		        'url',
		        [
		            'label'   => esc_html__( 'URL', 'etrade-elements' ),
		            'type'    => Controls_Manager::URL,
		            'placeholder' => 'https://your-link.com',
		        ]
		);    


		$this->add_control(
		    'image',
		    [
		        'label' => esc_html__('Image','etrade-elements'),
		        'type'=>Controls_Manager::MEDIA,
		        'default' => [
		            'url' =>  $this->axil_get_img( 'poster-03.png' ),
		        ],
		        'dynamic' => [
		            'active' => true,
		        ],
		            
		    ]
		);
		  $this->add_group_control(
                Group_Control_Image_Size::get_type(),
                [
                    'name' => 'image_size',
                    'default' => 'full',
                    'separator' => 'none',
                       
                ]
            );
        $this->add_control(
            'animation_on',
            [
                'label' => esc_html__( 'Animation', 'etrade-elements' ),
                'type' => Controls_Manager::SWITCHER,
                'label_on'    => esc_html__( 'On', 'etrade-elements' ),
                'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
                'default'     => 'no',
               
            ]
        );   

       $this->end_controls_section();   


      
     $this->start_controls_section(
            'subtitle_style_section',
            [
                'label' => esc_html__( 'Before Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
 

 
          $this->add_control(
            'subtitle_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
               
                'selectors' => array(
                    '{{WRAPPER}} .title-highlighter' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .title-highlighter i' => 'background-color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'subtitle_font_size',
                'label' => esc_html__( 'Typography', 'etrade-elements' ),                
                
                'selector' => '{{WRAPPER}} .title-highlighter',
            ]
        );
       
        $this->add_responsive_control(
            'subtitle_margin',
            [
                'label' => esc_html__( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                
                'selectors' => [
                    '{{WRAPPER}} .title-highlighter' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    
                ],
            ]
        );
        
    $this->end_controls_section();

     $this->start_controls_section(
            'title_style_section',
            [
                'label' => esc_html__( 'Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
   

 
          $this->add_control(
            'title_color',
            [
                'label' => esc_html__( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
               
                'selectors' => array(
                    '{{WRAPPER}} .poster-countdown-wrap .section-title-wrapper .title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_font_size',
                'label' => esc_html__( 'Typography', 'etrade-elements' ),                
                
                'selector' => '{{WRAPPER}} .poster-countdown-wrap .section-title-wrapper .title',
            ]
        );
       
        $this->add_responsive_control(
            'title_margin',
            [
                'label' => esc_html__( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                
                'selectors' => [
                    '{{WRAPPER}} .poster-countdown-wrap .section-title-wrapper .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    
                ],
            ]
        );
        
    $this->end_controls_section();


    $this->start_controls_section(
        'counter_style_section',
        [
            'label' => esc_html__( 'Counter', 'etrade-elements' ),
            'tab' => Controls_Manager::TAB_STYLE,                
        ]
    );


      $this->add_control(
        'counter_color',
        [
            'label' => esc_html__( 'Text Color', 'etrade-elements' ),
            'type' => Controls_Manager::COLOR,  
            'default' => '',
           
            'selectors' => array(
                '{{WRAPPER}} .poster-countdown-content .poster-countdown .countdown-number, {{WRAPPER}} .poster-countdown-content .poster-countdown .countdown-unit' => 'color: {{VALUE}}',
                
                
            ),
        ]
    );
    
   
    $this->add_responsive_control(
        'counter_margin',
        [
            'label' => esc_html__( 'Margin', 'etrade-elements' ),
            'type' => Controls_Manager::DIMENSIONS,
            'size_units' => [ 'px', '%', 'em' ],
            
            'selectors' => [
                '{{WRAPPER}} .poster-countdown-content .poster-countdown.countdown' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                
            ],
        ]
    );
    
$this->end_controls_section(); 

    $this->start_controls_section(
        'button_style_section',
        [
            'label' => esc_html__( 'Button', 'etrade-elements' ),
            'tab' => Controls_Manager::TAB_STYLE,                
        ]
    );


      $this->add_control(
        'button_color',
        [
            'label' => esc_html__( 'Text Color', 'etrade-elements' ),
            'type' => Controls_Manager::COLOR,  
            'default' => '',
           
            'selectors' => array(
                '{{WRAPPER}} .poster-countdown-content a.axil-btn' => 'color: {{VALUE}}',
                
                
            ),
        ]
    );
    $this->add_control(
        'button_bg_color',
        [
            'label' => esc_html__( 'Background Color', 'etrade-elements' ),
            'type' => Controls_Manager::COLOR,  
            'default' => '',
           
            'selectors' => array(
                  
                '{{WRAPPER}} .poster-countdown-content a.axil-btn:before' => 'background: {{VALUE}}',
                
            ),
        ]
    );
     $this->add_group_control(
        Group_Control_Typography::get_type(),
        [
            'name' => 'button_font_size',
            'label' => esc_html__( 'Typography', 'etrade-elements' ),                
            
            'selector' => '{{WRAPPER}} .poster-countdown-content a.axil-btn',
        ]
    );
   
    $this->add_responsive_control(
        'button_margin',
        [
            'label' => esc_html__( 'Margin', 'etrade-elements' ),
            'type' => Controls_Manager::DIMENSIONS,
            'size_units' => [ 'px', '%', 'em' ],
            
            'selectors' => [
                '{{WRAPPER}} .poster-countdown-content a.axil-btn' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                
            ],
        ]
    );
    
$this->end_controls_section(); 
    }

	private function wooc_load_scripts(){
		wp_enqueue_script( 'jquery-countdown' );
	}
	protected function render() {
		$settings = $this->get_settings();
		$this->wooc_load_scripts();		
		$attr = '';
		$btn = '';
		$img    = wp_get_attachment_image( $settings['image']['id'], 'full' );

		if ( !empty( $settings['url']['url'] ) ) {
			$attr  = 'href="' . $settings['url']['url'] . '"';
			$attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
			$attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
			$title = '<a ' . $attr . '>' . $settings['title'] . '</a>';
		}
		if ( !empty( $settings['buttontext'] ) ) {
			$btn = '<a class="axil-btn btn-bg-primary" ' . $attr . '>' . $settings['buttontext'] . '</a>';
		}
        $allowed_tags = wp_kses_allowed_html( 'post' );
		?>
		<div class="axil-poster-countdown axilcoutdown">
		        <div class="poster-countdown-wrap bg-lighter">
		            <div class="row">
		                <div class="col-lg-5">
		                    <div class="poster-countdown-content">
		                        <div class="section-title-wrapper">
		                             <?php if ( $settings['subtitle'] ): ?>
		                                <span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?>"> <?php Icons_Manager::render_icon( $settings['icon'] ); ?><?php echo esc_html( $settings['subtitle'] );?></span>
		                            <?php endif; ?>
		                            <?php if ( $settings['title'] ): ?>
		                            	 <h2 class="title"><?php echo wp_kses( $settings['title'] , $allowed_tags ); ?></h2> 
		                               <?php endif; ?>                       
		                        </div>                             
		                        <?php if ( $settings['date'] ): ?>
		                            <div class="poster-countdown countdown mb--30" data-time="<?php echo esc_attr( $settings['date'] ); ?>"></div>
		                        <?php endif; ?> 
                                    <?php echo wp_kses( $btn , $allowed_tags ); ?> 
		                    </div>
		                </div>
		                <div class="col-lg-7">
						 <?php if ( $settings['image']['url'] ): ?>
		                    <div class="poster-countdown-thumbnail">  
		                         <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' ); ?>  

		                        <?php if( $settings['animation_on']  == 'yes' ): ?>
			                        <div class="music-singnal">
			                            <div class="item-circle circle-1"></div>
			                            <div class="item-circle circle-2"></div>
			                            <div class="item-circle circle-3"></div>
			                            <div class="item-circle circle-4"></div>
			                            <div class="item-circle circle-5"></div>
			                        </div>
								<?php endif; ?>
		                    </div>
 						<?php endif; ?> 
		                </div>
		            </div>
		        </div>
		  
		</div>
	<?php	}

	}