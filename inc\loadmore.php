<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

class LoadMore {

    protected static $instance = null;

    private function __construct() {
        add_action( 'wp_ajax_axiltheme_loadmore', array( $this, 'loadmore' ) );
        add_action( 'wp_ajax_nopriv_axiltheme_loadmore', array( $this, 'loadmore' ) );
    }

    public static function instance() {
        if ( null == self::$instance ) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function init( $type = 'loadmore' ) {
        $this->axil_view_data_html();

        if ( $type == 'loadmore' ) {
            $this->axil_view_button_html();
        } else {
            $this->axil_spinner_button_html();
        }
    }

    public function loadmore() {
        check_ajax_referer( 'etrade_loadmore_nonce', 'nonce' );

        $args = json_decode( stripslashes( $_POST['query'] ), true );
        $args['paged'] = intval( $_POST['paged'] ) + 1;
        $view = 'grid';
        $_REQUEST['ajax_product_loadmore'] = 1;

        $query = new \WP_Query( $args );

        if ( $query->have_posts() ):
            while ( $query->have_posts() ): $query->the_post();
                Helper::get_template_part( 'woocommerce/content-product', array( 'isloadmore' => true, 'view' => $view ) );
            endwhile;
        endif;

        wp_die();
    }

    private function axil_view_data_html() {
        global $wp_query;
        $paged = get_query_var( 'paged' ) ? absint( get_query_var( 'paged' ) ) : 1;
        $nonce = wp_create_nonce( 'etrade_loadmore_nonce' );
        echo '<div class="axiltheme-loadmore-data" data-query="' . esc_attr( json_encode( $wp_query->query_vars ) ) . '" data-paged="' . esc_attr( $paged ) . '" data-max="' . esc_attr( $wp_query->max_num_pages ) . '" data-nonce="' . esc_attr( $nonce ) . '"></div>';
    }

    private function axil_view_button_html() {
        global $wp_query;
        $paged = $wp_query->max_num_pages;
        if ( $paged > 1 ) {
            ?>		<div class="axiltheme-loadmore-btn-area text-center pt--30">
			<button class="axiltheme-loadmore-btn axil-btn btn-bg-lighter btn-load-more">
				<span class="axiltheme-loadmore-btn-text"><?php esc_html_e( 'Load More', 'etrade' );?></span>
				<span class="axiltheme-loadmore-btn-icon"><i class="fa fa-spinner fa-spin"></i></span>
			</button>
		</div>
		<?php
        }
    }

        private function axil_spinner_button_html() {
            ?>
    			<div class="text-center pt--30">
    				<div class="axiltheme-infscroll-icon axil-btn"><i class="fa fa-spinner fa-spin"></i></div>
    			</div>
    		<?php
    }
}

LoadMore::instance();