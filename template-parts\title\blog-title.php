<?php
/**
 * Template part for displaying header page title
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */
 
$axil_options           = Helper::axil_get_options();
$page_breadcrumb        = Helper::axil_page_breadcrumb();
$page_breadcrumb_enable = $page_breadcrumb['breadcrumbs'];
$banner_img             = $axil_options['axil_select_blog_banner_img'];
$banner_img             = "background-image:url({$banner_img['url']});";
$size                   = 'full'; 
 $pimage  = '';
if (isset($axil_options['axil_select_blog_banner_img']['id'])) {
   $pimage                 = wp_get_attachment_image( $axil_options['axil_select_blog_banner_img']['id'], $size );

} 
$allowed_tags           = wp_kses_allowed_html( 'post' ); 
?>

<?php if($axil_options['axil_enable_blog_title'] !== 'no'){ ?>
 <div class="axil-breadcrumb-area">
     <div class="container">
         <div class="row align-items-center">
             <div class="col-lg-6 col-md-8">
                 <div class="inner"> 
                     <?php
                        if ("no" !== $page_breadcrumb_enable && "0" !== $page_breadcrumb_enable) {
                            axil_breadcrumbs();
                        }  ?>  
                     <?php
                     if($axil_options['axil_enable_blog_title'] !== 'no'){
                         if ( is_archive() ): ?>
                             <h1 class="title"><?php the_archive_title(); ?></h1>
                         <?php elseif( is_search() ): ?>
                             <h1 class="title"><?php esc_html_e( 'Search results for: ', 'etrade' ) ?><?php echo get_search_query(); ?></h1>
                         <?php else: ?>
                             <h3 class="title">
                                 <?php  if ( isset( $axil_options['axil_blog_text'] ) && !empty( $axil_options['axil_blog_text'] ) ){
                                     echo esc_html( $axil_options['axil_blog_text'] );
                                 } else {
                                     echo esc_html__('Blog', 'etrade');
                                 }  ?>
                             </h3>
                         <?php endif;
                     } ?> 
                 </div>
             </div>
             <div class="col-lg-6 col-md-4">
                <?php if( $pimage ){ ?>
                     <div class="inner">
                         <div class="bradcrumb-thumb">
                            <?php echo wp_kses( $pimage, $allowed_tags );?> 
                         </div>
                     </div>
                <?php } ?>
             </div>
         </div>
     </div>
 </div>
<?php } ?>