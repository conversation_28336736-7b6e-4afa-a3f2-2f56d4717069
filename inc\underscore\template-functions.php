<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package etrade
 */

/**
 * Adds custom classes to the array of body classes.
 *
 * @param array $classes Classes for the body element.
 * @return array
 */

if ( class_exists( 'WooCommerce' ) ) {
    add_action( 'wp_enqueue_scripts', 'axil_styles_method', 15);
}

function axil_styles_method() { 
 $axil_options = Helper::axil_get_options();
      if ( $axil_options['wooc_select_attribute'] ) {
        $custom_css = ".woocommerce div.single-product-content form.cart .variations select {
            display: inline-block !important;
        }.woocommerce div.product form.cart table.variations td.value {
        display: block;
        
        }.woocommerce div.product form.cart table.variations td.value .variation-radios {
    overflow: visible;
    display: none;
}";

    }else{
         $custom_css = "";
    }
    wp_add_inline_style( 'axil-style', $custom_css );
}


if ( !function_exists( 'etrade_related_post_grid' ) ) {
    function etrade_related_post_grid() {

        // Get Value
        $axil_options = Helper::axil_get_options();
        if ( $axil_options['show_related_post'] ) {

            $post_id = get_the_id();
            $active_post = array( $post_id );
            $related_post_count = $axil_options['show_related_post_number'];
            $query_type = $axil_options['related_post_query'];
            $args = array(
                'post__not_in'           => $active_post,
                'posts_per_page'         => $related_post_count,
                'post_status'            => 'publish',
                'no_found_rows'          => true,
                'update_post_term_cache' => false,
                'ignore_sticky_posts'    => true,
            );
            if ( !empty( $axil_options['related_post_sort'] ) && isset( $axil_options['related_post_sort'] ) ) {
                $post_order = $axil_options['related_post_sort'];
                if ( $post_order == 'rand' ) {
                    $args['orderby'] = 'rand';
                } elseif ( $post_order == 'popular' ) {
                    $args['orderby'] = 'comment_count';
                } elseif ( $post_order == 'modified' ) {
                    $args['orderby'] = 'modified';
                    $args['order'] = 'ASC';
                } elseif ( $post_order == 'recent' ) {
                    $args['orderby'] = '';
                    $args['order'] = '';
                }
            }
            if ( $query_type == 'author' ) {
                $args['author'] = get_the_author_meta( 'ID' );
            } elseif ( $query_type == 'tag' ) {
                $tags_ids = array();
                $post_tags = get_the_terms( $post_id, 'post_tag' );

                if ( !empty( $post_tags ) ) {
                    foreach ( $post_tags as $individual_tag ) {
                        $tags_ids[] = $individual_tag->term_id;
                    }

                    $args['tag__in'] = $tags_ids;
                }
            } else {
                $category_ids = array();
                $categories = get_the_category( $post_id );
                foreach ( $categories as $individual_category ) {
                    $category_ids[] = $individual_category->term_id;
                }
                $args['category__in'] = $category_ids;
            }

            $related_query = new \wp_query( $args );

            if ( $related_query->have_posts() ) {?>

                <!-- Start Related Blog Area  -->
                <div class="related-blog-area bg-color-white pb--60 pb_sm--40">
                    <div class="container">
                        <div class="section-title-wrapper mb--50 mb_sm--40 pr--110">
                                <?php if ( !empty( $axil_options['related_post_before_title'] ) ): ?>
                                <span class="title-highlighter highlighter-primary mb--10">
                                <i class="fal fa-bell"></i><?php echo esc_html( $axil_options['related_post_before_title'] ); ?></span>
                                <?php endif;?>
                                <?php if ( !empty( $axil_options['related_post_area_title'] ) ): ?>
                                <h3 class="mb--25"><?php echo esc_html( $axil_options['related_post_area_title'] ); ?></h3>
                                <?php endif;?>
                        </div>

                        <div class="related-blog-activation slick-layout-wrapper--15 axil-slick-arrow  arrow-top-slide">

                        <?php
                            while ( $related_query->have_posts() ) {
                            $related_query->the_post();
                            $title = get_the_title();
                            $title = wp_trim_words( $title, $axil_options['related_title_limit'] );
                            ?>
                            <div class="slick-single-layout">
                                <div class="content-blog">
                                    <div class="inner">
                                        <div class="axil-gallery-activation axil-slick-arrow arrow-between-side">
                                         <?php if ( has_post_thumbnail() ) {?>
                                            <div class="thumbnail">
                                                  <a href="<?php the_permalink();?>">
                                                    <?php the_post_thumbnail( 'axil-blog-grid' );?>
                                                </a>
                                            </div>
                                        <?php }?>
                                        </div>
                                        <div class="content">
                                            <h5 class="title"><a href="<?php the_permalink();?>"><?php echo esc_html( $title ); ?></a></h5>
                                            <?php Helper::etrade_singlepostmeta();?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                         <?php }?>

                        </div>
                    </div>
                </div>
                <!-- End Related Blog Area  -->
            <?php }
            wp_reset_postdata();

        }
    }
}

/**
 * @param $classes
 * @return array
 */
function etrade_body_classes( $classes ) {

    $axil_options = Helper::axil_get_options();

    global $post;
    if ( isset( $post ) ) {
        $classes[] = $post->post_type . '-' . $post->post_name;
    }

    // Adds a class of no-sidebar when there is no sidebar present.
    if ( !is_active_sidebar( 'sidebar-1' ) ) {
        $classes[] = 'no-sidebar';
    }

    if ( WOOC_WOO_ACTIVED ):
        if ( is_page() ) {
            $classes[] = " woocommerce";
        }

        if ( is_product() ) {
            $classes[] = " overflow-visible";
        }

    endif;

    // Header sticky and transparent
    $header_layout = Helper::axil_header_layout();
    $header_sticky = $header_layout['header_sticky'];
    $header_style = $header_layout['header_style'];

    if ( $header_style != "3" ) {
        $classes[] = ( "no" !== $header_sticky && "0" !== $header_sticky ) ? "header-sticky-active" : "";
    }

    return $classes;
}
add_filter( 'body_class', 'etrade_body_classes' );

/**
 * @param $classes
 * @return string
 */
function etrade_admin_body_classes( $classes ) {
    global $post;
    if ( isset( $post ) ) {
        return $post->post_type . '-' . $post->post_name;
    }
}
add_filter( 'admin_body_class', 'etrade_admin_body_classes' );

/**
 * Get unique ID.
 */
function etrade_unique_id( $prefix = '' ) {
    static $id_counter = 0;
    if ( function_exists( 'wp_unique_id' ) ) {
        return wp_unique_id( $prefix );
    }
    return $prefix . (string) ++$id_counter;
}

/**
 * Add a pingback url auto-discovery header for singularly identifiable articles.
 */
function etrade_pingback_header() {
    if ( is_singular() && pings_open() ) {
        printf( '<link rel="pingback" href="%s">', esc_url( get_bloginfo( 'pingback_url' ) ) );
    }
}

add_action( 'wp_head', 'etrade_pingback_header' );

/**
 * Comment navigation
 */
function etrade_get_post_navigation() {
    if ( get_comment_pages_count() > 1 && get_option( 'page_comments' ) ):
        require get_template_directory() . '/inc/comment-nav.php';
    endif;
}

require get_template_directory() . '/inc/comment-form.php';

/**
 * Maintenance Mode
 */

add_action( 'template_include', 'axil_underconstruction_mode_enable', 999 );

function axil_underconstruction_mode_enable( $template ) {
    $axil_options = Helper::axil_get_options();

    if ( !class_exists( 'ReduxFramework' ) ) {
        return $template;
    }
    $enable = ( isset( $axil_options['under_construction_mode_enable'] ) ) ? $axil_options['under_construction_mode_enable'] : 'off';

    $enable = isset( $_GET['emm'] ) ? '1' : $enable;

    if ( is_user_logged_in() || 'off' === $enable ) {
        return $template;
    }
    $maintenance_mode = true;
    if ( !$maintenance_mode ) {
        return $template;
    }
    $new_template = locate_template( array( 'construction.php' ) );
    if ( '' != $new_template ) {
        return $new_template;
    }
    return $template;
}
function axil_highlight_results( $text ) {
    if ( is_search() ) {
        $sr = get_query_var( 's' );
        $keys = explode( " ", $sr );
        $text = preg_replace( '/(' . implode( '|', $keys ) . ')/iu', '<strong class="search-excerpt"> ' . $sr . ' </strong>', $text );
    }
    return $text;
}