(function () {
	'use strict';


	var $ = jQuery;
	var rtltrue;
		if (etradeObj.rtl == 'yes') { 
			rtltrue = true;
			
		}else{
			rtltrue = false;
		}

	function _typeof(obj) {
		"@babel/helpers - typeof";

		if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
			_typeof = function (obj) {
				return typeof obj;
			};
		} else {
			_typeof = function (obj) {
				return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
			};
		}

		return _typeof(obj);
	}

	function _defineProperty(obj, key, value) {
		if (key in obj) {
			Object.defineProperty(obj, key, {
				value: value,
				enumerable: true,
				configurable: true,
				writable: true
			});
		} else {
			obj[key] = value;
		}

		return obj;
	}

	var ThemeHelper = {
		run_closeMenuAreaLayout: function run_closeMenuAreaLayout() {
			var menuArea = $('.additional-menu-area');
			var trigger = $('.side-menu-trigger', menuArea);
			trigger.removeClass('side-menu-close').addClass('side-menu-open');

			if (menuArea.find('> .wooc-cover').length) {
				menuArea.find('> .wooc-cover').remove();
			}

			$('.sidenav').css('transform', 'translateX(-100%)');
		},
		run_closeIconMenuAreaLayout: function run_closeIconMenuAreaLayout() {
			var menuArea = $('.additional-menu-area.icon-menu');
			var trigger = $('.side-menu-trigger', menuArea);
			trigger.removeClass('side-menu-close').addClass('side-menu-open');
			menuArea.removeClass('open');

			if (menuArea.find('> .wooc-cover').length) {
				menuArea.find('> .wooc-cover').remove();
			}

			$('.icon-menu-wrp').css('transform', 'translateX(-100%)');
			$('.icon-menu-wrp').removeClass('open');
		},
		run_closeSideMenu: function run_closeSideMenu() {
			var wrapper = $('body').find('>#page'),
				$this = $('#side-menu-trigger a.menu-times');
			wrapper.removeClass('open').find('.offcanvas-mask').remove();
			$("#offcanvas-body-wrapper").attr('style', '');
			$this.prev('.menu-bar').removeClass('open');
			$this.addClass('close');
		},
		run_sticky_menu: function run_sticky_menu() {
			var wrapperHtml = $('<div class="header-main-block-sticky-wrapper"></div>');
			var wrapperClass = '.header-main-block-sticky-wrapper';
			$('.header-main-block').clone().appendTo(wrapperHtml);
			$('#page').append(wrapperHtml);
			var height = $(wrapperClass).outerHeight() + 30;
			$(wrapperClass).css('margin-top', '-' + height + 'px');
			$(window).scroll(function () {
				if ($(this).scrollTop() > 300) {
					$('body').addClass('axilthemeSticky');
				} else {
					$('body').removeClass('axilthemeSticky');
				}
			});
		},
		run_sticky_meanmenu: function run_sticky_meanmenu() {
			$(window).scroll(function () {
				if ($(this).scrollTop() > 50) {
					$('body').addClass("mean-stick");
				} else {
					$('body').removeClass("mean-stick");
				}
			});
		},
		run_isotope: function run_isotope($container, filter) {
			$container.isotope({
				filter: filter,
				layoutMode: 'fitRows',
				animationOptions: {
					duration: 750,
					easing: 'linear',
					queue: true
				}
			});
		},
		add_vertical_menu_class: function add_vertical_menu_class() {
			var screenWidth = $('body').outerWidth();

			if (screenWidth < 992) {
				$('.vertical-menu').addClass('vertical-menu-mobile');
			} else {
				$('.vertical-menu').removeClass('vertical-menu-mobile');
			}
		},
		owl_change_num_pagination: function owl_change_num_pagination($owlParent, index) {
			$owlParent.find('.owl-numbered-dots-items span').removeClass('active');
		}
	};

	var Theme = {

		onLoadClassAdd: function onLoadClassAdd() {
			$(window).on("load", function () {
				setTimeout(function () {
					$('.main-slider-style-4').addClass('animation-init');
				}, 500);
			});

		},
 		/**
         *  Single product: Featured video
         */
        singleProductFeaturedVideoInit: function singleProductFeaturedVideoInit() {
            var self = this;
            
            self.hasFeaturedVideo = false;
            self.$featuredVideoBtn = $('#axil-featured-video-link');
            
            if (self.$featuredVideoBtn.length) {
                self.hasFeaturedVideo = true;
                
                // Bind: Featured video button
                self.$featuredVideoBtn.on('click', function(e) {
                    e.preventDefault();
                    
                    // Modal settings
                    var mfpSettings = {
                        mainClass: 'axil-featured-video-popup axil-mfp-fade-in',
                        closeMarkup: '<a class="mfp-close axil-font axil-font-close2"><i class="fal fa-times"></i></a>',
                        removalDelay: 180,
                        type: 'iframe',
                        closeOnContentClick: true,
                        closeBtnInside: true
                    };
                    // Modal settings: YouTube - Disable related videos ("rel=0")
                    if (etradeObj.shopYouTubeRelated == '0') {
                        mfpSettings['iframe'] = {
                            patterns: {
                                youtube: {
                                   src: '//www.youtube.com/embed/%id%?rel=0&autoplay=1'
                                }
                            }
                        };
                    }
                    
                    // Open video modal
                    self.$featuredVideoBtn.magnificPopup(mfpSettings).magnificPopup('open');
                });
            }
        },
		/*axilMasonary: function axilMasonary() {
	 
				$('.axil-isotope-wrapper').imagesLoaded(function () {
				    
					$('.isotope-button').on('click', 'button', function () {
						var filterValue = $(this).attr('data-filter');
						$grid.isotope({
							filter: filterValue
						});
					});
				    
					 
					var $grid = $('.isotope-list').isotope({
						itemSelector: '.product',
						percentPosition: true,
						transitionDuration: '0.7s',
						layoutMode: 'fitRows',
						masonry: {
						    
							columnWidth: 1,
						}
					});
				});
		    
				$('.isotope-button button').on('click', function (event) {
					$(this).siblings('.is-checked').removeClass('is-checked');
					$(this).addClass('is-checked');
					event.preventDefault();
				});
			},
	*/

		axilMasonary: function axilMasonary() {
			if (typeof $.fn.isotope == 'function' && typeof $.fn.imagesLoaded == 'function') {
				var $blogIsotopeContainer = $('.post-isotope');
				$blogIsotopeContainer.imagesLoaded(function () {
					$blogIsotopeContainer.isotope();
				});
				var $isotopeContainer = $('.axil-product-area');
				$isotopeContainer.imagesLoaded(function () {
					$isotopeContainer.each(function () {
						var $container = $(this).find('.isotope-list'),
							filter = $(this).find('.isotope-button button.is-checked').data('filter');
						ThemeHelper.run_isotope($container, filter);
					});
				});
				$('.isotope-button button').on('click', function () {
					$(this).closest('.isotope-button').find('.is-checked').removeClass('is-checked');
					$(this).addClass('is-checked');
					var $container = $(this).closest('.axil-product-area').find('.isotope-list'),
						filter = $(this).attr('data-filter');
					ThemeHelper.run_isotope($container, filter);
					return false;
				});
			}
		},

		stickyHeaderMenu: function stickyHeaderMenu() {

			$(window).on('scroll', function () {
				if ($('body').hasClass('header-sticky-active')) {
					var stickyPlaceHolder = $("#axil-sticky-placeholder"),
						menu = $(".axil-mainmenu"),
						menuH = menu.outerHeight(),
						topHeaderH = $('.axil-header-top').outerHeight() || 0,
						targrtScroll = topHeaderH + menuH;
					if ($(window).scrollTop() > targrtScroll) {
						menu.addClass('axil-sticky');
						stickyPlaceHolder.height(menuH);
					} else {
						menu.removeClass('axil-sticky');
						stickyPlaceHolder.height(0);
					}
				}
			});
		},

		multipurpose_slider_slick_activation: function multipurpose_slider_slick_activation() {

			var SlickCarousel = $('.axil-main-slider-area');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.has-multipurpose-slider-area').hasClass('slick-initialized')) {
						SlickCarousel.find('.has-multipurpose-slider-area').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.has-multipurpose-slider-area').slick({
					infinite: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: false,
					dots: false,
					fade: true,
					rtl: rtltrue,
					adaptiveHeight: true,
					cssEase: 'linear',
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>',


				});
			}

		},

		related_slider_slick_activation: function related_slider_slick_activation() {

			var SlickCarousel = $('.related-blog-area');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.related-blog-activation').hasClass('slick-initialized')) {
						SlickCarousel.find('.related-blog-activation').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.related-blog-activation').slick({
					infinite: true,
					slidesToShow: 3,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					speed: 500,
					rtl: rtltrue,
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="fal fa-long-arrow-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="fal fa-long-arrow-right"></i></button>',
					responsive: [{
						breakpoint: 1199,
						settings: {
							slidesToShow: 2,
						}
					},
					{
						breakpoint: 768,
						settings: {
							slidesToShow: 1,
						}
					}

					]
				});
			}

		},


		axilslickactivation2: function axilslickactivation2() {

			var SlickCarousel = $('.single-product-thumb');


			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.product-small-thumb').hasClass('slick-initialized')) {
						SlickCarousel.find('.product-small-thumb').slick('unslick');
					}

					if (SlickCarousel.find('.product-large-thumbnail').hasClass('slick-initialized')) {
						SlickCarousel.find('.product-large-thumbnail').slick('unslick');
					}

				} catch (e) { }



				SlickCarousel.find('.product-large-thumbnail').slick({
					infinite: false,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					speed: 800,
					draggable: false,
					rtl: rtltrue,
					asNavFor: '.product-small-thumb'

				});
				SlickCarousel.find('.product-small-thumb').slick({
					infinite: false,
					slidesToShow: 6,
					slidesToScroll: 1,
					arrows: false,
					dots: false,
					focusOnSelect: true,
					vertical: true,
					rtl: rtltrue,
					speed: 800,
					asNavFor: '.product-large-thumbnail',
					responsive: [{
						breakpoint: 992,
						settings: {
							vertical: false,
						}
					},
					{
						breakpoint: 768,
						settings: {
							vertical: false,
							slidesToShow: 4,
						}
					}
					]


				});
			}
		},


		axilslickactivation3: function axilslickactivation3() {

			var SlickCarousel = $('.single-product-thumb');


			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.product-small-thumb-2').hasClass('slick-initialized')) {
						SlickCarousel.find('.product-small-thumb-2').slick('unslick');
					}

					if (SlickCarousel.find('.product-large-thumbnail-2').hasClass('slick-initialized')) {
						SlickCarousel.find('.product-large-thumbnail-2').slick('unslick');
					}

				} catch (e) { }

				SlickCarousel.find('.product-large-thumbnail-2').slick({
					infinite: false,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					speed: 800,
					draggable: false,
					rtl: rtltrue,
					asNavFor: '.product-small-thumb-2',
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>'


				});
				SlickCarousel.find('.product-small-thumb-2').slick({
					infinite: false,
					slidesToShow: 6,
					slidesToScroll: 1,
					arrows: false,
					dots: false,
					focusOnSelect: true,
					speed: 800,
					rtl: rtltrue,
					asNavFor: '.product-large-thumbnail-2',
					responsive: [{
						breakpoint: 768,
						settings: {
							slidesToShow: 5,
						}
					},
					{
						breakpoint: 479,
						settings: {
							slidesToShow: 4,
						}
					}
					]


				});

			}
		},
		

	axilslickactivationpupup: function axilslickactivationpupup() {

			var SlickCarousel = $('.single-product-thumb');


			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.product-small-thumb-pupup').hasClass('slick-initialized')) {
						SlickCarousel.find('.product-small-thumb-pupup').slick('unslick');
					}

					if (SlickCarousel.find('.product-large-thumbnail-pupup').hasClass('slick-initialized')) {
						SlickCarousel.find('.product-large-thumbnail-pupup').slick('unslick');
					}

				} catch (e) { }

				SlickCarousel.find('.product-large-thumbnail-pupup').slick({
					infinite: false,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					speed: 800,
					draggable: false,
					rtl: rtltrue,
					asNavFor: '.product-small-thumb-pupup',
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>'


				});
				SlickCarousel.find('.product-small-thumb-pupup').slick({
					infinite: false,
					slidesToShow: 6,
					slidesToScroll: 1,
					arrows: false,
					dots: false,
					focusOnSelect: true,
					speed: 800,
					rtl: rtltrue,
					asNavFor: '.product-large-thumbnail-pupup',
					responsive: [{
						breakpoint: 768,
						settings: {
							slidesToShow: 5,
						}
					},
					{
						breakpoint: 479,
						settings: {
							slidesToShow: 4,
						}
					}
					]


				});

				$('#yith-quick-view-modal').on('shown.bs.modal', function (event) {

					$('.product-small-thumb-pupup').slick('setPosition');
					$('.product-large-thumbnail-pupup').slick('setPosition');

				});


			}
		},


		axilslickactivation: function axilslickactivation() {

			$('.post-gallery-activation').slick({
				infinite: true,
				slidesToShow: 1,
				slidesToScroll: 1,
				arrows: true,
				dots: false,
				fade: true,
				rtl: rtltrue,
				adaptiveHeight: true,
				cssEase: 'linear',
				prevArrow: '<button class="slide-arrow prev-arrow"><i class="fal fa-arrow-left"></i></button>',
				nextArrow: '<button class="slide-arrow next-arrow"><i class="fal fa-arrow-right"></i></button>',
			});


		},

		slider_slick_activation: function slider_slick_activation() {

			var SlickCarousel = $('.has-axil-slider-area');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.slider-slick-activation').hasClass('slick-initialized')) {
						SlickCarousel.find('.slider-slick-activation').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.slider-slick-activation').slick({
					infinite: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: false,
					dots: true,
					fade: true,
					adaptiveHeight: true,
					cssEase: 'linear',
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>',


				});
			}

		},


		slider_slick_activation_campaign: function slider_slick_activation_campaign() {

			var SlickCarousel = $('.header-top-campaign');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.header-campaign-activation').hasClass('slick-initialized')) {
						SlickCarousel.find('.header-campaign-activation').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.header-campaign-activation').slick({
					infinite: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					rtl: rtltrue,
					autoplay: true,
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="fal fa-long-arrow-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="fal fa-long-arrow-right"></i></button>',


				});
			}

		},


		slider_slick_activation_two: function slider_slick_activation_two() {

			var SlickCarousel = $('.main-slider-style-5');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.slider-activation-two').hasClass('slick-initialized')) {
						SlickCarousel.find('.slider-activation-two').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.slider-activation-two').slick({
					infinite: true,
					autoplay: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: false,
					dots: true,
					fade: true,
					rtl: rtltrue,
					adaptiveHeight: true,
					cssEase: 'linear',
					speed: 400

				});
			}

		},

		slider_slick_activation_one: function slider_slick_activation_one() {

			var SlickCarousel = $('.main-slider-style-2');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.slider-activation-one').hasClass('slick-initialized')) {
						SlickCarousel.find('.slider-activation-one').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.slider-activation-one').slick({
					infinite: true,
					autoplay: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: false,
					dots: false,
					fade: true,
					rtl: rtltrue,
					focusOnSelect: false,
					speed: 400

				});
			}

		},


		slider_slick_activation_4: function slider_slick_activation_4() {

			var SlickCarousel = $('.main-slider-style-4');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.slider-content-activation-4').hasClass('slick-initialized')) {
						SlickCarousel.find('.slider-content-activation-4').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.slider-content-activation-4').slick({
					infinite: true,
					autoplay: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: false,
					dots: true,
					fade: true,
					rtl: rtltrue,
					adaptiveHeight: true,
					cssEase: 'linear',
					speed: 400

				});
			}

		},

		explore_slider_slick_activation: function explore_slider_slick_activation() {

			var SlickCarousel = $('.has-axil-explore-area');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.explore-product-activation').hasClass('slick-initialized')) {
						SlickCarousel.find('.explore-product-activation').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.explore-product-activation').slick({
					infinite: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					rtl: rtltrue,
					prevArrow: '<button class="slide-arrow prev-arrow slick-arrow"><i class="fal fa-angle-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow slick-arrow"><i class="fal fa-angle-right"></i></button>',

				});
			}

		},


		header_ad_activation: function header_ad_activation() {

			var SlickCarousel = $('.has-header-ad-activation');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.header-ad-activation').hasClass('slick-initialized')) {
						SlickCarousel.find('.header-ad-activation').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.header-ad-activation').slick({
					infinite: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					rtl: rtltrue,
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>',


				});
			}

		},


		categrie_product_activation: function categrie_product_activation() {


			$('.categrie-product-activation').not('.slick-initialized').slick();

		},



		new_arrivals_product_activation: function new_arrivals_product_activation() {

			var SlickCarousel = $('.axil-new-arrivals-activation');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.new-arrivals-product-activation').hasClass('slick-initialized')) {
						SlickCarousel.find('.new-arrivals-product-activation').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.new-arrivals-product-activation').slick({
					infinite: true,
					slidesToShow: 4,
					slidesToScroll: 4,
					arrows: true,
					dots: false,
					rtl: rtltrue,
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="fal fa-long-arrow-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="fal fa-long-arrow-right"></i></button>',
					responsive: [{
						breakpoint: 1199,
						settings: {
							slidesToShow: 3,
							slidesToScroll: 3
						}
					},
					{
						breakpoint: 991,
						settings: {
							slidesToShow: 3,
							slidesToScroll: 3
						}
					},
					{
						breakpoint: 767,
						settings: {
							slidesToShow: 2,
							slidesToScroll: 2
						}
					},
					{
						breakpoint: 480,
						settings: {
							slidesToShow: 1,
							slidesToScroll: 1
						}
					}
					]

				});
			}

		},


		new_arrivals_product_activation2: function new_arrivals_product_activation2() {

			var SlickCarousel = $('.axil-new-arrivals-activation');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.new-arrivals-product-activation2').hasClass('slick-initialized')) {
						SlickCarousel.find('.new-arrivals-product-activation2').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.new-arrivals-product-activation2').slick({
					infinite: true,
					slidesToShow: 4,
					slidesToScroll: 4,
					arrows: true,
					dots: false,
					rtl: rtltrue,
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="fal fa-long-arrow-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="fal fa-long-arrow-right"></i></button>',
					responsive: [{
						breakpoint: 1199,
						settings: {
							slidesToShow: 3,
							slidesToScroll: 3
						}
					},
					{
						breakpoint: 991,
						settings: {
							slidesToShow: 3,
							slidesToScroll: 3
						}
					},
					{
						breakpoint: 767,
						settings: {
							slidesToShow: 2,
							slidesToScroll: 2
						}
					},
					{
						breakpoint: 575,
						settings: {
							slidesToShow: 1,
							slidesToScroll: 1
						}
					}
					]

				});
			}

		},



		testimonial_slick_activation: function testimonial_slick_activation() {

			var SlickCarousel = $('.testimonial-slick-activation-wrapper');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.testimonial-slick-activation').hasClass('slick-initialized')) {
						SlickCarousel.find('.testimonial-slick-activation').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.testimonial-slick-activation').slick({

					infinite: true,
					slidesToShow: 3,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					speed: 500,
					rtl: rtltrue,
					draggable: false,
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="fal fa-long-arrow-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="fal fa-long-arrow-right"></i></button>',
					responsive: [{
						breakpoint: 1199,
						settings: {
							slidesToShow: 1,
						}
					}
					]
				});
			}

		},

		team_slick_activation: function team_slick_activation() {

			var SlickCarousel = $('.axil-team-area');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.team-slide-activation').hasClass('slick-initialized')) {
						SlickCarousel.find('.team-slide-activation').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.team-slide-activation').slick({

					infinite: true,
					slidesToShow: 4,
					slidesToScroll: 4,
					arrows: true,
					dots: false,
					rtl: rtltrue,
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>',
					responsive: [{
						breakpoint: 1199,
						settings: {
							slidesToShow: 3,
							slidesToScroll: 3
						}
					},
					{
						breakpoint: 991,
						settings: {
							slidesToShow: 2,
							slidesToScroll: 2
						}
					},
					{
						breakpoint: 576,
						settings: {
							slidesToShow: 1,
							slidesToScroll: 1
						}
					}
					]
				});
			}

		},


		testimonial_slick_activation3: function testimonial_slick_activation3() {

			var $slideStatus = $('.slick-slide-count');
			var SlickCarousel = $('.testimonial-style-three-wrapper');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.testimonial-slick-activation-three').hasClass('slick-initialized')) {
						SlickCarousel.find('.testimonial-slick-activation-three').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.testimonial-slick-activation-three').slick({
					infinite: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: true,
					dots: false,
					speed: 500,
					draggable: true,
					rtl: rtltrue,
					prevArrow: $('.prev-custom-nav'),
					nextArrow: $('.next-custom-nav'),
					responsive: [{
						breakpoint: 991,
						settings: {
							slidesToShow: 1,
						}
					}
					]
				});
			}

			$('.testimonial-slick-activation-three').on('init reInit afterChange', function (event, slick, currentSlide, nextSlide) {
				var i = (currentSlide ? currentSlide : 0) + 1;
				$slideStatus.text(i + '/' + slick.slideCount);
			});

		},


		testimonial_slick_activation2: function testimonial_slick_activation2() {

			var SlickCarousel = $('.testimonial-slick-activation-wrapper2');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.testimonial-slick-activation-two').hasClass('slick-initialized')) {
						SlickCarousel.find('.testimonial-slick-activation-two').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.testimonial-slick-activation-two').slick({

					infinite: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: true,
					dots: true,
					rtl: rtltrue,
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>',

				});
			}

		},


		slider_thumb_activation_two: function slider_thumb_activation_two() {

			var SlickCarousel = $('.main-slider-style-3');
			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.slider-thumb-activation-two').hasClass('slick-initialized')) {
						SlickCarousel.find('.slider-thumb-activation-two').slick('unslick');
					}
				} catch (e) { }

				SlickCarousel.find('.slider-thumb-activation-two').slick({

					infinite: true,
					slidesToShow: 3,
					centerPadding: '0',
					arrows: false,
					dots: true,
					speed: 1500,
					autoplay: false,
					centerMode: true,
					rtl: rtltrue,
					responsive: [
						{
							breakpoint: 575,
							settings: {
								slidesToShow: 1,
								slidesToScroll: 1,
							}
						},
					]
				});
			}

		},



		slider_thumb_activation_one: function slider_thumb_activation_one() {
			var SlickCarousel = $('.main-slider-style-1');

			if (SlickCarousel.length) {
				try {
					if (SlickCarousel.find('.slider-thumb-activation-one').hasClass('slick-initialized')) {
						SlickCarousel.find('.slider-thumb-activation-one').slick('unslick');
					}

					if (SlickCarousel.find('.slider-content-activation-one').hasClass('slick-initialized')) {
						SlickCarousel.find('.slider-content-activation-one').slick('unslick');
					}


				} catch (e) { }


				SlickCarousel.find('.slider-thumb-activation-one').slick({
					infinite: true,
					slidesToShow: 2,
					slidesToScroll: 1,
					arrows: false,
					dots: true,
					focusOnSelect: false,
					speed: 1000,
					rtl: rtltrue,
					asNavFor: '.slider-content-activation-one',
					prevArrow: '<button class="slide-arrow prev-arrow"><i class="fal fa-long-arrow-left"></i></button>',
					nextArrow: '<button class="slide-arrow next-arrow"><i class="fal fa-long-arrow-right"></i></button>',
					responsive: [{
						breakpoint: 991,
						settings: {
							slidesToShow: 1,
						}
					}
					]

				});

				SlickCarousel.find('.slider-content-activation-one').slick({

					infinite: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrows: false,
					dots: false,
					focusOnSelect: false,
					speed: 500,
					fade: true,
					autoplay: false,
					rtl: rtltrue,
					asNavFor: '.slider-thumb-activation-one',

				});

			}
		},




		salActivation: function salActivation() {
			sal({
				threshold: 0.3,
				once: true
			});
		},


		wooc_active_animation: function wooc_active_animation() {
			if (!!window.IntersectionObserver) {
				var observer = new IntersectionObserver(function (entries, observer) {
					entries.forEach(function (entry) {
						if (entry.isIntersecting) {
							entry.target.classList.add("active-animation");
							observer.unobserve(entry.target);
						}
					});
				}, {
					rootMargin: "0px 0px -100px 0px"
				});
				document.querySelectorAll('.has-animation').forEach(function (block) {
					observer.observe(block);
				});
			} else {
				document.querySelectorAll('.has-animation').forEach(function (block) {
					block.classList.remove('has-animation');
				});
			}
		},
		wooc_removeAttr_right: function wooc_removeAttr_right() {
			var $ = jQuery;
			$("svg").removeAttr("id");
		},
		wooc_toggle_right: function wooc_toggle_right() {
			var $ = jQuery;
			$(".cart-icon-area").mouseover(function () {
				$(this).find(" > .cart-icon-products").css("display", "block");
				$(this).find(" > .cart-icon-products").css("transition", "all 0.5s");
			}).mouseout(function () {
				$(this).find(" > .cart-icon-products").css("display", "none");
			});
		},

		wooc_SimpleBar: function wooc_SimpleBar() {
			var $ = jQuery;

			if ($('.additional-menu-msidebar').length > 0) {
				new SimpleBar($('.additional-menu-msidebar')[0]);
			}
		},
		wooc_tooltip: function wooc_tooltip() {
			var $ = jQuery;

			if ($('[data-toggle="tooltip"]').length > 0) {
				$('[data-toggle="tooltip"]').tooltip();
			}
		},
		wooc_toltp: function wooc_toltp() {
			$(document).on('mouseover', '.wooc-toltp', function () {
				var self = $(this),
					tips = self.attr('data-tips');
				$tooltip = '<div class="etrade-tooltip">' + '<div class="etrade-tooltip-content">' + tips + '</div>' + '<div class="etrade-tooltip-bottom"></div>' + '</div>';
				$('body').append($tooltip);
				var $tooltip = $('body > .etrade-tooltip');
				var tHeight = $tooltip.outerHeight();
				var tBottomHeight = $tooltip.find('.etrade-tooltip-bottom').outerHeight();
				var tWidth = $tooltip.outerWidth();
				var tHolderWidth = self.outerWidth();
				var top = self.offset().top - (tHeight - tBottomHeight) - 26;
				var left = self.offset().left;
				$tooltip.css({
					'top': top + 'px',
					'left': left + 'px',
					'opacity': 1
				}).show();

				if (tWidth <= tHolderWidth) {
					var itemLeft = (tHolderWidth - tWidth) / 2;
					left = left + itemLeft;
					$tooltip.css('left', left + 'px');
				} else {
					var itemLeft = (tWidth - tHolderWidth) / 2;
					left = left - itemLeft;

					if (left < 0) {
						left = 0;
					}

					$tooltip.css('left', left + 'px');
				}
			}).on('mouseout', '.wooc-toltp', function () {
				$('body > .etrade-tooltip').remove();
			});
		},

		wooc_niceSelect: function wooc_niceSelect() {
			var $ = jQuery;

			if ($('select').length > 0) {
				$('select').niceSelect();
			}
		},


		scroll_to_top: function scroll_to_top() {
			$('.scrollToTop').on('click', function () {
				$('html, body').animate({
					scrollTop: 0
				}, 800);
				return false;
			});
			$(window).scroll(function () {
				if ($(window).scrollTop() > 300) {
					$('.scrollToTop').addClass('back-top');
				} else {
					$('.scrollToTop').removeClass('back-top');
				}
			});
		},
		preloader: function preloader() {
			$('#preloader').fadeOut('slow', function () {
				$(this).remove();
			});
		},
		sticky_menu: function sticky_menu() {
			if (etradeObj.hasStickyMenu == 1) {
				ThemeHelper.run_sticky_menu();
				ThemeHelper.run_sticky_meanmenu();
			}
		},
		ripple_effect: function ripple_effect() {
			if (typeof $.fn.ripples == 'function') {
				$('.wooc-water-ripple').ripples({
					resolution: 712,
					dropRadius: 30,
					perturbance: 0.01
				});
			}
		},
		category_search_dropdown: function category_search_dropdown() {
			$('.category-search-dropdown-js .dropdown-menu li').on('click', function (e) {
				var $parent = $(this).closest('.category-search-dropdown-js'),
					slug = $(this).data('slug'),
					name = $(this).text();
				$parent.find('.dropdown-toggle').text($.trim(name));
				$parent.find('input[name="product_cat"]').val(slug);
			});

			if ($.fn.autocomplete) {
				$(".ps-autocomplete-js .product-autocomplete-js").autocomplete({
					minChars: 2,
					search: function search(event, ui) {
						if (!$(event.target).parent().find('.product-autocomplete-spinner').length) {
							$('<i class="product-autoaomplete-spinner fa fa-spinner fa-spin"></i>').insertAfter(event.target);
						}
					},
					source: function source(req, response) {
						req.action = 'etrade_product_search_autocomplete';
						$.ajax({
							dataType: "json",
							type: "POST",
							url: etradeObj.ajaxurl,
							data: req,
							success: function success(data) {
								response(data);
							}
						});
					},
					response: function response(event, ui) {
						$(event.target).parent().find('.product-autoaomplete-spinner').remove();
					}
				});
			}
		},
		search_popup: function search_popup() {
			$('.search-icon-area a').on("click", function (event) {
				event.preventDefault();
				$("#axiltheme-search-popup").addClass("open");
				$('#axiltheme-search-popup > form > input').focus();
			});
			$("#axiltheme-search-popup, #axiltheme-search-popup button.close").on("click keyup", function (event) {
				if (event.target == this || event.target.className == "close" || event.keyCode == 27) {
					$(this).removeClass("open");
				}
			});
		},
		vertical_menu: function vertical_menu() {
			$('.vertical-menu-btn').on('click', function (e) {
				e.preventDefault();
				$(this).closest('.vertical-menu-area').toggleClass("opened");
			});
		},
		vertical_menu_mobile: function vertical_menu_mobile() {
			ThemeHelper.add_vertical_menu_class();
			$(window).on('resize', function () {
				ThemeHelper.add_vertical_menu_class();
			});
			$('.vertical-menu').on('click', 'li.menu-item-has-children span.has-dropdown', function (e) {
				if ($(this).find('+ ul.sub-menu').length) {
					$(this).closest('li').toggleClass('submenu-opend');
					$(this).find('+ ul.sub-menu').slideToggle();
				}

				return false;
			});
		},
		mobile_menu: function mobile_menu() {
			$('#site-header .main-navigation nav').meanmenu({
				meanMenuContainer: '#meanmenu',
				meanScreenWidth: etradeObj.meanWidth,
				removeElements: "#site-header, .top-header-desktop",
				siteLogo: etradeObj.siteLogo,
				meanExpand: '<i class="flaticon-menu"></i>',
				meanContract: '<i class="flaticon-remove-1"></i>',
				meanMenuClose: '<i class="flaticon-remove-1"></i>',
				appendHtml: etradeObj.appendHtml
			});
		},
		mobile_menu_max_height: function mobile_menu_max_height() {
			var wHeight = $(window).height();
			wHeight = wHeight - 50;
			$('.mean-nav > ul').css('max-height', wHeight + 'px');
		},
		multi_column_menu: function multi_column_menu() {
			$('.main-navigation ul > li.mega-menu').each(function () {
				var items = $(this).find(' > ul.sub-menu > li').length;
				var bodyWidth = $('body').outerWidth();
				var parentLinkWidth = $(this).find(' > a').outerWidth();
				var parentLinkpos = $(this).find(' > a').offset().left;
				var width = items * 244;
				var left = width / 6 - parentLinkWidth / 2;
				var linkleftWidth = parentLinkpos + parentLinkWidth / 2;
				var linkRightWidth = bodyWidth - (parentLinkpos + parentLinkWidth);

				if (width / 2 > linkleftWidth) {
					$(this).find(' > ul.sub-menu').css({
						width: width + 'px',
						right: 'inherit',
						left: '-' + parentLinkpos + 'px'
					});
				} else if (width / 2 > linkRightWidth) {
					$(this).find(' > ul.sub-menu').css({
						width: width + 'px',
						left: 'inherit',
						right: '-' + linkRightWidth + 'px'
					});
				} else {
					$(this).find(' > ul.sub-menu').css({
						width: width + 'px',
						left: '-' + left + 'px'
					});
				}
			});
		},
		axilsliderslickactivation: function axilsliderslickactivation() {
			$('.slider-slick-activation').slick({
				infinite: true,
				slidesToShow: 1,
				slidesToScroll: 1,
				arrows: false,
				dots: true,
				fade: true,
				adaptiveHeight: true,
				cssEase: 'linear',
				prevArrow: '<button class="slide-arrow prev-arrow"><i class="far fa-angle-left"></i></button>',
				nextArrow: '<button class="slide-arrow next-arrow"><i class="far fa-angle-right"></i></button>',
			});
		},
		slick_carousel: function slick_carousel() {
			if (typeof $.fn.slick == 'function') {
				$(".wooc-slick-slider").each(function () {
					$(this).slick({
						rtl: etradeObj.rtl
					});
				});
				$(document).on('afterLoadMore afterInfinityScroll', function () {
					$(".product_loaded .wooc-slick-slider").each(function () {
						$(this).slick({
							rtl: etradeObj.rtl
						});
					});
					$(".product_loaded").removeClass('product_loaded');
				});
			}
		},
		owl_carousel: function owl_carousel() {
			if (typeof $.fn.owlCarousel == 'function') {
				$(".owl-custom-nav .owl-next").on('click', function () {
					$(this).closest('.owl-wrap').find('.owl-carousel').trigger('next.owl.carousel');
				});
				$(".owl-custom-nav .owl-prev").on('click', function () {
					$(this).closest('.owl-wrap').find('.owl-carousel').trigger('prev.owl.carousel');
				});
				$(".wooc-owl-carousel").each(function () {
					var options = $(this).data('carousel-options');

					if (etradeObj.rtl == 'yes') {
						options['rtl'] = true;
						options['navText'] = ["<i class='fa fa-angle-right'></i>", "<i class='fa fa-angle-left'></i>"];
					}

					$(this).owlCarousel(options);
				});
				$(".owl-numbered-dots .owl-numbered-dots-items span").on('click', function () {
					var index = $(this).data('num');
					var $owlParent = $(this).closest('.owl-wrap').find('.owl-carousel');
					$owlParent.trigger('to.owl.carousel', index);
					$owlParent.find('.owl-numbered-dots-items span').removeClass('active');
					$owlParent.find('.owl-numbered-dots-items [data-num="' + index + '"]').addClass('active');
				});
			}
		},
		countdown: function countdown() {
			if (typeof $.fn.countdown == 'function') {
				try {
					var day = etradeObj.day == 'Day' ? 'Day%!D' : etradeObj.day,
						hour = etradeObj.hour == 'Hour' ? 'Hour%!D' : etradeObj.hour,
						minute = etradeObj.minute == 'Minute' ? 'Minute%!D' : etradeObj.minute,
						second = etradeObj.second == 'Second' ? 'Second%!D' : etradeObj.second;

					$('.axilcoutdown').each(function () {
						var $CountdownSelector = $(this).find('.countdown');
						var eventCountdownTime = $CountdownSelector.data('time');
						$CountdownSelector.countdown(eventCountdownTime).on('update.countdown', function (event) {
							$(this).html(event.strftime('' + '<div class="countdown-section"><div><div class="countdown-number">%D</div><div class="countdown-unit">' + day + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%H</div><div class="countdown-unit">' + hour + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%M</div><div class="countdown-unit">' + minute + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%S</div><div class="countdown-unit">' + second + '</div></div></div>'));
						}).on('finish.countdown', function (event) {
							$(this).html(event.strftime(''));
						});
					});
					$('.axilcoutdown2').each(function () {
						var $CountdownSelector = $(this).find('.countdown');
						var eventCountdownTime = $CountdownSelector.data('time');
						$CountdownSelector.countdown(eventCountdownTime).on('update.countdown', function (event) {
							$(this).html(event.strftime('' + '<div class="countdown-section"><div><div class="countdown-number">%D</div><div class="countdown-unit">' + day + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%H</div><div class="countdown-unit">' + hour + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%M</div><div class="countdown-unit">' + minute + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%S</div><div class="countdown-unit">' + second + '</div></div></div>'));
						}).on('finish.countdown', function (event) {
							$(this).html(event.strftime(''));
						});
					});
					$('.header-top-campaign').each(function () {
						var $CountdownSelector = $(this).find('.campaign-countdown');
						var eventCountdownTime = $CountdownSelector.data('time');
						$CountdownSelector.countdown(eventCountdownTime).on('update.countdown', function (event) {
							$(this).html(event.strftime('' + '<div class="countdown-section"><div><div class="countdown-number">%-D</div> <div class="countdown-unit">D</div> </div></div><div class="countdown-section"><div><div class="countdown-number">%H</div> <div class="countdown-unit">H</div> </div></div><div class="countdown-section"><div><div class="countdown-number">%M</div> <div class="countdown-unit">M</div> </div></div><div class="countdown-section"><div><div class="countdown-number">%S</div> <div class="countdown-unit">S</div> </div></div>'));
						}).on('finish.countdown', function (event) {
							$(this).html(event.strftime(''));
						});
					});


				} catch (err) {
					console.log('Countdown : ' + err.message);
				}
			}
		},

		countdown_coming_soon: function countdown_coming_soon() {
			if (typeof $.fn.countdown == 'function') {
				try {
					var day = etradeObj.under_day == 'Day' ? 'Day%!D' : etradeObj.under_day,
						hour = etradeObj.under_hour == 'Hrs' ? 'Hr%!D' : etradeObj.under_hour,
						minute = etradeObj.under_minute == 'Min' ? 'Min%!D' : etradeObj.under_minute,
						second = etradeObj.under_second == 'Sec' ? 'Sec%!D' : etradeObj.under_second;

					$('.comming-soon-area').each(function () {
						var $CountdownSelector = $(this).find('.countdown');
						var eventCountdownTime = $CountdownSelector.data('time');
						$CountdownSelector.countdown(eventCountdownTime).on('update.countdown', function (event) {
							$(this).html(event.strftime('' + '<div class="countdown-section"><div><div class="countdown-number">%D</div><div class="countdown-unit">' + day + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%H</div><div class="countdown-unit">' + hour + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%M</div><div class="countdown-unit">' + minute + '</div></div></div>' + '<div class="countdown-section"><div><div class="countdown-number">%S</div><div class="countdown-unit">' + second + '</div></div></div>'));
						}).on('finish.countdown', function (event) {
							$(this).html(event.strftime(''));
						});
					});



				} catch (err) {
					console.log('Countdown : ' + err.message);
				}
			}
		},
		magnific_popup: function magnific_popup() {

			if (typeof $.fn.magnificPopup == 'function') {
				$('.popup-youtube').magnificPopup({
					disableOn: 700,
					type: 'iframe',
					mainClass: 'mfp-fade',
					removalDelay: 160,
					preloader: false,
					fixedContentPos: false
				});
			}
		},

		productShowOnhover: function productShowOnhover() {
			let hoverEelmParnt = $(".plus-hover-items");
			let hoverItem = hoverEelmParnt.children(".plus-icon");

			if (hoverEelmParnt.length > 1) {
				hoverItem.mouseover(function () {
					let parentSelect = $(this).parent();
					if (!parentSelect.hasClass("icon-active")) {
						parentSelect.addClass("icon-active");
						parentSelect.siblings().removeClass("icon-active");
					}
				})
			}
		}

	};

	var WooCommerce = {


		meta_reloation: function meta_reloation() {
			$('.product-type-variable .single-product-top-2 .product_meta-area-js, .product-type-variable .single-product-top-3 .product_meta-area-js').insertAfter('form.variations_form table.variations');
		},
		sticky_product_thumbnail: function sticky_product_thumbnail() {
			if (typeof $.fn.stickySidebar == 'function') {
				var screenWidth = $('body').outerWidth();

				if (screenWidth > 991) {
					var top = 20;

					if (etradeObj.hasStickyMenu == 1) {
						top += $('.header-main-block-sticky-wrapper').outerHeight();
					}

					if (etradeObj.hasAdminBar == 1) {
						top += $('#wpadminbar').outerHeight();
					}

					$('.single-product-area-2 .wooc-sticky-left').stickySidebar({
						topSpacing: top
					});
				}
			}
		},
		quantity_change: function quantity_change() {
			$(document).on('click', '.quantity .input-group-btn .quantity-btn', function () {
				var $input = $(this).closest('.quantity').find('.input-text');

				if ($(this).hasClass('quantity-plus')) {
					$input.trigger('stepUp').trigger('change');
				}

				if ($(this).hasClass('quantity-minus')) {
					$input.trigger('stepDown').trigger('change');
				}
			});
		},
		slider_nav: function slider_nav() {
			$('.woocueproduct-slider').each(function () {
				var $target = $(this).find('.owl-custom-nav .owl-nav button.owl-prev, .owl-custom-nav .owl-nav button.owl-next'),
					$img = $(this).find('.woocue-thumb-wrapper').first();

				if ($img.length) {
					var height = $img.outerHeight();
					height = height / 2 - 24;
					$target.css('top', height + 'px');
				}
			});
		},

	};

	function loadmore_n_infinityscroll() {
		var loadMoreWrapper = $('.axiltheme-loadmore-wrapper'),
			infinityScrollWrapper = $('.axiltheme-infscroll-wrapper');

		if (loadMoreWrapper.length) {
			loadMore(loadMoreWrapper);
		}

		if (infinityScrollWrapper.length) {
			infinityScroll(infinityScrollWrapper);
		}

		function loadMore($wrapper) {
			var button = $('.axiltheme-loadmore-btn'),
				shopData = $('.axiltheme-loadmore-data'),
				maxPage = shopData.data('max'),
				query = shopData.attr('data-query'),
				CurrentPage = 1;
			button.on('click', button, function () {
				var data = {
					'action': 'axiltheme_loadmore',
					'context': 'frontend',
					'nonce': shopData.data('nonce'),
					'query': query,
					'view': 'grid',
					'paged': CurrentPage
				};
				$.ajax({
					url: etradeObj.ajaxurl,
					type: 'POST',
					data: data,
					beforeSend: function beforeSend() {
						disableBtn(button);
					},
					success: function success(data) {
						console.log();
						if (data) {
							CurrentPage++;
							$wrapper.append(data);
							WcUpdateResultCount($wrapper);

							if (CurrentPage == maxPage) {
								removeBtn(button);
							} else {
								enableBtn(button);
							}

							$(document).trigger("afterLoadMore");
						} else {
							removeBtn(button);
						}
					}
				});
				return false;
			});
		}

		function infinityScroll($wrapper) {
			var canBeLoaded = true,
				shopData = $('.axiltheme-loadmore-data'),
				icon = $('.axiltheme-infscroll-icon'),
				query = shopData.attr('data-query'),
				CurrentPage = 1;
			$(window).on('scroll load', function () {
				if (!canBeLoaded) {
					return;
				}

				var data = {
					'action': 'axiltheme_loadmore',
					'context': 'frontend',
					'nonce': shopData.data('nonce'),
					'query': query,
					'paged': CurrentPage
				};

				if (isScrollable($wrapper)) {
					$.ajax({
						url: etradeObj.ajaxurl,
						type: 'POST',
						data: data,
						beforeSend: function beforeSend() {
							canBeLoaded = false;
							icon.show();
						},
						success: function success(data) {
							if (data) {
								CurrentPage++;
								canBeLoaded = true;
								$wrapper.append(data);
								WcUpdateResultCount($wrapper);
								icon.hide();
								$(document).trigger("afterInfinityScroll");
							} else {
								icon.remove();
							}
						}
					});
				}
			});
		}

		function isScrollable($wrapper) {
			var ajaxVisible = $wrapper.offset().top + $wrapper.outerHeight(true),
				ajaxScrollTop = $(window).scrollTop() + $(window).height();

			if (ajaxVisible <= ajaxScrollTop && ajaxVisible + $(window).height() > ajaxScrollTop) {
				return true;
			}

			return false;
		}

		function WcUpdateResultCount($wrapper) {
			var count = $($wrapper).find('.product').length;
			$('.wc-last-result-count').text(count);
		}

		function disableBtn(button) {
			button.attr('disabled', 'disabled');
			button.find('.axiltheme-loadmore-btn-text').hide();
			button.find('.axiltheme-loadmore-btn-icon').show();
		}

		function enableBtn(button) {
			button.find('.axiltheme-loadmore-btn-icon').hide();
			button.find('.axiltheme-loadmore-btn-text').show();
			button.removeAttr('disabled');
		}

		function removeBtn(button) {
			button.parent('.axiltheme-loadmore-btn-area').remove();
		}
	}

	function widthgen() {
		$(window).on('load resize', elementWidth);

		function elementWidth() {
			$('.elementwidth').each(function () {
				var $container = $(this),
					width = $container.outerWidth(),
					classes = $container.attr("class").split(' ');
				var classes1 = startWith(classes, 'elwidth');
				classes1 = classes1[0].split('-');
				classes1.splice(0, 1);
				var classes2 = startWith(classes, 'elmaxwidth');
				classes2.forEach(function (el) {
					$container.removeClass(el);
				});
				classes1.forEach(function (el) {
					var maxWidth = parseInt(el);

					if (width <= maxWidth) {
						$container.addClass('elmaxwidth-' + maxWidth);
					}
				});
			});
		}

		function startWith(item, stringName) {
			return $.grep(item, function (elem) {
				return elem.indexOf(stringName) == 0;
			});
		}
	}

	function sameHeightFunction() {
		let elements = $('.same-height-class');
		let heights  = [];
		elements.each(function() { 
			var height = $(this).height();
			heights.push(height);
		});
		var maxHeight = Math.max.apply(null, heights);
		
		elements.each(function(){
			$(this).height(maxHeight);
		}); 
		// console.log(maxHeight)
	}

	loadmore_n_infinityscroll();
	widthgen();
	$(document).on('axil_filter_ajax_load', function () {
		Theme.slick_carousel();
		typeof slick_carousel === "undefined" ? "undefined" : _typeof(slick_carousel);
		loadmore_n_infinityscroll();
	});

	function content_ready_scripts() {
		Theme.countdown();
		Theme.countdown_coming_soon();
		Theme.magnific_popup();
		Theme.vertical_menu();
		Theme.vertical_menu_mobile();
		Theme.category_search_dropdown();
		Theme.wooc_tooltip();
		Theme.wooc_toltp();
		Theme.wooc_toggle_right();
		Theme.wooc_active_animation();
		Theme.salActivation();
		Theme.onLoadClassAdd();
		Theme.testimonial_slick_activation3();
	}

	function content_load_scripts() {
		Theme.related_slider_slick_activation();
		Theme.multipurpose_slider_slick_activation();
		Theme.owl_carousel();
		Theme.slick_carousel();
		Theme.wooc_removeAttr_right();
		Theme.stickyHeaderMenu();
		Theme.slider_slick_activation();
		Theme.categrie_product_activation();
		Theme.testimonial_slick_activation();
		Theme.testimonial_slick_activation2();
		Theme.testimonial_slick_activation3();
		Theme.team_slick_activation();
		Theme.slider_thumb_activation_one();
		Theme.slider_thumb_activation_two();
		Theme.header_ad_activation();
		Theme.new_arrivals_product_activation();
		Theme.new_arrivals_product_activation2();
		Theme.explore_slider_slick_activation();
		Theme.slider_slick_activation_one();
		Theme.slider_slick_activation_two();
		Theme.slider_slick_activation_4();
		Theme.slider_slick_activation_campaign();

		Theme.axilslickactivation();
		Theme.axilslickactivation2();
		Theme.axilslickactivation3();
		Theme.axilslickactivationpupup();
		Theme.axilMasonary();
		Theme.onLoadClassAdd();
		Theme.productShowOnhover();
		Theme.singleProductFeaturedVideoInit();

	}

	$(document).ready(function () {
		Theme.scroll_to_top();
		Theme.multi_column_menu();
		Theme.search_popup();
		WooCommerce.quantity_change();
		WooCommerce.meta_reloation();
		content_ready_scripts();
		Theme.onLoadClassAdd();
	});

	$(window).on('load', function () {
		content_load_scripts();
		Theme.preloader();
		WooCommerce.sticky_product_thumbnail();
		sameHeightFunction();
	});

	$(window).on('load resize', function () {
		Theme.mobile_menu_max_height();
		Theme.onLoadClassAdd();
	});


	$(document).on('qv_loader_stop', function () {
		Theme.axilslickactivation2();
		Theme.axilslickactivation3();
		Theme.axilslickactivationpupup();

	}); // YITH Quickview

	$(window).on('elementor/frontend/init', function () {
		if (elementorFrontend.isEditMode()) {
			elementorFrontend.hooks.addAction('frontend/element_ready/widget', function () {
				content_ready_scripts();
				content_load_scripts();

				let interv = setInterval(()=>{
					console.log(document)
					// let parent = document.getElementsByClassName('elementor-control-sec_title_tag');
					// console.log(parent)
					// if(parent){
					// 	let child = parent.getElementsByClassName('elementor-choices-label');
					// 	console.log(child)

					// }
				},5000)
				// let parent = document.querySelector('.elementor-control-sec_title_tag');
				// let parentx = document.getElementsByClassName('elementor-control-sec_title_tag');
				// console.log(parent,parentx)
				// let child = parent.getElementsByClassName('elementor-choices-label');
				// console.log(child)

 




			});
		}
	});

}());


jQuery(document).ready(function ($) {

	$(document).on('change', '.variation-radios input', function (e) {
		$('select[name="' + $(this).attr('name') + '"]').val($(this).val()).trigger('change');
	});

	$(document).on('change', '.dropdown-set-attribute', function (e) {
	
			let parentEl = e.target.parentNode; 

			let radioEl = parentEl.querySelector('ul').querySelectorAll('input');
			for(let item of radioEl){
				//console.log(item.value)
				if(item.value == e.target.value){
					item.checked = 'checked'
				}else{
					item.checked = false
				}
			}


	});

// $(document).on('change', '.dropdown-set-attribute', function (e) {
// 		//console.log('ccccccc', e.target.value)
// 		let radioEl = document.querySelector('.color-variant').querySelectorAll('input');
// 		for(let item of radioEl){
// 			console.log(item.value)
// 			if(item.value == e.target.value){
// 				item.checked = 'checked'
// 			}
// 		}
// 		console.log(radioEl)
// 	});



	$(document).on('click', '.reset_variations', function (e) {
		
		var ele = document.getElementsByName("attribute_pa_size");
		for(var i=0;i<ele.length;i++)
		    ele[i].checked = false;	
		var ele = document.getElementsByName("attribute_pa_colors");
		for(var i=0;i<ele.length;i++)
		    ele[i].checked = false;	
		var ele = document.getElementsByName("attribute_pa_image");
		for(var i=0;i<ele.length;i++)
		    ele[i].checked = false;
	});


	$(document).on('change', '.axil-shop-widget-content select', function (e) {
		
		let val = $(this).val();
		let url = new URL(val);
		let shapedParams = new URLSearchParams(url.search);
		let min = shapedParams.get('min_price');
		let max = shapedParams.get('max_price');

		let newURL = new URL(window.location.href);

		let urlQueryParams = new URLSearchParams(newURL.search);
		if (urlQueryParams.has('min_price')) {
			urlQueryParams.delete('min_price');
		}

		if (urlQueryParams.has('max_price')) {
			urlQueryParams.delete('max_price');
		}

		urlQueryParams.append('min_price', min);
		urlQueryParams.append('max_price', max);
		window.location.replace(newURL.origin + newURL.pathname + '?' + urlQueryParams.toString());
	});

});


(function ($) {
	$(document).on('mouseover', '.wcvaswatchinput', function (event) {
		event.preventDefault();
		var hoverimage = $(this).attr('data-o-src');

		if (hoverimage) {
			$(this).closest('.axil-product').find(".thumbnail img.attachment-woocommerce_thumbnail").attr("src", hoverimage);
			$(this).closest('.axil-product').find(".thumbnail img.attachment-woocommerce_thumbnail").attr("srcset", hoverimage);
			$(this).css({ "border-color": "#292930" }).parent().siblings().children('.wcvaswatchinput').css({ "border-color": "transparent" });
		}
		return false;

	});


})(jQuery);



(function ($) {

	"use strict";

	//===============================================================
	// Global Debounce
	//===============================================================

	// Returns a function, that, as long as it continues to be invoked, will not
	// be triggered. The function will be called after it stops being called for
	// N milliseconds. If `immediate` is passed, trigger the function on the
	// leading edge, instead of the trailing.

	window.gb_debounce = function (func, wait, immediate) {
		var timeout;
		return function () {
			var context = this, args = arguments;
			var later = function () {
				timeout = null;
				if (!immediate) func.apply(context, args);
			};
			var callNow = immediate && !timeout;
			clearTimeout(timeout);
			timeout = setTimeout(later, wait);
			if (callNow) func.apply(context, args);
		};
	};


	//===============================================================
	// Global Throttle
	//===============================================================

	// Returns a function, that, as long as it continues to be invoked, will only
	// trigger every N milliseconds. If <code>immediate</code> is passed, trigger the 
	// function on the leading edge, instead of the trailing.

	window.gb_throttle = function (func, wait, immediate) {
		var timeout;
		return function () {
			var context = this, args = arguments;
			var later = function () {
				timeout = null;
				if (!immediate) func.apply(context, args);
			};
			var callNow = immediate && !timeout;
			if (!timeout) timeout = setTimeout(later, wait);
			if (callNow) func.apply(context, args);
		};
	};


	//===============================================================
	// Scroll Detection
	//===============================================================

	window.scroll_position = $(window).scrollTop();
	window.scroll_direction = 'fixed';

	function scroll_detection() {
		var scroll = $(window).scrollTop();
		if (scroll > window.scroll_position) {
			window.scroll_direction = 'down';
		} else {
			window.scroll_direction = 'up';
		}
		window.scroll_position = scroll;
	}

	$(window).on('scroll', function () {
		scroll_detection();
	});

})(jQuery);

(function ($) {

	"use strict";

	// =============================================================================
	// Header Search
	// =============================================================================

	// Init

	$(document).on('keyup', function (e) {
		if (e.keyCode == 27) {
			$('.hover_overlay_body').removeClass('visible');
			$('.header_search_ajax_results_wrapper').removeClass('visible animated');
			return false;
		}
	});

	if (typeof $.fn.select2 === 'function') {

		$('.header_search_select').select2({
			minimumResultsForSearch: -1,
			allowClear: false,
			dropdownParent: $('.header_search_form'),
			containerCssClass: "select2_no_border",
			dropdownCssClass: "select2_no_border",
		});

	}

	// Show it

	$('.header_search_select_wrapper').addClass('visible');


	// Content Overlay

	$('.site-header-style-1 .header_search_select').on('select2:opening', function (e) {
		$('.hover_overlay_content').addClass('visible');
		$('.hover_overlay_footer').addClass('visible');
		$(this).parents('form.header_search_form').addClass('active');
	});

	$('.site-header-style-1 .header_search_select').on('select2:closing', function (e) {
		$('.hover_overlay_content').removeClass('visible');
		$('.hover_overlay_footer').removeClass('visible');
	});

	$('.site-header-style-2 .simple-header-search').on('click', function () {
		$('.hover_overlay_body').addClass('visible');
	});

	$('.site-search.off-canvas .header-search .close-button').on('click', function () {
		$('.hover_overlay_body').removeClass('visible');
	});


	// Content Overlay

	// Open
	$('.site-header-style-1 .header_search_input_wrapper input').on('click', function () {
		$(this).parents('form.header_search_form').addClass('active');
		$('.header_search_ajax_results_wrapper').addClass('visible animated');

		if ($('.header_search_ajax_results').html() != '') {
			$('.hover_overlay_content').addClass('visible');
			$('.hover_overlay_footer').addClass('visible');
		}
	});

	$('.site-search.off-canvas .header_search_input_wrapper input').on('click', function () {
		$(this).parents('form.header_search_form').addClass('active');
		$('.header_search_ajax_results_wrapper').addClass('visible animated');
	});


	$(".header-sticky .header-search").on('click', function () {
		$('html, body').animate({
			scrollTop: 0
		}, 500);


		setTimeout(function () {
			$('.header_search_input_wrapper .header_search_input').focus().trigger('click');
			$('.header-search .search-form .search-field').focus().trigger('click');
		}, 600);
	});



	window.original_results = $('.header_search_ajax_results').html();


	// Start Close
	window.header_search_results_close = function (e) {

		var header_search_results_hiding = function (e) {
			var container = $(".header_search_input_wrapper input, .header_search_ajax_results_wrapper");
			if (!container.is(e.target) && container.has(e.target).length === 0) {
				$('.header_search_ajax_results_wrapper').removeClass('animated');
			}
		};

		var header_search_results_hide = gb_debounce(function (e) {
			var container = $(".header_search_input_wrapper input, .header_search_ajax_results_wrapper");
			if (!container.is(e.target) && container.has(e.target).length === 0) {
				$('.header_search_ajax_results_wrapper').removeClass('visible');
			}
		}, 300);

		var header_search_border = function (e) {
			var container = $('.header_search_form');
			if (!container.is(e.target) && container.has(e.target).length === 0) {
				container.removeClass('active');
			}
		}

		var header_search_results_reset = gb_debounce(function (e) {
			var container = $(".header_search_input_wrapper input, .header_search_ajax_results_wrapper");
			if (!container.is(e.target) && container.has(e.target).length === 0) {
				if (!$('.header_search_input').val()) {
					$('.header_search_ajax_results').html(window.original_results);
				}
			}
		}, 400);

		header_search_results_hiding(e);
		header_search_results_hide(e);
		header_search_border(e);
		header_search_results_reset(e);

	}

	$(document).on('click', function (e) {
		header_search_results_close(e);
	});

	$('.header_search_form').on('click', 'a.view-all', function () {
		$(this).parents('.header_search_form').submit();
	})
	// End Close

	// =============================================================================
	// WP Search
	// =============================================================================

	// Open
	$('.woocommerce-product-search input').on('click', function () {
		$(this).parents('form.woocommerce-product-search').addClass('active');
	});

	$('.search-form input').on('click', function () {
		$(this).parents('form.search-form').addClass('active');
	});

	// Close
	$(document).on('click', function (e) {
		header_wp_search_border(e);
		header_wc_search_border(e);
	});

	var header_wp_search_border = function (e) {
		var container = $('.search-form');
		if (!container.is(e.target) && container.has(e.target).length === 0) {
			container.removeClass('active');
		}
	}

	var header_wc_search_border = function (e) {
		var container = $('.woocommerce-product-search');
		if (!container.is(e.target) && container.has(e.target).length === 0) {
			container.removeClass('active');
		}
	}

})(jQuery);

jQuery(function ($) {

	"use strict";

	// Content Overlay

	var overlay_triggers_list = [

		".gbt-mega-dropdown-wrapper",
		".navigation-foundation > ul > .getetrade_megamenu",
		".navigation-foundation > ul > .is-dropdown-submenu-parent",
		".header-megamenu-placeholder",
		"body:not(.woocommerce-cart):not(.woocommerce-checkout) .header-tools .header-cart",

	];

	var overlay_triggers = overlay_triggers_list.join(", ");

	$(overlay_triggers).on({
		mouseenter: function (e) {
			$('.hover_overlay_content').addClass('visible').trigger("show.gbt.overlay_content");
			$('.hover_overlay_footer').addClass('visible').trigger("show.gbt.overlay_content");
		},
		mouseleave: function (e) {
			$('.hover_overlay_content').removeClass('visible').trigger("hide.gbt.overlay_content");
			$('.hover_overlay_footer').removeClass('visible').trigger("hide.gbt.overlay_content");
		}
	});


	// Header Overlay

	var overlay_triggers_list = [

		".topbar .navigation-foundation > ul > .is-dropdown-submenu-parent"

	];

	var overlay_triggers = overlay_triggers_list.join(", ");

	$(overlay_triggers).on({
		mouseenter: function (e) {
			$('.hover_overlay_header').addClass('visible');
		},
		mouseleave: function (e) {
			$('.hover_overlay_header').removeClass('visible');
		}
	});


	// Remove Overlays on Click

	window.hover_overlay_remove = function (e) {

		// Exceptions
		var containers = [

			".site-header-style-1 .header_search_input_wrapper input",
			".site-header-style-1 .header_search_ajax_results_wrapper",
			".select2-selection",
			".gbt-mega-dropdown-button",
			".gbt-mega-dropdown-content-inside",
			".is-dropdown-submenu",
			".dropdown-pane",
			".getetrade_product_quick_view_button"

		];

		var container = $(containers.join(", "));

		if (!container.is(e.target) && container.has(e.target).length === 0) {
			$('.hover_overlay_content').removeClass('visible');
			$('.hover_overlay_footer').removeClass('visible');
		}
	};

	$(document).on('click', function (e) {
		hover_overlay_remove(e);
	});

});
(function ($) {

	"use strict";

	function getSuggestions() {

		var keyword = $('.header_search_form input.header_search_input').val();
		var category = $('.header_search_form select.header_search_select').val();


		if (keyword.length >= 3 && keyword != keydown) {
			$.xhrPool.abortAll();
			$('.header_search_ajax_results').addClass('loading');
			$('form.header_search_form .header_search_button').addClass('loading');

			if (search_cache[keyword + category] !== undefined) {
				$('.header_search_ajax_results').html('<div class="ajax_results_wrapper">' + search_cache[keyword + category] + '</div>');
				$('.header_search_ajax_results').removeClass('loading');
				$('form.header_search_form .header_search_button').removeClass('loading');
			} else {
				$.ajax({
					type: 'GET',
					url: etradeObj.ajax_url,
					cache: true,
					data: {
						"action": "getetrade_ajax_search",
						"search_keyword": keyword,
						"search_category": category
					},
					dataType: "json",
					contentType: "application/json",
					success: function (results) {
						if ($('header').hasClass('site-header-style-1')) {
							$('.hover_overlay_content').addClass('visible');
							$('.hover_overlay_footer').addClass('visible');
						}
						search_cache[keyword + category] = results.suggestions;
						$('.header_search_ajax_results').html('<div class="ajax_results_wrapper">' + results.suggestions + '</div>');
						$('.header_search_ajax_results').removeClass('loading');
						$('form.header_search_form .header_search_button').removeClass('loading');
					},

					always: function (results) {
						$('.header_search_ajax_results').removeClass('loading');
						$('form.header_search_form .header_search_button').removeClass('loading');
					}
				});
			}
		}
	};

	$.xhrPool = [];
	$.xhrPool.abortAll = function () {
		$(this).each(function (i, jqXHR) {   //  cycle through list of recorded connection
			jqXHR.abort();  //  aborts connection
			$.xhrPool.splice(i, 1); //  removes from list by index
		});
	}
	$.ajaxSetup({
		beforeSend: function (jqXHR) { $.xhrPool.push(jqXHR); }, //  annd connection to list
		complete: function (jqXHR) {
			var i = $.xhrPool.indexOf(jqXHR);   //  get index for current connection completed
			if (i > -1) $.xhrPool.splice(i, 1); //  removes from list by index
		}
	});

	var search_cache = new Array;

	var keydown = $('.header_search_form input.header_search_input').val();

	$('input.header_search_input').on('keydown', function (e) {
		keydown = $(this).val();
	})

	$('input.header_search_input').on('keyup focus', function (e) {
		getSuggestions();
	});

	$('select.header_search_select').on('change', function () {
		var keyword = $('.header_search_form input.header_search_input').val();

		if (keyword.length >= 3) {
			getSuggestions();
			$('input.header_search_input').trigger('click');
		}
	});

	$('form.header_search_form').on('click', 'span.view-all a', function () {
		$(this).parents('form.header_search_form').submit();
	})
})(jQuery);