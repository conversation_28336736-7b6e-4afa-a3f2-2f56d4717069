.fw-backend-option-input-type-rgba-color-picker .fw-option-help-in-input {
    top: 2px !important;
}
.fw-backend-option-input-type-rgba-color-picker .wp-picker-holder {
    position:absolute;
    z-index:99999;
}
.fw-backend-option-input-type-rgba-color-picker .wp-picker-clear {
    height: 23px !important;
}
.fw-backend-option-input-type-rgba-color-picker .wp-color-result {
    box-shadow:none;
}
.fw-backend-option-input-type-rgba-color-picker input {
    max-width: 140px !important;
}
#customize-controls .customize-pane-child .customize-control:last-child .fw-backend-customizer-option .fw-backend-option-type-rgba-color-picker {
    padding-bottom: 220px;
}
/* Picker WP style */
.wp-color-result {
     background-color: #f7f7f7;
     border: 1px solid #ccc;
     -webkit-border-radius: 3px;
     border-radius: 3px;
     cursor: pointer;
     height: 22px;
     margin: 0 6px 6px 0;
     position: relative;
     -webkit-user-select: none;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none;
     vertical-align: bottom;
     display: inline-block;
     padding-left: 30px;
     -webkit-box-shadow: 0 1px 0 #ccc;
     box-shadow: 0 1px 0 #ccc;
     top: 0;
 }
.wp-color-result:focus,
.wp-color-result:hover {
    background: #fafafa;
    border-color: #999;
    color: #23282d;
}
.wp-color-result:after {
    background: #f7f7f7;
    -webkit-border-radius: 0 2px 2px 0;
    border-radius: 0 2px 2px 0;
    border-left: 1px solid #ccc;
    color: #555;
    content: attr(title);
    display: block;
    font-size: 11px;
    line-height: 22px;
    padding: 0 6px;
    position: relative;
    right: 0;
    text-align: center;
    top: 0;
}
.wp-color-result.wp-picker-open:after {
    content: attr(data-current);
}
.wp-color-result:focus:after,
.wp-color-result:hover:after {
    color: #23282d;
    border-color: #a0a5aa;
    border-left: 1px solid #999;
}
.wp-picker-open+.wp-picker-input-wrap {
    display: inline-block;
    vertical-align: top;
}
.wp-picker-container input[type=text].wp-color-picker {
    width: 65px;
    height: 24px;
    font-size: 12px;
    font-family: monospace;
    line-height: 16px;
    margin: 0;
}
.wp-picker-container .button {
    margin-left: 6px;
}