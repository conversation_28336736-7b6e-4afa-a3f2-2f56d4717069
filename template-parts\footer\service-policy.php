<?php
/**
 * Template part for displaying footer top layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options = Helper::axil_get_options();

$ot_service_policy_area = $axil_options['axil_service_policy_section_enable'];

$title1                 = (!empty($axil_options['service_policies_title_text1'])) ? $axil_options['service_policies_title_text1'] : "";
$subtitle1              = (!empty($axil_options['service_policies_subtitle_text1'])) ? $axil_options['service_policies_subtitle_text1'] : "";
$img1                   = (!empty($axil_options['service_policies_page_image1']['url'])) ? $axil_options['service_policies_page_image1']['url'] : "";
$title2                 = (!empty($axil_options['service_policies_title_text2'])) ? $axil_options['service_policies_title_text2'] : "";
$subtitle2              = (!empty($axil_options['service_policies_subtitle_text2'])) ? $axil_options['service_policies_subtitle_text2'] : "";
$img2                   = (!empty($axil_options['service_policies_page_image2']['url'])) ? $axil_options['service_policies_page_image2']['url'] : "";

$title3                 = (!empty($axil_options['service_policies_title_text3'])) ? $axil_options['service_policies_title_text3'] : "";
$subtitle3              = (!empty($axil_options['service_policies_subtitle_text3'])) ? $axil_options['service_policies_subtitle_text3'] : "";
$img3                   = (!empty($axil_options['service_policies_page_image3']['url'])) ? $axil_options['service_policies_page_image3']['url'] : "";

$title4                 = (!empty($axil_options['service_policies_title_text4'])) ? $axil_options['service_policies_title_text4'] : "";
$subtitle4              = (!empty($axil_options['service_policies_subtitle_text4'])) ? $axil_options['service_policies_subtitle_text4'] : "";
$img4                   = (!empty($axil_options['service_policies_page_image4']['url'])) ? $axil_options['service_policies_page_image4']['url'] : "";
 
 if( $axil_options['axil_enable_header_search'] ){
 ?> 
<div class="service-area">
    <div class="container">
        <div class="d-flex flex-wrap justify-content-between">
            <?php if(!empty($axil_options['service_policies_title_text1'])) { ?>
                <div class="col-sm-6 col-lg-3 col-12">
                    <div class="service-box service-style-2">
                        <div class="icon">
                            <img src="<?php echo esc_url( $img1 ); ?>" alt="<?php echo esc_html($title1); ?>">
                        </div>
                        <div class="content">
                            <h6 class="title"><?php echo esc_html($title1); ?></h6>
                            <p><?php echo esc_html($subtitle1); ?></p>
                        </div>
                    </div>
                </div>
            <?php } 
            if(!empty($axil_options['service_policies_title_text2'])) {?>
                <div class="col-sm-6 col-lg-3 col-12">
                    <div class="service-box service-style-2">
                        <div class="icon">
                            <img src="<?php echo esc_url( $img2 ); ?>" alt="<?php echo esc_html($title3); ?>">
                        </div>
                        <div class="content">
                            <h6 class="title"><?php echo esc_html($title2); ?></h6>
                            <p><?php echo esc_html($subtitle2); ?></p>
                        </div>
                    </div>
                </div>
            <?php } 
            if(!empty($axil_options['service_policies_title_text3'])) {?>
                <div class="col-sm-6 col-lg-3 col-12">
                    <div class="service-box service-style-2">
                        <div class="icon">
                            <img src="<?php echo esc_url( $img3 ); ?>" alt="<?php echo esc_html($title3); ?>">
                        </div>
                        <div class="content">
                            <h6 class="title"><?php echo esc_html($title3); ?></h6>
                            <p><?php echo esc_html($subtitle3); ?></p>
                        </div>
                    </div>
                </div>
            <?php } 
            if(!empty($axil_options['service_policies_title_text4'])) {?>
                <div class="col-sm-6 col-lg-3 col-12">
                    <div class="service-box service-style-2">
                        <div class="icon">
                            <img src="<?php echo esc_url( $img4 ); ?>" alt="<?php echo esc_html($title4); ?>">
                        </div>
                        <div class="content">
                            <h6 class="title"><?php echo esc_html($title4); ?></h6>
                            <p><?php echo esc_html($subtitle4); ?></p>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</div>
<?php } ?> 