<?php
/**
 * @param $options
 * dialog
 */
function etrade_confirmation_dialog_options($options)
{
    return array_merge($options, array(
        'width' => 500,
        'dialogClass' => 'wp-dialog',
        'resizable' => false,
        'height' => 'auto',
        'modal' => true,
    ));
}

add_filter('pt-ocdi/confirmation_dialog_options', 'etrade_confirmation_dialog_options', 10, 1);

/**
 * etrade_import_files
 * @return array
 */
function etrade_import_files()
{
    $demo_location = ETRADE_DEMO_CONTENT_URL;
    $preview_image_location = ETRADE_DEMO_PREVIEW_IMAGE_URL;
    $preview_url = 'https://dev.axilthemes.com/themes/etrade';
    return array(
        array(
            'import_file_name' => 'Home - Multipurpose',
            'import_file_url' => $demo_location . 'content.xml',
            'import_widget_file_url' => $demo_location . 'widget.wie',
            'import_customizer_file_url' => $demo_location . 'customizer.json',
            'import_redux' => array(
                array(
                    'file_url' => $demo_location . 'redux_options.json',
                    'option_name' => 'axil_options',
                )
            ),
            'import_preview_image_url' => $preview_image_location . 'home-06.png',
            'preview_url' => $preview_url,
            'import_notice' => esc_html__('After you import this demo, you will have setup all content.', 'etrade'),
        ),
        array(
            'import_file_name' => 'Home - Electronics',
            'import_file_url' => $demo_location . 'content.xml',
            'import_widget_file_url' => $demo_location . 'widget.wie',
            'import_customizer_file_url' => $demo_location . 'customizer.json',
            'import_redux' => array(
                array(
                    'file_url' => $demo_location . 'redux_options.json',
                    'option_name' => 'axil_options',
                )
            ),
            'import_preview_image_url' => $preview_image_location . 'home-01.png',
            'preview_url' => $preview_url . '/home2/',
            'import_notice' => esc_html__('After you import this demo, you will have setup all content.', 'etrade'),
        ),
        array(
            'import_file_name' => 'Home - NFT',
            'import_file_url' => $demo_location . 'content.xml',
            'import_widget_file_url' => $demo_location . 'widget.wie',
            'import_customizer_file_url' => $demo_location . 'customizer.json',
            'import_redux' => array(
                array(
                    'file_url' => $demo_location . 'redux_options.json',
                    'option_name' => 'axil_options',
                )
            ),
            'import_preview_image_url' => $preview_image_location . 'home-02.png',
            'preview_url' => $preview_url . '/home3/',
            'import_notice' => esc_html__('After you import this demo, you will have setup all content.', 'etrade'),
        ),
        array(
            'import_file_name' => 'Home - Fashion',
            'import_file_url' => $demo_location . 'content.xml',
            'import_widget_file_url' => $demo_location . 'widget.wie',
            'import_customizer_file_url' => $demo_location . 'customizer.json',
            'import_redux' => array(
                array(
                    'file_url' => $demo_location . 'redux_options.json',
                    'option_name' => 'axil_options',
                )
            ),
            'import_preview_image_url' => $preview_image_location . 'home-03.png',
            'preview_url' => $preview_url . '/home4/',
            'import_notice' => esc_html__('After you import this demo, you will have setup all content.', 'etrade'),
        ),
        array(
            'import_file_name' => 'Home - Jewellery',
            'import_file_url' => $demo_location . 'content.xml',
            'import_widget_file_url' => $demo_location . 'widget.wie',
            'import_customizer_file_url' => $demo_location . 'customizer.json',
            'import_redux' => array(
                array(
                    'file_url' => $demo_location . 'redux_options.json',
                    'option_name' => 'axil_options',
                )
            ),
            'import_preview_image_url' => $preview_image_location . 'home-04.png',
            'preview_url' => $preview_url . '/home5/',
            'import_notice' => esc_html__('After you import this demo, you will have setup all content.', 'etrade'),
        ),
        array(
            'import_file_name' => 'Home - Furniture',
            'import_file_url' => $demo_location . 'content.xml',
            'import_widget_file_url' => $demo_location . 'widget.wie',
            'import_customizer_file_url' => $demo_location . 'customizer.json',
            'import_redux' => array(
                array(
                    'file_url' => $demo_location . 'redux_options.json',
                    'option_name' => 'axil_options',
                )
            ),
            'import_preview_image_url' => $preview_image_location . 'home-05.png',
            'preview_url' => $preview_url . '/home4/',
            'import_notice' => esc_html__('After you import this demo, you will have setup all content.', 'etrade'),
        ),  
         
        
    );
}

add_filter('pt-ocdi/import_files', 'etrade_import_files');

/**
 * etrade_before_widgets_import
 * @param $selected_import
 */
function etrade_before_widgets_import($selected_import)
{

    // Remove 'Hello World!' post
    wp_delete_post(1, true);
    // Remove 'Sample page' page
    wp_delete_post(2, true);

    $sidebars_widgets = get_option('sidebars_widgets');
    $sidebars_widgets['sidebar'] = array();
    update_option('sidebars_widgets', $sidebars_widgets);

}

add_action('pt-ocdi/before_widgets_import', 'etrade_before_widgets_import');

/*
 * Automatically assign
 * "Front page",
 * "Posts page" and menu
 * locations after the importer is done
 */
function etrade_after_import_setup($selected_import)
{

    $demo_imported = get_option('etrade_demo_imported');

    $cpt_support = get_option('elementor_cpt_support');
    $elementor_disable_color_schemes = get_option('elementor_disable_color_schemes');
    $elementor_disable_typography_schemes = get_option('elementor_disable_typography_schemes');

     $elementor_container_width =  \Elementor\Plugin::$instance->kits_manager->get_current_settings( 'container_width' );

    //print_r($elementor_container_width);
    //$elementor_container_width = get_option('elementor_container_width');

    //check if option DOESN'T exist in db
    if (!$cpt_support) {
        $cpt_support = ['page', 'post', 'product', 'elementor_disable_color_schemes']; //create array of our default supported post types
        update_option('elementor_cpt_support', $cpt_support); //write it to the database
    }
    if (empty($elementor_disable_color_schemes)) {
        update_option('elementor_disable_color_schemes', 'yes'); //update database
    }
    if (empty($elementor_disable_typography_schemes)) {
        update_option('elementor_disable_typography_schemes', 'yes'); //update database
    }
    if (empty($elementor_container_width)) {
        update_option('elementor_container_width', '1320'); //update database
    }



    $elementor_general_settings = array(
        'container_width' => (!empty($elementor_container_width)) ? $elementor_container_width : '1320',
    );
    update_option('_elementor_general_settings', $elementor_general_settings); //update database

    // Update Global Css Options For Elementor
    $currentTime = strtotime("now");
    $elementor_global_css = array(
        'time' => $currentTime,
        'fonts' => array()
    );
    update_option('_elementor_global_css', $elementor_global_css); //update database

    update_option('etrade_elementor_custom_setting_imported', 'elementor_custom_setting_imported');

    update_option('mc4wp_default_form_id', '25'); //update database


    if (empty($demo_imported)) {

        // Home page selected
        if ('Home - Multipurpose' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Multipurpose');
            update_option('etrade_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Electronics' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Electronics');
            update_option('etrade_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - NFT' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - NFT');
            update_option('etrade_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Fashion' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Fashion');
            update_option('etrade_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Jewellery' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Jewellery');
            update_option('etrade_theme_active_demo', $selected_import['import_file_name']);
        } elseif ('Home - Furniture' === $selected_import['import_file_name']) {
            $front_page_id = get_page_by_title('Home - Furniture');
            update_option('etrade_theme_active_demo', $selected_import['import_file_name']);
        }

 

        $blog_page_id = get_page_by_title('Blog Grid');
        update_option('show_on_front', 'page');
        update_option('page_on_front', $front_page_id->ID);
        update_option('page_for_posts', $blog_page_id->ID);
        update_option('etrade_demo_imported', 'imported');
    }

    // Set Menu As Primary && Off Canvus Menu
    $main_menu = get_term_by('name', 'Primary Menu', 'nav_menu');
    $cat_icon_menu = get_term_by('name', 'Category Icon Menu', 'nav_menu');
    $icon_menu = get_term_by('name', 'Icon With Text Menu', 'nav_menu');
    $headertop = get_term_by('name', 'Top Quick Link', 'nav_menu');
    $footerbottom = get_term_by('name', 'Footer Menu', 'nav_menu');
    $myaccount = get_term_by('name', 'Account Menu', 'nav_menu');

    set_theme_mod('nav_menu_locations', array(
        'primary' => $main_menu->term_id,
        'categoryiconmenu' => $cat_icon_menu->term_id,
        'iconmenu' => $icon_menu->term_id,
        'headertop' => $headertop->term_id,
        'footerbottom' => $footerbottom->term_id,
        'myaccount' => $myaccount->term_id
    ));

}

add_action('pt-ocdi/after_import', 'etrade_after_import_setup');


/**
 * time_for_one_ajax_call
 * @return int
 */
function etrade_change_time_of_single_ajax_call()
{
    return 20;
}

add_action('pt-ocdi/time_for_one_ajax_call', 'etrade_change_time_of_single_ajax_call');


// To make demo imported items selected
add_action('admin_footer', 'etrade_pt_ocdi_add_scripts');
function etrade_pt_ocdi_add_scripts()
{
    $demo_imported = get_option('etrade_theme_active_demo');
    if (!empty($demo_imported)) {
        ?>
        <script>
            jQuery(document).ready(function ($) {
                $('.ocdi__gl-item.js-ocdi-gl-item').each(function () {
                    var ocdi_theme_title = $(this).data('name');
                    var current_ocdi_theme_title = '<?php echo strtolower($demo_imported); ?>';
                    if (ocdi_theme_title == current_ocdi_theme_title) {
                        $(this).addClass('active_demo');
                        return false;
                    }
                });
            });
        </script>
        <?php
    }
}

/**
 * Remove ads
 */
add_filter('pt-ocdi/disable_pt_branding', '__return_true');



