<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

$axil_options = Helper::axil_get_options();
?>
<?php if( WOOC_WOO_ACTIVED ):?>
    <?php if(  $axil_options['axil_enable_cart'] ):?>
         <?php global $woocommerce; ?>
           <li class="shopping-cart shopping-items">  
               <a class="cart-icon-area  cart-dropdown-btn" href="#">   
                 <?php
                 $cartCount = $woocommerce->cart->get_cart_contents_count();
                 $className = $count = '';
                 if ($cartCount > 0) {
                    $className = 'cart-count';
                    $count = $cartCount;
                 } 
                 ?> 
                <span class="<?php echo esc_attr( $className); ?> header-cart-num"> <?php echo esc_html($count); ?></span>
               <i class="fal fa-shopping-cart"></i>
           </a>  
           </li>
    <?php endif ?> 
<?php endif ?>  