<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Group_Control_Css_Filter;
use Elementor\Repeater;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Box_Shadow;
use Elementor\Group_Control_Image_Size;
use Elementor\Utils;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly
class Login_Image_Map extends Widget_Base {
   
    public function get_name() {
        return 'etrade-img-map';
    }
    
    public function get_title() {
        return __( 'Image Map', 'etrade-elements' );
    }

    public function get_icon() {
        return 'eicon-image-before-after';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
    protected function register_controls() {
          $this->start_controls_section(
            'img_map_content',
            [
                'label' => __( 'Image Map', 'etrade-elements' ),
            ]
        );    
		$this->add_control(
		    'image',
		    [
		        'label' => __('Image','etrade-elements'),
		        'type'=>Controls_Manager::MEDIA,
		        'default' => [
		            'url' => Utils::get_placeholder_image_src(),
		        ],
		        'dynamic' => [
		            'active' => true,
		        ],
		        'selectors' => [					
					'{{WRAPPER}} .axil-signin-banner' => 'background-image: url({{URL}});',
				],
		            
		    ]
		);		
	   $this->add_control(
	        'info_title',
	        [
	            'label'   => __( 'Title', 'etrade-elements' ),
	            'type'    => Controls_Manager::TEXT,
	            'default' => 'We Offer the Best Products',
	        ]
	    );
  		$this->add_control(
	        'info_contact',
	        [
	            'label'   => __( 'Content', 'etrade-elements' ),
	            'type'    => Controls_Manager::TEXTAREA,
	            'default' => 'Built with a good hand',
	        ]
	    );		           
	    $this->end_controls_section();	
	}
	
    protected function render() {
		$settings = $this->get_settings();  ?>	
			<div class="axil-signin-banner bg_image bg_image--9">
				<h3 class="title"><?php echo esc_attr( $settings['info_title'] );?></h3>
				<?php if ( $settings['info_contact'] ): ?>
					<p><?php echo esc_attr( $settings['info_contact'] );?></p>
				<?php endif; ?>	
	</div>
	   <?php   }
	}