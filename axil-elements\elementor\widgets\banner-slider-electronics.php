<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;
use Elementor\Repeater;
use <PERSON>ementor\Widget_Base;

if ( !defined( 'ABSPATH' ) ) {
    exit;
}
// Exit if accessed directly

class axil_banner_slider_electronics extends Widget_Base {

    public function get_name() {
        return 'axil-banner-slider-electronics';
    }
    public function get_title() {
        return __( 'Banner Slider - Electronics', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-banner';
    }
    public function get_categories() {
        return array( ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' );
    }
    public function axil_get_img( $img ) {
        $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
        return $img;
    }
    private function axil_get_all_pages() {

        $page_list = get_posts( array(
            'post_type'      => 'page',
            'orderby'        => 'date',
            'order'          => 'DESC',
            'posts_per_page' => -1,
        ) );

        $pages = array();

        if ( !empty( $page_list ) && !is_wp_error( $page_list ) ) {
            foreach ( $page_list as $page ) {
                $pages[$page->ID] = $page->post_title;
            }
        }

        return $pages;
    }
    private function wooc_cat_dropdown() {
        $terms = get_terms( array( 'taxonomy' => 'product_cat' ) );
        $category_dropdown = array( '0' => esc_html__( 'All Categories', 'etrade-elements' ) );

        foreach ( $terms as $category ) {
            $category_dropdown[$category->term_id] = $category->name;
        }

        return $category_dropdown;
    }
    public function get_post_template( $type = 'page' ) {
        $posts = get_posts(
            array(
                'post_type'      => 'elementor_library',
                'orderby'        => 'title',
                'order'          => 'ASC',
                'posts_per_page' => '-1',
                'tax_query'      => array(
                    array(
                        'taxonomy' => 'elementor_library_type',
                        'field'    => 'slug',
                        'terms'    => $type,
                    ),
                ),
            )
        );
        $templates = array();
        foreach ( $posts as $post ) {
            $templates[] = array(
                'id'   => $post->ID,
                'name' => $post->post_title,
            );
        }
        return $templates;
    }

    public function get_saved_data( $type = 'section' ) {
        $saved_widgets = $this->get_post_template( $type );
        $options[-1] = esc_html__( 'Select', 'etrade-elements' );
        if ( count( $saved_widgets ) ) {
            foreach ( $saved_widgets as $saved_row ) {
                $options[$saved_row['id']] = $saved_row['name'];
            }
        } else {
            $options['no_template'] = esc_html__( 'It seems that, you have not saved any template yet.', 'etrade-elements' );
        }
        return $options;
    }
    protected function register_controls() {

        $this->start_controls_section(
            'axilbanner_content_sec',
            array(
                'label' => esc_html__( ' Banner Content', 'etrade-elements' ),

            )
        );

        $repeater = new Repeater();

        $repeater->add_control(
            'beforetitlestyle',
            array(
                'label'   => esc_html__( 'Before Color', 'etrade-elements' ),
                'type'    => Controls_Manager::SELECT,
                'default' => 'primary',
                'options' => array(
                    'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                    'secondary' => esc_html__( 'Secondary', 'etrade-elements' ),
                    'primary2'  => esc_html__( 'Primary 2', 'etrade-elements' ),
                ),
            )
        );

        $repeater->add_control(
            'list_before',
            array(
                'label'       => esc_html__( 'Before Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => esc_html__( 'Hot Deal In This Week', 'etrade-elements' ),
                'label_block' => true,

            )
        );

        $repeater->add_control(
            'before_icon',
            array(
                'label'   => esc_html__( 'Before Title Icons', 'etrade-elements' ),
                'type'    => Controls_Manager::ICONS,
                'default' => array(
                    'value'   => 'fas fa-fire',
                    'library' => 'solid',
                ),

            )
        );

        $repeater->add_control(
            'list_title',
            array(
                'label'       => esc_html__( 'Slider Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXTAREA,
                'default'     => esc_html__( 'Roco Wireless Headphone', 'etrade-elements' ),
                'label_block' => true,
            )
        );

        $repeater->add_control(
            'list_prices_label',
            array(
                'label'       => esc_html__( 'Label', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => esc_html__( 'From', 'etrade-elements' ),
                'label_block' => true,
            )
        );
        $repeater->add_control(
            'list_prices',
            array(
                'label'       => esc_html__( 'prices', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => esc_html__( '$456', 'etrade-elements' ),
                'label_block' => true,
            )
        );

        $repeater->add_control(
            'image',
            array(
                'label'   => esc_html__( 'Product Image', 'etrade-elements' ),
                'type'    => Controls_Manager::MEDIA,
                'default' => array(
                    'url' => $this->axil_get_img( 'product-38.png' ),
                ),
                'dynamic' => array(
                    'active' => true,
                ),

            )
        );

        $repeater->add_group_control(
            Group_Control_Image_Size::get_type(),
            array(
                'name'      => 'image_size',
                'default'   => 'full',
                'separator' => 'none',

            )
        );

        $repeater->add_control(
            'btntext',
            array(
                'label'     => __( 'Button Text', 'etrade-elements' ),
                'type'      => Controls_Manager::TEXT,
                'default'   => 'Shop Now',
                'separator' => 'before',

            )
        );

        $repeater->add_control(
            'axil_link_type',
            array(
                'label'       => esc_html__( 'See All Link Type', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT,
                'options'     => array(
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ),
                'default'     => '1',

                'label_block' => true,
            )
        );

        $repeater->add_control(
            'axil_page_link',
            array(
                'label'       => esc_html__( 'Select See All Page', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT2,
                'label_block' => true,
                'options'     => $this->axil_get_all_pages(),
                'condition'   => array(
                    'axil_link_type' => '2',

                ),
            )
        );

        $repeater->add_control(
            'url',
            array(
                'label'       => __( 'Detail URL', 'etrade-elements' ),
                'type'        => Controls_Manager::URL,
                'placeholder' => 'https://your-link.com',
                'condition'   => array(
                    'axil_link_type' => '1',

                ),
            )
        );

        $this->add_control(
            'list',
            array(
                'label'       => esc_html__( 'Slider List', 'etrade-elements' ),
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $repeater->get_controls(),

                'default'     => array(
                    array(
                        'list_before' => esc_html__( 'Hot Deal In This Week', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Roco Wireless Headphone', 'etrade-elements' ),
                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-38.png' ),
                        ),

                    ),
                    array(
                        'list_before' => esc_html__( 'Hot Deal In This Week', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Smart Digital Watch', 'etrade-elements' ),
                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-31.png' ),
                        ),

                    ),

                    array(
                        'list_before' => esc_html__( 'Hot Deal In This Week', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Roco Wireless Headphone', 'etrade-elements' ),
                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-39.png' ),
                        ),

                    ),
                    array(
                        'list_before' => esc_html__( 'Hot Deal In This Week', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Roco Wireless Headphone', 'etrade-elements' ),
                        'image'       => array(
                            'url' => $this->axil_get_img( 'poster-04.png' ),
                        ),

                    ),
                ),
                'title_field' => '{{{ list_title }}}',
            )
        );
        $this->end_controls_section();

        $this->start_controls_section(
            'subtitle_style_section',
            array(
                'label' => esc_html__( 'Title Before', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,

            )
        );

        $this->add_control(
            'subtitle_color',
            array(
                'label'     => esc_html__( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => array(
                    '{{WRAPPER}} .main-slider-content .subtitle'   => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .subtitle i' => 'background-color: {{VALUE}}',
                ),
            )
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'subtitle_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .main-slider-content .subtitle',
            )
        );

        $this->add_responsive_control(
            'subtitle_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .main-slider-content .subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'title_style_section',
            array(
                'label' => esc_html__( 'Title', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'title_color',
            array(
                'label'     => esc_html__( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .inner .title'               => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .title' => 'color: {{VALUE}}',
                ),
            )
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'title_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .inner .title , {{WRAPPER}} .main-slider-content .title',
            )
        );

        $this->add_responsive_control(
            'title_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .inner .title'               => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .main-slider-content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'button_style_section',
            array(
                'label' => esc_html__( 'Button', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'button_color',
            array(
                'label'     => esc_html__( 'Text Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .main-slider-content .shop-btn a'   => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .shop-btn a i' => 'color: {{VALUE}}',

                ),
            )
        );
        $this->add_control(
            'button_bg_color',
            array(
                'label'     => esc_html__( 'Background Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .main-slider-content .shop-btn a'        => 'background: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .shop-btn a:before' => 'background: {{VALUE}}',

                ),
            )
        );
        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'button_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .main-slider-content .shop-btn a, {{WRAPPER}} .main-slider-content .shop-btn a i',
            )
        );

        $this->add_responsive_control(
            'button_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .main-slider-content .shop-btn' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'poster_shape10',
            array(
                'label' => esc_html__( 'Background Shapes', 'etrade-elements' ),

            )
        );
        $this->add_control(
            'shape_style_on',
            array(
                'label'     => esc_html__( 'Shape Condition', 'etrade-elements' ),
                'type'      => Controls_Manager::SWITCHER,
                'label_on'  => esc_html__( 'On', 'etrade-elements' ),
                'label_off' => esc_html__( 'Off', 'etrade-elements' ),
                'default'   => 'no',

            )
        );
        $this->add_control(
            'shape1',
            array(
                'label'     => esc_html__( 'Shape 1', 'etrade-elements' ),
                'type'      => Controls_Manager::MEDIA,
                'default'   => array(
                    'url' => $this->axil_get_img( 'shape-1.png' ),
                ),
                'condition' => array( 'shape_style_on' => array( 'yes' ) ),
                'dynamic'   => array(
                    'active' => true,
                ),

            )
        );
        $this->add_control(
            'shape2',
            array(
                'label'     => esc_html__( 'Shape 2', 'etrade-elements' ),
                'type'      => Controls_Manager::MEDIA,
                'default'   => array(
                    'url' => $this->axil_get_img( 'shape-2.png' ),
                ),
                'condition' => array( 'shape_style_on' => array( 'yes' ) ),
                'dynamic'   => array(
                    'active' => true,
                ),

            )
        );

        $this->end_controls_section();

    }

    private function slick_load_scripts() {
        wp_enqueue_style( 'slick-theme' );
        wp_enqueue_style( 'slick' );
        wp_enqueue_script( 'slick' );
    }

    protected function render() {
        $settings = $this->get_settings();
        $this->slick_load_scripts();

        $template = 'banner-slider-electronics';
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
    }

}