<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

global $product;

$product_id = $product->get_id();
$is_in_wishlist = YITH_WCWL()->is_product_in_wishlist( $product_id, false );
$wishlist_url = YITH_WCWL()->get_wishlist_url();
$title_before = esc_html__( 'Add to Wishlist', 'etrade' );
$title_after = esc_html__( 'Added to Wishlist', 'etrade' );

if ( $is_in_wishlist ) {
    $class = 'axil-remove-from-wishlist';
    $icon_font = 'fas fa-heart';
    $title = $title_after;
} else {
    $class = 'axil-add-to-wishlist';
    $icon_font = 'far fa-heart';
    $title = $title_before;
}

$html = '';

if ( $icon ) {
    $html .= '<i class="wishlist-icon ' . $icon_font . '"></i>';
}

if ( $text ) {
    $html .= '<span class="wishlist-label">' . esc_html__( 'WishList', 'etrade' ) . '</span>';
}
$html .= '<i class="fas fa-spinner ajax-loading"></i>';
if ( $text ) {
    $html .= '<span>' . esc_html__( 'WishList', 'etrade' ) . '</span>';
}
$nonce = wp_create_nonce( 'add_to_wishlist' );
$allowed_tags = wp_kses_allowed_html( 'post' );
?>
<a href="<?php echo esc_url( $wishlist_url ); ?>" rel="nofollow"
   data-product-id="<?php echo esc_attr( $product_id ); ?>" data-title-after="<?php echo esc_attr( $title_after ); ?>"
   class="axil-wishlist-icon <?php echo esc_attr( $class ); ?>" data-nonce="<?php echo esc_attr( $nonce ); ?>"
   target="_blank">
	<?php echo wp_kses( $html, $allowed_tags ); ?>
</a>
