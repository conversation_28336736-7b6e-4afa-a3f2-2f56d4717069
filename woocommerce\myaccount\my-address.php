<?php
/**
 * My Addresses
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/my-address.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.3.0
 */

defined( 'ABSPATH' ) || exit;

$customer_id = get_current_user_id();
$allowed_tags = wp_kses_allowed_html( 'post' );

if ( !wc_ship_to_billing_address_only() && wc_shipping_enabled() ) {
    $get_addresses = apply_filters(
        'woocommerce_my_account_get_addresses',
        array(
            'billing'  => esc_html__( 'Billing address', 'etrade' ),
            'shipping' => esc_html__( 'Shipping address', 'etrade' ),
        ),
        $customer_id
    );
} else {
    $get_addresses = apply_filters(
        'woocommerce_my_account_get_addresses',
        array(
            'billing' => esc_html__( 'Billing address', 'etrade' ),
        ),
        $customer_id
    );
}

$oldcol = 1;
$col = 1;
?>
<div class="axil-dashboard-address">
<p class="notice-text">
	<?php echo apply_filters( 'woocommerce_my_account_my_address_description', esc_html__( 'The following addresses will be used on the checkout page by default.', 'etrade' ) ); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped  ?>
</p> 
<?php if ( !wc_ship_to_billing_address_only() && wc_shipping_enabled() ): ?>
	<div class="u-columns woocommerce-Addresses col2-set addresses row">
<?php endif;?>

<?php foreach ( $get_addresses as $name => $address_title ): ?>
	<?php
		$address = wc_get_account_formatted_address( $name );
		$col = $col * -1;
		$oldcol = $oldcol * -1;
		?>

	<div class="u-column<?php echo esc_html( $col < 0 ? 1 : 2 ); ?> col-lg-<?php echo esc_html( $oldcol < 0 ? 6 : 6 ); ?> woocommerce-Address">
		<div class="address-info mb--40">
			<header class="woocommerce-Address-title title addrss-header d-flex align-items-center justify-content-between">
				<h4 class="title mb-0"><?php echo esc_html( $address_title ); ?></h4>
				<a href="<?php echo esc_url( wc_get_endpoint_url( 'edit-address', $name ) ); ?>" class="edit address-edit">
					<?php $address = $address ? '<i class="far fa-edit"></i>' : esc_html__( 'Add', 'etrade' );
						echo wp_kses( $address, $allowed_tags );?>
					</a>
			</header> 
			<address class="address-details">
				<?php
				$address = $address ? wp_kses( $address, $allowed_tags ) : esc_html_e( 'You have not set up this type of address yet.', 'etrade' );
				echo wp_kses( $address, $allowed_tags );
				?>
			</address>
		</div>
	</div> 
	<?php endforeach;?> 
	<?php if ( !wc_ship_to_billing_address_only() && wc_shipping_enabled() ): ?>
	</div>
	<?php
endif;?>
</div>

