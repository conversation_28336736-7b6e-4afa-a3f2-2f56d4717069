<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

if ( !woocommerce_products_will_display() ) {
    return;
}
$wooc_has_sidebar = Helper::wooc_has_sidebar();?>
  <div class="axil-shop-top">
     <?php
if ( $wooc_has_sidebar ) {
    ?>
        <div class="row">
            <div class="col-12 ">
        	        <div class="category-select align-items-center justify-content-end">
        	        	<?php do_action( 'etrade_woocommerce_result_count' );?>
        	            <div class="sort-list"><?php woocommerce_catalog_ordering();?></div>
        	        </div>
                    <div class="d-lg-none">
                    <button class="product-filter-mobile filter-toggle">
                        <i class="fas fa-filter"></i>
                        <?php echo esc_attr__( 'FILTER', 'etrade' ); ?>
                    </button>
                </div>
            </div>
         </div>
        <?php } else {?>
        <div class="row">
            <div class="col-lg-9">
                <div class="category-select">
        		<?php
                    if ( is_active_sidebar( 'widgets-shop-header' ) ) {
                        dynamic_sidebar( 'widgets-shop-header' );
                    } ?>
                </div>
            </div>
            <div class="col-lg-3">
                <div class="category-select justify-content-lg-end">
                    <div class="sort-list"><?php woocommerce_catalog_ordering();?></div>
                </div>
            </div>
        </div>
    <?php }?>
</div>