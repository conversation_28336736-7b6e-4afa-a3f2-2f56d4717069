<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;
use Elementor\Group_Control_Image_Size;
$query = $settings['query'];

if ( !empty( $settings['cat'] ) ) {
	$shop_permalink = get_category_link( $settings['cat'] );
}
else {
	$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
}
$sub_title       = $settings['sub_title'] ? ' sub_title' : ' no-sub_title';
$block_data = array(
	'layout'         			=> $settings['style'],	
	'rating_display' 			=> $settings['rating_display'] ? true : false,		
	'display_attributes'  		=> $settings['display_attributes'] ? true : false,	
 
	'product_display_hover' 	=> $settings['product_display_hover'],
	'sale_price_only'        	=> $settings['sale_price_only'] ? true : false,
);

if ( $block_data['layout'] == "3") {
	$slwVal =' slick-layout-wrapper--15 ';
} else {	
	$slwVal =' slick-layout-wrapper--15 ';
}
$wrapper_start = '<div class="slick-single-layout slick-slide">';
$wrapper_end   = '</div>';
$wrapper_flashstart = '';
$wrapper_flashend   = '';
if ( $settings['iscountdown'] ): 
	$wrapper_flashstart = '<div class="d-md-flex align-items-end flash-sale-section axilcoutdown2">';
	$wrapper_flashend   = '</div>';
endif;  
$iscountdown = $settings['iscountdown'] ? 'is-countdown' : 'is-not-countdown';

$slider_btn_style = $settings['slider_btn_style'] == '1' ? 'axil-slick-arrow' : 'axil-slick-angle';
$title_style = $settings['title_style'] == '2' ? 'title-border-style' : 'title-non-border-style';
?>
 <div class="axil-new-arrivals-product-area axil-new-arrivals-activation bg-color-white <?php echo esc_attr( $iscountdown ); ?> <?php echo esc_attr( $title_style ); ?>">
    <div class="ml--0">
        <div class="product-wrp">
	       <?php if ( $settings['section_title_display'] ): ?> 
					<?php if ( '1' == $settings['title_style'] ) { ?>	 
				       <?php echo wp_kses_post( $wrapper_flashstart );?> 
			            	<div class="section-title-wrapper">
				            	<?php if ( $settings['sub_title'] ): ?>
				                	<span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?>"> <?php Icons_Manager::render_icon( $settings['icon'] ); ?> <?php echo wp_kses_post( $settings['sub_title'] );?></span>
				                  <?php endif; ?>	
				               
							<?php  if($settings['title']){ ?>
								<<?php echo esc_html( $settings['sec_title_tag'] );?> class="sec-title mb--0"><?php echo wp_kses_post( $settings['title'] );?></<?php echo esc_html( $settings['sec_title_tag'] );?>>   
							<?php  } ?> 
				            </div>                      
				            <?php if ( $settings['iscountdown'] ): ?>
					            <?php if ( $settings['date'] ): ?>
									<div class="sale-countdown countdown" data-time="<?php echo esc_attr( $settings['date'] ); ?>"></div>
								<?php endif; ?>
							<?php endif; ?>
		      			<?php echo wp_kses_post( $wrapper_flashend );?>
	 
					<?php }else{ ?>
					<div class="section-title-wrapper section-title-border"> 
					    <<?php echo esc_html( $settings['sec_title_tag'] );?> class="sec-title title">
					    <?php echo esc_html( $settings['title'] );?>
					    <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?> 
					    </<?php echo esc_html( $settings['sec_title_tag'] );?>>  
					</div>
					<?php } ?>	 
				<?php endif; ?>	

				<?php if ( $query->have_posts() ) : ?>
					<div class="new-arrivals-product-activation <?php echo esc_attr( $slwVal );?> <?php echo esc_attr( $slider_btn_style );?> arrow-top-slide">
						<?php
						while ( $query->have_posts() ) {
								$query->the_post();
								$id = get_the_ID();
								$product = wc_get_product( $id );	
								$number = $settings['number'];
								$number_off_row = $settings['number_off_row'];	
								
								echo wp_kses_post( $wrapper_start);

									wc_get_template( "custom/product-block/blocks.php" , compact( 'product', 'block_data' ) );	

								echo wp_kses_post( $wrapper_end);

							}
						?>
					</div>								
				<?php else:?>
					<div><?php esc_html_e( 'No products available', 'etrade-elements' ); ?></div>
				<?php endif;?>         
            </div>
        </div>
    </div>     
<?php wp_reset_postdata();?>
