<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;
 
  $thumb_size = 'axil-cat-thumbnail-sm';
  $shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
  $hide_empty_category       = $settings['hide_empty_category'] ? $settings['hide_empty_category'] : 0;
  $sub_title                 = $settings['sub_title'] ? ' sub_title' : ' no-sub_title';
  $cate_list                  = $settings['select_categories'];
  $sub_cats_list              = $settings['sub_select_categories'];
  $number                     = $settings['number']; 
  $sliderautoplay    = $settings['sliderautoplay'] ? true : false; 

  $slidesToShow       = $settings['slidesToShow'] ? $settings['slidesToShow'] : '6'; 
  $slidesToScroll     = $settings['slidesToScroll'] ? $settings['slidesToScroll'] : '6'; 
  $sliderinfinite     = $settings['sliderinfinite'] ? true : false; 
  
   $carasolArray = json_encode([
      "infinite"        => $sliderinfinite,
      "slidesToShow"    => $slidesToShow,
      "slidesToScroll"  => $slidesToScroll,
      "arrows"          => true,
      "dots"            => false,
       "autoplay"        => $sliderautoplay,
      "speed"           => 1000,  
      "prevArrow"       => '<button class="slide-arrow prev-arrow"><i class="fal fa-long-arrow-left"></i></button>',
      "nextArrow"       => '<button class="slide-arrow next-arrow"><i class="fal fa-long-arrow-right"></i></button>',
      "responsive" => [
        [
          "breakpoint" => 1199,
          "settings" => [
            "slidesToShow" => 5,
            "slidesToScroll" => 5
          ]
        ],
        [
          "breakpoint" => 991,
          "settings" => [
            "slidesToShow" => 4,
            "slidesToScroll" => 4
          ]
        ],
        [
          "breakpoint" => 767,
          "settings" => [
            "slidesToShow" => 3,
            "slidesToScroll" => 3
          ]
        ],
        [
          "breakpoint" => 575,
          "settings" => [
            "slidesToShow" => 2,
            "slidesToScroll" => 2
          ]
        ],
      ]
    ]);


if ( $settings['show_only_sub_category'] ){
 
      $parent_cat = array(
        'parent' => 0,
      );

      $filter_cat_arg = array(
        'include'    => $cate_list,
      );

      $cat_arg = array(
        'taxonomy'   => 'product_cat',
        'hide_empty' => 1,
        'orderby'    => 'date',
        'order'      => 'DESC',
        'number'     => $number,
        'hide_empty' => $hide_empty_category,

      );
      $cat_args    = array_merge( $cat_arg, $filter_cat_arg );
      $product_categories   = get_categories( $cat_args );

?>
<div class="axil-categorie-area has-axil-categrie-area bg-color-white axil-section-gapcommo is-cat-slider">  
    <?php if ( $settings['section_title_display'] ): ?>
      <div class="section-title-wrapper">
        <?php if ( $settings['sub_title'] ): ?>
          <span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?>"> 
            <?php Icons_Manager::render_icon( $settings['icon'] ); ?>
            <?php echo wp_kses_post( $settings['sub_title'] );?> 
           </span>
            <?php endif; ?> 
            <?php  if($settings['title']){ ?>
              <<?php echo esc_html( $settings['sec_title_tag'] );?> class="sec-title title"><?php echo wp_kses_post( $settings['title'] );?></<?php echo esc_html( $settings['sec_title_tag'] );?>>   
            <?php  } ?> 
      </div>
    <?php endif; ?>
     <div data-slick='<?= $carasolArray; ?>' class="categrie-product-activation slick-layout-wrapper--15 axil-slick-arrow  <?php echo esc_attr( $settings['nav_style'] );?>">           
   <?php 
        foreach ($product_categories as $product_category) :
          $cat_thumb = get_term_meta($product_category->term_id, 'thumbnail_id', true);  
          $cat_thumnail_url  = wp_get_attachment_image(  $cat_thumb, $thumb_size, " ", array( "class" => "img-responsive" ) );  
          ?>
           <div class="slick-single-layout slick-slide">
             <div class="categrie-product categrie-product-3">
                  <a href="<?php echo get_term_link($product_category->term_id);?>">
                     <?php if ( $settings['cat_image_show'] ): ?>
                      <?php echo wp_kses_post( $cat_thumnail_url );?>
                    <?php endif; ?>
                      <h6 class="cat-title"><?php echo esc_html( $product_category->name );?></h6>
                  </a>
              </div>               
          </div>
     <?php  endforeach; ?>            
  </div>
</div>
<?php 

}else{

  $taxonomy = 'product_cat';
  $subcategories = get_terms( $taxonomy, array(
    'parent'     => $sub_cats_list,
    'orderby'    => 'name',
    'order'      => 'asc', 
    'number'     => $number,
    'hide_empty' => $hide_empty_category,
  ) );
  ?>  
  <div class="axil-categorie-area has-axil-categrie-area bg-color-white axil-section-gapcommo is-cat-slider">  
      <?php if ( $settings['section_title_display'] ): ?>
        <div class="section-title-wrapper">
          <?php if ( $settings['sub_title'] ): ?>
            <span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?>"> 
                <?php Icons_Manager::render_icon( $settings['icon'] ); ?>
                <?php echo wp_kses_post( $settings['sub_title'] );?>                
             </span>
              <?php endif; ?> 
              <?php  if($settings['title']){ ?>
              <<?php echo esc_html( $settings['sec_title_tag'] );?> class="sec-title title"><?php echo wp_kses_post( $settings['title'] );?></<?php echo esc_html( $settings['sec_title_tag'] );?>>   
            <?php  } ?> 
        </div>
      <?php endif; ?>
       <div data-slick='<?= $carasolArray; ?>' class="categrie-product-activation slick-layout-wrapper--15 axil-slick-arrow  <?php echo esc_attr( $settings['nav_style'] );?>">           
     <?php 
            foreach ($subcategories as $product_category) :
              $cat_thumb = get_term_meta($product_category->term_id, 'thumbnail_id', true); 
              $cat_thumnail_url  = wp_get_attachment_image(  $cat_thumb, $thumb_size, " ", array( "class" => "img-responsive" ) ); 
            ?>
             <div class="slick-single-layout slick-slide">
                <div class="categrie-product categrie-product-3">
                    <a href="<?php echo get_term_link($product_category->term_id);?>">
                       <?php if ( $settings['cat_image_show'] ): ?>
                        <?php echo wp_kses_post( $cat_thumnail_url );?>
                      <?php endif; ?>
                        <h6 class="cat-title"><?php echo esc_html( $product_category->name );?></h6>
                    </a>
                </div>               
            </div>
       <?php  endforeach; ?>            
    </div>
  </div>
<?php } ?>
  