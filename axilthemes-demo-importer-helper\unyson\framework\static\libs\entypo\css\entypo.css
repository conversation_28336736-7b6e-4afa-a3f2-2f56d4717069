@charset "UTF-8";

@font-face {
  font-family: 'entypo';
  src: url('../fonts/entypo.eot');
  src: url('../fonts/entypo.eot?#iefix') format('embedded-opentype'),
       url('../fonts/entypo.woff') format('woff'),
       url('../fonts/entypo.ttf') format('truetype'),
       url('../fonts/entypo.svg#entypo') format('svg');
  font-weight: normal; font-style: normal;
}

[class^="entypo-"], [class*=" entypo-"] {
  font-family: entypo;
  font-style: normal;

  /* font-size: 14px; */

  display: inline-block;
  width: 1.1em;
  margin-right: .1em;
  text-align: center;
}

.the-icons li {
  font-size: 14px;
  line-height: 24px;
  height: 24px;
}

.entypo-note:before { content: "\266a"; } /* '\266a' */
.entypo-note-beamed:before { content: "\266b"; } /* '\266b' */
.entypo-music:before { content: "🎵"; } /* '\1f3b5' */
.entypo-search:before { content: "🔍"; } /* '\1f50d' */
.entypo-flashlight:before { content: "🔦"; } /* '\1f526' */
.entypo-mail:before { content: "\2709"; } /* '\2709' */
.entypo-heart:before { content: "\2665"; } /* '\2665' */
.entypo-heart-empty:before { content: "\2661"; } /* '\2661' */
.entypo-star:before { content: "\2605"; } /* '\2605' */
.entypo-star-empty:before { content: "\2606"; } /* '\2606' */
.entypo-user:before { content: "👤"; } /* '\1f464' */
.entypo-users:before { content: "👥"; } /* '\1f465' */
.entypo-user-add:before { content: "\e700"; } /* '\e700' */
.entypo-video:before { content: "🎬"; } /* '\1f3ac' */
.entypo-picture:before { content: "🌄"; } /* '\1f304' */
.entypo-camera:before { content: "📷"; } /* '\1f4f7' */
.entypo-layout:before { content: "\268f"; } /* '\268f' */
.entypo-menu:before { content: "\2630"; } /* '\2630' */
.entypo-check:before { content: "\2713"; } /* '\2713' */
.entypo-cancel:before { content: "\2715"; } /* '\2715' */
.entypo-cancel-circled:before { content: "\2716"; } /* '\2716' */
.entypo-cancel-squared:before { content: "\274e"; } /* '\274e' */
.entypo-plus:before { content: "\2b"; } /* '\2b' */
.entypo-plus-circled:before { content: "\2795"; } /* '\2795' */
.entypo-plus-squared:before { content: "\229e"; } /* '\229e' */
.entypo-minus:before { content: "\2d"; } /* '\2d' */
.entypo-minus-circled:before { content: "\2796"; } /* '\2796' */
.entypo-minus-squared:before { content: "\229f"; } /* '\229f' */
.entypo-help:before { content: "\2753"; } /* '\2753' */
.entypo-help-circled:before { content: "\e704"; } /* '\e704' */
.entypo-info:before { content: "\2139"; } /* '\2139' */
.entypo-info-circled:before { content: "\e705"; } /* '\e705' */
.entypo-back:before { content: "🔙"; } /* '\1f519' */
.entypo-home:before { content: "\2302"; } /* '\2302' */
.entypo-link:before { content: "🔗"; } /* '\1f517' */
.entypo-attach:before { content: "📎"; } /* '\1f4ce' */
.entypo-lock:before { content: "🔒"; } /* '\1f512' */
.entypo-lock-open:before { content: "🔓"; } /* '\1f513' */
.entypo-eye:before { content: "\e70a"; } /* '\e70a' */
.entypo-tag:before { content: "\e70c"; } /* '\e70c' */
.entypo-bookmark:before { content: "🔖"; } /* '\1f516' */
.entypo-bookmarks:before { content: "📑"; } /* '\1f4d1' */
.entypo-flag:before { content: "\2691"; } /* '\2691' */
.entypo-thumbs-up:before { content: "👍"; } /* '\1f44d' */
.entypo-thumbs-down:before { content: "👎"; } /* '\1f44e' */
.entypo-download:before { content: "📥"; } /* '\1f4e5' */
.entypo-upload:before { content: "📤"; } /* '\1f4e4' */
.entypo-upload-cloud:before { content: "\e711"; } /* '\e711' */
.entypo-reply:before { content: "\e712"; } /* '\e712' */
.entypo-reply-all:before { content: "\e713"; } /* '\e713' */
.entypo-forward:before { content: "\27a6"; } /* '\27a6' */
.entypo-quote:before { content: "\275e"; } /* '\275e' */
.entypo-code:before { content: "\e714"; } /* '\e714' */
.entypo-export:before { content: "\e715"; } /* '\e715' */
.entypo-pencil:before { content: "\270e"; } /* '\270e' */
.entypo-feather:before { content: "\2712"; } /* '\2712' */
.entypo-print:before { content: "\e716"; } /* '\e716' */
.entypo-retweet:before { content: "\e717"; } /* '\e717' */
.entypo-keyboard:before { content: "\2328"; } /* '\2328' */
.entypo-comment:before { content: "\e718"; } /* '\e718' */
.entypo-chat:before { content: "\e720"; } /* '\e720' */
.entypo-bell:before { content: "🔔"; } /* '\1f514' */
.entypo-attention:before { content: "\26a0"; } /* '\26a0' */
.entypo-alert:before { content: "💥"; } /* '\1f4a5' */
.entypo-vcard:before { content: "\e722"; } /* '\e722' */
.entypo-address:before { content: "\e723"; } /* '\e723' */
.entypo-location:before { content: "\e724"; } /* '\e724' */
.entypo-map:before { content: "\e727"; } /* '\e727' */
.entypo-direction:before { content: "\27a2"; } /* '\27a2' */
.entypo-compass:before { content: "\e728"; } /* '\e728' */
.entypo-cup:before { content: "\2615"; } /* '\2615' */
.entypo-trash:before { content: "\e729"; } /* '\e729' */
.entypo-doc:before { content: "\e730"; } /* '\e730' */
.entypo-docs:before { content: "\e736"; } /* '\e736' */
.entypo-doc-landscape:before { content: "\e737"; } /* '\e737' */
.entypo-doc-text:before { content: "📄"; } /* '\1f4c4' */
.entypo-doc-text-inv:before { content: "\e731"; } /* '\e731' */
.entypo-newspaper:before { content: "📰"; } /* '\1f4f0' */
.entypo-book-open:before { content: "📖"; } /* '\1f4d6' */
.entypo-book:before { content: "📕"; } /* '\1f4d5' */
.entypo-folder:before { content: "📁"; } /* '\1f4c1' */
.entypo-archive:before { content: "\e738"; } /* '\e738' */
.entypo-box:before { content: "📦"; } /* '\1f4e6' */
.entypo-rss:before { content: "\e73a"; } /* '\e73a' */
.entypo-phone:before { content: "📞"; } /* '\1f4de' */
.entypo-cog:before { content: "\2699"; } /* '\2699' */
.entypo-tools:before { content: "\2692"; } /* '\2692' */
.entypo-share:before { content: "\e73c"; } /* '\e73c' */
.entypo-shareable:before { content: "\e73e"; } /* '\e73e' */
.entypo-basket:before { content: "\e73d"; } /* '\e73d' */
.entypo-bag:before { content: "👜"; } /* '\1f45c' */
.entypo-calendar:before { content: "📅"; } /* '\1f4c5' */
.entypo-login:before { content: "\e740"; } /* '\e740' */
.entypo-logout:before { content: "\e741"; } /* '\e741' */
.entypo-mic:before { content: "🎤"; } /* '\1f3a4' */
.entypo-mute:before { content: "🔇"; } /* '\1f507' */
.entypo-sound:before { content: "🔊"; } /* '\1f50a' */
.entypo-volume:before { content: "\e742"; } /* '\e742' */
.entypo-clock:before { content: "🕔"; } /* '\1f554' */
.entypo-hourglass:before { content: "\23f3"; } /* '\23f3' */
.entypo-lamp:before { content: "💡"; } /* '\1f4a1' */
.entypo-light-down:before { content: "🔅"; } /* '\1f505' */
.entypo-light-up:before { content: "🔆"; } /* '\1f506' */
.entypo-adjust:before { content: "\25d1"; } /* '\25d1' */
.entypo-block:before { content: "🚫"; } /* '\1f6ab' */
.entypo-resize-full:before { content: "\e744"; } /* '\e744' */
.entypo-resize-small:before { content: "\e746"; } /* '\e746' */
.entypo-popup:before { content: "\e74c"; } /* '\e74c' */
.entypo-publish:before { content: "\e74d"; } /* '\e74d' */
.entypo-window:before { content: "\e74e"; } /* '\e74e' */
.entypo-arrow-combo:before { content: "\e74f"; } /* '\e74f' */
.entypo-down-circled:before { content: "\e758"; } /* '\e758' */
.entypo-left-circled:before { content: "\e759"; } /* '\e759' */
.entypo-right-circled:before { content: "\e75a"; } /* '\e75a' */
.entypo-up-circled:before { content: "\e75b"; } /* '\e75b' */
.entypo-down-open:before { content: "\e75c"; } /* '\e75c' */
.entypo-left-open:before { content: "\e75d"; } /* '\e75d' */
.entypo-right-open:before { content: "\e75e"; } /* '\e75e' */
.entypo-up-open:before { content: "\e75f"; } /* '\e75f' */
.entypo-down-open-mini:before { content: "\e760"; } /* '\e760' */
.entypo-left-open-mini:before { content: "\e761"; } /* '\e761' */
.entypo-right-open-mini:before { content: "\e762"; } /* '\e762' */
.entypo-up-open-mini:before { content: "\e763"; } /* '\e763' */
.entypo-down-open-big:before { content: "\e764"; } /* '\e764' */
.entypo-left-open-big:before { content: "\e765"; } /* '\e765' */
.entypo-right-open-big:before { content: "\e766"; } /* '\e766' */
.entypo-up-open-big:before { content: "\e767"; } /* '\e767' */
.entypo-down:before { content: "\2b07"; } /* '\2b07' */
.entypo-left:before { content: "\2b05"; } /* '\2b05' */
.entypo-right:before { content: "\27a1"; } /* '\27a1' */
.entypo-up:before { content: "\2b06"; } /* '\2b06' */
.entypo-down-dir:before { content: "\25be"; } /* '\25be' */
.entypo-left-dir:before { content: "\25c2"; } /* '\25c2' */
.entypo-right-dir:before { content: "\25b8"; } /* '\25b8' */
.entypo-up-dir:before { content: "\25b4"; } /* '\25b4' */
.entypo-down-bold:before { content: "\e4b0"; } /* '\e4b0' */
.entypo-left-bold:before { content: "\e4ad"; } /* '\e4ad' */
.entypo-right-bold:before { content: "\e4ae"; } /* '\e4ae' */
.entypo-up-bold:before { content: "\e4af"; } /* '\e4af' */
.entypo-down-thin:before { content: "\2193"; } /* '\2193' */
.entypo-left-thin:before { content: "\2190"; } /* '\2190' */
.entypo-right-thin:before { content: "\2192"; } /* '\2192' */
.entypo-up-thin:before { content: "\2191"; } /* '\2191' */
.entypo-ccw:before { content: "\27f2"; } /* '\27f2' */
.entypo-cw:before { content: "\27f3"; } /* '\27f3' */
.entypo-arrows-ccw:before { content: "🔄"; } /* '\1f504' */
.entypo-level-down:before { content: "\21b3"; } /* '\21b3' */
.entypo-level-up:before { content: "\21b0"; } /* '\21b0' */
.entypo-shuffle:before { content: "🔀"; } /* '\1f500' */
.entypo-loop:before { content: "🔁"; } /* '\1f501' */
.entypo-switch:before { content: "\21c6"; } /* '\21c6' */
.entypo-play:before { content: "\25b6"; } /* '\25b6' */
.entypo-stop:before { content: "\25a0"; } /* '\25a0' */
.entypo-pause:before { content: "\2389"; } /* '\2389' */
.entypo-record:before { content: "\26ab"; } /* '\26ab' */
.entypo-to-end:before { content: "\23ed"; } /* '\23ed' */
.entypo-to-start:before { content: "\23ee"; } /* '\23ee' */
.entypo-fast-forward:before { content: "\23e9"; } /* '\23e9' */
.entypo-fast-backward:before { content: "\23ea"; } /* '\23ea' */
.entypo-progress-0:before { content: "\e768"; } /* '\e768' */
.entypo-progress-1:before { content: "\e769"; } /* '\e769' */
.entypo-progress-2:before { content: "\e76a"; } /* '\e76a' */
.entypo-progress-3:before { content: "\e76b"; } /* '\e76b' */
.entypo-target:before { content: "🎯"; } /* '\1f3af' */
.entypo-palette:before { content: "🎨"; } /* '\1f3a8' */
.entypo-list:before { content: "\e005"; } /* '\e005' */
.entypo-list-add:before { content: "\e003"; } /* '\e003' */
.entypo-signal:before { content: "📶"; } /* '\1f4f6' */
.entypo-trophy:before { content: "🏆"; } /* '\1f3c6' */
.entypo-battery:before { content: "🔋"; } /* '\1f50b' */
.entypo-back-in-time:before { content: "\e771"; } /* '\e771' */
.entypo-monitor:before { content: "💻"; } /* '\1f4bb' */
.entypo-mobile:before { content: "📱"; } /* '\1f4f1' */
.entypo-network:before { content: "\e776"; } /* '\e776' */
.entypo-cd:before { content: "💿"; } /* '\1f4bf' */
.entypo-inbox:before { content: "\e777"; } /* '\e777' */
.entypo-install:before { content: "\e778"; } /* '\e778' */
.entypo-globe:before { content: "🌎"; } /* '\1f30e' */
.entypo-cloud:before { content: "\2601"; } /* '\2601' */
.entypo-cloud-thunder:before { content: "\26c8"; } /* '\26c8' */
.entypo-flash:before { content: "\26a1"; } /* '\26a1' */
.entypo-moon:before { content: "\263d"; } /* '\263d' */
.entypo-flight:before { content: "\2708"; } /* '\2708' */
.entypo-paper-plane:before { content: "\e79b"; } /* '\e79b' */
.entypo-leaf:before { content: "🍂"; } /* '\1f342' */
.entypo-lifebuoy:before { content: "\e788"; } /* '\e788' */
.entypo-mouse:before { content: "\e789"; } /* '\e789' */
.entypo-briefcase:before { content: "💼"; } /* '\1f4bc' */
.entypo-suitcase:before { content: "\e78e"; } /* '\e78e' */
.entypo-dot:before { content: "\e78b"; } /* '\e78b' */
.entypo-dot-2:before { content: "\e78c"; } /* '\e78c' */
.entypo-dot-3:before { content: "\e78d"; } /* '\e78d' */
.entypo-brush:before { content: "\e79a"; } /* '\e79a' */
.entypo-magnet:before { content: "\e7a1"; } /* '\e7a1' */
.entypo-infinity:before { content: "\221e"; } /* '\221e' */
.entypo-erase:before { content: "\232b"; } /* '\232b' */
.entypo-chart-pie:before { content: "\e751"; } /* '\e751' */
.entypo-chart-line:before { content: "📈"; } /* '\1f4c8' */
.entypo-chart-bar:before { content: "📊"; } /* '\1f4ca' */
.entypo-chart-area:before { content: "🔾"; } /* '\1f53e' */
.entypo-tape:before { content: "\2707"; } /* '\2707' */
.entypo-graduation-cap:before { content: "🎓"; } /* '\1f393' */
.entypo-language:before { content: "\e752"; } /* '\e752' */
.entypo-ticket:before { content: "🎫"; } /* '\1f3ab' */
.entypo-water:before { content: "💦"; } /* '\1f4a6' */
.entypo-droplet:before { content: "💧"; } /* '\1f4a7' */
.entypo-air:before { content: "\e753"; } /* '\e753' */
.entypo-credit-card:before { content: "💳"; } /* '\1f4b3' */
.entypo-floppy:before { content: "💾"; } /* '\1f4be' */
.entypo-clipboard:before { content: "📋"; } /* '\1f4cb' */
.entypo-megaphone:before { content: "📣"; } /* '\1f4e3' */
.entypo-database:before { content: "\e754"; } /* '\e754' */
.entypo-drive:before { content: "\e755"; } /* '\e755' */
.entypo-bucket:before { content: "\e756"; } /* '\e756' */
.entypo-thermometer:before { content: "\e757"; } /* '\e757' */
.entypo-key:before { content: "🔑"; } /* '\1f511' */
.entypo-flow-cascade:before { content: "\e790"; } /* '\e790' */
.entypo-flow-branch:before { content: "\e791"; } /* '\e791' */
.entypo-flow-tree:before { content: "\e792"; } /* '\e792' */
.entypo-flow-line:before { content: "\e793"; } /* '\e793' */
.entypo-flow-parallel:before { content: "\e794"; } /* '\e794' */
.entypo-rocket:before { content: "🚀"; } /* '\1f680' */
.entypo-gauge:before { content: "\e7a2"; } /* '\e7a2' */
.entypo-traffic-cone:before { content: "\e7a3"; } /* '\e7a3' */
.entypo-cc:before { content: "\e7a5"; } /* '\e7a5' */
.entypo-cc-by:before { content: "\e7a6"; } /* '\e7a6' */
.entypo-cc-nc:before { content: "\e7a7"; } /* '\e7a7' */
.entypo-cc-nc-eu:before { content: "\e7a8"; } /* '\e7a8' */
.entypo-cc-nc-jp:before { content: "\e7a9"; } /* '\e7a9' */
.entypo-cc-sa:before { content: "\e7aa"; } /* '\e7aa' */
.entypo-cc-nd:before { content: "\e7ab"; } /* '\e7ab' */
.entypo-cc-pd:before { content: "\e7ac"; } /* '\e7ac' */
.entypo-cc-zero:before { content: "\e7ad"; } /* '\e7ad' */
.entypo-cc-share:before { content: "\e7ae"; } /* '\e7ae' */
.entypo-cc-remix:before { content: "\e7af"; } /* '\e7af' */
.entypo-github:before { content: "\f300"; } /* '\f300' */
.entypo-github-circled:before { content: "\f301"; } /* '\f301' */
.entypo-flickr:before { content: "\f303"; } /* '\f303' */
.entypo-flickr-circled:before { content: "\f304"; } /* '\f304' */
.entypo-vimeo:before { content: "\f306"; } /* '\f306' */
.entypo-vimeo-circled:before { content: "\f307"; } /* '\f307' */
.entypo-twitter:before { content: "\f309"; } /* '\f309' */
.entypo-twitter-circled:before { content: "\f30a"; } /* '\f30a' */
.entypo-facebook:before { content: "\f30c"; } /* '\f30c' */
.entypo-facebook-circled:before { content: "\f30d"; } /* '\f30d' */
.entypo-facebook-squared:before { content: "\f30e"; } /* '\f30e' */
.entypo-gplus:before { content: "\f30f"; } /* '\f30f' */
.entypo-gplus-circled:before { content: "\f310"; } /* '\f310' */
.entypo-pinterest:before { content: "\f312"; } /* '\f312' */
.entypo-pinterest-circled:before { content: "\f313"; } /* '\f313' */
.entypo-tumblr:before { content: "\f315"; } /* '\f315' */
.entypo-tumblr-circled:before { content: "\f316"; } /* '\f316' */
.entypo-linkedin:before { content: "\f318"; } /* '\f318' */
.entypo-linkedin-circled:before { content: "\f319"; } /* '\f319' */
.entypo-dribbble:before { content: "\f31b"; } /* '\f31b' */
.entypo-dribbble-circled:before { content: "\f31c"; } /* '\f31c' */
.entypo-stumbleupon:before { content: "\f31e"; } /* '\f31e' */
.entypo-stumbleupon-circled:before { content: "\f31f"; } /* '\f31f' */
.entypo-lastfm:before { content: "\f321"; } /* '\f321' */
.entypo-lastfm-circled:before { content: "\f322"; } /* '\f322' */
.entypo-rdio:before { content: "\f324"; } /* '\f324' */
.entypo-rdio-circled:before { content: "\f325"; } /* '\f325' */
.entypo-spotify:before { content: "\f327"; } /* '\f327' */
.entypo-spotify-circled:before { content: "\f328"; } /* '\f328' */
.entypo-qq:before { content: "\f32a"; } /* '\f32a' */
.entypo-instagrem:before { content: "\f32d"; } /* '\f32d' */
.entypo-dropbox:before { content: "\f330"; } /* '\f330' */
.entypo-evernote:before { content: "\f333"; } /* '\f333' */
.entypo-flattr:before { content: "\f336"; } /* '\f336' */
.entypo-skype:before { content: "\f339"; } /* '\f339' */
.entypo-skype-circled:before { content: "\f33a"; } /* '\f33a' */
.entypo-renren:before { content: "\f33c"; } /* '\f33c' */
.entypo-sina-weibo:before { content: "\f33f"; } /* '\f33f' */
.entypo-paypal:before { content: "\f342"; } /* '\f342' */
.entypo-picasa:before { content: "\f345"; } /* '\f345' */
.entypo-soundcloud:before { content: "\f348"; } /* '\f348' */
.entypo-mixi:before { content: "\f34b"; } /* '\f34b' */
.entypo-behance:before { content: "\f34e"; } /* '\f34e' */
.entypo-google-circles:before { content: "\f351"; } /* '\f351' */
.entypo-vkontakte:before { content: "\f354"; } /* '\f354' */
.entypo-smashing:before { content: "\f357"; } /* '\f357' */
.entypo-sweden:before { content: "\f601"; } /* '\f601' */
.entypo-db-shape:before { content: "\f600"; } /* '\f600' */
.entypo-logo-db:before { content: "\f603"; } /* '\f603' */

