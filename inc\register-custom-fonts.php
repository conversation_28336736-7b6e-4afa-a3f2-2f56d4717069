<?php
/**
 * Register custom fonts.
 */
if ( !function_exists( 'axil_fonts_url' ) ):
    function axil_fonts_url() {
        $fonts_url = '';
        $fonts = array();
        $subsets = 'latin,latin-ext';

        /* translators: If there are characters in your language that are not supported by Nunito+Sans Sans, translate this to 'off'. Do not translate into your own language. */
        if ( 'off' !== esc_attr_x( 'on', 'DM Sans Display font: on or off', 'etrade' ) ) {
            $fonts[] = 'DM Sans:ital,wght@0,400;0,500;0,700;1,400';
        }

        if ( $fonts ) {
            $fonts_url = add_query_arg( array(
                'family' => urlencode( implode( '|', $fonts ) ),
                'subset' => urlencode( $subsets ),
            ), 'https://fonts.googleapis.com/css' );
        }

        return esc_url_raw( $fonts_url );
    }
endif;