<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options  = Helper::axil_get_options();
$header_layout = Helper::axil_header_layout();
$header_sticky = $header_layout['header_sticky'];
$header_sticky = ("no" !== $header_sticky && "0" !== $header_sticky) ? " header-sticky " : "";
$axil_nav_menu_args = Helper::axil_nav_menu_args();
?>
<header class="header axil-header header-style-4">
<?php get_template_part('template-parts/header/header', 'notification-top-2');?>
<?php get_template_part('template-parts/header/header', 'top4');?>
<!-- Start Mainmenu Area  -->
<div id="axil-sticky-placeholder"></div>
    <div class="axil-mainmenu">
        <div class="container">
            <div class="header-navbar">
                <div class="header-main-nav">   
                    <!-- Start Mainmanu Nav -->
                    <?php if (has_nav_menu('primary')) {
                         wp_nav_menu($axil_nav_menu_args);
                    } ?>
                    <!-- End Mainmanu Nav --> 
                </div> 
            </div>
        </div>
    </div>
<!-- End Mainmenu Area  -->
</header> 