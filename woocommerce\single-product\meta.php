<?php
/**
 * Single Product Meta
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/meta.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://docs.woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     3.0.0
 */

if ( !defined( 'ABSPATH' ) ) {
    exit;
}

global $product;

$axil_options = Helper::axil_get_options();
$has_sku = wc_product_sku_enabled() && ( $product->get_sku() || $product->is_type( 'variable' ) ) ? true : false;
$sku = $product->get_sku();
$sku = $sku ? $sku : esc_html__( 'N/A', 'etrade' );
$stock_status = WooC_Functions::get_stock_status();

$cats_title = _n( 'Category', 'Categories', count( $product->get_category_ids() ), 'etrade' );
$tags_title = _n( 'Tag', 'Tags', count( $product->get_tag_ids() ), 'etrade' );
$cats_html = wc_get_product_category_list( $product->get_id() );
$tags_html = wc_get_product_tag_list( $product->get_id() );
$sku = $product->get_sku() ? $sku : esc_html__( 'N/A', 'etrade' );
$layout = Helper::axil_product_layout_style();

if ( $layout == "7" ) { ?>

	<?php do_action( 'woocommerce_product_meta_start' );
    ?>
		 <div class="nft-short-meta">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="nft-category">
                       <?php do_action( 'woocommerce_product_meta_start' );?>

               			<?php if ( wc_product_sku_enabled() && ( $product->get_sku() || $product->is_type( 'variable' ) ) ): ?>
                            <?php if ( $axil_options['wooc_nft_sku'] ): ?>
                            <span class="sku_wrapper"><?php esc_html_e( 'SKU:', 'etrade' );?> <span class="sku"><?php echo esc_html( $sku = $product->get_sku() ) ? $sku : esc_html__( 'N/A', 'etrade' ); ?></span></span>

                            <?php endif;?>
                        <?php endif;?>

               			<?php if ( $axil_options['wooc_nft_cats'] && $cats_html ): ?>
               				<?php echo wc_get_product_category_list( $product->get_id(), ', ', '<span class="posted_in">' . _n( 'Category:', 'Categories:', count( $product->get_category_ids() ), 'etrade' ) . ' ', '</span>' ); ?>
               			<?php endif;?>

               			<?php if ( $axil_options['wooc_nft_tags'] && $tags_html ): ?>
               				<?php echo wc_get_product_tag_list( $product->get_id(), ', ', '<span class="tagged_as">' . _n( 'Tag:', 'Tags:', count( $product->get_tag_ids() ), 'etrade' ) . ' ', '</span>' ); ?>
               			<?php endif;?>

                   		<?php do_action( 'woocommerce_product_meta_end' );?>

                    </div>
                </div>
                <div class="col-md-6">
                	<?php if ( $axil_options['axil_nft_verifiy_enable'] ):

                        $nft_verifiy_img = wp_get_attachment_url( $product->get_image_id() );
                        $nft_verifiy = 'https://images.google.com/searchbyimage?image_url=' . $nft_verifiy_img;

                        ?>
	                    <div class="nft-verified-option">
	                        <label><?php echo esc_attr( $axil_options['axil_nft_verifiy_title'] ); ?></label>
	                        <a href="<?php echo esc_url( $nft_verifiy ); ?>" class="verify-btn axil-btn btn-bg-secondary"><?php echo esc_attr( $axil_options['axil_nft_btn_txt'] ); ?></a>
	                    </div>
	                    <?php endif;?>
                </div>
            </div>
        </div>
	<?php do_action( 'woocommerce_product_meta_end' );?> 
	<?php } else {?> 
	<div class="product_meta"> 
		<?php do_action( 'woocommerce_product_meta_start' );?>

			<?php if ( wc_product_sku_enabled() && ( $product->get_sku() || $product->is_type( 'variable' ) ) ): ?>

			<span class="sku_wrapper"><?php esc_html_e( 'SKU:', 'etrade' );?> <span class="sku"><?php echo esc_html( $sku = $product->get_sku() ) ? $sku : esc_html__( 'N/A', 'etrade' ); ?></span></span>

			<?php endif;?>
			<?php if ( $axil_options['wooc_cats'] && $cats_html ): ?>
				<?php echo wc_get_product_category_list( $product->get_id(), ', ', '<span class="posted_in">' . _n( 'Category:', 'Categories:', count( $product->get_category_ids() ), 'etrade' ) . ' ', '</span>' ); ?>
			<?php endif;?>

			<?php if ( $axil_options['wooc_tags'] && $tags_html ): ?>
				<?php echo wc_get_product_tag_list( $product->get_id(), ', ', '<span class="tagged_as">' . _n( 'Tag:', 'Tags:', count( $product->get_tag_ids() ), 'etrade' ) . ' ', '</span>' ); ?>
			<?php endif;?> 

            <?php if ( $axil_options['wc_social'] ): ?>
        <div class="product-social">
            <span class="product-social-title"><?php esc_html_e( 'Share:', 'etrade' );?></span>
            <ul class="product-social-items">
                <?php foreach ( WooC_Functions::axil_social_sharing() as $key => $sharer ): ?>
                    <li class="social-<?php echo esc_attr( $key );?>"><a href="<?php echo esc_url( $sharer['url'] );?>" target="_blank" title="<?php echo esc_attr( $sharer['icon'] );?>"><span class="fab <?php echo esc_attr( $sharer['icon'] );?>"></span></a></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
		<?php do_action( 'woocommerce_product_meta_end' );?>

	</div>

<?php }?>