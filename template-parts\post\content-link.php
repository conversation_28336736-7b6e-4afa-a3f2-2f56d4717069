<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

$custom_link        = axil_get_acf_data("axil_custom_link");
$link               = !empty($custom_link) ? $custom_link : get_the_permalink();
$axil_options       = Helper::axil_get_options();
$thumb_size         = ($axil_options['axil_blog_sidebar'] === 'no') ? 'axil-single-blog-thumb':'axil-blog-list';
$post_share_icon    = (isset($axil_options['axil_show_post_share_icon'])) ? $axil_options['axil_show_post_share_icon'] : 'yes';
$content            = get_the_content();
$content            = apply_filters( 'the_content', $content );
$content            = wp_trim_words( get_the_excerpt(),  $axil_options['post_content_limit'], '.' );
$readmore           = $axil_options['read_more_btn_txt'];
$allowed_tags = wp_kses_allowed_html( 'post' );
?>
<!-- Start Post List  -->
<div  id="post-<?php the_ID(); ?>" <?php post_class('content-blog  mt--60'); ?>>
    <div class="inner">
         <?php if(has_post_thumbnail()){ ?>
        <div class="post-thumbnail thumbnail">
            <a href="<?php echo esc_url($link); ?>">
                <?php the_post_thumbnail($thumb_size) ?>
            </a>
        </div>
    <?php } ?>       
        <div class="content">
            <h4 class="title"><a href="<?php echo esc_url($link); ?>"><?php the_title(); ?></a></h4>           
            <?php Helper::axil_postmeta(); ?>
           <p><?php echo wp_kses( $content, $allowed_tags ); ?></p>
            <?php if( !empty($readmore)  ){ ?>
                <div class="read-more-btn">
                    <a class="axil-btn btn-bg-primary" href="<?php echo esc_url($link); ?>"><?php echo esc_html($readmore); ?></a>
                </div>
              <?php } ?> 
        </div>
    </div>
</div>
<!-- End Post List  -->