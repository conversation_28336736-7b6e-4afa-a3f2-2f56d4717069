<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

$axil_options = Helper::axil_get_options();
$block_data = array( 'display_attributes' => false );
$layout = Helper::axil_product_layout_style();
$layout_class = ( empty( $layout ) || $layout == "7" ) ? 'pt--10 pb--50 pb_sm--30' : ' axil-section-gap pb--50 pb_sm--30';
?>
<div class="axil-product-area axil-new-arrivals-activation bg-color-white <?php echo esc_attr( $layout_class ); ?>">
    <div class="container">
        <div class="section-title-wrapper">
            <span class="title-highlighter highlighter-primary"><i class="far fa-shopping-basket"></i><?php echo esc_html( $before_heading ); ?></span>
            <h2 class="title"><?php echo esc_html( $title ); ?></h2>
        </div>
        <div class="new-arrivals-product-activation slick-layout-wrapper--15 axil-slick-arrow  arrow-top-slide">
            <?php
                foreach ( $products as $product ) {
                    $post_object = get_post( $product->get_id() );

                    setup_postdata( $GLOBALS['post'] = &$post_object );
                    echo '<div class="slick-single-layout">';
                    wc_get_template( "content-product.php", compact( 'block_data' ) );
                    echo "</div>";
                }
                ?>
            </div>
        </div>
    </div>
