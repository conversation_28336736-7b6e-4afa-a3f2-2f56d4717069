<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

if (!defined('ABSPATH')) exit; // Exit if accessed directly.

class etrade_Widgets_Control
{
    public function __construct()
    {
        $this->etrade_widgets_control();

    }

    public function etrade_widgets_control()
    {
        $sectiontitle = 'on';
        $widgets_manager = \Elementor\Plugin::instance()->widgets_manager;

        $widget_files = [
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/title.php',
                'class' => 'Title',
            ], 
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/etrade-btn.php',
                'class' => 'axil_btn',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/banner-slider-multipurpose.php',
                'class' => 'axil_banner_slider_multipurpose',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/banner-slider-electronics.php',
                'class' => 'axil_banner_slider_electronics',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/banner-slider-nft.php',
                'class' => 'axil_banner_slider_nft',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/banner-jewellery.php',
                'class' => 'axil_banner_jewellery',
            ],  
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/banner-slider-fashion.php',
                'class' => 'axil_banner_slider_fashion',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/banner-slider-furniture.php',
                'class' => 'axil_banner_slider_furniture',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/product-cat-slider.php',
                'class' => 'axil_product_cat_slider',
            ],
         
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/best-deals.php',
                'class' => 'axil_best_deals',
            ],  
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/mega-collections.php',
                'class' => 'axil_mega_collections',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/poster-banner.php',
                'class' => 'Poster_banner',
            ],
          
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/product-nft-slider.php',
                'class' => 'Nfts_banner_slider',
            ],
       
            
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/countdown.php',
                'class' => 'Wooc_Product_Countdown',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/product-carousel.php',
                'class' => 'Product_Carousel',
            ],

            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/icon-box.php',
                'class' => 'Icon_Box',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/product-list.php',
                'class' => 'Axil_Product_list',
            ],


            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/video-popup.php',
                'class' => 'Video_Popup',
            ],
            
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/testimonial-carousel.php',
                'class' => 'Testimonial_Carousel',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/testimonial-and-video.php',
                'class' => 'Testimonial_video',
            ],
 
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/product-grid.php',
                'class' => 'Wooc_Product_Grid',
            ],

            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/login-img-map.php',
                'class' => 'Login_Image_Map',
            ],
 
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/product-box.php',
                'class' => 'axil_Single_Product',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/team-carousel.php',
                'class' => 'axil_team_members',
            ], 
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/product-isotope-filter.php',
                'class' => 'Axil_Isotope_Filter',
            ], 
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/post-grid.php',
                'class' => 'etrade_post_grid',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/multi-column-product-carousel.php',
                'class' => 'multi_column_Product_Carousel',
            ],
            [
                'section_title' => 'on',
                'file_path' => 'elementor/widgets/newsletter.php',
                'class' => 'axil_newsletter',
            ],

        ];


        foreach ($widget_files as $widget_file) {
            if (file_exists(ETRADE_ELEMENTS_BASE_DIR . $widget_file['file_path']) && $widget_file['section_title'] == 'on') {
                require_once ETRADE_ELEMENTS_BASE_DIR . $widget_file['file_path'];
                $class_name_with_namespace = "axiltheme\\etrade_elements\\" . $widget_file['class'];
                $widgets_manager->register(new $class_name_with_namespace());
            }
        }


    }
}

new etrade_Widgets_Control();