<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Controls_Manager;
use Elementor\Group_Control_Typography;
use Elementor\Widget_Base;

if ( !defined( 'ABSPATH' ) ) {
    exit;
}
// Exit if accessed directly
class etrade_post_grid extends Widget_Base {

    public function get_name() {
        return 'etrade-post-grid';
    }
    public function get_title() {
        return esc_html__( 'Blog Post', 'etrade-elements' );
    }
    public function get_icon() {
        return 'eicon-banner';
    }
    public function get_categories() {
        return array( ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' );
    }

    public function get_post_name( $post_type = 'post' ) {
        $options = array();
        $options = array( '0' => esc_html__( 'None', 'etrade-elements' ) );
        $axil_post = array( 'posts_per_page' => -1, 'post_type' => $post_type );
        $axil_post_terms = get_posts( $axil_post );
        if ( !empty( $axil_post_terms ) && !is_wp_error( $axil_post_terms ) ) {
            foreach ( $axil_post_terms as $term ) {
                $options[$term->ID] = $term->post_title;
            }
            return $options;
        }
    }

    protected function register_controls() {

        $terms = get_terms( array( 'taxonomy' => 'category', 'fields' => 'id=>name' ) );
        $category_dropdown = array( '0' => esc_html__( 'All Categories', 'etrade-elements' ) );

        foreach ( $terms as $id => $name ) {
            $category_dropdown[$id] = $name;
        }

        $this->start_controls_section(
            'sec_query',
            array(
                'label' => __( 'Query', 'etrade-elements' ),
            )
        );
        $this->add_control(
            'post_sorting',
            array(
                'label'   => __( 'Post Sorting', 'etrade-elements' ),
                'type'    => Controls_Manager::SELECT2,
                'options' => array(
                    'recent'        => esc_html__( 'Recent Post', 'etrade-elements' ),
                    'rand'          => esc_html__( 'Random Post', 'etrade-elements' ),
                    'modified'      => esc_html__( 'Last Modified Post', 'etrade-elements' ),
                    'comment_count' => esc_html__( 'Most commented Post', 'etrade-elements' ),
                    'view'          => esc_html__( 'Most viewed Post', 'etrade-elements' ),
                ),
                'default' => 'recent',
            )
        );

        $this->add_control(
            'post_ordering',
            array(
                'label'   => __( 'Post Ordering', 'etrade-elements' ),
                'type'    => Controls_Manager::SELECT2,
                'options' => array(
                    'DESC' => esc_html__( 'Desecending', 'etrade-elements' ),
                    'ASC'  => esc_html__( 'Ascending', 'etrade-elements' ),
                ),
                'default' => 'DESC',
            )
        );

        $this->add_control(
            'cat_single_list',
            array(
                'label'    => __( 'Categories', 'etrade-elements' ),
                'type'     => Controls_Manager::SELECT2,
                'default'  => '0',
                'multiple' => true,
                'options'  => $category_dropdown,
            )
        );

        $this->add_control(
            'number_of_post',
            array(
                'label'       => __( 'Number', 'etrade-elements' ),
                'type'        => Controls_Manager::NUMBER,
                'default'     => '3',
                'description' => __( 'Write -1 to show all', 'etrade-elements' ),
            )

        );
        $this->add_control(
            'posts_not_in',
            array(
                'label'       => __( 'Select The Posts that will not display', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT2,
                'options'     => $this->get_post_name(),
                'label_block' => true,
                'multiple'    => true,
                'separator'   => 'before',
            )
        );
        $this->end_controls_section();

        $this->start_controls_section(
            'display_options_section',
            array(
                'label' => __( 'Display Options', 'etrade-elements' ),
            )
        );

        $this->add_control(
            'post_title_length',
            array(
                'label'   => __( 'Post Title Length', 'etrade-elements' ),
                'type'    => Controls_Manager::NUMBER,
                'default' => 10,

            )

        );

        $this->add_control(
            'post_details',
            array(

                'type'        => Controls_Manager::SWITCHER,
                'label'       => __( 'Post Details', 'etrade-elements' ),
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
                'description' => __( 'Show or Hide Details. Default: On', 'etrade-elements' ),
                'separator'   => 'before',

            )
        );
        $this->add_control(
            'show_post_categories',
            array(

                'type'        => Controls_Manager::SWITCHER,
                'label'       => __( 'Post Categories', 'etrade-elements' ),
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
                'description' => __( 'Show or Hide Details. Default: On', 'etrade-elements' ),
                'separator'   => 'before',

            )
        );
        $this->add_control(
            'post_details_text',
            array(
                'label'     => __( 'Detail Text', 'etrade-elements' ),
                'type'      => Controls_Manager::TEXT,
                'default'   => 'LOREM IPSUM',
                'condition' => array( 'post_details' => array( 'yes' ) ),

            )
        );

        $this->end_controls_section();

        // Style Title tab section
        $this->start_controls_section(
            'blog_title_style_section',
            array(
                'label' => __( 'Title', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'blog_title_style_on',
            array(
                'label'     => __( 'Customize', 'etrade-elements' ),
                'type'      => Controls_Manager::SWITCHER,
                'label_on'  => __( 'On', 'etrade-elements' ),
                'label_off' => __( 'Off', 'etrade-elements' ),
                'default'   => 'no',

            )
        );

        $this->add_control(
            'blog_title_color',
            array(
                'label'     => __( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '#333',
                'selectors' => array(
                    '{{WRAPPER}} .content-blog .content .title a' => 'color: {{VALUE}}',
                ),
                'condition' => array( 'blog_title_style_on' => array( 'yes' ) ),
            )
        );
        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'      => 'blog_title_typography',
                'label'     => __( 'Typography', 'etrade-elements' ),

                'condition' => array( 'blog_title_style_on' => array( 'yes' ) ),
                'selector'  => '{{WRAPPER}} .content-blog .content .title a',

            )
        );
        $this->add_responsive_control(
            'blog_title_margin',
            array(
                'label'      => __( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'condition'  => array( 'blog_title_style_on' => array( 'yes' ) ),
                'size_units' => array( 'px', '%', 'em' ),
                'selectors'  => array(
                    '{{WRAPPER}} .content-blog .content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'blog_meta_style_section',
            array(
                'label' => __( 'Meta', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'blog_meta_style_on',
            array(
                'label'     => __( 'Customize', 'etrade-elements' ),
                'type'      => Controls_Manager::SWITCHER,
                'label_on'  => __( 'On', 'etrade-elements' ),
                'label_off' => __( 'Off', 'etrade-elements' ),
                'default'   => 'no',

            )
        );

        $this->add_control(
            'blog_meta_color',
            array(
                'label'     => __( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '#333',
                'selectors' => array(
                    '{{WRAPPER}} .content-blog .blog-category a' => 'color: {{VALUE}}',
                ),

                'condition' => array( 'blog_meta_style_on' => array( 'yes' ) ),
            )
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'      => 'blog_meta_typography',
                'label'     => __( 'Typography', 'etrade-elements' ),

                'condition' => array( 'blog_meta_style_on' => array( 'yes' ) ),
                'selector'  => '{{WRAPPER}} .content-blog .blog-category a',

            )
        );
        $this->add_responsive_control(
            'blog_meta_margin',
            array(
                'label'      => __( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'condition'  => array( 'blog_meta_style_on' => array( 'yes' ) ),
                'size_units' => array( 'px', '%', 'em' ),
                'selectors'  => array(
                    '{{WRAPPER}} .content-blog .blog-category a' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ),
            )
        );
        $this->add_responsive_control(
            'blog_meta_padding',
            array(
                'label'      => __( 'Padding', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'condition'  => array( 'blog_meta_style_on' => array( 'yes' ) ),
                'size_units' => array( 'px', '%', 'em' ),
                'selectors'  => array(
                    '{{WRAPPER}} .content-blog .blog-category a' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ),
            )
        );
        $this->end_controls_section();
    }

    protected function render() {
        $settings = $this->get_settings();
        $template = 'post-grid-1';
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
    }
}
