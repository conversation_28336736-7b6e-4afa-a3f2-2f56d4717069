<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */
$axil_options = Helper::axil_get_options();
$shop_notification = Helper::axil_shop_notification_enable();
$shop_notification = $shop_notification['shop_notification'];
$allowed_tags   = wp_kses_allowed_html( 'post' );

 if ("no" !== $shop_notification && "0" !== $shop_notification): ?>
    <?php if( WOOC_WOO_ACTIVED ):?> 
        <?php
        if ( is_store_notice_showing() ) {
                $notice = get_option( 'woocommerce_demo_store_notice' );
                if ( empty( $notice ) ) {
                    $notice = __( 'This is a demo store for testing purposes &mdash; no orders shall be fulfilled.', 'etrade' );
                }
                $notice_id = md5( $notice );
                // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
                echo apply_filters( 'woocommerce_demo_store', '<div class="woocommerce-store-notice demo_store"><div class="header-top-campaign">
                <div class="container position-relative axilcoutdown3">
                    <div class="campaign-content">
                     <div data-time= '. esc_html($axil_options["shop_notification_date"]) .'" class="campaign-countdown"></div>                
                        <p class="woocommerce-store-notice demo_store" data-notice-id="' . esc_attr( $notice_id ) . '" style="display:none;">' . wp_kses( $notice, $allowed_tags ) . ' <a href="#" class="woocommerce-store-notice__dismiss-link"><i class="fal fa-times"></i></a></p> 
                        </div> 
                </div>
                </div>
            </div>', $notice );
            }
         ?> 
    <?php endif ?>
<?php endif ?>

 