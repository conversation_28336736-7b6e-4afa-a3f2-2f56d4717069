<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;
use Elementor\Repeater;
use <PERSON>ementor\Widget_Base;

if ( !defined( 'ABSPATH' ) ) {
    exit;
}
// Exit if accessed directly

class axil_banner_slider_furniture extends Widget_Base {

    public function get_name() {
        return 'axil-banner-slider-furniture';
    }
    public function get_title() {
        return __( 'Banner Slider- Furniture', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-banner';
    }
    public function get_categories() {
        return array( ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' );
    }
    public function axil_get_img( $img ) {
        $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
        return $img;
    }
    private function axil_get_all_pages() {

        $page_list = get_posts( array(
            'post_type'      => 'page',
            'orderby'        => 'date',
            'order'          => 'DESC',
            'posts_per_page' => -1,
        ) );

        $pages = array();

        if ( !empty( $page_list ) && !is_wp_error( $page_list ) ) {
            foreach ( $page_list as $page ) {
                $pages[$page->ID] = $page->post_title;
            }
        }

        return $pages;
    }
    protected function register_controls() {
        $this->start_controls_section(
            'axilbanner_content_sec',
            array(
                'label' => esc_html__( ' Banner Content', 'etrade-elements' ),

            )
        );
        $repeater = new Repeater();
        $repeater->add_control(
            'list_before',
            array(
                'label'       => esc_html__( 'Before Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => esc_html__( 'Before titlet', 'etrade-elements' ),
                'label_block' => true,

            )
        );
        $repeater->add_control(
            'beforetitlestyle',
            array(
                'label'   => esc_html__( 'Before Color', 'etrade-elements' ),
                'type'    => Controls_Manager::SELECT,
                'default' => 'primary',
                'options' => array(
                    'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                    'secondary' => esc_html__( 'Secondary', 'etrade-elements' ),
                    'primary2'  => esc_html__( 'Primary 2', 'etrade-elements' ),

                ),
            )
        );
        $repeater->add_control(
            'before_icon',
            array(
                'label'   => esc_html__( 'Before Title Icons', 'etrade-elements' ),
                'type'    => Controls_Manager::ICONS,
                'default' => array(
                    'value'   => 'fas fa-fire',
                    'library' => 'solid',
                ),

            )
        );
        $repeater->add_control(
            'list_title',
            array(
                'label'       => esc_html__( 'Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => esc_html__( 'List Title', 'etrade-elements' ),
                'label_block' => true,
            )
        );

        $repeater->add_control(
            'image',
            array(
                'label'   => esc_html__( 'Image', 'etrade-elements' ),
                'type'    => Controls_Manager::MEDIA,
                'default' => array(
                    'url' => "",
                ),
                'dynamic' => array(
                    'active' => true,
                ),

            )
        );
        $repeater->add_group_control(
            Group_Control_Image_Size::get_type(),
            array(
                'name'      => 'image_size',
                'default'   => 'full',
                'separator' => 'none',

            )
        );
        $repeater->add_control(
            'btntext',
            array(
                'label'     => __( 'Button Text', 'etrade-elements' ),
                'type'      => Controls_Manager::TEXT,
                'default'   => 'Shop Now',
                'separator' => 'before',

            )
        );
        $repeater->add_control(
            'axil_link_type',
            array(
                'label'       => esc_html__( 'See All Link Type', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT,
                'options'     => array(
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ),
                'default'     => '1',

                'label_block' => true,
            )
        );

        $repeater->add_control(
            'axil_page_link',
            array(
                'label'       => esc_html__( 'Select See All Page', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT2,
                'label_block' => true,
                'options'     => $this->axil_get_all_pages(),
                'condition'   => array(
                    'axil_link_type' => '2',

                ),
            )
        );

        $repeater->add_control(
            'url',
            array(
                'label'       => __( 'Detail URL', 'etrade-elements' ),
                'type'        => Controls_Manager::URL,
                'placeholder' => 'https://your-link.com',
                'condition'   => array(
                    'axil_link_type' => '1',

                ),
            )
        );

        $this->add_control(
            'list',
            array(
                'label'       => esc_html__( 'Slider List', 'etrade-elements' ),
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $repeater->get_controls(),
                'default'     => array(
                    array(
                        'list_before' => esc_html__( 'Hot Deal In This Week', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Neon Stylish Sofa Chair', 'etrade-elements' ),

                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-47.png' ),
                        ),
                    ),
                    array(
                        'list_before' => esc_html__( 'Hot Deal In This Week', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Sofa Chair with Lamp', 'etrade-elements' ),

                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-48.png' ),
                        ),
                    ),

                    array(
                        'list_before' => esc_html__( 'Hot Deal In This Week', 'etrade-elements' ),
                        'list_title'  => esc_html__( 'Neon Stylish Sofa Chair', 'etrade-elements' ),

                        'image'       => array(
                            'url' => $this->axil_get_img( 'product-49.png' ),
                        ),
                    ),
                ),
                'title_field' => '{{{ list_title }}}',
            )
        );
        $this->end_controls_section();

        $this->start_controls_section(
            'subtitle_style_section',
            array(
                'label' => esc_html__( 'Sub Title or Title Before', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );
        $this->add_control(
            'subtitle_color',
            array(
                'label'     => esc_html__( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .main-slider-content .subtitle'   => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .subtitle i' => 'background-color: {{VALUE}}',
                ),
            )
        );
        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'subtitle_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .main-slider-content .subtitle',
            )
        );
        $this->add_responsive_control(
            'subtitle_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .main-slider-content .subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'title_style_section',
            array(
                'label' => esc_html__( 'Title', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'title_color',
            array(
                'label'     => esc_html__( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .inner .title'               => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .title' => 'color: {{VALUE}}',
                ),
            )
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'title_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .inner .title , {{WRAPPER}} .main-slider-content .title',
            )
        );

        $this->add_responsive_control(
            'title_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .inner .title'               => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .main-slider-content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'axil_btn_style_section',
            array(
                'label' => __( 'Button', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'btn_color',
            array(
                'label'     => __( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white'   => 'color: {{VALUE}}',
                    '{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white i' => 'color: {{VALUE}}',
                ),
            )
        );

        $this->add_control(
            'button_bg_color',
            array(
                'label'     => esc_html__( 'Background Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .main-slider-content .shop-btn a'        => 'background: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .shop-btn a:before' => 'background: {{VALUE}}',

                ),
            )
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'btn_font_size',
                'label'    => __( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white',
            )
        );

        $this->add_responsive_control(
            'btn_margin',
            array(
                'label'      => __( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ),
            )
        );
        $this->add_responsive_control(
            'btn_padding',
            array(
                'label'      => __( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .shop-btn a.axil-btn.btn-bg-white' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',

                ),
            )
        );

        $this->end_controls_section();

    }
    private function slick_load_scripts() {
        wp_enqueue_style( 'slick' );
        wp_enqueue_style( 'slick-theme' );
        wp_enqueue_script( 'slick' );
    }

    protected function render() {
        $settings = $this->get_settings();
        $this->slick_load_scripts();
        $template = 'banner-slider-furniture';
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
    }

}