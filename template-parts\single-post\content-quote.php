<?php
/**
 * Template part for displaying results in search pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */ 

$axil_options   = Helper::axil_get_options();
$sidebar        = Helper::axil_sidebar_options();
$axil_single_blog_sidebar_class = ($sidebar === 'full')  || !is_active_sidebar( 'sidebar-1' )? ' col-lg-11':' col-lg-7';
if(  $axil_options['axil_blog_details_social_share']  ){
    $axil_single_blog_sidebar_class = ($sidebar === 'full')  || !is_active_sidebar( 'sidebar-1' )? 'col-lg-11':' col-lg-7';
}else{
    $axil_single_blog_sidebar_class = ($sidebar === 'full')  || !is_active_sidebar( 'sidebar-1' )? 'col-lg-12':' col-lg-8';
}

$thumb_size         = 'axil-single-blog-thumb';
$has_post_thumbnail = has_post_thumbnail() ? 'axil-section-gap':'axil-section-gapBottom';
?>
<div class="axil-blog-area axil-section-gap">
    <?php  if (has_post_thumbnail()) { ?>
        <div class="axil-single-post post-formate post-quote">
            <div class="container">
                <div class="content-blog format-quote">
                    <div class="inner">
                        <div class="content">
                            <blockquote>
                                <h4 class="title">“<?php the_title(); ?>”</h4>
                            </blockquote>
                             <?php Helper::etrade_singlepostmeta(); ?>      
                        </div>
                    </div>
                </div>
            </div>
        </div> 
     <?php } ?> 
    <div class="post-single-wrapper position-relative">
        <div class="container">
            <div class="row"> 
                <?php if ( is_active_sidebar( 'sidebar-1' ) && $sidebar == 'right' || $sidebar == 'full') { ?>
                    <?php if ($axil_options['axil_blog_details_social_share'] && function_exists('axil_sharing_icon_links_bottom')) { ?>
                            <div class="col-lg-1">
                                <?php axil_sharing_icon_links_bottom(); ?>
                            </div> 
                    <?php } ?>  
                <?php } ?> 
                <?php if ( is_active_sidebar( 'sidebar-1' ) && $sidebar == 'left') { ?>
                    <div class="col-lg-4">
                        <aside class="axil-sidebar-area">
                           <?php dynamic_sidebar(); ?>                        
                        </aside>
                    </div>
                <?php } ?>   
                <div class="<?php echo esc_attr( $axil_single_blog_sidebar_class ); ?> axil-post-wrapper"> 
                      <?php  if (has_post_thumbnail()) { ?>
                        <div class="post-thumbnail">
                            <?php the_post_thumbnail($thumb_size, ['class' => 'blog-single-image']) ?>
                        </div>
                    <?php } ?>   
                    <?php 
                        the_content();

                        wp_link_pages(array(
                            'before' => '<div class="axil-page-links"><span class="page-link-holder">' . esc_html__('Pages:', 'etrade') . '</span>',
                            'after' => '</div>',
                            'link_before' => '<span>',
                            'link_after' => '</span>',
                        ));
                       ?>

                    <?php 
                    /**
                     *  Output comments wrapper if it's a post, or if comments are open,
                     * or if there's a comment number – and check for password.
                     * */

                    if ((is_single() || is_page()) && (comments_open() || get_comments_number()) && !post_password_required()) {
                        ?>
                        <div class="axil-comment-area"> 
                            <?php comments_template(); ?> 
                        </div><!-- .comments-wrapper --> 
                    <?php } ?>  
                </div> 
                    <?php if ( is_active_sidebar( 'sidebar-1' ) && $sidebar == 'left') { ?>
                        <?php if ($axil_options['axil_blog_details_social_share'] && function_exists('axil_sharing_icon_links_bottom')) { ?>
                                <div class="col-lg-1">
                                    <?php axil_sharing_icon_links_bottom(); ?>
                                </div> 
                        <?php  } ?>  
                    <?php } ?> 
                    <?php if ( is_active_sidebar( 'sidebar-1' ) && $sidebar == 'right' ) { ?>
                        <div class="col-lg-4">
                            <aside class="axil-sidebar-area">
                               <?php dynamic_sidebar(); ?>                        
                            </aside>
                        </div>
                    <?php } ?> 
            </div>
        </div>
    </div>
</div>  