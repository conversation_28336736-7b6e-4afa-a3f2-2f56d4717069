<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
use Elementor\Group_Control_Image_Size;
use Elementor\Utils;
use Elementor\Group_Control_Typography;

$allowed_tags = wp_kses_allowed_html( 'post' );
?>
<div class="row">
<?php 
    foreach ( $settings['deals_list'] as $deals_list ): 
    $attr = ''; 
    $btn = ''; 
    if ('1' == $deals_list['link_type']) {
            if ( !empty( $deals_list['link']['url'] ) ) {
                $attr  = 'href="' . $deals_list['link']['url'] . '"';
                $attr .= !empty( $deals_list['link']['is_external'] ) ? ' target="_blank"' : '';
                $attr .= !empty( $deals_list['link']['nofollow'] ) ? ' rel="nofollow"' : '';
                $title = '<a ' . $attr . '>' . $deals_list['title'] . '</a>';
            } 
        }else {
        $attr  = 'href="' . get_permalink($deals_list['page_link']) . '"';
        $attr .= ' target="_self"';
        $attr .= ' rel="nofollow"';     
        $title = '<a ' . $attr . '>' . $deals_list['title'] . '</a>';                   
         
    }
    ?>   
 <div class="col-md-6">
      <div class="product-collection-three">
          <div class="collection-content">
            <?php  if ( !empty( $deals_list['title'] ) ) { ?>    
                  <h6 class="title"><?php echo wp_kses( $title , $allowed_tags ); ?></h6> 
              <?php } ?>  
               <div class="price-warp">
                  <?php  if ( !empty( $deals_list['subtitle'] ) ) { ?>    
                      <span class="price-text"><?php echo wp_kses( $deals_list['subtitle'] , $allowed_tags ); ?></span>
                  <?php } ?>  
                  <?php  if ( !empty( $deals_list['current-price'] ) ) { ?>
                      <span class="price"><?php echo wp_kses( $deals_list['current-price'] , $allowed_tags ); ?></span>
                  <?php } ?>  
              </div>   
          </div>
          <div class="collection-thumbnail">
             <?php echo Group_Control_Image_Size::get_attachment_image_html( $deals_list, 'image_size', 'image' );?>
          </div>
      </div>
    </div>
     <?php endforeach;?>  
</div>