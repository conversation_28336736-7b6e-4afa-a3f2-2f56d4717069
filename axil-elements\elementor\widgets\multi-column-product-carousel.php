<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Typography;
use Elementor\Scheme_Typography;
use Elementor\Scheme_Color;
use Elementor\DATE_TIME;
use Elementor\Group_Control_Image_Size;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class multi_column_Product_Carousel extends Widget_Base {

 public function get_name() {
        return 'wooc-mc-product-carousel';
    }    
    public function get_title() {
        return __( 'MultiColom Product Carousel', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-posts-carousel';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
    public function axil_get_img($img)
     {
         $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
         return $img;
    }

 	public function get_product_name ( $post_type = 'product' ){
        $options = array();
        $options = ['0' => esc_html__( 'None', 'etrade-elements' )];
        $axil_post = array( 'posts_per_page' => -1, 'post_type'=> $post_type );
        $axil_post_terms = get_posts( $axil_post );
        if ( ! empty( $axil_post_terms ) && ! is_wp_error( $axil_post_terms ) ){
            foreach ( $axil_post_terms as $term ) {
                $options[ $term->ID ] = $term->post_title;
            }
            return $options;
        }
    }
  
    private function wooc_sub_cat_dropdown() {
        $terms = get_terms( array( 'taxonomy' => 'product_cat', 'orderby' => 'count', 'hide_empty' => 0 ) );
        $category_dropdown = array( '' => esc_html__( 'All Categories', 'etrade-elements' ) );

        foreach ( $terms as $category ) {
          $category_dropdown[$category->term_id] = $category->name;
        }

        return $category_dropdown;
    }
 

	 private function axil_get_all_pages()
	    {

	        $page_list = get_posts(array(
	            'post_type' => 'page',
	            'orderby' => 'date',
	            'order' => 'DESC',
	            'posts_per_page' => -1,
	        ));

	        $pages = array();

	        if (!empty($page_list) && !is_wp_error($page_list)) {
	            foreach ($page_list as $page) {
	                $pages[$page->ID] = $page->post_title;
	            }
	        }

	        return $pages;
	    }

	private function wooc_build_query( $settings ) {

		if ( !$settings['custom_id'] ) {
 
			$p_ids = array(); 
			if ( !empty($settings['posts_not_in'])){
			    foreach ( $settings['posts_not_in'] as $p_idsn ) {
			        $p_ids[] = $p_idsn;
			    }
			} 
			$number = $settings['number'];
			$args = array(
				'post_type'      => 'product',
				'posts_per_page' => $number ? $number : 3,
				'ignore_sticky_posts' => true,
				'post_status'         => 'publish',
				'suppress_filters'    => false,
				'post__not_in'   => $p_ids
			);

			$args['tax_query'] = array();

			// Category
			if ( !empty( $settings['cat'] ) ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_cat',
					'field'    => 'term_id',
					'terms'    => $settings['cat'],
				);
			}

			// Featured only
			if ( $settings['featured_only'] ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_visibility',
					'field'    => 'slug',
					'terms'    => 'featured',
				);
			}

			// Out-of-stock hide
			if ( $settings['out_stock_hide'] ) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_visibility',
					'field'    => 'slug',
					'terms'    => 'outofstock',
					'operator' => 'NOT IN',
				);
			}

			// Order
			$args['orderby'] = $settings['orderby'];
			switch ( $settings['orderby'] ) {

				case 'title':
				case 'menu_order':
				$args['order']    = 'ASC';
				break;

				case 'bestseller':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = 'total_sales';
				break;

				case 'rating':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_wc_average_rating';
				break;

				case 'price_l':
				$args['orderby']  = 'meta_value_num';
				$args['order']    = 'ASC';
				$args['meta_key'] = '_price';
				break;

				case 'price_h':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_price';
				break;
			}
		}else {

			$posts = array_map( 'trim' , explode( ',', $settings['product_ids'] ) );
			

			$args = array(
				'post_type'      => 'product',
				'ignore_sticky_posts' => true,
				'nopaging'       => true,
				'post__in'       => $posts,
				'orderby'        => 'post__in',
			);

			

		}

		return new \WP_Query( $args );
	}


    protected function register_controls() {
         

 	$this->start_controls_section(
            'sec_general',
            [
                'label' => esc_html__( 'General', 'etrade-elements' ),                
            ]
        );

		$this->add_control(
		    'style',
		    [
		        'label' => esc_html__( 'Product Loop Style', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT,
		        'default' => '1',

		        'options' => [
		            '1'   => esc_html__( 'Style One', 'etrade-elements' ),
		            '2'   => esc_html__( 'Style Two', 'etrade-elements' ),
		            '3'   => esc_html__( 'Style Three', 'etrade-elements' ),                           
		            '4'   => esc_html__( 'Style four', 'etrade-elements' ),                           
		            '5'   => esc_html__( 'Style Five', 'etrade-elements' ),
		            '6'   => esc_html__( 'Style Six', 'etrade-elements' ),                            
		            '7'   => esc_html__( 'Style Seven', 'etrade-elements' ),                            
		            '8'   => esc_html__( 'Style Eight', 'etrade-elements' ),                            
		            '9'   => esc_html__( 'Style Nine', 'etrade-elements' ),                            
		           
		        ],
		    ] 
		);

		$this->add_control(
		    'section_title_display',
		    [
		         'type' => Controls_Manager::SWITCHER,
				'label'       => esc_html__( 'Section Title Display', 'etrade-elements' ),
				'label_on'    => esc_html__( 'On', 'etrade-elements' ),
				'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
				'default'     => 'yes',
				 'separator'     => 'before',
	        
		    ] 
		);    
      
        $this->add_control(
            'title_style',
            [
                'label' => __( 'Title Layout', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT,
                'default' => '1',
                'options' => [
                    '1'   => __( 'Style One', 'etrade-elements' ),
                    '2'   => __( 'Style Two', 'etrade-elements' ),                  
                  

                ],
            ] 
        ); 
 
		$this->add_control(
		    'sub_title',
		    [
		    	'type'    => Controls_Manager::TEXT,
				'label'       => esc_html__( 'Section Title before', 'etrade-elements' ),
				'default'     => esc_html__( 'Our Products', 'etrade-elements' ),
				'condition'   => array( 'section_title_display' => 'yes', 'title_style' => '1' ),	
				'label_block'   => true,   		
				'separator'     => 'before',	
		    ]
		); 
		
		$this->add_control(
		    'beforetitlestyle',
		    [
		        'label' => esc_html__( 'Before Color', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT,
		        'default' => 'primary',
		        	'condition'   => array( 'section_title_display' => 'yes', 'title_style' => '1' ),	
		        'options' => [
		            'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
		            'secondary'   => esc_html__( 'Secondary', 'etrade-elements' ), 
		            'primary2'   => esc_html__( 'Primary 2', 'etrade-elements' ),                                          
		                                                      
		        ],
		    ] 
		);   

 		$this->add_control(
		        'icon',
		        [
		            'label' => esc_html__( 'Icons', 'etrade-elements' ),
		            'type' => Controls_Manager::ICONS,
		            'condition'   => array( 'section_title_display' => 'yes', 'title_style' => '1' ),	
		            'default' => [
		                'value' => 'far fa-shopping-basket',
		                'library' => 'solid',
		            ],
		                  
		        ]
		    );


		$this->add_control(
		    'title',
		    [
		    	'type'    => Controls_Manager::TEXT,
				'label'       => esc_html__( 'Section Title', 'etrade-elements' ),
				'default'     => esc_html__( 'Explore our Products', 'etrade-elements' ),
				'label_block'   => true,   
				'condition'   => array( 'section_title_display' => 'yes' ),	
				'separator'     => 'before',		
		    ]
		);
		$this->add_control(
			'sec_title_tag',
			[
				'label' => esc_html__('Title HTML Tag','etrade-elements'),
				'type' => Controls_Manager::CHOOSE,
				'options' => [
					'h1' => [
						'title' => esc_html__('H1','etrade-elements'),
						'icon' => 'eicon-editor-h1'
					],
					'h2' => [
						'title' => esc_html__('H2','etrade-elements'),
						'icon' => 'eicon-editor-h2'
					],
					'h3' => [
						'title' => esc_html__('H3','etrade-elements'),
						'icon' => 'eicon-editor-h3'
					],
					'h4' => [
						'title' => esc_html__('H4','etrade-elements'),
						'icon' => 'eicon-editor-h4'
					],
					'h5' => [
						'title' => esc_html__('H5','etrade-elements'),
						'icon' => 'eicon-editor-h5'
					],
					'h6' => [
						'title' => esc_html__('H6','etrade-elements'),
						'icon' => 'eicon-editor-h6'
					]
				],
				'default' => 'h2',
				'condition'   => array( 'section_title_display' => 'yes' ),	
				'toggle' => false,
				'label_block' => true,
	
			]
		);
		 $this->add_control(
         'image',
            [
                'label' => esc_html__('Product Image','etrade-elements'),
                'type'=>Controls_Manager::MEDIA,
                'default' => [
                    'url' =>  $this->axil_get_img( 'title-icon.png' ),
                ],
             	'condition'   => array( 'section_title_display' => 'yes', 'title_style' => '2'  ),	
                'dynamic' => [
                    'active' => true,
                ],
                    
            ]
        );       
			$this->add_group_control(
                Group_Control_Image_Size::get_type(),
                [
                    'name' => 'image_size',
                    'default' => 'full',
                    'separator' => 'none',
                    	'condition'   => array( 'section_title_display' => 'yes', 'title_style' => '2'  ),
                       
                ]
            );
 	$this->end_controls_section();
		
         	 
        
    $this->start_controls_section(
	        'sec_filter',
	        [
	            'label' => esc_html__( 'Product Filtering', 'etrade-elements' ),
	            
	        ]
	    );

         $this->add_control(
            'rating_display',
            [
                
                'type' => Controls_Manager::SWITCHER,
                'label'       => esc_html__( 'Rating Display', 'etrade-elements' ),
                'label_on'    => esc_html__( 'On', 'etrade-elements' ),
                'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
               
                
            ] 
        ); 	

 
        $this->add_control(
            'display_attributes',
            [
                
                'type' => Controls_Manager::SWITCHER,
                'label'       => esc_html__( 'Attributes Display', 'etrade-elements' ),
                'label_on'    => esc_html__( 'On', 'etrade-elements' ),
                'label_off'   => esc_html__( 'Off', 'etrade-elements' ),
                'default'     => 'yes',
                
            ] 
        ); 	
       				
	 	$this->add_control(
	        'number',
	            [
	                'label'   => esc_html__( 'Number of items', 'etrade-elements' ),
	                'type'    => Controls_Manager::NUMBER,
	                'default'     => 16,               
	                
	            ]

	        );
		
	 	$this->add_control(
	        'number_off_row',
	            [
	                'label'   => esc_html__( 'Items per slide', 'etrade-elements' ),
	                'type'    => Controls_Manager::NUMBER,
	                'default'     => 8, 
	                
	            ]

	        );
 
	
         $this->add_control(      
            'cat',
                [
                'label' => esc_html__( 'Categories', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT2,
               	'options'     => $this->wooc_sub_cat_dropdown(),            
                'label_block'   => true,                
                'default'     => '0',
                'separator'     => 'before',
                 'multiple'  => true,
                ] 

            );


		$this->add_control(      
            'orderby',
                [
                'label' => esc_html__( 'OrderBy', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT2,
	               'options'     => array(
						'date'        => esc_html__( 'Date (Recents comes first)', 'etrade-elements' ),
						'title'       => esc_html__( 'Title', 'etrade-elements' ),
						'bestseller'  => esc_html__( 'Bestseller', 'etrade-elements' ),
						'rating'      => esc_html__( 'Rating(High-Low)', 'etrade-elements' ),
						'price_l'     => esc_html__( 'Price(Low-High)', 'etrade-elements' ),
						'price_h'     => esc_html__( 'Price(High-Low)', 'etrade-elements' ),
						'rand'        => esc_html__( 'Random(Changes on every page load)', 'etrade-elements' ),
						'menu_order'  => esc_html__( 'Custom Order (Available via Order field inside Page Attributes box)', 'etrade-elements' ),
					),    
                'label_block'   => true,                
                'default'     => 'date',
                'separator'     => 'before',
                ] 
            );

		$this->add_control(
		    'out_stock_hide',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Hide Out-of-stock Products', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		); 		

		$this->add_control(
		    'featured_only',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Display only Featured Products', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		); 	
		$this->add_control(
		    'product_display_hover',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Display Hover Image', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => '',
		        
		    ] 
		); 		
			$this->add_control(
		    'sale_price_only',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Display only sale price', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'yes',		      
		        
		    ] 
		);  
 
		$this->add_control(
		    'custom_id',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Custom Product ID', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => '',
		         'separator'     => 'before',
		        
		    ] 
		);   



		$this->add_control(
		    'product_ids',
		    [
			'label'       => __( "Product ID's, seperated by commas", 'etrade-elements' ),
			'type'    => Controls_Manager::TEXT,
			'description' => __( "Put the comma seperated ID's here eg. 23,26,89", 'etrade-elements' ),
			'condition'   => array( 'custom_id' => 'yes' ),
		    ]
		);


		$this->add_control(      
		    'posts_not_in',
		        [
		        'label' => __( 'Select The Posts that will not display', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT2,
		        'options'       => $this->get_product_name(),                  
		        'label_block'   => true,
		        'multiple'      => true,
		        'separator'     => 'before',
		        ] 
		    );

		$this->add_control(
		    'slider_btn_style',
		    [
		        'label' => esc_html__( 'Slider Button Style', 'etrade-elements' ),
		        'type' => Controls_Manager::SELECT,
		        'default' => '1',

		        'options' => [
		            '1'   => esc_html__( 'Style One', 'etrade-elements' ),
		            '2'   => esc_html__( 'Style Two', 'etrade-elements' ),
		             
		        ],
		    ] 
		);

 
       $this->end_controls_section();   


	$this->start_controls_section(
		'sec_countdown',
		    [
		        'label' => __( 'Sale Countdown', 'etrade-elements' ),  
		        'condition'   => array( 'section_title_display' => 'yes', 'title_style' => '1'  ),
		                  
		    ]
		);    

		$this->add_control(
		    'iscountdown',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Sale Countdown', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'no',
		        
		    ] 
		); 		             

		$this->add_control(
			'date',
			[
				'label' => __( 'Date-Time', 'etrade-elements' ),
				'type' => Controls_Manager::DATE_TIME,
				'condition'   => array( 'iscountdown' => 'yes' ),
			]
		);

		$this->end_controls_section();

		$this->start_controls_section(
		'sec_linking',
		    [
		        'label' => __( 'Link and Button', 'etrade-elements' ),  
		          	
		                  
		    ]
		);    

		$this->add_control(
		    'islink',
		    [
		        
		        'type' => Controls_Manager::SWITCHER,
		        'label'       => __( 'Show Button', 'etrade-elements' ),
		        'label_on'    => __( 'On', 'etrade-elements' ),
		        'label_off'   => __( 'Off', 'etrade-elements' ),
		        'default'     => 'no',
		        
		    ] 
		); 		              


		$this->add_control(
		    'btntext',
		    [
		        'label'   => __( 'Button Text', 'etrade-elements' ),
		        'type'    => Controls_Manager::TEXT,
		        'default' => 'View All Products',
		        'condition'   => array( 'islink' => 'yes' ),
		    ]
		);


		 $this->add_control(
            'axil_link_type',
            [
                'label' => esc_html__( 'See All Link Type', 'etrade-elements'),
                'type' => Controls_Manager::SELECT,
                'options' => [
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ],
                'default' => '1',
                'condition'   => array( 'islink' => 'yes' ),
                'label_block' => true
            ]
        );


     $this->add_control(
            'axil_page_link',
            [
                'label' => esc_html__('Select See All Page', 'etrade-elements'),
                'type' => Controls_Manager::SELECT2,
                'label_block' => true,
                'options' => $this-> axil_get_all_pages(),
                'condition' => [
                    'axil_link_type' => '2',
                    'islink' => 'yes',
                ]
            ]
        );

		$this->add_control(
		    'url',
		    [
		        'label'   => __( 'Detail URL', 'etrade-elements' ),
		        'type'    => Controls_Manager::URL,
		        'placeholder' => 'https://your-link.com',	
		        'condition' => [
                    'axil_link_type' => '1',
                    'islink' => 'yes',
                ]	        
		    ]
		);   



		$this->end_controls_section();
		
        $this->start_controls_section(
            'title_style_section',
            [
                'label' => __( 'Section Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        );
  
          $this->add_control(
            'title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
                
                'selectors' => array(
                    '{{WRAPPER}} .sec-title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
                 
                'selector' => '{{WRAPPER}} .sec-title',
            ]
        );
       
        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                 
                'selectors' => [
                    '{{WRAPPER}} .sec-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    
                ],
            ]
        );
       
    $this->end_controls_section();

    $this->start_controls_section(
            'sub_title_style_section',
            [
                'label' => __( 'Section Title before', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        ); 
 
          $this->add_control(
            'sub_title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
              
                'selectors' => array(
                    '{{WRAPPER}} .title-highlighter' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'sub_title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
               
                'selector' => '{{WRAPPER}} .title-highlighter',
            ]
        );
       
        $this->add_responsive_control(
            'sub_title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                'selectors' => [
                    '{{WRAPPER}} .title-highlighter' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    
                ],
            ]
        );
       
    $this->end_controls_section(); 

    $this->start_controls_section(
            'p_title_style_section',
            [
                'label' => __( 'Products Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        ); 
 
          $this->add_control(
            'p_title_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
              
                'selectors' => array(
                    '{{WRAPPER}} .product-content .title' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'p_title_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
               
                'selector' => '{{WRAPPER}} .product-content .title',
            ]
        );
       
        $this->add_responsive_control(
            'p_title_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                'selectors' => [
                    '{{WRAPPER}} .product-content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    
                ],
            ]
        );
       
    $this->end_controls_section();

    $this->start_controls_section(
            'prices_style_section',
            [
                'label' => __( 'Products Prices', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,                
            ]
        ); 
 
          $this->add_control(
            'prices_color',
            [
                'label' => __( 'Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,  
                'default' => '',
              
                'selectors' => array(
                    '{{WRAPPER}} .product-content .product-price-variant div.price' => 'color: {{VALUE}}',
                ),
            ]
        );

         $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'prices_font_size',
                'label' => __( 'Typography', 'etrade-elements' ),                
               
                'selector' => '{{WRAPPER}} .product-content .product-price-variant div.price',
            ]
        );
       
        $this->add_responsive_control(
            'prices_margin',
            [
                'label' => __( 'Margin', 'etrade-elements' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%', 'em' ],
                'selectors' => [
                    '{{WRAPPER}} .product-content .product-price-variant div.price' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    
                ],
            ]
        );
       
    $this->end_controls_section();


      $this->start_controls_section(
            'etrade_responsive',
                [
                'label' => __( 'Responsive Columns', 'etrade-elements' ),
               
                ]
            );

            $this->add_control(
                'col_xl',
                [
                    'label' => __( 'Desktops: > 1199px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '3',
                ] 
            );
            $this->add_control(
            'col_lg',
                [
                    'label' => __( 'Desktops: > 991px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                       '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '4',
                ] 
            );
            $this->add_control(
            'col_md',
                [
                    'label' => __( 'Tablets: > 767px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                            '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '4',
                ] 
            );

            $this->add_control(
            'col_sm',
                [
                    'label' => __( 'Phones: >575px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                       '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '6',
                ] 
            );         
            $this->add_control(
            'col_mobile',
                [
                    'label' => __( 'Small Phones: <576px', 'etrade-elements' ),
                    'type' => Controls_Manager::SELECT,
                    'options' => [
                        '12'  => esc_html__( '1 Col', 'etrade-elements' ),
                        '6'  => esc_html__( '2 Col', 'etrade-elements' ),
                        '4'  => esc_html__( '3 Col', 'etrade-elements' ),
                        '3'  => esc_html__( '4 Col', 'etrade-elements' ),  
                        '2'  => esc_html__( '6 Col', 'etrade-elements' ),
                        ],
                    'default' => '12',
                ] 
            );
       $this->end_controls_section();

    }
 
    private function slick_load_scripts(){
        wp_enqueue_style(  'slick' );
        wp_enqueue_style(  'slick-theme' );
        wp_enqueue_script( 'slick' );
    } 
	private function countdown_load_scripts(){
		wp_enqueue_script( 'jquery-countdown' );
	}
	protected function render() {
		$settings = $this->get_settings();

			$this->slick_load_scripts();	
			$this->countdown_load_scripts();
			$settings['query']    = $this->wooc_build_query( $settings );
			$template = 'muc-product-carousel';
	 

		return wooc_Elements_Helper::wooc_element_template( $template, $settings );
	}
}