<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

$axil_options           = Helper::axil_get_options();
$search      = isset( $_GET['s'] ) ? $_GET['s'] : '';
$product_cat = isset( $_GET['product_cat'] ) ? $_GET['product_cat'] : '';

$all_label = $label = esc_html__( 'All Categories', 'etrade' );
if ( isset( $_GET['product_cat'] ) ) {
	$pcat = $_GET['product_cat'];
	if ( isset( $category_dropdown[$pcat] ) ) {
		$label = $category_dropdown[$pcat]['name'];
	}
}
	$orderby = 'name';
	$order = 'asc';
	$hide_empty = false ;
	$cat_args = array(
		'orderby'    => $orderby,
		'order'      => $order,
		'hide_empty' => $hide_empty,
		'parent' 	 => 0,

	);

	$product_categories = get_terms( 'product_cat', $cat_args );
?>
<div class="product-search"> 
	<div class="search-wrap style-1"> 
		<div class="search_box_wrapper">
			<div class="category_field">
				<div class="form-group m-0">
					<select id="search_category_filter" class="form-control search_category">
						<option value="" selected><?php esc_html_e('Categories','etrade'); ?></option> 
						<?php
						if( !empty($product_categories) ) {
							foreach ( $product_categories as $key => $category ) {
								echo "<option value='{$category->term_id}'>{$category->name}</option>";
							}
						}
						?>
					</select>
				</div>
			</div>                            
			<div class="input_field">
				<div class="form-group m-0">
					<input type="text" placeholder="<?php esc_html_e('What are you looking for....','etrade'); ?>"
						class="product-search-input form-control" id="product_search">
				</div>
			</div>
			<div class="search_input_loader"></div>
			<div class="search_suggetion_field"></div>
		</div> 
	</div>
</div>


 

