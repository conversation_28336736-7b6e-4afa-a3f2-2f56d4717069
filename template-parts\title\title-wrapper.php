<?php
/**
 * Template part for displaying header page title
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options   = Helper::axil_get_options();
$banner_layout  = Helper::axil_banner_layout();
$banner_area    = $banner_layout['banner_area'];
$banner_style   = '2';
if ( is_home() ) {
    get_template_part('/template-parts/title/blog-title');
} elseif( is_search() ) {
    get_template_part('/template-parts/title/blog-title');
} elseif( !is_front_page() && is_page() ) {
    if ("no" !== $banner_area && "0" !== $banner_area) {
         if (is_page('cart')) {
          
        } else {
           get_template_part('/template-parts/title/layout', $banner_style);
        }
        
    } 
}elseif(is_author()) {
    get_template_part('/template-parts/title/author');
} elseif(is_archive()) { 
    if( WOOC_WOO_ACTIVED ){
        if ( is_shop() || is_product_category() ) { 

        	get_template_part('/template-parts/title/wooc-shop');

    	}else{
    		get_template_part('/template-parts/title/blog-title');
    	} 
    }else{
        get_template_part('/template-parts/title/blog-title');
    } 
} elseif(is_single()) {  

} else { 
 
}