<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Group_Control_Css_Filter;
use Elementor\Repeater;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Box_Shadow;
use Elementor\Group_Control_Image_Size;
use Elementor\Utils;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly
class Video_Popup extends Widget_Base {
   
    public function get_name() {
        return 'axil-video-popup';
    }
    
    public function get_title() {
        return __( 'Video Popup', 'etrade-elements' );
    }

    public function get_icon() {
        return 'eicon-video-camera';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
    protected function register_controls() {
          $this->start_controls_section(
            'img_v_content',
            [
                'label' => __( 'Image', 'etrade-elements' ),
            ]
        );    
 
        $this->add_control(
            'layout',
            [
                'label' => __( 'Layout', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT,
                'default' => '2',
                'options' => [
                    '1'   => __( 'Layout One', 'etrade-elements' ),
                    '2'   => __( 'Layout Two', 'etrade-elements' ),                  
                    

                ],
            ] 
        ); 
       
		$this->add_control(
		    'image',
		    [
		        'label' => __('Image','etrade-elements'),
		        'type'=>Controls_Manager::MEDIA,
		        'default' => [
		            'url' => Utils::get_placeholder_image_src(),
		        ],
		        'dynamic' => [
		            'active' => true,
		        ],
		        'selectors' => [					
					'{{WRAPPER}} .axil-signin-banner' => 'background-image: url({{URL}});',
				],
		            
		    ]
		);		
	  $this->add_group_control(
            Group_Control_Image_Size::get_type(),
            [
                'name' => 'image_size',
                'default' => 'full',
                'separator' => 'none',                     
            ]
        );

		$this->add_control(
			'videourl',
			[
			    'label'   => __( 'Video Popup URL', 'etrade-elements' ),
			    'type'    => Controls_Manager::URL,
			    'placeholder' => 'https://your-link.com',
			]
		);         

	    $this->end_controls_section();	
	}
	

    private function magnific_load_scripts(){
        wp_enqueue_script(  'jquery-magnific-popup' );
        wp_enqueue_style(  'magnific-popup' );    
    }

	

protected function render() {
	$settings = $this->get_settings(); 
	$this->magnific_load_scripts();	
	$simagev =  $settings['videourl']['url'];  

	if ('1' == $settings['layout']) { ?>
	 <div class="testimonial-video-box">
		<div class="thumbnail">
			<?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>
		</div>
     	<?php if ( !empty( $settings['videourl']['url'] ) ): ?>
         <div class="play-btn">
            <a href="<?php echo esc_url( $simagev );?>" class="popup-youtube video-icon">
                <i class="fas fa-play"></i>
            </a>
        </div>
    	<?php endif; ?>  
    </div>
	<?php  }else{ ?>	 
	 <div class="video-banner">
	    <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>
	     <?php if ( !empty( $settings['videourl']['url'] ) ): ?>
	        <div class="popup-video-icon">
	            <a href="<?php echo esc_url( $simagev );?>" class="popup-youtube video-icon">
	                <i class="fas fa-play"></i>
	            </a>
	        </div>
	    <?php endif; ?>
    </div> 

<?php }
}
}