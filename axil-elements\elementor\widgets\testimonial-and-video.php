<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Repeater;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Utils;
use Elementor\Group_Control_Typography;

if (!defined('ABSPATH')) exit; // Exit if accessed directly
class Testimonial_video extends Widget_Base
{

    public function get_name()
    {
        return 'testimonial-and-video';
    }
    public function get_title()
    {
        return esc_html__('Testimonial & video', 'etrade-elements');
    }
    public function get_icon()
    {
        return 'eicon-testimonial-carousel';
    }
    public function get_categories()
    {
        return [ETRADE_ELEMENTS_THEME_PREFIX . '-widgets'];
    }
    protected function register_controls()
    {

        $this->start_controls_section(
            'img_vand_content',
            [
                'label' => __('Image & Video Url', 'etrade-elements'),
            ]
        );
        $this->add_control(
            'image',
            [
                'label' => __('Image', 'etrade-elements'),
                'type' => Controls_Manager::MEDIA,
                'default' => [
                    'url' => Utils::get_placeholder_image_src(),
                ],
                'dynamic' => [
                    'active' => true,
                ],
                'selectors' => [
                    '{{WRAPPER}} .axil-signin-banner' => 'background-image: url({{URL}});',
                ],

            ]
        );
        $this->add_group_control(
            Group_Control_Image_Size::get_type(),
            [
                'name' => 'image_size',
                'default' => 'full',
                'separator' => 'none',
            ]
        );

        $this->add_control(
            'videourl',
            [
                'label'   => __('Video Popup URL', 'etrade-elements'),
                'type'    => Controls_Manager::URL,
                'placeholder' => 'https://your-link.com',
            ]
        );
        $this->end_controls_section();
        $this->start_controls_section(
            'testimonial_layout',
            [
                'label' => esc_html__('Testimonial Section Title', 'etrade-elements'),
            ]
        );

        $this->add_control(
            'title',
            [
                'type'          => Controls_Manager::TEXT,
                'label'         => esc_html__('Title', 'etrade-elements'),
                'default'       => 'Lorem Ipsum',
                'label_block'   => true,
            ]
        );
        $this->add_control(
            'sec_title_tag',
            [
                'label' => esc_html__('Title HTML Tag', 'etrade-elements'),
                'type' => Controls_Manager::CHOOSE,
                'options' => [
                    'h1' => [
                        'title' => esc_html__('H1', 'etrade-elements'),
                        'icon' => 'eicon-editor-h1'
                    ],
                    'h2' => [
                        'title' => esc_html__('H2', 'etrade-elements'),
                        'icon' => 'eicon-editor-h2'
                    ],
                    'h3' => [
                        'title' => esc_html__('H3', 'etrade-elements'),
                        'icon' => 'eicon-editor-h3'
                    ],
                    'h4' => [
                        'title' => esc_html__('H4', 'etrade-elements'),
                        'icon' => 'eicon-editor-h4'
                    ],
                    'h5' => [
                        'title' => esc_html__('H5', 'etrade-elements'),
                        'icon' => 'eicon-editor-h5'
                    ],
                    'h6' => [
                        'title' => esc_html__('H6', 'etrade-elements'),
                        'icon' => 'eicon-editor-h6'
                    ]
                ],
                'default' => 'h2',

                'label_block' => true,

            ]
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'testimonial_content',
            [
                'label' => esc_html__('Testimonial List', 'etrade-elements'),
            ]
        );

        $repeater = new Repeater();


        $repeater->add_control(
            'author',
            [
                'label'   => esc_html__('Author', 'etrade-elements'),
                'type'    => Controls_Manager::TEXT,
                'default' => 'Lorem Ipsum',
                'label_block'   => true,
            ]
        );
        $repeater->add_control(
            'designation',
            [
                'label'   => esc_html__('Designation', 'etrade-elements'),
                'type'    => Controls_Manager::TEXT,
                'default' => 'Lorem Ipsum',
                'label_block'   => true,
            ]
        );
        $repeater->add_control(
            'content',
            [
                'label'   => esc_html__('Content', 'etrade-elements'),
                'type'    => Controls_Manager::TEXTAREA,
                'default' => esc_html__('Very good Design. Flexible. Fast Support.', 'etrade-elements'),
            ]
        );

        $this->add_control(
            'list_testimonial',
            [
                'type' => Controls_Manager::REPEATER,
                'fields' => $repeater->get_controls(),
                'show_label' => false,
                'default' => [
                    [
                        'author' => esc_html__('Amy Smith', 'etrade-elements'),
                        'content' => 'This is the best website I have ordered something from. I highly recommend.',
                    ],
                    [
                        'author' => esc_html__('Amy Smith', 'etrade-elements'),
                        'content' => 'This is the best website I have ordered something from. I highly recommend.',
                    ],
                    [
                        'author' => esc_html__('Amy Smith', 'etrade-elements'),
                        'content' => 'This is the best website I have ordered something from. I highly recommend.',
                    ],
                    [
                        'author' => esc_html__('Amy Smith', 'etrade-elements'),
                        'content' => 'This is the best website I have ordered something from. I highly recommend.',
                    ],

                ],
                'title_field' => '{{{ author }}}',
            ]
        );

        $this->end_controls_section();
        $this->start_controls_section(
            'testimonial_custom_nav',
            [
                'label' => esc_html__('Custom Nav Text', 'etrade-elements'),
            ]
        );
        $this->add_control(
            'prev',
            [
                'type'    => Controls_Manager::TEXT,
                'label'       => esc_html__('Prev', 'etrade-elements'),
                'default'     => 'Prev',
            ]
        );
        $this->add_control(
            'next',
            [
                'type'    => Controls_Manager::TEXT,
                'label'       => esc_html__('Next', 'etrade-elements'),
                'default'     => 'Next',
            ]
        );
        $this->end_controls_section();

        $this->start_controls_section(
            'section_title_sty',
            [
                'label' => esc_html__('Testimonial Section Title', 'etrade-elements'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'section_title_color',
            [
                'label' => esc_html__('Color', 'etrade-elements'),
                'type' => Controls_Manager::COLOR,
                'default' => '',

                'selectors' => array(
                    '{{WRAPPER}} .heading-title .title' => 'color: {{VALUE}}',

                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'title_font_size',
                'label' => esc_html__('Typography', 'etrade-elements'),

                'selector' => '{{WRAPPER}} .heading-title .title',
            ]
        );

        $this->add_responsive_control(
            'nav_title_margin',
            [
                'label' => esc_html__('Margin', 'etrade-elements'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],

                'selectors' => [
                    '{{WRAPPER}} .heading-title .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',


                ],
            ]
        );
        $this->end_controls_section();

        $this->start_controls_section(
            'title_style_section',
            [
                'label' => esc_html__('Testimonial Content', 'etrade-elements'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'content_color',
            [
                'label' => esc_html__('Color', 'etrade-elements'),
                'type' => Controls_Manager::COLOR,
                'default' => '',

                'selectors' => array(
                    '{{WRAPPER}} .testimonial-style-three p' => 'color: {{VALUE}}',

                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'content_font_size',
                'label' => esc_html__('Typography', 'etrade-elements'),

                'selector' => '{{WRAPPER}} .testimonial-style-three p',
            ]
        );

        $this->add_responsive_control(
            'content_title_margin',
            [
                'label' => esc_html__('Margin', 'etrade-elements'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],

                'selectors' => [
                    '{{WRAPPER}} .testimonial-style-three p' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',


                ],
            ]
        );
        $this->end_controls_section();
        $this->start_controls_section(
            'section_title_style',
            [
                'label' => __('Testimonial Author', 'etrade-elements'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );


        $this->add_control(
            'stitle_color',
            [
                'label' => __('Color', 'etrade-elements'),
                'type' => Controls_Manager::COLOR,
                'default' => '',

                'selectors' => array(
                    '{{WRAPPER}} .author-info .author-name' => 'color: {{VALUE}}',

                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'stitle_font_size',
                'label' => __('Typography', 'etrade-elements'),

                'selector' => '{{WRAPPER}} .author-info .author-name',
            ]
        );

        $this->add_responsive_control(
            'stitle_margin',
            [
                'label' => __('Margin', 'etrade-elements'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],

                'selectors' => [
                    '{{WRAPPER}} .author-info .author-name' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',


                ],
            ]
        );

        $this->end_controls_section();
        $this->start_controls_section(
            'sub_title_style_section',
            [
                'label' => __('Testimonial Designation', 'etrade-elements'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'sub_title_color',
            [
                'label' => __('Color', 'etrade-elements'),
                'type' => Controls_Manager::COLOR,
                'default' => '',

                'selectors' => array(
                    '{{WRAPPER}} .author-info .author-desg' => 'color: {{VALUE}}',

                ),
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'sub_title_font_size',
                'label' => __('Typography', 'etrade-elements'),

                'selector' => '{{WRAPPER}} .author-info .author-desg',
            ]
        );
        $this->add_responsive_control(
            'sub_title_margin',
            [
                'label' => __('Margin', 'etrade-elements'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],

                'selectors' => [
                    '{{WRAPPER}} .author-info .author-desg' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',


                ],
            ]
        );
        $this->end_controls_section();
    }

    private function slick_load_scripts()
    {
        wp_enqueue_style('slick');
        wp_enqueue_style('slick-theme');
        wp_enqueue_script('slick');
    }

    private function magnific_load_scripts()
    {
        wp_enqueue_script('jquery-magnific-popup');
        wp_enqueue_style('magnific-popup');
    }

    protected function render()
    {
        $settings = $this->get_settings();
        $this->magnific_load_scripts();
        $this->slick_load_scripts();
        $simagev =  $settings['videourl']['url'];
?>
        <!-- End Expolre Product Area  -->
        <div class="axil-testimoial-area-two axil-section-gapBottom">
            <div class="container">
                <div class="testimonial-container-box">
                    <div class="row">
                        <div class="col-lg-7">
                            <div class="testimonial-video-box">
                                <div class="thumbnail">
                                    <?php echo Group_Control_Image_Size::get_attachment_image_html($settings, 'image_size', 'image'); ?>
                                </div>
                                <?php if (!empty($settings['videourl']['url'])) : ?>
                                    <div class="play-btn">
                                        <a href="<?php echo esc_url($simagev); ?>" class="popup-youtube video-icon">
                                            <i class="fas fa-play"></i>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-lg-5">
                            <div class="testimonial-style-three-wrapper">
                                <?php if ($settings['title']) { ?>
                                    <div class="heading-title">
                                        <<?php echo esc_html($settings['sec_title_tag']); ?> class="title">
                                            <?php echo wp_kses_post($settings['title']); ?></<?php echo esc_html($settings['sec_title_tag']); ?>>
                                    </div>
                                <?php  } ?>
                                <div class="testimonial-slick-activation-three">
                                    <?php foreach ($settings['list_testimonial'] as $testimonial) :
                                        $has_designation    = $testimonial['designation'] == 'yes' ? true : false;
                                        $designation        = $testimonial['designation'];
                                        $title              = $testimonial['author'];
                                        $content            = $testimonial['content']; 
                                        
                                    ?>
                                        <div class="slick-single-layout testimonial-style-three">
                                            <p><?php echo esc_html($content); ?></p>
                                            <div class="author-info">
                                                <h6 class="author-name"><?php echo esc_html($title); ?></h6>
                                                <span class="author-desg"><?php echo esc_html($designation); ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="testimonial-custom-nav">
                                    <div class="row align-items-center">
                                        <div class="col-6">
                                            <div class="slick-slide-count"></div>
                                        </div>
                                        <div class="col-6">
                                            <div class="slide-custom-nav">
                                                <button class="prev-custom-nav"><i class="fal fa-angle-left"></i><?php echo esc_html($settings['prev']); ?></button>
                                                <button class="next-custom-nav"><?php echo esc_html($settings['next']); ?> <i class="fal fa-angle-right"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    }
}
