<?php
/**
 * Single Product Thumbnails
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/product-thumbnails.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     9.5.0
 */

defined( 'ABSPATH' ) || exit;

// Note: `wc_get_gallery_image_html` was added in WC 3.3.2 and did not exist prior. This check protects against theme overrides being used on older versions of WC.

global $product;

$post_thumbnail_id = $product->get_image_id();

if ( WooC_Functions::wooc_woocommerce_version_check() ) {
    // Use new, updated functions
    $attachment_ids = $product->get_gallery_image_ids();
} else {
    // Use older, deprecated functions
    $attachment_ids = $product->get_gallery_attachment_ids();
}

$gallery_thumbnail = wc_get_image_size( 'gallery_thumbnail' );
$thumbnail_size = apply_filters( 'woocommerce_gallery_thumbnail_size', array( $gallery_thumbnail['width'], $gallery_thumbnail['height'] ) );
$axil_options = Helper::axil_get_options();
$layout = Helper::axil_product_layout_style();
if ( $attachment_ids && has_post_thumbnail() ) {
    if ( $layout == '2' ) {
        echo '<div class="col-lg-2 order-lg-1">';
        echo '<div class="product-small-thumb small-thumb-wrapper small-thumb-style-two ">';
        $image = wp_get_attachment_image( $post_thumbnail_id, 'shop_thumbnail', true );
        echo '<div class="small-thumb-img">' . $image . '</div>';
        foreach ( $attachment_ids as $attachment_id ) {
            $thumbnail_image = wp_get_attachment_image( $attachment_id, $thumbnail_size );

            echo '<div class="small-thumb-img">' . $thumbnail_image . '</div>';
        }
        echo "</div>";
        echo "</div>";

    } elseif ( $layout == '4' || $layout == '6' ) {

        echo '<div class="col-lg-12">';
        echo '<div class="small-thumb-wrapper product-small-thumb-2 small-thumb-style-two small-thumb-style-three">';
        $image = wp_get_attachment_image( $post_thumbnail_id, 'shop_thumbnail', true );
        echo '<div class="small-thumb-img">' . $image . '</div>';

        foreach ( $attachment_ids as $attachment_id ) {
            $thumbnail_image = wp_get_attachment_image( $attachment_id, $thumbnail_size );

            echo '<div class="small-thumb-img">' . $thumbnail_image . '</div>';
        }
        echo "</div>";
        echo "</div>";

    } elseif ( $layout == '5' || $layout == '7' ) {
        echo '<div class="col-lg-12">';
        echo '<div class="small-thumb-wrapper product-small-thumb-2 small-thumb-style-two small-thumb-style-three">';
        $image = wp_get_attachment_image( $post_thumbnail_id, 'shop_thumbnail', true );
        echo '<div class="small-thumb-img">' . $image . '</div>';

        foreach ( $attachment_ids as $attachment_id ) {
            $thumbnail_image = wp_get_attachment_image( $attachment_id, $thumbnail_size );

            echo '<div class="small-thumb-img">' . $thumbnail_image . '</div>';
        }
        echo "</div>";
        echo "</div>";
    } elseif ( $layout == '1' ) {
        echo '<div class="col-lg-2 order-lg-1">';
        echo '<div class="product-small-thumb small-thumb-wrapper">';
        $image = wp_get_attachment_image( $post_thumbnail_id, 'shop_thumbnail', true );
        echo '<div class="small-thumb-img">' . $image . '</div>';

        foreach ( $attachment_ids as $attachment_id ) {
            $thumbnail_image = wp_get_attachment_image( $attachment_id, $thumbnail_size );

            echo '<div class="small-thumb-img">' . $thumbnail_image . '</div>';
        }
        echo "</div>";
        echo "</div>";

    } else {
      

    $attachment_ids = $product->get_gallery_image_ids();

    if ( $attachment_ids && $product->get_image_id() ) {
        foreach ( $attachment_ids as $attachment_id ) {
            echo apply_filters( 'woocommerce_single_product_image_thumbnail_html', wc_get_gallery_image_html( $attachment_id ), $attachment_id ); // phpcs:disable WordPress.XSS.EscapeOutput.OutputNotEscaped
        }
    }
    }

}