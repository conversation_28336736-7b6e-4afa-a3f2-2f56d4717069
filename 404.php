<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 * @package etrade
 */ 
$axil_options = Helper::axil_get_options();
?>
<!doctype html>
<html <?php language_attributes();?>>
<head>
    <meta charset="<?php echo esc_attr( get_bloginfo( 'charset' ) ) ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <?php wp_head();?>
</head>
<body <?php body_class();?>>
<?php

if ( function_exists( 'wp_body_open' ) ) {
    wp_body_open();
}?>
<div id="main-wrapper" class="main-wrapper">
     <section class="error-page onepage-screen-area">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="content" data-sal="slide-up" data-sal-duration="800" data-sal-delay="400">
                        <?php if ( !empty( $axil_options['axil_404_before_title'] ) ) {?>
                            <span class="title-highlighter highlighter-secondary"> <i class="fal fa-exclamation-circle"></i><?php echo esc_html( $axil_options['axil_404_before_title'] ); ?></span>
                        <?php }?>
                        <?php if ( !empty( $axil_options['axil_404_title'] ) ) {?> <h1 class="title"><?php echo esc_html( $axil_options['axil_404_title'] ); ?></h1> <?php }?>
                        <?php if ( !empty( $axil_options['axil_404_subtitle'] ) ) {?> <p><?php echo esc_html( $axil_options['axil_404_subtitle'] ); ?></p> <?php }?>
                        <a class="axil-btn btn-bg-secondary right-icon" href="<?php echo esc_url( home_url( '/' ) ); ?>">
                                <?php echo esc_html( $axil_options['axil_button_text'] ); ?>
                            <i class="fal fa-long-arrow-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="thumbnail" data-sal="zoom-in" data-sal-duration="800" data-sal-delay="400">
                        <?php if ( !empty( $axil_options['axil_404_image'] ) ) {?>
                            <img src="<?php echo esc_url( $axil_options['axil_404_image']['url'] ); ?>" alt="<?php echo esc_attr__( '404 Image', 'etrade' ); ?>">
                        <?php }?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?php wp_footer();?>
</body>
</html>
