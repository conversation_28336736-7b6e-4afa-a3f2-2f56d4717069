/* Gallery  */
:root {
	--gallery-block--gutter-size: 15px;
	--wp--style--block-gap: 15px;
}


input[type=text],
input[type=password],
input[type=email],
input[type=number],
input[type=tel],
textarea {
	border: 1px solid var(--color-light);
}


@media only screen and (max-width: 991px) {
	body.overflow-visible {
		overflow: hidden !important;
		overflow-y: auto !important;

	}
}

.axil-signin-form-wrap {
	align-items: flex-start;
}

.axil-signin-form {
	.title {
		margin-bottom: 55px;
	}

	.form-row {
		&.rememberme {
			margin-bottom: 30px;

			label {
				position: initial;
				pointer-events: all;
			}
		}

		.submit-btn {
			color: var(--color-white);

			&:before {
				background-color: var(--color-primary);
			}

			&:hover {
				color: var(--color-white);
			}
		}
	}

	.woocommerce-form-login__rememberme {
		input[type="checkbox"] {
			width: auto;

			~span {
				position: relative;
				font-size: 14px;
				line-height: 20px;
				color: var(--color-dark);
				font-weight: 500;
				padding-left: 28px;
				cursor: pointer;

				&::before {
					content: " ";
					position: absolute;
					top: 2px;
					left: 0;
					width: 16px;
					height: 16px;
					background-color: #fff;
					border: var(--border-thin) solid var(--color-light);
					border-radius: 2px;
					transition: all .3s;
				}

				&::after {
					content: " ";
					position: absolute;
					top: 6px;
					left: 3px;
					width: 10px;
					height: 5px;
					background-color: transparent;
					border-bottom: 2px solid #fff;
					border-left: 2px solid #fff;
					border-radius: 2px;
					transform: rotate(-45deg);
					opacity: 0;
					transition: all .3s;
				}
			}

			&:checked {
				~span {
					&::before {
						background-color: var(--color-primary);
						border: var(--border-width) solid var(--color-primary);
					}

					&::after {
						opacity: 1;
					}
				}
			}
		}
	}
}

.signin-header {
	.back-btn {
		line-height: 37px;
	}

	.singin-header-btn {
		@media (max-width: 575px) {
			margin-top: 15px;
			flex-direction: row;
		}

		.axil-btn {
			@media (max-width: 1199px) {
				padding: 12px 20px;
				font-size: 14px;
				margin-left: 20px;
			}

			@media (max-width: 991px) {
				margin-left: 10px;
			}
		}
	}
}

#axil-register-wrap {
	.u-column2 {
		>h2 {
			font-size: 28px;
		}
	}

	form.register {
		border: none;
		padding: 0;
		margin-top: 55px;

		button.button {
			color: var(--color-white);
			transition: 0.3s;
			margin-top: 30px;

			&:before {
				content: "";
				height: 100%;
				width: 100%;
				border-radius: 6px;
				position: absolute;
				background-color: var(--color-primary);
				top: 0;
				bottom: 0;
				left: 0;
				right: 0;
				z-index: -1;
				transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
			}

			&:hover {
				&:before {
					transform: scale(1.1);
				}
			}
		}
	}
}

.woocommerce .woocommerce-form-login .woocommerce-form-login__rememberme {
	left: 0;
	padding: 0;
}

.axil-dashboard-aside {
	padding: 40px 32px 30px;

	ul {
		padding: 0;
		margin-bottom: 0;

		li {
			margin: 0;

			a {
				&:before {
					font-family: var(--font-awesome);
					font-size: 18px;
					font-weight: 600;
					position: absolute;
					top: 7px;
					left: 24px;

					@media #{$md-layout} {
						left: 10px;
					}
				}
			}

			&.is-active {
				a {
					color: var(--color-primary);
					background-color: var(--color-lighter);
				}
			}

			&.woocommerce-MyAccount-navigation-link--dashboard {
				a {
					&:before {
						content: "\f009";
					}
				}
			}

			&.woocommerce-MyAccount-navigation-link--orders {
				a {
					&:before {
						content: "\f291";
					}
				}
			}

			&.woocommerce-MyAccount-navigation-link--downloads {
				a {
					&:before {
						content: "\f56d";
					}
				}
			}

			&.woocommerce-MyAccount-navigation-link--edit-address {
				a {
					&:before {
						content: "\f015";
					}
				}
			}

			&.woocommerce-MyAccount-navigation-link--edit-account {
				a {
					&:before {
						content: "\f007";
					}
				}
			}

			&.woocommerce-MyAccount-navigation-link--customer-logout {
				a {
					&:before {
						content: "\f08b";
					}
				}
			}
		}
	}
}

.woocommerce-MyAccount-content {
	form {
		max-width: 650px;

		fieldset {
			padding: 0;
			margin: 0;
		}

		legend {
			font-size: 24px;
			font-weight: 600;
			color: var(--color-heading);
			margin-bottom: 30px;
		}

		.woocommerce-Button {
			width: auto;
		}
	}

	.woocommerce-Input {
		height: 60px;
		border-color: var(--color-light);
	}

	.woocommerce-form-row {
		em {
			font-size: var(--font-size-b3);
			margin-top: 10px;
			display: block;
			font-style: normal;
		}
	}

	.woocommerce-Message--info {
		padding: 15px 20px;
		border-radius: 16px;
		line-height: 60px;
		font-size: 16px;

		.button {
			background-color: var(--color-primary);
			color: var(--color-white);

			&:hover {
				color: var(--color-white);
				transform: scale(1.02);
			}
		}
	}

	button.axil-btn {
		color: var(--color-white) !important;

		&:before {
			background-color: var(--color-primary);
		}
	}

	.woocommerce-Addresses {
		.woocommerce-Address-title {
			&:before {
				display: none;
			}

			&:after {
				display: none;
			}
		}
	}
}

.woocommerce-view-order {
	.woocommerce-MyAccount-content {
		p {
			mark {
				background-color: transparent;
				color: var(--color-heading);
				font-weight: 500;
			}
		}

		.woocommerce-table--order-details {
			border: 1px solid var(--color-border-light);

			thead {
				th {
					color: var(--color-heading);
				}
			}

			tbody {
				tr {
					td {
						border-top: 1px solid var(--color-border-light);
					}
				}
			}

			tfoot {
				tr {

					th,
					td {
						color: var(--color-heading);
					}
				}
			}
		}

		.woocommerce-customer-details {
			margin-bottom: 0;

			.col-1,
			.col-2 {
				width: 100%;
			}

			.woocommerce-column--billing-address {
				margin-bottom: 40px;
			}

			.woocommerce-column--billing-address,
			.woocommerce-column--shipping-address {
				address {
					border-color: var(--color-border-light);
					border-radius: 6px;
				}
			}
		}
	}
}

.woocommerce form.login {
	margin: 0;
	padding: 0;
	border: none;
}

.woocommerce form .form-row {
	margin: 0 0 30px;
	padding: 0;
}

.woocommerce form .form-row label {
	line-height: inherit;
}


.woocommerce #respond input#submit,
.woocommerce a.button,
.woocommerce button.button,
.woocommerce input.button {
	background-color: transparent;
	border-radius: 6px;
	font-size: var(--font-size-b1);
	line-height: var(--line-height-b1);
	font-weight: 700;
	display: inline-block;
	padding: 14px 38px 16px;
	color: var(--color-dark);
	transition: 0.3s;
	border: 2px solid transparent;
	width: auto;

	&.btn-outline {
		border-color: #efefef;
	}

	&:hover {
		background-color: var(--color-primary);
		border-color: var(--color-primary);
	}

	&.wc-backward {
		background-color: var(--color-primary);
		color: var(--color-white);

		&:before {
			background-color: var(--color-primary);
		}
	}
}

.woocommerce button.button:disabled,
.woocommerce button.button:disabled[disabled] {
	padding: 14px 38px 16px;

	&:hover {
		border-color: #efefef;
		background-color: transparent;
	}
}

.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt {
	background-color: var(--color-primary);

	&:hover {
		background-color: transparent;
	}
}

.woocommerce a.button.alt.disabled,
.woocommerce button.button.alt.disabled,
.woocommerce input.button.alt.disabled {
	background-color: var(--color-primary);
	padding: 16px 40px;
}

.woocommerce-address-fields {
	.form-row {
		display: block;
		position: relative;

		label {
			position: absolute;
			top: -11px;
			left: 20px;
			pointer-events: none;
			z-index: 9;
			background: #fff;
			padding: 0 10px;
			margin-bottom: 6px;
			font-size: 14px;
			line-height: 22px;
			font-weight: 500;
			color: var(--color-dark);
		}

		.input-text {
			border: 1px solid #CBD3D9;
			height: 60px;
		}
	}
}

.woocommerce-LostPassword {
	margin-bottom: 35px;
}

.woocommerce div.product form.cart .variations td,
.woocommerce div.product form.cart .variations th {
	vertical-align: middle;
}

.woocommerce div.product form.cart div.quantity {
	margin: 0;
}


.woocommerce div.product form.cart .variations label {
	font-weight: 500;
}

.woocommerce div.product p.price,
.woocommerce div.product span.price {
	font-size: 24px;
	color: var(--color-black);
}

.woocommerce div.product .woocommerce-tabs ul.tabs {
	margin: -8px -20px 60px;
	padding: 0;

	&::before {
		display: none;
	}

	li {
		padding: 0 20px;
		border: none;
		background-color: transparent;
		margin: 8px 0;

		&:before {
			display: none;
		}

		&:after {
			display: none;
		}

		&.active {
			background-color: transparent;

			a {
				color: var(--color-primary);
			}
		}

		a {
			padding: 0;
			font-weight: 500;
			background-color: transparent;
			color: var(--color-body);
		}
	}
}

.woocommerce table.shop_attributes {
	margin-bottom: 0;
	border: none;

	td {
		font-style: normal;
		font-weight: 400;
		color: var(--color-body);

		p {
			font-size: var(--font-size-b2);
		}
	}

	th {
		font-weight: 500;
	}

	th,
	td {
		padding: 6px 30px 6px;
		min-width: 200px;
		width: auto;
		border: none;
	}

}

.woocommerce-Tabs-panel--reviews {
	.comment-respond {
		margin-top: 0;
	}

	.rating-wrapper {
		.stars {
			margin-bottom: 0;
			margin-left: 20px;
		}
	}

	#respond .comment-reply-title {
		font-size: 24px;
	}
}

// Cart Page Style
.woocommerce table.shop_table {
	border: none;
	margin: 0;
}

.woocommerce table.shop_table th {
	padding: 18px 15px;
}

.woocommerce table.shop_table td {
	border-top: none;
	padding: 20px 15px;
}

.axil-product-table {
	tbody {
		td {
			text-align: left;
			border: none;
			border-bottom: 2px solid var(--color-lighter);

			&.product-thumbnail {
				min-width: 80px !important;

				@media (min-width: 768px) {
					min-width: 100px !important;
				}

				@media (max-width: 767px) {
					padding-left: 0;
					padding-right: 0;
				}

				a {
					display: block;
					border-radius: 10px;

					img {
						border-radius: 10px !important;
						width: 80px !important;
						margin-right: 0 !important;
					}
				}
			}

			&.product-title {
				@media (max-width: 1199px) and (min-width: 768px) {
					width: 25%;
				}

				@media (max-width: 767px) {
					padding-left: 0;
				}

				h6 {
					margin-bottom: 0;

					@media (max-width: 767px) {
						margin-right: 30px;
					}
				}
			}

			&.product-remove {
				.remove-wishlist {
					line-height: 31px;

					@media (max-width: 767px) {
						line-height: 22px;
					}
				}
			}

			&.product-price {
				@media (max-width: 767px) {
					text-align: right;
				}
			}

			&.product-quantity {
				@media (max-width: 767px) {
					&:before {
						margin-top: 5px;
					}
				}

				.pro-qty {
					margin-right: 0 !important;
					width: 150px;

					@media (max-width: 767px) {
						margin-bottom: 0;
						width: 130px;
					}

					input {
						@media (max-width: 767px) {
							width: 60px;
						}
					}
				}

				.product-action-wrapper {
					@media (max-width: 767px) {
						flex-direction: column;
						align-items: flex-end;
					}
				}
			}

			&.product-stock-status {
				@media (max-width: 767px) {
					text-align: right;
				}
			}

			&.product-add-to-cart {
				&:before {
					display: none;
				}
			}
		}

		tr {
			&.cart-action {
				border-bottom: none;
				padding-left: 0;

				td {
					&:before {
						display: none;
					}

					.cart-update-btn-area {
						padding-top: 0;

						.update-btn {
							.axil-btn {
								margin-top: 20px;
							}
						}
					}
				}
			}
		}
	}
}

.woocommerce a.remove {
	color: var(--color-black) !important;

	&:hover {
		color: var(--color-black) !important;

	}
}

.woocommerce-page table.cart td.actions {
	border-bottom: none;
	padding: 0;
}

.woocommerce-page table.cart td.actions .coupon {
	align-items: flex-end;
}

.woocommerce-page table.cart td.actions .input-text {
	width: 100%;
	border-color: transparent !important;
	border-bottom-color: #efefef !important;
	border-width: 2px !important;
	padding: 0 !important;
}

.axil-product-cart-wrap .cart-update-btn-area {
	align-items: flex-end;
}


.axil-order-summery .summery-table tbody th {
	font-weight: 500 !important;
	text-transform: capitalize;

	@media (max-width: 575px) {
		min-width: 130px;
	}
}

.woocommerce .cart-collaterals .cart_totals,
.woocommerce-page .cart-collaterals .cart_totals {
	float: inherit;
	width: 100%;
}

.woocommerce-cart {
	.wc-proceed-to-checkout {
		padding: 0;

		a.checkout-button {
			margin-bottom: 0;
			font-size: 16px;

			&:before {
				background-color: var(--color-primary);
				border: 2px solid var(--color-primary);
			}

			&:hover {
				border-color: var(--color-primary);
			}
		}
	}

	.cart-collaterals {
		.cart_totals {
			table {
				margin-bottom: 30px;

				th,
				td {
					padding: 18px 15px 18px 0;
					vertical-align: middle;
					font-size: 16px;
					font-weight: var(--s-medium);
					border-top: none !important;

					@media (max-width: 575px) {
						font-size: 15px;
					}
				}

				td.order-total-amount {
					font-size: 20px;
					font-weight: var(--s-bold) !important;
				}
			}

			&.processing {
				opacity: 0.6;

				.blockUI.blockOverlay {
					background: transparent !important;
				}
			}
		}
	}

	.shipping-calculator-form {
		button.button {
			border-color: var(--color-light);
			padding: 11px 38px 13px;

			&:hover {
				background-color: var(--color-primary);
				border-color: var(--color-primary);
				color: var(--color-white);
			}
		}

		p {
			margin-bottom: 0;

			&.form-row {
				margin-bottom: 10px;
			}
		}

		.form-row {
			input {
				border: 2px solid var(--color-light) !important;
				height: 55px !important;
				font-size: 16px;
				color: var(--color-dark);
				font-weight: 500;
			}
		}
	}
}

.woocommerce ul.woocommerce-shipping-methods {
	li {
		margin-bottom: 10px;
		position: relative;

		input {
			margin: 0;
		}

		label {
			color: var(--color-dark);
			font-weight: 400;

			&:before {
				border-width: 2px;
				border-color: #d5d4d4;
			}

			&:after {
				background-color: var(--color-primary);
				border: none;
			}
		}

		.amount {
			font-weight: 400 !important;
		}
	}
}

.woocommerce .shop_table.wishlist_table .product-price del {
	color: var(--color-body);
}

.woocommerce .shop_table.wishlist_table .product-price ins {
	color: var(--color-body);
}

table.wishlist_table thead th {
	border-top: none;
	font-weight: 500 !important;
}

.wishlist_table .product-add-to-cart a {
	margin: 0 0 0 auto !important;
}

.woocommerce .shop_table.wishlist_table .product-add-to-cart a.add_to_cart_button {
	border-color: #efefef;
	padding: 10px 20px;
	font-size: 14px;
	border-radius: 6px;
	font-weight: 700;

}

.woocommerce .shop_table.wishlist_table tr td.product-remove {
	min-width: auto;
	padding: 20px 15px;
}

.wishlist_table tbody.wishlist-items-wrapper .product-remove a.remove_from_wishlist {
	font-weight: 400;
	line-height: 27px;
}

.woocommerce .wishlist-title-container {
	margin-bottom: 20px;

	.wishlist-title {
		h2 {
			margin-bottom: 0;
			margin-right: 15px;
		}

		.show-title-form {
			padding: 0;
			background-color: transparent;
			border: none;
			font-size: 14px;

			i {
				margin-right: 2px;
			}

			&:hover {
				color: var(--color-primary);
			}
		}
	}

	.hidden-title-form {
		display: none;
		position: relative;
		padding: 0;
		width: 100%;

		input {
			height: 50px;
			padding: 0 40px 0 15px;
			font-size: 16px;
			border-color: #efefef;

			&:focus {
				border-color: #efefef;

			}
		}

		.edit-title-buttons {
			position: absolute;
			right: 15px;
			top: 10px;

			.save-title-form {
				font-size: 20px;
				transition: 0.3s;
			}
		}
	}
}

.cart-update-btn-area {

	.product-cupon-btn,
	.update-btn {
		button {
			&::before {
				display: none;
			}
		}
	}
}

.cart-empty.woocommerce-info {
	font-size: 24px;
	font-weight: 500;
	color: var(--color-dark);
	margin-bottom: 30px;
	background-color: transparent;
	text-align: center;
}

.cart_is_empty {
	.return-to-shop {
		text-align: center;
	}
}

.return-to-shop {
	text-align: center;
}

.wishlist-items-wrapper .wishlist-empty {
	text-align: center !important;
}

.wishlist_table {
	&.mobile {
		margin: 0;
		padding: 0;

		li {
			margin-bottom: 15px;
			margin-top: 0;

			&:last-child {
				margin-bottom: 0;
			}

			.item-wrapper {
				width: 100%;
				margin-bottom: 0;
			}

			.product-thumbnail {
				img {
					margin: 0 !important;
				}
			}

			.item-details {
				.product-name {
					h3 {
						font-size: 17px;
					}
				}

				table {
					&.item-details-table {
						margin-bottom: 0;

						td {
							padding: 0;

							&.label,
							&.value {
								font-size: 14px;
								font-weight: 500;
								min-width: auto;
							}

							del {
								color: var(--color-light);
							}

							ins {
								background-color: transparent;
								color: var(--color-body);
							}
						}
					}
				}
			}
		}

		.additional-info-wrapper {
			padding-left: 74px;

			table.additional-info {
				td {
					padding: 0;

					&.label,
					&.value {
						font-size: 14px;
						font-weight: 500;
						min-width: auto;
					}
				}
			}

			.product-add-to-cart {
				.add_to_cart_button {
					font-size: 13px !important;
					color: var(--color-body);
				}
			}

			.product-remove {
				margin-top: 12px;

				a {
					color: var(--color-body);
				}
			}
		}
	}
}

// Checkout Page
.woocommerce-info {
	border-top: none;
	margin-bottom: 0;

	&:before {
		display: none;
	}
}

.woocommerce form .form-row input.input-text {
	border: 1px solid var(--color-light);
	height: 60px;
	border-radius: 6px;
	background-color: transparent;
}

.woocommerce form .form-row {
	position: relative;
	flex-direction: column
}

.woocommerce-page form .form-row label {
	position: absolute;
	top: -11px;
	left: 20px;
	pointer-events: none;
	z-index: 5;
	background: #fff;
	padding: 0 10px;
	font-size: 14px;
	color: var(--color-body);
}

.woocommerce form .form-row .select2-container {
	margin: 0;
	background: url(../images/arrow-icon2.png) 95% center no-repeat transparent;
	z-index: 4;
}

.woocommerce form.checkout_coupon {
	margin: 20px 0 0 0;

	.apply-btn {
		margin-left: 10px !important;
	}

	.form-row {
		input.input-text {
			height: 50px;
			border-radius: 24px;
		}
	}

	.form-row-last {
		width: 100%;
	}
}

.axil-checkout-notice {
	.toggle-bar {
		.woocommerce-info {
			padding: 0;
		}
	}

	.toggle-bar {
		align-items: flex-start;

		i {
			margin-top: 5px;
		}
	}

	.axil-checkout-coupon {
		.input-group {
			@media (max-width: 575px) {
				display: block;

				.apply-btn {
					margin-left: 0 !important;
				}

				.form-row.form-row-first {
					width: 100%;
				}
			}
		}

		.axil-btn {
			background-color: transparent;
			color: var(--color-dark);
			height: 50px;
			line-height: 1;
			color: var(--color-white);
			padding: 10px 20px 12px;

			&:before {
				background-color: var(--color-primary);
			}

			&:hover {
				background-color: var(--color-primary);
			}

			@media (max-width: 575px) {
				margin-top: 10px;
			}
		}

		.form-row {
			margin: 0;
		}
	}
	.woocommerce-error {
		margin: 10px 0 0 0;
	}
}

.axil-checkout-login {
	margin-bottom: 20px;
	.woocommerce-form-login-toggle {
		display: flex;
		align-items: flex-start;
		background-color: var(--color-lighter);
		border-radius: 6px;
		padding: 17px 30px;
		i {
			margin-top: 5px;
			margin-right: 8px;
		}
		.woocommerce-info {
			padding: 0;
			a {
				font-weight: 500;
    			transition: var(--transition);
				color: var(--color-heading);
				i {
					color: var(--color-body);
					font-weight: 400;
					margin-left: 5px;
				}
			}
		}
	}
	.woocommerce-form-login.login {
		border: 1px solid #d3ced2;
		padding: 20px;
		margin: 20px 0 0 0;
		text-align: left;
		border-radius: 5px;
		.form-row {
			margin-bottom: 20px;
			.woocommerce-form-login__rememberme {
				position: initial;
				display: block;
				margin-bottom: 20px;
				.woocommerce-form__input-checkbox {
					~span {
						position: relative;
						font-size: 14px;
						line-height: 20px;
						color: var(--color-body);
						font-weight: 500;
						padding-left: 25px;
						cursor: pointer;
						&::before {
							content: " ";
							position: absolute;
							top: 2px;
							left: 0;
							width: 16px;
							height: 16px;
							background-color: #fff;
							border: var(--border-thin) solid var(--color-body);
							border-radius: 2px;
							transition: all .3s;
						}
						&::after {
							content: " ";
							position: absolute;
							top: 5px;
							left: 2px;
							width: 10px;
							height: 5px;
							background-color: transparent;
							border-bottom: var(--border-thin) solid #fff;
							border-left: var(--border-thin) solid #fff;
							border-radius: 2px;
							transform: rotate(-45deg);
							opacity: 0;
							transition: all .3s;
						}
					}
					&:checked {
						~span {
							&::before {
								background-color: var(--color-primary);
								border: var(--border-width) solid var(--color-primary);
							}
							&::after {
								opacity: 1;
							}
						}
					}
				}
			}
			.password-input {
				.show-password-input {
					top: 17px;
				}
			}
			.woocommerce-form-login__submit {
				color: var(--color-white);
				position: relative;
				z-index: 1;
				&:after {
					background-color: var(--color-primary);
					content: "";
					height: 100%;
					width: 100%;
					border-radius: 6px;
					position: absolute;
					top: 0;
					bottom: 0;
					left: 0;
					right: 0;
					z-index: -1;
					transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
				}
				&:hover {
					&:after {
						transform: scale(1.1);
					}
				}
			}
		}
		.lost_password {
			text-align: right;
			margin-bottom: 10px;
			a {
				color: var(--color-heading);
				transition: 0.3s;
				&:hover {
					color: var(--color-secondary);
				}
			}
		}
	}
}

.woocommerce {
	form.woocommerce-checkout {
		.form-row {
			textarea {
				border: 1px solid var(--color-light);
				height: auto;
				min-height: 160px;
				padding: 15px 30px;
			}

			.select2-container {
				border-width: 1px;
				font-size: 14px;
			}
		}
	}
}

.axil-order-summery {
	&.order-checkout-summery {
		#order_review_heading {
			font-weight: 500;
		}

		.summery-table {
			tr {
				&:last-child {
					td {
						border-bottom: 1px solid var(--color-light);
						padding: 18px 0;
					}
				}
			}

			th {
				border: none;
				font-weight: 500;
				border-bottom: 1px solid var(--color-light);
			}

			td {
				border: none;
				border-bottom: 1px solid var(--color-light);
				min-width: 130px;

				.product-quantity {
					font-weight: 500;
				}
			}

			tfoot {

				th,
				td {
					border-top: none;
					font-weight: 500;
					font-size: 16px;
					color: #292930;
				}

				tr {
					&:last-child {

						td,
						th {
							border-bottom: none;
							padding-bottom: 0;
						}
					}
				}
			}

			.order-total {
				th {
					font-size: 20px;
					font-weight: 600;
				}
			}
		}

		.woocommerce-shipping-methods {
			li {
				label {
					padding-left: 0;
					padding-right: 28px;
					display: inline-block !important;

					&:before {
						left: auto;
						right: 0;
					}

					&:after {
						left: auto;
						right: 4px;
					}
				}
			}
		}

		.order-payment-method {
			.single-payment {
				&.payment_method_paypal {
					.input-group {
						label {
							&:before {
								top: 17px;
							}

							&:after {
								top: 21px;
							}
						}
					}
				}
			}
		}
	}
}

.woocommerce-checkout-review-order-table {
	.order-total {
		.woocommerce-Price-amount {
			font-size: 20px;
		}
	}
}

.woocommerce-checkout #payment {
	background-color: transparent;
}

.woocommerce-checkout #payment {
	ul.payment_methods {
		padding: 0;
		border: none;

		li {
			margin-bottom: 20px;
			padding-bottom: 20px;
		}

		.input-group {
			margin-bottom: 0;

			label {
				width: 100%;
			}
		}
	}

	div.payment_box {
		margin: 15px 0 0 0;
		background-color: transparent;
		padding: 0;

		&:before {
			display: none;
		}
	}

	div.place-order {
		padding: 0;
		margin-bottom: 0;
	}
}

.woocommerce #payment #place_order,
.woocommerce-page #payment #place_order {
	width: 100%;

	&:before {
		background-color: var(--color-primary);
	}
}

.woocommerce-order-received {
	.woocommerce-order {
		.woocommerce-thankyou-order-received {
			color: var(--color-heading);
			font-weight: 500;
			margin-bottom: 30px;
		}

		ul.woocommerce-thankyou-order-details {
			padding: 10px 20px;
			border: 1px solid var(--color-border-light);
			border-radius: 6px;
			margin-bottom: 10px;

			li {
				font-size: 14px;
				border-right-color: var(--color-border-light);
				margin-bottom: 10px;
				margin-top: 10px;

				@media (max-width: 767px) {
					float: none;
					border-right: none;
				}

				strong {
					color: var(--color-heading);
				}
			}
		}

		.woocommerce-table--order-details {
			border: 1px solid var(--color-border-light);

			thead {
				tr {
					th {
						padding: 20px 15px;
					}
				}
			}

			tbody {
				td {
					border-top: 1px solid var(--color-border-light);
				}
			}

			tfoot {
				tr {

					th,
					td {
						border-color: var(--color-border-light);
					}
				}
			}
		}

		.woocommerce-customer-details {
			.woocommerce-columns--addresses {
				.woocommerce-column--billing-address {
					@media (max-width: 767px) {
						margin-bottom: 30px;
					}
				}

				.woocommerce-column--billing-address,
				.woocommerce-column--shipping-address {
					@media (max-width: 767px) {
						width: 100%;
					}

					address {
						border-color: var(--color-border-light);
						border-radius: 6px;
					}
				}
			}
		}
	}
}



// Product Single Page

.range-variant {
	li {
		padding: 0;
		border: none;
		background-color: transparent;
		padding: 0;
		height: auto;
		width: auto;

		label {
			border: 2px solid #F6F7FB !important;
			background-color: var(--color-white);
			border-radius: 30px;
			min-width: 44px;
			min-height: 44px;
			height: auto;
			width: auto;
			padding: 5px 13px;
			display: flex;
			align-items: center;
			justify-content: center;

			&::after {
				display: none;
			}
		}
	}

	input[type="radio"]~label::before {
		height: 100%;
		width: 100%;
		border-radius: 30px;
		border: 2px solid transparent;
	}

	input[type=radio]:checked~label::before {
		border-color: var(--color-primary);
	}
}

.image-variant {
	label {
		img {
			height: 30px;
			width: 30px;
		}
	}
}

.product_meta {
	a {
		transition: 0.3s;
	}

	.sku_wrapper {
		display: block;

		.sku {
			color: var(--color-heading);
		}
	}

	.posted_in {
		display: block;
	}
}

.product-stock-meta {
	list-style: none;
	padding: 0;
	margin: 20px 0;

	li {
		display: flex;
		align-items: center;

		i {
			margin-right: 5px;
			color: var(--color-primary);
		}

		.stock.in-stock {
			color: var(--color-primary);
		}
	}
}

.variation-radios input[type="radio"]~label::before {
	left: 0;
}

.color-variant-wrapper {
	li {
		span {
			z-index: 2;
		}

		&.color-list {
			label {
				&:before {
					top: -1px;
					left: -1px;
				}
			}
		}
	}
}

.single-product-content {
	.inner {
		.product-rating {
			.star-rating {
				margin: 0;
				font-size: 13px;
			}
		}

		.price-amount {
			&.price-offer-amount {
				margin: 0 0 20px;

				span {
					margin: 0;
				}
			}

			del {
				color: var(--color-lightest);

				.amount {
					text-decoration: line-through;
				}
			}

			ins {
				background: transparent;
				color: var(--color-black);
				margin-left: 10px;
			}
		}

		form {
			&.cart {
				.variations {
					th {
						padding-left: 0;
					}

					td {
						position: relative;
					}
				}
			}
		}
	}

	.product-action-wrapper {
		width: 100%;

		.product-action {
			flex: 0;

			.add-to-cart {
				@media (max-width: 479px) {
					margin: 10px 0 0;
				}
			}
		}

		&.quantity {
			width: auto;
		}

		.button.single_add_to_cart_button {
			flex: 1;
			margin-right: 10px;
			border: none;

			@media (max-width: 1199px) and (min-width: 992px) {
				padding: 14px 20px 16px;
			}

			@media (max-width: 479px) {
				margin-right: 0;
			}

			&:before {
				background-color: var(--color-primary);
			}

			&:hover {
				background-color: var(--color-primary);
				transform: scale(1.02);

				&:before {
					transform: scale(1.05);
				}
			}
		}

		&.d-flex {
			flex: inherit;
		}

		.axil-wishlist-icon {
			border: 2px solid var(--color-light);
			padding: 14px 18px 14px;
			border-radius: 6px;
			display: block;
			height: 56px;
			text-align: center;

			&:hover {
				background-color: var(--color-primary);
				border-color: var(--color-primary);
				color: var(--color-white) !important;
			}

			.ajax-loading {
				top: 2px;
			}

			@media (max-width: 479px) {}
		}
	}

	.woocommerce-variation-price {
		margin-bottom: 30px;

		span {
			font-weight: 600;
			color: var(--color-primary);

			&.price {
				ins {
					color: #000;
					font-weight: 500 !important;
				}
			}
		}
	}

	.quantity .qty {
		width: 40px;
	}

	.variation-radios {
		input[type="radio"] {
			~label::before {
				width: 25px;
				height: 25px;
			}
		}
	}

	.axiltheme-star-rating {
		width: 85px;
		height: 100%;

		&:before {
			letter-spacing: 4px;
		}

		.star-rating {
			font-size: 12px;
			width: 85px;

			&:before {
				letter-spacing: 4px;
			}

			span {
				&:before {
					color: #ffdc60;
					letter-spacing: 4px;
				}
			}
		}
	}

	.range-variant {
		input[type=radio]~label::before {
			width: 100%;
			height: 100%;
		}
	}

	table.woocommerce-grouped-product-list {
		tr {
			border-bottom-color: var(--color-light) !important;

			&:first-child {
				border-top-color: var(--color-light) !important;
			}

			td {
				padding: 15px 20px !important;

				&.woocommerce-grouped-product-list-item__quantity {
					padding-left: 0 !important;
				}

				&.woocommerce-grouped-product-list-item__price {
					padding-right: 0 !important;
					color: var(--color-black);

					del {
						color: var(--color-lightest);
						margin-right: 5px;

						.woocommerce-Price-amount {
							text-decoration: line-through;

						}
					}

					ins {
						color: var(--color-black);
						background-color: transparent;
					}
				}
			}
		}
	}
}

.woocommerce div.product form.cart table.variations {
	margin-bottom: 20px;

	td {
		&.value {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.variation-radios {
				overflow: visible;
			}
		}
	}
}

.woocommerce div.product form.cart .reset_variations {
	margin-top: 5px;
	color: var(--color-primary);
	transition: 0.3s;

	&:hover {
		color: var(--color-secondary);
	}
}

.single-product-thumbnail {
	&.thumbnail-badge {
		.thumbnail {
			padding-right: 0;
		}
	}
}

.single-product-thumbnail-wrap {
	.label-block {
		z-index: 1;
	}
}


.woocommerce div.product .woocommerce-tabs .panel {
	margin: 0 0 5px 0;
}

.product-additional-info {
	@media only screen and (max-width: 767px) {
		padding: 20px 15px;
	}

	table {
		tbody {
			tr {
				&:nth-child(even) {
					background-color: var(--color-white);

					th,
					td {
						background-color: transparent !important;
					}
				}
			}
		}
	}
}

.single-product-2 {
	.reviews-wrapper {
		.col-lg-6 {
			flex: 0 0 100%;
			max-width: 100%;
		}

		.rating-wrapper {
			label {
				margin-bottom: 10px;
				margin-right: 10px;
			}
		}
	}
}

.single-product-3 {
	.reviews-wrapper {
		.row {
			display: flex;
			margin-right: -15px;
			margin-left: -15px;
		}
	}
}

.single-product-4 {
	.reviews-wrapper {
		.row {
			display: flex;
			margin-right: -15px;
			margin-left: -15px;
		}
	}
}

.single-product-5 {
	.single-product-content {
		.price-wrp {
			display: flex;
			align-items: center;

			.product-badget {
				margin-left: 10px;
				margin-bottom: 20px;
				background-color: var(--color-chart03);
				height: 48px;
				line-height: 40px;
				padding: 5px 20px;
				font-size: 16px;
				font-weight: 500;
				color: var(--color-white);
				border-radius: 24px;
				font-family: var(--font-secondary);
			}
		}
	}
}

.axil-single-product-area {
	.reviews-wrapper {
		.rating-wrapper {
			label {
				margin-bottom: 10px;
			}

			p.stars {
				margin-bottom: 10px;
				margin-left: 20px;
				line-height: 1;

				a {
					color: #ffdc60;
				}

				&.selected {
					a {
						&.active {
							~a {
								&:before {
									content: "\e021";
								}
							}
						}
					}
				}
			}
		}
	}

	.single-product-thumb {
		.axil--with-images {
			&.images {
				@media (max-width: 991px) {
					margin-bottom: 0;
				}
			}
		}
	}

	.single-product-content {
		.inner {
			form.cart {
				@media (max-width: 991px) {
					margin-bottom: 0;
				}
			}
		}
	}
}

.woocommerce-tabs {
	.tabs {
		.nav-item {
			a {
				&:after {
					height: 3px;
				}
			}

			&.active {
				a {
					&:after {
						width: 100%;
						opacity: 1;
					}
				}
			}
		}
	}

	.reviews-wrapper {
		.star-rating {
			width: 90px;
			font-size: 12px;

			&:before {
				letter-spacing: 4px;
			}

			span {
				&:before {
					color: #ffdc60;
					letter-spacing: 4px;
				}
			}
		}

		.single-comment {
			.media-body {
				flex: 1;
			}
		}
	}

	#respond input#submit {
		background-color: var(--color-primary);
		color: var(--color-white);

		&:hover {
			color: var(--color-white);
			transform: scale(1.02);
		}
	}
}

.nft-single-product-content {
	.woocommerce-tabs.nft-info-tabs {
		ul.tabs {
			margin: 0 -5px 10px !important;
			overflow: visible !important;

			li {
				margin: 0 5px !important;
				padding: 0 !important;

				&.active {
					background-color: var(--color-white) !important;

					a {
						border-radius: 6px 6px 0 0 !important;

						&:before {
							visibility: visible;
							opacity: 1;
						}

						&:after {
							visibility: visible;
							opacity: 1;
						}
					}
				}
			}
		}
	}
}

.single-product.woocommerce {
	.woocommerce-notices-wrapper {
		.woocommerce-message {
			line-height: 46px;
			margin: 20px 0;

			@media (max-width: 767px) {
				line-height: 1.5;
			}
		}
	}
}

.woocommerce-notices-wrapper {
	.woocommerce-message {
		@media (max-width: 767px) {
			display: flex;
			align-items: flex-start;
			flex-direction: column-reverse;
		}

		.wc-forward {
			background-color: var(--color-primary);
			color: var(--color-white);
			padding: 10px 30px;
			border-color: var(--color-primary);

			@media (max-width: 767px) {
				margin-top: 15px;
			}

			&:hover {
				transform: scale(1.02);
				color: var(--color-white);
			}
		}
	}
}

.woocommerce-message {
	border-top-color: var(--color-primary);
	margin: 0 0 20px 0;
}

.woocommerce-message::before {
	color: var(--color-primary);
}

.pro-qty {
	.qty {
		border: none;
	}
}

// Minicart
.cart-dropdown {
	.cart-item {
		.item-price {
			font-weight: 500;
			font-size: 18px;
			top: -3px;
			transform: translateY(0);

			i {
				font-weight: 400;
				font-size: 15px;
				margin-left: 5px;
			}

			.woocommerce-Price-amount {
				font-weight: 600;
				margin-left: 5px;
			}
		}

		.item-img {
			.close-btn {
				line-height: 30px;

				@media only screen and (max-width: 767px) {
					line-height: 23px;
				}

				&:hover {
					color: var(--color-white) !important;
				}
			}

			img {
				border-radius: 10px;
				width: 100px;
			}
		}
		dl.variation {
			padding-left: 0;
			border-left: 0;
			display: flex;
			align-items: center;
			.variation-Vendor {
				margin-bottom: 0;
			}
		}
	}

	.return-to-shop {
		.axil-btn {
			color: var(--color-white);

			&:before {
				background-color: var(--color-primary);
			}
		}
	}
}

.woocommerce ul.cart_list li,
.woocommerce ul.product_list_widget li {
	padding: 0 0 20px;
	margin: 8px 0 20px 0;
	align-items: center;
}

.woocommerce ul.cart_list li img,
.woocommerce ul.product_list_widget li img {
	margin-left: 0;
	width: 100%;
	border-radius: 10px;
}

.pro-desc-commnet-area .comment-list .comment .single-comment {
	width: 100%;
}


.pro-desc-commnet-area .comment-list .comment .commenter .commenter-rating {
	margin-bottom: 0;
}

.pro-desc-commnet-area .comment-list .comment .commenter {
	margin-bottom: 12px;
}

.wc-review-date {
	display: block;
	margin-bottom: 5px;
	font-size: 16px;
}

.small-thumb-wrapper .small-thumb-img {
	margin-bottom: 16px;
}

.woocommerce div.product-small-thumb div.small-thumb-img img {
	height: 80px;
	width: 80px;
}

.woocommerce div.product-small-thumb.small-thumb-style-two div.small-thumb-img img {
	height: 60px;
	width: 60px;
}

.woocommerce div.product form.cart .variations td.label {
	max-width: 40px;
}



.single-product-2,
.single-product-4 {
	.single-desc {
		margin-bottom: 0 !important;
	}

	.pro-des-commend-respond {
		margin-bottom: 0 !important;
	}
}


// Shop Page Sidebar
.woocommerce .widget_layered_nav_filters ul .chosen {
	display: block;
	float: initial;
}

.woocommerce .widget_layered_nav_filters ul .chosen a[aria-label="Remove filter"]::before {
	top: 3px;
}

.axil-shop-sidebar {
	.toggle-list {
		.title {
			&:before {
				content: "\f068";
			}
		}

		&.active {
			.title {
				&:before {
					content: "\f067";
				}
			}
		}
	}

	.product-categories {
		ul {
			li {
				a {
					display: flex;
					align-items: center;

					&::before {
						top: auto;
					}

					&:hover {
						color: var(--color-primary);
					}
				}

				ul.children {
					padding-top: 10px;
				}

				&.current {
					padding: 6px 0 6px 28px !important;
				}

				&.chosen {
					a {
						&::before {
							line-height: 14px;
						}
					}
				}
			}
		}
	}

	.widget_rating_filter {
		ul {
			li {
				.star-rating {
					width: 90px;
					margin: 0;
					font-size: 13px;

					&:before {
						letter-spacing: 4px;
					}

					span {
						&:before {
							color: #ffdc60;
							letter-spacing: 4px;
						}
					}
				}
			}
		}
	}

	.widget.woocommerce {
		.select2-container.select2-container--default {
			margin: 0;
		}
		.product-categories {
			.cat-item {
				a {
					display: inline-flex;
				}
			}
		}
		.product_list_widget {
			li {
				a {
					padding-left: 0;
					&:before {
						display: none;
					}
				}
			}
			.content {
				.product-title {
					padding-left: 0;
					&:before {
						display: none;
					}
				}
			}
		}
	}
	.widget.woocommerce.widget_recent_reviews {
		.product_list_widget {
			li {
				display: block;
				a {
					display: block;
					img {
						margin-bottom: 10px;
					}
					.product-title {
						margin-bottom: 10px;
						font-size: 16px;
						font-weight: 500;
						color: var(--color-body);
						display: block;
					}
					&:hover {
						.product-title {
							color: var(--color-primary);
						}
					}
				}
				.star-rating {
					font-size: 12px;
					width: 80px;
					margin-bottom: 10px;
	
					&:before {
						letter-spacing: 4px;
					}
	
					span {
						&:before {
							color: #ffdc60;
							letter-spacing: 4px;
						}
					}
				}
	
			}
		}
	}
	.widget.woocommerce.widget_top_rated_products {
		.product_list_widget {
			li {
				.content {
					.product-title {
						font-size: 16px;
						font-weight: 500;
						margin-bottom: 10px;
					}
					.star-rating {
						font-size: 12px;
						width: 80px;
						margin-bottom: 10px;
		
						&:before {
							letter-spacing: 4px;
						}
		
						span {
							&:before {
								color: #ffdc60;
								letter-spacing: 4px;
							}
						}
					}
				}
			}
		}
	}
	.widget.woocommerce.widget_shopping_cart {
		.product_list_widget {
			.woocommerce-mini-cart-item {
				padding-left: 0;
				.remove_from_cart_button {
					z-index: 1;
					top: 4px;
					left: 4px;
					justify-content: center;
					&:hover {
						color: var(--color-white) !important;
						background-color: var(--color-primary);
					}
				}
				a {
					gap: 10px;
					img {
						width: 120px;
					}
				}
			}
		}
		.woocommerce-mini-cart__total {
			font-size: 20px;
			color: var(--color-heading);
			font-weight: 700;
		}
		.woocommerce-mini-cart__buttons {
			a {
				width: 100%;
				text-align: center;
			}
		}
	}
}

.woocommerce .woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item {
	padding: 4px 0;
}

.woocommerce .woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item a {
	padding-left: 28px;
	z-index: 1;
}

.woocommerce .widget_layered_nav ul li a .wooc-pa-color {
	position: absolute;
	left: 0;
	z-index: -1;
}

.woocommerce .woocommerce-ordering select {
	margin: 0;
	border: 2px solid var(--color-light);

}

.woocommerce .woocommerce-ordering {
	margin-bottom: 0;
}

.woocommerce .widget_price_filter .ui-slider .ui-slider-range {
	height: 4px;
	background: #DBDEFF none repeat scroll 0 0
}

.woocommerce .widget_price_filter .price_slider_wrapper .ui-widget-content {
	height: 4px;
	background-color: #CBD3D9;
	margin-bottom: 85px;
	margin-top: 25px;

}

.woocommerce .widget_price_filter .ui-slider .ui-slider-handle {
	height: 13px;
	width: 13px;
	border: none;
	box-shadow: none;
	background: var(--color-primary) none repeat scroll 0 0;
	top: 1px;
}

.price_slider_wrapper {
	position: relative;
}

.price_slider_amount .price_label {
	position: absolute;
	top: 32px;
	width: auto;
}

.woocommerce .widget_price_filter .price_slider_amount .button {
	background-color: transparent;
	border: 2px solid var(--color-light);
	color: var(--color-heading);
	font-size: 15px;
	padding: 12px 35px;

}

.woocommerce .widget_price_filter .price_slider_amount .button:hover {
	background-color: var(--color-primary);
	border-color: var(--color-primary);
	color: #ffffff;
}

.woocommerce .woocommerce-error li,
.woocommerce .woocommerce-info li,
.woocommerce .woocommerce-message li {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

.woocommerce-error {
	border-top-color: var(--color-secondary);
}

.woocommerce-error::before {
	color: var(--color-secondary);
}

.category-select {
	.axil-shop-widget-content {
		select {
			margin: 10px 10px 10px 0;
			border: 2px solid #cbd3d9;
			background: url(../images/arrow-icon2.png) 91% center no-repeat transparent;
			color: var(--color-dark);
			font-weight: 500;
		}
	}

	.widget_categories {
		margin-right: 10px;
	}

	.orderby,
	.select2-container.select2-container--default {
		z-index: 5;
		border: 2px solid var(--color-light);
		font-size: 16px;
		background-position-x: 91%;

		@media (max-width: 991px) {
			background-position-x: 95%;
		}
	}

	.select2-container.select2-container--default {
		padding: 0;
	}

	.select2-container--default .select2-selection--single {
		margin: 0 !important;
		padding: 0 20px;
		height: 55px;
	}

	.select2-container--default .select2-selection--single .select2-selection__rendered {
		line-height: 52px;
	}
}


.axil-shop-top {
	margin-bottom: 40px;

	.category-select {
		margin: -10px !important;

		@media (max-width: 991px) {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
		}

		@media (max-width: 767px) {
			grid-template-columns: repeat(1, 1fr);
		}

		.widget {
			margin: 10px;
		}

		.sort-list {
			margin: 10px;

			@media (max-width: 991px) {
				margin-top: 30px;
			}
		}

		select {
			margin: 0;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.woocommerce-ordering {
			select {
				padding-right: 35px;
			}
		}

		.select2-container.select2-container--default {
			margin: 0;
		}
	}

	.axil-shop-widget-content {
		.price_slider_wrapper {
			width: 205px;
			display: flex;

			@media (max-width: 991px) {
				width: 100%;
			}

			.ui-widget-content {
				min-width: 170px;
				margin-bottom: 0;
				margin-top: 10px;

				@media (max-width: 991px) {
					min-width: calc(100% - 140px);
				}
			}

			.price_slider_amount {
				margin-left: 20px;

				button {
					border-radius: 4px;
					border-color: var(--color-light);
					font-size: 16px;
					color: var(--color-dark);
					padding: 10px 30px;
				}

				.price_label {
					left: 0;
				}
			}
		}
	}

	.filter-results {
		margin: 0 20px 0 10px !important;
	}
}



// Category
.categrie-product-4 {
	transition: 0.3s;

	.cat-title {
		transition: 0.3s;
	}

	&:hover {
		transform: scale(1.02);

		.cat-title {
			color: var(--color-primary);
		}
	}
}


// Select 2 
.select2-container.select2-container--default {
	z-index: 5;
	font-size: 16px;
}

.select2-container--open .select2-dropdown--below {
	border-top: 1px solid !important;
	border-color: var(--color-light) !important;
	border-radius: 6px !important;
}

.select2-container--open .select2-dropdown--above {
	border-bottom: 1px solid !important;
	border-color: var(--color-light) !important;
	border-radius: 6px !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--default .select2-results__option--highlighted[data-selected] {
	background-color: var(--color-primary) !important;
}

.select2-container--default .select2-results__option[aria-selected=true],
.select2-container--default .select2-results__option[data-selected=true] {
	background-color: var(--color-primary) !important;
	color: var(--color-white);

	&:focus-visible {
		outline: none;
	}
}

.select2-results__option {
	padding: 5px 15px !important;
	font-size: 16px;
	font-weight: 400;
	border-radius: 4px;
	margin: 5px;
	transition: var(--transition);

	&:focus-visible {
		outline: none;
	}
}

.select2-container--open .select2-dropdown {
	left: -12px !important;
	top: -11px;
}

.select2-container--default.select2-search--dropdown .select2-search__field {
	line-height: 40px;
	border-color: var(--color-light);
}

.select2-container--default .select2-search--dropdown .select2-search__field {
	line-height: 40px;
	border-color: var(--color-light) !important;
}

.select2-container--default .select2-selection--single {
	margin-top: 12px !important;

	&:focus-visible {
		outline: none;
	}
}

.select2-container--default .select2-selection--single .select2-selection__clear {
	margin-left: 10px;
}

// Product Box Style
.axil-product {
	.thumbnail {
		.label-block {
			.product-badget {
				box-shadow: 0 6px 10px 0 rgba(53, 119, 240, 20%);
			}
		}
	}

	.product-hover-action {
		.cart-action {
			li {
				&.add-to-cart {
					a {
						&.added {
							display: none;
						}

						&.added_to_cart {
							&.wc-forward {
								height: 40px;
								line-height: 39px;
								background: var(--color-primary);
								color: var(--color-white);
								padding: 0 18px;
								border-radius: 4px;
								font-weight: 700;
								font-size: 14px;
								border: none;
								transition: 0.3s;
							}
						}

						&.loading {
							opacity: 0.8;

							&:before {
								z-index: 1;
							}

							&:after {
								z-index: 1;
								top: 1px;
								right: 44%;
							}
						}
					}
				}

				&.wishlist {
					a {
						&.axiltheme-wishlist-icon {
							line-height: 42px;

							&:hover {
								i {
									color: var(--color-heading);
								}
							}

							&.axiltheme-remove-from-wishlist {
								color: var(--color-primary);

								&:hover {
									i {
										color: var(--color-primary);
									}
								}
							}
						}
					}
				}

				&.select-option {
					a {
						background: var(--color-secondary);
						border: none;
					}
				}
			}
		}
	}

	.product-content {
		.cart-action {
			li {
				&.add-to-cart {
					a {
						line-height: 37px;

						&.added {
							display: none;
						}

						&.added_to_cart {
							&.wc-forward {
								height: 40px;
								line-height: 39px;
								background: var(--color-primary);
								color: var(--color-white);
								padding: 0 18px;
								border-radius: 4px;
								font-weight: 700;
								font-size: 14px;
								border: none;
								transition: 0.3s;
							}
						}

						&.loading {
							opacity: 0.8;

							&:before {
								z-index: 1;
							}

							&:after {
								z-index: 1;
								top: 1px;
								right: 44%;
							}
						}

						&:hover {
							border-color: var(--color-secondary);
						}
					}
				}
			}
		}

		.product-rating {
			align-items: center;

			.rating {
				font-weight: 500;
			}

			.star-rating {
				width: 90px;
				margin: 0 0 3px;
				font-size: 13px;

				&:before {
					letter-spacing: 4px;
				}

				span {
					&:before {
						color: #ffdc60;
						letter-spacing: 4px;
					}
				}
			}
		}

		.product-price-variant {
			margin: 0;

			div.price {
				font-weight: 700;
				margin: 0;
			}

			del {
				color: #d6d6d6;
				margin-right: 4px;
			}
		}

		.label-block {
			position: absolute;
			top: -10px;

			.product-badget {
				background-color: var(--color-primary);
				line-height: 1;
				padding: 6px 10px 5px;
				font-size: 12px;
				font-weight: 700;
				color: #fff;
				border-radius: 4px;
				box-shadow: 0 8px 16px 0 rgb(53 119 240 / 30%);
			}
		}
	}

	&.has-gallery-image {
		.thumbnail {
			&:hover {
				a {
					img {
						opacity: 0;
						visibility: hidden;

						&.hover-img {
							opacity: 1 !important;
							visibility: visible !important;
						}
					}
				}
			}
		}

		&:hover {
			.thumbnail {
				a {
					img {
						&.hover-img {
							opacity: 0;
							visibility: hidden;
						}
					}
				}
			}
		}
	}

	&.product-style-one {
		.color-variant {
			margin-top: 5px;

			.wcvaswatchinput {
				display: block;
			}
		}
	}

	&.product-style-two {
		.thumbnail {
			a {
				max-width: 275px;
				max-height: 275px;
				width: auto;
				height: auto;
			}

			.label-block {
				&.label-right {
					right: 0;
				}
			}
		}
	}

	&.product-style-three {
		.thumbnail {
			.label-block.label-right {
				right: -14px;
			}
		}
	}

	&.product-style-seven {
		margin-top: 20px;

		.product-content {
			>.cart-btn {
				>.cart-btn {
					padding: 0;
					line-height: 45px;
					position: initial;

					&.loading {
						opacity: 1;
						background-color: var(--color-primary);
						border-color: var(--color-primary);
						color: var(--color-white);

						i {
							opacity: 0;
						}

						&:after {
							top: 0px;
							right: 14px;
						}
					}

					&.added {
						display: none;
					}
				}

				.added_to_cart.wc-forward {
					width: auto;
					border-radius: 6px;
					padding: 0 15px;
					line-height: 40px;
				}
			}
		}
	}

	&.product-list-style-3 {
		.thumbnail {
			a {
				img {
					width: auto;
					margin: 0 auto;
				}
			}
		}

		.product-content {
			.product-price-variant {
				.price {
					font-size: 24px;
				}
			}
		}
	}
}

.axil-product {
	>.thumbnail {
		.label-block {
			&.label-right {
				@media (max-width: 575px) {
					right: 15px;
					top: 15px;
				}
			}
		}
	}
}

.axil-product-list {
	.thumbnail {
		@media (min-width: 576px) {
			width: 120px;
		}
	}

	.product-content {
		.product-rating {
			align-items: center;

			.rating {
				font-weight: 500;
			}

			.star-rating {
				font-size: 12px;
				width: 80px;

				&:before {
					letter-spacing: 4px;
				}

				span {
					&:before {
						color: #ffa800;
						letter-spacing: 4px;
					}
				}
			}
		}

		.product-cart {
			.cart-btn {
				padding: 0;
				background-color: transparent;
				margin-bottom: 0;
			}

		}

		.product-price-variant {
			del {
				color: #d6d6d6;
				margin-right: 10px;
			}
		}

		.add-to-cart {
			display: flex;
			align-items: center;
			flex-direction: column;

			@media (max-width: 575px) {
				flex-direction: row;
				justify-content: center;
			}

			.add_to_cart_button.added {
				display: none;
			}

			a.added_to_cart.wc-forward {
				background-color: var(--color-white);
				border: 1px solid var(--color-light);
				border-radius: 6px;
				padding: 7px 15px;
				font-size: 14px;
				transition: all 0.3s ease-in-out;

				@media (max-width: 575px) {
					margin: 0 5px;
				}
			}

			.axil-wishlist-icon {
				text-align: center;
				display: block;
				height: 40px;
				width: 40px;
				line-height: 40px;
				border: 1px solid #efefef;
				border-radius: 6px;
				color: var(--color-heading);
				font-size: 14px;
				transition: var(--transition);
				margin-top: 10px;

				.wishlist-icon {
					font-weight: 300;
				}

				&:hover {
					background-color: var(--color-primary);
					border-color: var(--color-primary);
					color: var(--color-white);
				}

				&.axil-remove-from-wishlist {
					.wishlist-icon {
						font-weight: 900;
					}

					&:hover {
						color: var(--color-white) !important;
					}
				}

				@media (max-width: 575px) {
					margin-top: 0;
				}
			}

			.cart-btn {
				&.loading {
					opacity: 1;

					i {
						opacity: 0;
					}

					&:after {
						top: 0;
						left: 0;
						right: 0;
					}
				}
			}
		}
	}

	&.product-list-style-2 {
		.product-content {
			.product-cart {
				.cart-btn {
					padding: 10px 15px;
				}
			}
		}
	}
}

.product-action-wrapper .product-action .add-to-cart .axil-btn {
	display: flex;
}

.axil-slick-dots.slick-dots-bottom .slick-dots li button:before {
	line-height: 22px;
}

.axil-wishlist-icon {
	.ajax-loading {
		display: none;
		animation: none !important;
		top: 13px;
		position: relative;
	}

	&.ajaxloading {
		.ajax-loading {
			display: block;
			animation: spin 2s linear infinite !important
		}
	}

	&.axil-remove-from-wishlist {
		color: var(--color-primary) !important;
	}
}

.color-variant {
	.wcvaswatchinput {
		display: inline-block;
		border: 1px solid transparent;
		border-radius: 50%;
		transition: all 0.3s ease-in-out;
	}
}

.variation-radios input:checked+label span.color {
	border-color: transparent;
}

// Footer Recent Post
.axil-footer-widget {
	.content-blog {
		align-items: flex-start;

		.post-thumbnail {
			margin-right: 15px;
			min-width: auto;
			width: 200px;

			a {
				img {
					width: 150px;
				}
			}
		}

		.content {
			.title {
				font-size: 15px;
				margin-bottom: 15px;
			}

			.post-meta-list {
				li {
					margin: 0 0 5px 0;
				}
			}
		}
	}
	&.widget_nav_menu, &.widget_pages, &.widget_categories {
		ul {
			padding-left: 20px;
			&.menu {
				padding-left: 0;
			}
		}
		.inner {
			>ul {
				padding-left: 0;
			}
		}
	}
}

// Product Quick View 
#yith-quick-view-modal {
	.yith-wcqv-wrapper {
		width: 1100px;

		.woocommerce-variation-price {
			margin-bottom: 5px;
		}

		.product_meta {
			padding-top: 0;
			margin-bottom: 15px;
			font-size: 16px;
		}

		.woocommerce div.product .product_title {
			font-size: 36px;

			@media only screen and (max-width: 767px) {
				font-size: 30px;
			}
		}
	}

	.yith-wcqv-main {
		overflow-x: hidden;
		box-shadow: none;
	}

	#yith-quick-view-content {
		margin-top: 50px;
		border-top: 1px solid #dee2e6;
		padding: 30px;

		@media only screen and (max-width: 767px) {
			padding: 30px 10px;

		}

		.small-thumb-style-two {
			.small-thumb-img {
				img {
					width: auto;
					height: auto;
				}
			}
		}
	}

	.single-product-content {
		form.cart {
			display: flex;
			@media (max-width: 479px) {
				flex-direction: column;
				.product-action-wrapper.quantity {
					justify-content: center;
				}
			}
			&.variations_form {
				display: block;
			}

			.single_add_to_cart_button {
				&:hover {
					background-color: var(--color-primary);
					transform: scale(1.01);
				}
			}
		}
	}

	.arrow-both-side-3 {
		.slide-arrow {
			left: -20px;

			&.next-arrow {
				right: -20px;
				left: auto;
			}
		}
	}
}

#yith-quick-view-close {
	border: none;
	font-size: 0;
	top: 12px;
	right: 15px;

	&:before {
		content: "\f00d";
		font-family: "Font Awesome 5 Pro";
		font-size: 24px;
		color: #656973;
		transition: all 0.3s ease-in-out;
	}
}

.yith-wcqv-button {
	.blockUI.blockOverlay {
		background: #fff !important;
		opacity: 1 !important;
		border-radius: 4px;

		&:before {
			content: "\f110";
			font-family: "Font Awesome 5 Pro";
			font-weight: 900;
			font-size: 14px;
			color: var(--color-heading);
			background: none !important;
			height: auto;
			width: auto;
			margin: 0;
			top: 13px;
			left: 14px;
		}
	}
}


// Testimonial
.testimonial-style-one-wrapper .slick-slide.slick-active.slick-current .play-btn {
	&:hover {
		transform: scale(1.1);
	}
}

.testimonial-style-three-wrapper {
	@media (max-width: 991px) {
		padding-top: 40px;

		.heading-title {
			margin-bottom: 30px;

			.title {
				font-size: 36px;
			}
		}
	}

	@media (max-width: 767px) {
		padding-left: 20px;
		padding-right: 20px;

		.heading-title {
			.title {
				font-size: 26px;
			}
		}

		p {
			font-size: 16px;
		}
	}
}

// Responsive

.axil-header .header-items .shopping-items .cart-dropdown-btn .cart-count {
	@media only screen and (max-width: 575px) {
		top: 6px;
		right: 0;
	}
}


// Header
.axil-header {
	z-index: 10;
	position: relative;

	.header-items {
		.shopping-items {
			.my-account {
				&:hover {
					.my-account-dropdown {
						visibility: visible;
						opacity: 1;
						z-index: 9;
						transform: translateY(0);
					}
				}
			}

			.cart-icon-area {
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 1;

				svg {
					path {
						transition: var(--transition);
					}
				}

				&:after {
					content: "";
					height: 40px;
					width: 40px;
					background-color: var(--color-primary);
					transform: scale(0);
					border-radius: 50%;
					position: absolute;
					z-index: -1;
					transition: var(--transition);
				}

				&:hover {
					svg {
						path {
							fill: var(--color-white);
						}
					}

					&:after {
						transform: scale(1);
					}
				}
			}
		}
	}

	.header-navbar {
		.header-brand {
			a {
				display: block;
			}

			img {
				max-height: 50px;

				@media (max-width: 479px) {
					max-height: 30px;
				}
			}
		}
	}

	.axil-mainmenu {
		&.aside-category-menu {
			.header-department {
				.department-megamenu {
					.department-megamenu-wrap {
						display: block;

						@media only screen and (max-width: 1199px) {
							padding-top: 0;
						}
					}
				}

				.department-nav-menu {
					>ul {
						>li {
							position: initial;

							a {
								&:hover {
									color: var(--color-secondary);
								}
							}
						}
					}
				}
			}
		}

		.mainmenu {
			>li {
				>a {
					>img {
						max-height: 50px;
					}
				}
			}
		}
	}

	.header-action {
		>ul {
			>li {
				@media only screen and (max-width: 479px) {
					margin: 0 8px;
				}

				>a {
					font-size: 18px;
					z-index: initial;

					@media (max-width: 767px) {
						font-size: 17px;
					}

					.cart-count,
					.wishlist-icon-num {
						z-index: 3;
					}

					>i {
						position: relative;
						z-index: 2;
					}

					&::after {
						z-index: inherit;
					}
				}
			}
		}

		.shopping-cart {
			.cart-dropdown-btn {
				.cart-count {
					top: -23px;
				}
			}
		}

		.axil-search {
			.icon {
				font-size: 16px;
			}
		}
	}

	&.header-style-2 {
		.axil-header-top {
			.header-brand {
				@media (max-width: 767px) {
					margin-bottom: 20px;
					text-align: center;
				}
			}

			.header-top-dropdown {
				flex-wrap: wrap;

				@media (max-width: 767px) {
					justify-content: center;
				}
			}
		}

		.product-search {
			flex: 1;
			margin-right: 20px;

			@media (max-width: 767px) {
				flex: 1 0 100%;
				margin-right: 0;
				margin-bottom: 15px;
			}

			.search_box_wrapper {
				border: 1px solid #f0f0f0;
				border-radius: 6px;
				position: relative;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.input_field {
					width: 100%;

					input {
						height: 40px;
						border: none;
					}
				}

				.search_category {
					border: none;
					height: 40px;
					width: 150px;
					border-right: 1px solid #f0f0f0;
					font-size: 14px;
					padding: 0 15px;
					background-color: transparent;
					background: url(../images/arrow-icon2.png) 90% center no-repeat transparent;

					@media (max-width: 991px) {
						width: 120px;
					}

					&:focus {
						outline: none;
						box-shadow: none;
					}
				}
			}
		}

	}

	&.header-style-7 {
		.axil-mainmenu {
			filter: none;

			@media (max-width: 1199px) and (min-width: 992px) {
				padding: 20px 15px;
			}
		}

		.mainmenu {
			@media (max-width: 1199px) {
				margin: 0 -8px;
			}

			>li {
				@media (max-width: 1650px) and (min-width: 1400px) {
					margin: 0 12px;
				}

				@media (max-width: 1199px) {
					margin: 0 8px;
				}

				>a {
					display: flex;
					align-items: center;
					padding: 8px 0;
					margin-right: 0;

					.menu-icon {
						margin-right: 10px;

						svg {
							width: 16px;

							path {
								fill: var(--color-body);
								transition: 0.3s;
							}
						}

						img {
							width: 16px;
						}
					}

					&:after {
						top: 9px;
						right: 0;
						color: var(--color-dark);
					}

					&:hover {
						.menu-icon {
							svg {
								path {
									fill: var(--color-primary);
								}
							}
						}
					}
				}

				&.menu-item-has-children {
					>a {
						padding-right: 18px;
					}

					.axil-submenu {
						top: 90%;

						li {
							a {
								display: flex;
								align-items: center;

								.menu-icon {
									margin-right: 8px;

									svg,
									img {
										width: 16px;
										display: block;
									}
								}
							}
						}
						.axil-submenu {
							top: 0 !important;
						}
					}

					&:hover {
						.axil-submenu {
							top: 100%;
						}
					}
				}

				&.active-menu,
				&.is-active {
					>a {
						border: 1px dashed var(--color-primary);
						border-radius: 8px;
						padding: 8px 28px 8px 10px;

						&:after {
							right: 10px;
						}
					}
				}
			}
		}

		.header-action {
			ul {
				@media (max-width: 479px) {
					margin: 0 -7px;
				}

				li {
					@media (max-width: 479px) {
						margin: 0 7px;
					}
				}
			}

			.axil-search {
				input {
					@media (max-width: 1599px) and (min-width: 1400px) {
						width: 280px;
					}
				}
			}

			.shopping-cart {
				.cart-dropdown-btn {
					.cart-count {
						height: 22px;
						width: 22px;
						line-height: 19px;
						border: 2px solid var(--color-white);
						font-size: 12px;
						right: -12px;
					}
				}
			}
		}
	}

	.mainmenu {
		.menu-item-has-children {
			.axil-submenu {
				.menu-item-has-children {
					position: relative;

					.axil-submenu {
						left: 100%;
						top: 0;
						visibility: hidden;
						opacity: 0;
					}

					&:hover {
						>.axil-submenu {
							visibility: visible;
							opacity: 1;
						}
					}
				}
			}
		}
	}
}

.mainmenu {
	>li {
		>a {
			&:before {
				bottom: 28px;
			}
		}

		&.is-active {
			a {
				color: var(--color-black);

				&:before {
					opacity: 1;
					width: 100%;
				}
			}
		}
	}

	>.menu-item-has-children {
		.axil-submenu {
			z-index: 99999 !important;

			>li {
				&.is-active {
					>a {
						color: var(--color-secondary);
					}
				}
			}

		}
	}

}

.axil-mobile-toggle {
	.menu-btn {
		font-size: 20px;

		@media only screen and (max-width: 479px) {
			font-size: 18px;
		}
	}
}

@media only screen and (max-width: 991px) {
	.header-style-7 {
		.header-main-nav {
			.mainmenu-nav {
				.mainmenu {
					>li {
						&.menu-item-has-children {
							a {
								&:after {
									right: 10px;
									top: 7px;
								}
							}
						}

					}
				}
			}
		}
	}

	.header-main-nav {
		.mainmenu-nav {
			padding: 20px 25px 10px;

			.mainmenu {
				>li {
					&.is-active {
						a {
							color: var(--color-black);

							&:after {
								color: var(--color-black);
							}
						}
					}

				}

				>.menu-item-has-children {
					.axil-submenu {
						>li {
							&.is-active {
								a {
									color: var(--color-primary);
								}
							}
						}

					}
				}
			}
		}
	}
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
	cursor: pointer;
	opacity: 1;
}

.header-search-modal {

	/* width */
	::-webkit-scrollbar {
		width: 8px;
	}

	/* Track */
	::-webkit-scrollbar-track {
		// box-shadow: inset 0 0 5px #ffffff;
		border-left: 1px solid var(--color-border-light);
		border-radius: 30px;
	}

	/* Handle */
	::-webkit-scrollbar-thumb {
		background: #a7a7a7;
		border-radius: 10px;
	}

	/* Handle on hover */
	::-webkit-scrollbar-thumb:hover {
		background: #a1a1a1;
	}

	.card-header {
		.form-control {
			color: var(--color-dark);

			/* -- Placeholder -- */
			&::placeholder {
				color: var(--color-body);
				/* Firefox */
				opacity: 1;
			}

			&:-ms-input-placeholder {
				/* Internet Explorer 10-11 */
				color: var(--color-body);
			}

			&::-ms-input-placeholder {
				/* Microsoft Edge */
				color: var(--color-body);
			}
		}
	}
}

.accountToggleMask {
	background-color: transparent;
	position: fixed;
	top: 0;
	bottom: 0;
	right: 0;
	left: 0;
	z-index: 1;
}

.header-main-nav.is-not-nav {
	height: 60px;
}

.default-menu {
	.mainmenu {
		justify-content: flex-end;
	}
}

//Footer 
.footer-style-3 {
	.axil-footer-widget {
		display: flex;
		margin-bottom: 30px;

		.widget-title {
			border-right: 1px solid rgba(119, 119, 119, .4);
			padding-right: 22px;
			margin-right: 22px;
			font-size: 14px;
			margin-bottom: 0;
			min-width: 100px;
			max-width: 100px;
			font-weight: 700;
			text-align: right;
		}

		.inner {
			flex: 1;

			ul {
				margin-bottom: 0;

				li {
					margin: 10px 0;

					&:first-child {
						margin-top: 0;
					}

					&:last-child {
						margin-bottom: 0;
					}

					a {
						font-size: 14px;
						color: #D6D6D6;
						font-family: var(--font-secondary);
					}
				}
			}
		}

		.download-btn-group {
			display: flex;
			align-items: center;
			margin-top: 15px;

			.qr-code {
				margin-right: 20px;

				img {
					@media #{$lg-layout} {
						height: 80px;
					}
				}
			}

			.app-link {
				flex: 1;

				a {
					margin-bottom: 15px;
					display: block;

					&:last-child {
						margin-bottom: 0;
					}
				}
			}
		}

		&.footer-widget-newsletter {
			padding-right: 50px;
			margin: 0;
			display: block;

			@media (max-width: 575px) {
				padding-right: 0;
			}

			.input-group {
				input {
					background-color: #49495F;
					height: 46px;
					border-radius: 8px 0 0 8px;
					padding: 0 20px;
					border: none;
					color: #D6D6D6;

					&:focus {
						background-color: #49495F;
						box-shadow: none;
						color: #D6D6D6;
					}

					&::placeholder {
						color: #D6D6D6;
						/* Firefox */
						opacity: 1;
					}

					&:-ms-input-placeholder {
						/* Internet Explorer 10-11 */
						color: #D6D6D6;
					}

					&::-ms-input-placeholder {
						/* Microsoft Edge */
						color: #D6D6D6;
					}
				}

				button {
					width: auto;
					background-color: var(--color-primary);
					font-size: 14px;
					font-weight: 700;
					border-radius: 0 8px 8px 0;
					color: var(--color-white);
					padding: 0 24px;

					&:hover {
						background-color: var(--color-secondary);
					}
				}
			}

			.widget-title {
				color: var(--color-white);
				font-size: 24px;
				margin-bottom: 8px;
				padding: 0;
				margin-right: 0;
				border: none;
				text-align: left;
				min-width: 100%;
				max-width: 100%;

			}
		}
	}

	.payment-method {
		.title {
			min-width: 100px;
			max-width: 100px;
			padding-right: 22px;
		}

		ul {
			border-left-color: rgba(119, 119, 119, .4);
		}
	}

	@media (max-width: 575px) {

		.footer-social-link,
		.payment-method {
			ul {
				padding-left: 10px;

				li {
					height: 30px;
					width: 30px;
					line-height: 30px;

					a {
						height: 30px;
						width: 30px;
						line-height: 30px;
						font-size: 12px;
					}

					img {
						max-width: 22px;
					}
				}
			}
		}
	}
}

.copyright-area {
	.social-share {
		padding-left: 10px;
		margin-left: -20px;
	}
}


// Contact Page
.axil-contact-page {
	.contact-form {
		.form-group {
			p {
				width: 100%;
				margin-bottom: 0;
			}

			textarea {
				max-height: 200px;
			}
		}
	}
}


//Widget Product Search
.widget_nav_menu {
	.nav-link {
		color: var(--color-heading);
		&:hover {
			color: var(--color-primary);
		}
	}
}
.axil-single-widget {

	ul,
	ol,
	.wp-block-group,
	.wp-block-search__button-outside.wp-block-search__text-button {
		margin-bottom: 0;
	}
}

.widget_products {
	.product_list_widget {
		.content {
			.product-title {
				margin-bottom: 10px;
				font-size: 16px;
				font-weight: 500;
			}

			.star-rating {
				font-size: 12px;
				width: 80px;
				margin-bottom: 10px;

				&:before {
					letter-spacing: 4px;
				}

				span {
					&:before {
						color: #ffdc60;
						letter-spacing: 4px;
					}
				}
			}

			li {
				&:last-child {
					margin-bottom: 0;
					padding-bottom: 0;
				}
			}
		}
	}
}

.widget_product_search {
	.input-group {
		position: relative;
	}

	.form-control {
		background-color: transparent;
		height: 50px;
		font-size: 16px;
		border-radius: 6px !important;
		padding: 0 20px 0 50px;

		&:focus {
			box-shadow: none;
		}
	}

	.input-group-btn {
		position: absolute;
		left: 0;
		top: 11px;
		padding: 0 20px;
		z-index: 3;

		button {
			font-size: 16px;
			color: var(--color-body);
			padding: 0;
			border: none;

			i {
				font-weight: 400;
			}

			&:focus {
				box-shadow: none;
				border: none;
			}
		}
	}
}

ol.wp-block-latest-comments {
	.wp-block-latest-comments__comment {
		list-style: auto;
		line-height: var(--line-height-b1);
		margin-bottom: 8px;
	}
}

.widget_search {
	.wp-block-search__label {
		font-size: 36px;
		margin-bottom: 30px;
		color: var(--color-heading);

		@media (max-width: 991px) {
			font-size: 30px;
		}

		@media (max-width: 767px) {
			font-size: 26px;
		}
	}
}

// Common
table.wishlist_table tbody td,
table.wishlist_table thead th {
	border-color: var(--color-lighter);
}

.etrade-newsletter-wrapper {
	.wpcf7 {
		form {
			&.invalid {
				.wpcf7-response-output {
					display: inline-block;
					padding: 2px 15px;
					border-radius: 4px;
					color: #dc3232;
					border: 1px solid #dc3232;
				}
			}

			&.sent {
				.wpcf7-response-output {
					display: inline-block;
					padding: 2px 15px;
					border-radius: 4px;
					border: 1px solid #46b450;
					color: #46b450;
				}
			}

			.wpcf7-response-output {
				margin: 0;
			}

			.wpcf7-not-valid-tip {
				margin-top: 5px;
			}
		}
	}
}

.wpcf7 {
	form {
		.wpcf7-response-output {
			margin-left: 0;
		}
	}
}

.newsletter-form {
	align-items: stretch;

	.wpcf7-submit {
		padding: 16px 40px;
		background-color: var(--color-heading);
		border-color: var(--color-heading);
		color: var(--color-white);

		&:hover {
			background-color: var(--color-primary);
			border-color: var(--color-primary);
			color: var(--color-white);

		}
	}
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
	color: var(--color-dark) !important;
}

.clearfix.page-numbers {
	justify-content: center;
}

.woocommerce-grouped-product-list {
	.woocommerce-grouped-product-list-item__thumbnail {
		display: none;
	}

	.woocommerce-grouped-product-list-item__price {
		span {
			display: inline-block;
		}

		.product-meta {
			display: none;
		}
	}

	.woocommerce-grouped-product-list-item__quantity {
		.product_type_variable {
			font-size: var(--font-size-b3);
			color: var(--color-white);
			font-weight: var(--p-medium);
			background-color: var(--color-body);
			border-radius: 20px;
			padding: 5px 14px;
			min-width: 100px;
			text-align: center;
		}
	}
}

.woocommerce div.product form.cart .group_table td {
	vertical-align: middle;
}

.comment-list {
	.comment {
		.single-comment {
			.comment-author {
				padding: 0;
				margin-bottom: 0;
			}
		}
	}
}

.comment-respond {
	.comment-reply-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	#cancel-comment-reply-link {
		margin-left: 10px;
		font-size: 17px;
		color: #dc3545;
		position: relative;
		display: flex;
		align-items: center;
		line-height: 1;

		&:before {
			content: "\f00d";
			font-family: var(--font-awesome);
			padding-right: 8px;
			font-size: 20px;
		}
	}
}

// =========== Style ADD for WP  ==============
.course_search_box_wrapper.row>div {
	padding: 0;
}

.course_search_box_wrapper.row {
	border: 1px solid #b3b3b3;
	border-radius: 5px;
	margin-right: 0;
	margin-left: 0;
	position: relative;

	.form-group {
		margin-bottom: 0;

		.form-control {
			height: 40px;
			padding: 5px 10px;
			border: none;
		}

		select {
			background: url(../images/angle-icon.png);
			background-repeat: no-repeat;
			background-size: 9px;
			background-color: transparent;
			background-position: right 10px center;
			border-radius: 10px 0 0 10px;
			font-size: 12px;

			&:focus {
				box-shadow: none;
			}
		}

		#product_search {
			padding-left: 15px;
			padding-right: 0;
		}
	}
}

.course_search_box_wrapper.row .input_field .form-group {
	padding-right: 15px;
}

.search-wrap .course_search_box_wrapper.row .input_field .form-group::before {
	content: "";
	display: block;
	height: 60%;
	position: absolute;
	top: 9px;
	left: 0;
	width: 1px;
	z-index: 1;
	background-color: rgb(0 0 0 / 27%);
}

// search suggestion field
.search_suggetion_field {
	width: 100%;
	max-height: 300px;
	overflow-y: auto;
	background: white;
	position: absolute;
	top: 55px;
	border-radius: 5px;
	z-index: 99;
	box-shadow: 0 10px 10px #0002;

	ul {
		margin-bottom: 0;

		li {
			border-bottom: solid 1px #ececec;
			padding: 15px 25px;
			margin: 0;
			display: flex;
			gap: 15px;

			.title {
				font-size: 16px;
			}

			img {
				max-width: 60px;
			}
		}
	}

	.error_message {
		width: 100%;
		padding: 5px 10px;
		text-align: center;
	}
}

.search_suggetion_field ul li .search_suggetion_field ul li .title {
	font-size: 16px;
}

//search loader
.search_input_loader {
	width: 25px;
	height: 25px;
	position: absolute;
	top: 50%;
	right: 10px;
	transform: translateY(-50%);
	display: none;
}

.search_input_loader:after {
	content: " ";
	display: block;
	width: 25px;
	height: 25px;
	border-radius: 50%;
	border: 2px solid;
	border-color: #0071dc transparent #0071dc transparent;
	animation: search_input_loader 1.2s linear infinite;
}

@keyframes search_input_loader {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.search_input_loader.loader1 {
	width: 75px;
	height: 75px;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	display: none;

	&:after {
		width: 75px;
		height: 75px;
	}
}

.page-content,
.entry-content,
.entry-summary {
	margin: 0 !important;
}

.psearch-results {
	.axil-product-list {
		.product-content {
			.product-cart {
				@media (max-width: 575px) {
					justify-content: flex-start;
				}

				.cart-btn {
					&.loading {
						opacity: 1;

						i {
							opacity: 0;
						}

						&:after {
							top: 0;
							left: 0;
							right: 0;
						}
					}
				}
			}
		}
	}
}

//==============================================
.product-area.border-off {
	border-bottom: none;
}

.section-border-off {
	.product-area {
		border-bottom: none;
	}
}

.comming-soon-area {
	.comming-soon-banner {
		padding: 0;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
}

.error-page {
	min-height: 100vh;
	display: flex;
	align-items: center;
}


.subtitle.title-highlighter.highlighter-primary {
	color: var(--light-primary);

	i {
		background-color: var(--light-primary);
	}
}

.subtitle.title-highlighter.highlighter-primary2 {
	color: var(--color-primary);

	i {
		background-color: var(--color-primary);
	}
}

.subtitle.title-highlighter.highlighter-secondary {
	color: var(--color-secondary);

	i {
		background-color: var(--color-secondary);
	}
}

body.woocommerce.woocommerce-page .axiltheme-loadmore-btn-area.pt--30 {
	padding-top: 20px !important;
}

.axiltheme-loadmore-btn.btn-load-more {
	width: auto;

	.axiltheme-loadmore-btn-icon {
		display: none;
	}
}

// Slider Style 
.axil-main-slider-area {
	.slick-slide {
		opacity: 0;
	}

	.slick-slider {
		.slick-slide {
			opacity: 1;
		}
	}
}

.main-slider-style-4 {
	@media (max-width: 767px) {
		padding-top: 40px;
	}

	.main-slider-content {
		z-index: 1;

		.title {
			@media (max-width: 1199px) {
				font-size: 40px;
			}

			@media (max-width: 991px) {
				font-size: 30px;
				margin-bottom: 30px;
			}

			@media (max-width: 767px) {
				font-size: 28px;
			}
		}
	}

	.slide-thumb-area {
		@media (max-width: 1199px) {
			margin-right: 0;
			margin-left: 0;
		}

		@media (max-width: 767px) {
			padding: 20px 0 0;
		}

		.banner-product {
			.plus-icon {
				height: 29px;
				width: 29px;
				line-height: 29px;
				background-color: var(--color-white);
				border-width: 1px;
				font-size: 12px;

				&:hover {
					background-color: var(--color-white);
				}
			}

			.product-details {
				padding: 15px 20px;
				box-shadow: 0px 54px 94px rgba(172, 128, 117, 0.2);
				left: -46px;
				width: 100%;
				min-width: 250px;
				z-index: 1;

				@media (max-width: 991px) {
					padding: 10px;
					min-width: 170px;
					text-align: center;
					left: -65px;
				}

				.title {
					font-size: 18px;
					margin-bottom: 0;

					@media (max-width: 991px) {
						font-size: 14px;
					}
				}

				.price {
					font-size: 22px;
					margin-bottom: 5px;

					@media (max-width: 991px) {
						font-size: 16px;
						margin-bottom: 0;
					}

					del {
						color: var(--color-light);
					}

					ins {
						color: var(--color-secondary);
						background: transparent;
					}
				}

				.product-rating {
					margin-bottom: 0;
					display: flex;
					align-items: center;

					@media (max-width: 991px) {
						justify-content: center;
					}

					.star-rating {
						float: none;
						width: 105px;
						font-size: 16px;
						margin-bottom: 2px;

						@media (max-width: 991px) {
							font-size: 10px;
							margin-bottom: 0;
							width: 75px;
						}

						&:before {
							letter-spacing: 4px;
						}

						span {
							&:before {
								color: #ffdc60;
								letter-spacing: 4px;
							}
						}
					}

					.rating-number {
						@media (max-width: 991px) {
							font-size: 12px;
						}
					}
				}

				&:after {
					border-left: 9px solid transparent;
					border-right: 9px solid transparent;
					border-top: 9px solid var(--color-white);
					bottom: -9px;

					@media (max-width: 991px) {
						left: 72px;
					}
				}
			}

			&.icon-active {
				.product-details {
					margin-bottom: 10px;
					visibility: visible;
					opacity: 1;
				}
			}
		}

		.shape-group {
			li {
				&.shape-2 {
					@media (max-width: 1199px) {
						svg {
							height: 430px;
							width: 590px;
						}
					}

					@media (max-width: 991px) {
						display: none;
					}
				}
			}
		}
	}
}


.plus-hover-product {
	position: absolute;
	bottom: 100%;
	left: -34px;
	margin-bottom: 20px;
	visibility: hidden;
	opacity: 0;
	transition: 0.3s;

	@media (max-width: 991px) {
		left: -55px;
	}

	.product-details {
		background-color: var(--color-white);
		border-radius: 8px;
		width: 100%;
		min-width: 250px;
		padding: 15px 20px;
		box-shadow: 0px 54px 94px rgba(172, 128, 117, 0.4);

		@media (max-width: 991px) {
			padding: 10px;
			min-width: 170px;
			text-align: center;
		}

		.title {
			font-size: 18px !important;
			margin-bottom: 0 !important;

			@media (max-width: 991px) {
				font-size: 14px !important;
			}

			a {
				transition: all 0.3s ease-in-out;
			}
		}

		.price {
			font-size: 22px;
			margin-bottom: 5px;
			font-weight: 700;
			color: var(--color-secondary);

			@media (max-width: 991px) {
				font-size: 16px;
				margin-bottom: 0;
			}

			del {
				color: var(--color-light);
			}

			ins {
				color: var(--color-secondary);
				background: transparent;
			}
		}

		.product-rating {
			margin-bottom: 0;
			display: flex;
			align-items: center;

			@media (max-width: 991px) {
				justify-content: center;
			}

			.star-rating {
				float: none;
				width: 105px;
				font-size: 16px;
				margin-bottom: 2px;

				@media (max-width: 991px) {
					font-size: 10px;
					margin-bottom: 0;
					width: 75px;
				}

				&:before {
					letter-spacing: 4px;
				}

				span {
					&:before {
						color: #ffdc60;
						letter-spacing: 4px;
					}
				}
			}

			.rating-number {
				@media (max-width: 991px) {
					font-size: 12px;
				}
			}
		}

		&:after {
			content: "";
			width: 0;
			height: 0;
			border-left: 9px solid transparent;
			border-right: 9px solid transparent;
			border-top: 9px solid var(--color-white);
			position: absolute;
			bottom: -8px;
			left: 50px;

			@media (max-width: 991px) {
				left: 72px;
			}
		}
	}
}

// Product Collection 
.product-collection {
	margin-bottom: 0;

	.collection-content {
		z-index: 3;

		.plus-btn {
			&:hover {
				.plus-hover-product {
					visibility: visible;
					opacity: 1;
					margin-bottom: 10px;
				}
			}
		}

		.title {
			@media (max-width: 991px) {
				font-size: 26px;
			}
		}
	}
}

.product-collection-three {
	@media (max-width: 1269px) {
		min-height: 175px;
	}

	.collection-content {
		position: relative;
		z-index: 2;

		.title {
			@media (max-width: 1399px) {
				margin-bottom: 20px;
			}

			a {
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}
		}
	}

	.collection-thumbnail {
		@media (max-width: 1399px) {
			width: 50px;
		}

		&:before {
			@media (max-width: 1399px) {
				height: 100px;
				width: 100px;
				bottom: 50px;
			}
		}
	}
}

// Slick Nav Style 
.section-title-wrapper {
	.title {
		@media (max-width: 767px) {
			font-size: 24px;
		}
	}

	&.section-title-left {
		padding-right: 0;
	}
}

.is-countdown {
	.arrow-top-slide {
		.slide-arrow {
			top: -110px;

			@media (max-width: 767px) {
				top: -195px;
			}
		}
	}
}

.arrow-both-side-3 {
	.slide-arrow {
		left: 10px;

		@media (min-width: 1600px) {
			left: -50px;
		}

		&.next-arrow {
			right: 10px;

			@media (min-width: 1600px) {
				right: -50px;
			}
		}
	}
}

.is-cat-slider,
.title-border-style {
	.section-title-border {
		padding-right: 115px;

		.title {
			padding-right: 0;
		}
	}

	.arrow-top-slide {
		.slide-arrow {
			@media (max-width: 767px) {
				top: -90px;
			}
		}
	}
}

// Team Style
.axil-team-area {
	&.bg-wild-sand {
		.arrow-top-slide {
			.slide-arrow {
				top: -90px;

				&:before {
					background-color: var(--color-white);
				}
			}
		}
	}
}

.axil-team-member {
	.thumbnail {
		width: 100%;

		img {
			width: 100%;
		}
	}
}

//Service
.service-box {
	&.service-style-1 {
		margin-bottom: 0;
	}
}

.fullwidth-container {
	.container {
		@media (max-width: 1349px) {
			max-width: 100%;
		}
	}
}

// Poster 
.delivery-poster {
	margin-bottom: 0;

	@media (max-width: 1199px) {
		padding: 30px;
	}

	.content {
		@media (max-width: 1199px) {
			flex: auto;
		}
	}
}

// Breadcrumbs
.author-breadcrumb {
	.media {
		display: flex;
		align-items: flex-start;

		.thumbnail {
			margin-right: 20px;

			img {
				border-radius: 50%;
			}
		}

		.media-body {
			flex: 1;

			.author-info {
				margin-bottom: 10px;

				.title {
					font-size: 30px;
					text-transform: capitalize;
					margin-bottom: 0;
				}
			}

			.content {
				p {
					width: 70%;
					margin-bottom: 15px;

					@media (max-width: 767px) {
						width: 100%;
					}
				}

				.social-share-transparent {
					list-style: none;
					margin: -2px;
					padding: 0;

					li {
						display: inline-block;
						margin: 2px;

						a {
							height: 40px;
							width: 40px;
							line-height: 40px;
							text-align: center;
							border-radius: 50%;
							background-color: var(--color-secondary);
							color: var(--color-white);
							display: block;
							font-size: 14px;
							transition: 0.3s;

							&:hover {
								background-color: var(--color-primary);
							}
						}
					}
				}
			}
		}
	}
}

// Blog Post
.content-blog {
	.thumbnail {
		a {
			img {
				width: auto;
			}
		}
	}

	&.format-quote {
		blockquote {
			background: transparent;
			padding: 0;
			border: none;
			margin: 0;
		}
	}
}

.blog-grid-layout {
	.content-blog {
		border: 1px solid #f1f1f1;
		border-radius: 6px;
		padding: 20px;

		&:first-child {
			border: 1px solid #f1f1f1;
			padding: 20px;
		}

		.thumbnail {
			margin-bottom: 25px;
			overflow: hidden;
			border-radius: 6px;
			position: relative;

			img {
				transition: .5s;
			}

			.blog-category {
				position: absolute;
				bottom: 20px;
				right: 20px;
				z-index: 2;

				a {
					background-color: rgba(255, 255, 255, 0.5);
					border: 1px solid rgba(255, 255, 255, 0.5);
					backdrop-filter: blur(25px);
					box-shadow: 0 4px 30px rgba(0, 0, 0, .1);
					padding: 2px 10px;
					border-radius: 4px;
					color: var(--color-white);
					font-size: 14px;
				}
			}

			.popup-video {
				.play-btn {
					font-size: 18px;
					height: 60px;
					width: 60px;

					span {
						margin-left: 4px;
					}
				}
			}
		}

		.content {
			.title {
				margin-bottom: 20px;
			}

			.axil-btn {
				padding: 0;
				align-items: center;
				color: var(--color-heading);

				i {
					padding-left: 6px;
					top: 1px;
					color: var(--color-heading);
					transition: var(--transition);
				}

				&:after {
					content: "";
					height: 1px;
					width: 0;
					background-color: var(--color-primary);
					position: absolute;
					bottom: 0;
					right: 0;
					transition: var(--transition);
				}

				&:hover {
					color: var(--color-primary);

					&:after {
						width: 100%;
						left: 0;
					}

					i {
						color: var(--color-primary);
					}
				}
			}
		}

		&:hover {
			.thumbnail {
				img {
					transform: scale(1.1);
				}
			}
		}

		&.format-quote {
			.inner {
				.content {
					blockquote {
						.title {
							font-size: 20px;
						}
					}
				}
			}
		}
	}
}

.entry-tags {
	margin-top: 60px;
	margin-bottom: 60px;

	@media (max-width: 767px) {
		margin-top: 40px;
		margin-bottom: 40px;
	}

	a {
		transition: 0.3s;

		&:hover {
			color: var(--color-primary);
		}
	}
}

.axil-post-wrapper {
	.post-heading {
		@media (max-width: 767px) {
			padding-bottom: 15px;
		}
	}
}

.post-single-wrapper {
	.about-author {
		.media {
			@media (max-width: 767px) {
				flex-direction: row;
			}

			.thumbnail {
				a {
					img {
						margin: 0 30px 0 0;
					}
				}
			}

			.media-body {
				.author-info {
					.title {
						margin-bottom: 0;
					}

					.subtitle {
						font-size: 14px;

					}

				}

				.description {
					font-size: 14px;
					margin-top: 10px;
					margin-bottom: 12px;
				}
			}
		}

		.social-share {
			margin: 0 -12px;

			li {
				margin: 0;

				a {
					font-size: 16px;
					margin: 0;
					height: 40px;
					width: 40px;
					line-height: 40px;
					text-align: center;

					&:hover {
						color: var(--color-white);
					}
				}
			}
		}
	}

	p {
		img {
			&.alignright {
				margin-bottom: 30px;

				@media only screen and (max-width: 1199px) {
					margin: 30px 0;
				}
			}
		}
	}

	.post-password-form {
		p {
			label {
				input[type=password] {
					height: 60px;
				}
			}
		}
	}
}

.comment-list {
	.comment {
		.comment-meta {
			@media (max-width: 575px) {
				display: block;
			}
		}

		.time-spent {
			.reply-edit {
				@media (max-width: 575px) {
					justify-content: flex-start;
					margin: 0;
				}
			}
		}

		.single-comment {
			.comment-text {
				p {
					img {
						border-radius: 6px;
						margin: 10px 0;
					}
				}
			}
		}
	}
}

// Common Style

.mfp-gallery {
	&.mfp-auto-cursor {
		cursor: auto;

		.mfp-image-holder {
			.mfp-close {
				cursor: pointer;
			}
		}
	}
}



// Unit Test Style 

// Block Image 
.wp-block-image {
	margin: 0 0 30px;

	.alignleft {
		margin: 10px 20px 10px 0;
	}

	.alignright {
		margin: 10px 0 10px 20px;
	}

	figcaption {
		color: var(--color-body);
		margin-top: 10px;
		margin-bottom: 20px;
	}
}

// Alignments 
.alignleft,
.wp-block-gallery:not(.has-nested-images).alignleft,
.wp-block-cover-image.alignleft,
.wp-block-cover.alignleft {
	float: left;
	margin-right: 20px;
	max-width: 350px;
}

.alignright,
.wp-block-cover.alignright,
.wp-block-cover-image.alignright {
	float: right;
	margin-left: 20px;
	max-width: 350px;
}

.aligncenter {
	clear: both;
	display: block;
	margin-left: auto;
	margin-right: auto;

	&.has-left-content {
		justify-content: flex-start;

		.wp-block-cover-text {
			text-align: left;
		}
	}
}

// Container Adjust 
.wp-block-gallery.alignfull,
.wp-block-cover.alignfull,
.wp-block-media-text.alignfull,
.alignfull {
	margin-left: calc(50% - 50vw);
	margin-right: calc(50% - 50vw);
	max-width: 100vw;
	width: 100vw;
}

.wp-block-gallery.alignwide,
.wp-block-cover.alignwide,
.wp-block-media-text.alignwide,
.alignwide {
	margin-left: -100px;
	margin-right: -100px;

	@media (max-width: 1520px) {
		margin-left: 0;
		margin-right: 0;
	}
}

.wp-block-cover-image .wp-block-cover-image-text,
.wp-block-cover .wp-block-cover-text,
section.wp-block-cover-image>h2 {
	max-width: 100%;
}

// Block Button
.wp-block-button__link {
	border-radius: 6px;
	font-size: 16px;
	transition: 0.3s;
	padding: 15px 35px;
	border: 2px solid;
}

.wp-block-button {
	margin-bottom: 30px;

	&:not(.is-style-outline) {
		.wp-block-button__link {
			background-color: var(--color-primary);
			border-color: var(--color-primary);

			&:hover {
				background-color: transparent;
			}
		}
	}

	&.is-style-outline {
		.wp-block-button__link {
			padding: 15px 35px;
			border: 2px solid var(--color-primary);

			&:hover {
				background-color: var(--color-primary);
				color: var(--color-white) !important;
			}

			&:not(.has-text-color) {
				color: var(--color-primary);
			}

			&.has-text-color:not(.has-background) {
				&:hover {
					background-color: var(--color-primary);
					border-color: var(--color-primary);
				}
			}

			&.has-etrade-secondary-color {
				color: var(--color-secondary);
				border-color: currentColor;
			}

			&.has-etrade-primary-color {
				color: var(--color-primary);
				border-color: currentColor;
			}

			&.has-etrade-tertiary-color {
				color: var(--color-tertiary);
				border-color: currentColor;
			}

			&.has-etrade-white-color {
				color: var(--color-white);
				border-color: currentColor;
			}

			&.has-etrade-dark-color {
				color: var(--color-dark);
				border-color: currentColor;
			}

			&.has-background {
				&:hover {
					background-color: transparent;
					border-color: var(--color-primary);
					color: var(--color-primary) !important;
				}
			}

			&.has-etrade-primary-background-color {
				background-color: var(--color-primary);
			}

			&.has-etrade-secondary-background-color {
				background-color: var(--color-secondary);
			}

			&.has-etrade-tertiary-background-color {
				background-color: var(--color-tertiary);
			}

			&.has-etrade-white-background-color {
				background-color: var(--color-white);
			}

			&.has-etrade-dark-background-color {
				background-color: var(--color-dark);
			}
		}
	}

	&.is-style-fill {
		.wp-block-button__link {

			&.has-background,
			&.has-text-color {
				&:hover {
					background-color: transparent;
					border-color: var(--color-primary);
					color: var(--color-primary);
				}
			}

			&.has-etrade-secondary-color {
				color: var(--color-secondary);
			}

			&.has-etrade-primary-color {
				color: var(--color-primary);
			}

			&.has-etrade-tertiary-color {
				color: var(--color-tertiary);
			}

			&.has-etrade-white-color {
				color: var(--color-white);
			}

			&.has-etrade-dark-color {
				color: var(--color-dark);
			}

			&.has-etrade-primary-background-color {
				background-color: var(--color-primary);
				border-color: var(--color-primary);
			}

			&.has-etrade-secondary-background-color {
				background-color: var(--color-secondary);
				border-color: var(--color-secondary);
			}

			&.has-etrade-tertiary-background-color {
				background-color: var(--color-tertiary);
				border-color: var(--color-tertiary);
			}

			&.has-etrade-white-background-color {
				background-color: var(--color-white);
				border-color: var(--color-white);
			}

			&.has-etrade-dark-background-color {
				background-color: var(--color-dark);
				border-color: var(--color-dark);
			}
		}
	}
}

// Block Cover
.wp-block-cover,
.wp-block-cover-image {
	padding: 20px;
	margin-bottom: 20px;

	p.wp-block-cover-text,
	.wp-block-cover-text {
		color: var(--color-white);
		font-size: 36px;
		padding: 10px;
	}

	&.aligncenter {
		.wp-block-cover-text {
			text-align: center;
		}
	}
}

// Block Gallery
.wp-block-gallery {
	margin-bottom: 30px;

	&:not(.has-nested-images) {
		.blocks-gallery-grid {
			.blocks-gallery-item {
				margin: 0 15px 15px 0;

				&:last-child {
					margin-right: 0;
				}

				.blocks-gallery-item__caption {
					padding: 10px;
					font-size: 13px;
					color: var(--color-white);
				}
			}
		}

		.blocks-gallery-caption {
			margin-bottom: 30px;
			color: var(--color-body);
		}
	}
}

.wp-block-video {
	margin: 0 0 20px;

	figcaption {
		margin-top: 10px;
		margin-bottom: 10px;
		color: var(--color-body);
	}
}

// Blockquote

blockquote {
	border-left: 6px solid #fab8c4;
	background: radial-gradient(134.22% 147.34% at -14.53% -24.7%, #FFFFFF 0%, #FEEBED 100%);
	padding: 50px 40px;
	border-radius: 6px;
	margin: 0 0 30px 0;

	p {
		cite {
			margin-top: 30px;
			display: block;
		}
	}

	cite {
		font-size: var(--font-size-b1);
		font-weight: 500;
		color: var(--color-dark);

		em {
			font-style: normal;
		}
	}

	&.wp-block-quote {
		border-left: 6px solid #fab8c4;
		margin: 0 0 30px 0;
		padding: 50px 40px;

		cite {
			font-size: var(--font-size-b1);
			font-weight: 500;
			color: var(--color-dark);

			em {
				font-style: normal;
			}
		}

		&.has-text-align-right {
			border-right: 6px solid #fab8c4;
			padding: 50px 40px;
		}

		&.is-style-large:not(.is-style-plain) {
			padding: 50px 40px;
			border-left: 6px solid #fab8c4;
			margin: 0 0 30px 0;

			p {
				font-size: 36px;
			}

			cite {
				font-size: 18px;
				font-weight: 500;
			}

			br {
				display: none;
			}
		}
	}
}

.wp-block-pullquote {
	border-color: #fab8c4 !important;
	padding: 50px 40px;
	margin-bottom: 30px;

	blockquote {
		margin-bottom: 0;
		padding: 0;
		background: transparent;
		border: none;

		cite {
			font-size: var(--font-size-b1);
			font-weight: 500;
			color: var(--color-dark);

			em {
				font-style: normal;
			}
		}
	}

	&.has-background {
		background: radial-gradient(134.22% 147.34% at -14.53% -24.7%, #FFFFFF 0%, #FEEBED 100%);
		padding: 50px 40px;
		border-top: 4px solid #fab8c4;
		border-bottom: 4px solid #fab8c4;

		blockquote {
			max-width: 100%;
		}

		&.has-etrade-primary-background-color {
			background: var(--color-primary);
			border-color: var(--color-primary) !important;
		}

		&.has-etrade-secondary-background-color {
			background: var(--color-secondary);
			border-color: var(--color-secondary) !important;
		}

		&.has-etrade-tertiary-background-color {
			background: var(--color-tertiary);
			border-color: var(--color-tertiary) !important;
		}

		&.has-etrade-white-background-color {
			background: var(--color-white);
			border-color: var(--color-white) !important;
		}

		&.has-etrade-dark-background-color {
			background: var(--color-dark);
			border-color: var(--color-dark) !important;
		}
	}

	&.is-style-solid-color {
		blockquote {
			max-width: 100%;

			p {
				font-size: var(--font-size-b1);
				margin-bottom: 30px;
			}

			cite {
				color: var(--color-dark);
			}
		}
	}

	&.has-cyan-bluish-gray-background-color {
		background: radial-gradient(134.22% 147.34% at -14.53% -24.7%, #FFFFFF 0%, #FEEBED 100%);
	}
}

// Block Common
.wp-block-file {
	margin-bottom: 30px;

	a {
		transition: 0.3s;

		&:not(.wp-block-file__button) {
			font-size: var(--font-size-b1);
			font-weight: 500;
		}

		&.wp-block-file__button {
			background-color: var(--color-primary);
			color: var(--color-white);
			border: 2px solid var(--color-primary);
			margin-left: 15px;

			&:hover {
				background-color: transparent;
				color: var(--color-primary);
			}
		}
	}
}

// Block Widget
.gallery.gallery-size-thumbnail {
	margin-bottom: 30px;
}

.gallery-caption {
	font-size: 13px;
	margin-top: 10px;
	margin-bottom: 10px;
	color: var(--color-body);
}

.wp-block-archives-dropdown.wp-block-archives,
.wp-block-categories-dropdown.wp-block-categories {
	margin-bottom: 30px;

	select {
		background-position-x: 98%;
	}

	label {
		margin-bottom: 10px;
	}
}

.wp-block-search__button-outside.wp-block-search__text-button,
.wp-block-search__button-inside.wp-block-search__text-button,
.wp-block-search__no-button,
.wp-block-search__button-outside.wp-block-search__icon-button,
.wp-block-search__button-inside.wp-block-search__icon-button {
	margin-bottom: 30px;

	label {
		margin-bottom: 10px;
	}

	.wp-block-search__input {
		width: 100%;
		padding: 0 20px;
		background-color: transparent;
		border-color: #e3e6e9;
	}

	.wp-block-search__button {
		background-color: var(--color-primary);
		color: var(--color-white);
		border: 2px solid var(--color-primary);
		padding: 5px 35px;
		border-radius: 6px;
		flex: 1;

		&:hover {
			background-color: transparent;
			color: var(--color-primary);
		}
	}
	.has-icon {
		padding: 5px 17px;
		svg {
			height: 40px;
			width: 40px;
		}
	}
}

.wp-block-search__button-inside.wp-block-search__text-button,
.wp-block-search__button-inside.wp-block-search__icon-button {
	.wp-block-search__inside-wrapper  {
		border-color: #e3e6e9;
		border-radius: 6px;
		.wp-block-search__input {
			border: none;
		}
	}
}

.wp-block-calendar {
	margin-bottom: 30px;

	.wp-calendar-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;

		a {
			text-decoration: none;
		}
	}

	table {
		caption {
			color: var(--color-heading);
			padding: 0;
			margin-bottom: 20px;
		}

		th,
		td {
			padding: 7px 10px;
			border-color: var(--color-border-light);
		}

		th {
			background-color: var(--color-lighter);
			border-color: var(--color-border-light);
		}

		td {
			color: var(--color-body);
		}
	}
}

hr {
	border-color: var(--color-border-light);
}

// Block Embeds
.wp-block-embed figcaption {
	color: var(--color-body);
	margin-top: 15px;
	margin-bottom: 30px;
}

// Block Layout elements
.wp-block-group,
.wp-block-media-text,
.wp-block-columns {
	margin-bottom: 30px;
}

// Seperator
.wp-block-separator {
	color: var(--color-border-light);

	&:not(.is-style-wide) {
		&:not(.is-style-dots) {
			width: 100px;
			color: var(--color-border-light);
		}
	}

	&.is-style-dots {
		&:before {
			font-size: 20px;
			color: var(--color-border-light);
			letter-spacing: 40px;
		}
	}
}

// Page Link paginator
.axil-page-links,
.page-links {
	display: flex;
	align-items: center;

	.page-link-holder {
		font-weight: 500;
		color: var(--color-dark);
	}

	.post-page-numbers {
		line-height: 42px;
		min-width: 42px;
		text-align: center;
		color: var(--color-heading);
		transition: all 0.5s;
		display: block;
		padding: 0 15px;
		transition: all 0.5s;
		border: 1px solid var(--color-border-light);
		border-radius: var(--radius-small);
		margin-left: 10px;

		&.current {
			opacity: 0.5;
		}
	}

	a.post-page-numbers {
		&:hover {
			background: var(--color-primary);
			color: #ffffff;
			border-color: var(--color-primary);
		}
	}
}

.wp-block-query-pagination {
	.wp-block-query-pagination-previous, 
	.wp-block-query-pagination-next {
		margin: 0;
		border: 1px solid var(--color-border-light);
		padding: 0 15px;
		line-height: 42px;
		min-width: 42px;
		text-align: center;
		border-radius: var(--radius-small);
		transition: all 0.5s;
		color: var(--color-primary);
		&:hover {
			background-color: var(--color-primary);
			color: #ffffff;
			border-color: var(--color-primary);
		}
	}
	.wp-block-query-pagination-numbers {
		display: flex;
		align-items: center;
		margin: 0;
		a {
			&:hover {
				background-color: var(--color-primary);
				color: #ffffff;
				border-color: var(--color-primary);
			}
		}
		.page-numbers {
			line-height: 42px;
			min-width: 42px;
			text-align: center;
			color: var(--color-heading);
			transition: all 0.5s;
			display: block;
			padding: 0 15px;
			transition: all 0.5s;
			border: 1px solid var(--color-border-light);
			border-radius: var(--radius-small);
			margin: 5px;
			&.current {
				opacity: 0.5;
			}
		}
	}
}

.entry-content .page-links {
	margin: 0;
	padding-top: 30px;
}

// Block Formatting
table {
	margin: 0 0 30px 0;

	th,
	td {
		border: 1px solid var(--color-border-light);
		padding: 7px 10px;
	}
}

pre {
	border-radius: 6px;

	&.wp-block-code {
		border: 1px solid var(--color-light);
		font-family: "Courier 10 Pitch", Courier, monospace;
		padding: 20px;
		font-size: 12px;
		border-radius: 6px;
	}

	&.wp-block-preformatted {
		border-radius: 6px;
		margin-top: 30px;
	}
}

table {
	thead {
		th {
			color: var(--color-heading);
		}
	}
}

.wp-block-table {
	table {

		td,
		th {
			padding: 7px 10px;
			color: var(--color-body);
			border-color: var(--color-light);
		}
	}

	&.is-style-stripes {
		border-bottom: none;

		tbody {
			tr {
				&:nth-child(odd) {
					background-color: var(--color-lighter);
				}
			}
		}
	}
}

// Block HTML & Formatting
kbd {
	background-color: var(--color-heading);
}

// Markup Image Alignment
figure.wp-caption {
	margin-bottom: 30px;

	.wp-caption-text {
		margin-top: 10px;
		margin-bottom: 10px;
		font-size: 13px;
	}
}

.page-page-image-alignment {
	p {
		&:last-child {
			img.alignright {
				clear: both;
			}
		}
	}
}

// Post Format Gallery
.gallery {
	.gallery-item {
		margin-bottom: 20px;
	}
}

// New Block Post 

.wp-block-comment-template {
	.comment {
		.wp-block-column {
			padding: 0;
		}
	}
}

.wp-block-navigation {
	.wp-block-page-list {
		margin: 0 -15px;
		>.wp-block-pages-list__item {
			margin: 0 15px;
			>.wp-block-pages-list__item__link {
				color: var(--color-heading);
				font-weight: 700;
				font-size: 15px;
				font-family: var(--font-primary);
				line-height: 80px;
				height: 80px;
				display: block;
				position: relative;
				transition: var(--transition);
				&:hover {
					color: var(--color-secondary);
				}
			}
		}
		.has-child {
			.wp-block-navigation__submenu-container {
				background: #ffffff;
				border: none;
				padding: 15px 0;
				border-radius: 4px;
				box-shadow: var(--shadow-primary);
				.wp-block-pages-list__item {
					.wp-block-pages-list__item__link {
						font-size: 15px;
						color: var(--color-heading);
						&:hover {
							color: var(--color-secondary);
						}
					}
				}
			}
		}
	}
}

.wp-block-navigation__responsive-container-content {
	.wp-block-page-list {
		.wp-block-pages-list__item {
			margin: 5px 15px;
			.wp-block-pages-list__item__link {
				line-height: 1;
				height: auto;
			}
		}
	}
}

.wp-block-navigation .has-child:not(.open-on-click):hover>.wp-block-navigation__submenu-container {
	min-width: 250px;
}

.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .has-child .wp-block-navigation__submenu-container {
	box-shadow: none;
}

.has-modal-open .admin-bar .is-menu-open .wp-block-navigation__responsive-dialog {
	margin-top: 0;
}

.wp-block-table th,
table th {
	text-transform: capitalize;
}


// Dokan Dashboard Styles 
.dokan-dashboard .dokan-btn, 
.dokan-dashboard a.dokan-btn, 
.dokan-dashboard input[type=submit].dokan-btn,
.dokan-btn, 
a.dokan-btn, 
input[type=submit].dokan-btn,
.dokan-btn-theme.dokan-btn, 
a.dokan-btn.dokan-btn-theme, 
input[type=submit].dokan-btn.dokan-btn-theme {
	transition: 0.3s;
	width: auto;
	height: auto;
	padding: 10px 20px;
}

select.dokan-form-control, input.dokan-form-control {
	padding: 5px 15px;

}
.dokan-dashboard {
	.dokan-dash-sidebar {
		ul {
			&.dokan-dashboard-menu {
				li {
					transition: 0.3s;
					&.dokan-common-links {
						a {
							transition: 0.3s;
						}
					}
				}
			}
		}
	}
	div.chart-container {
		> div.chart-placeholder {
			> div.legend {
				table {
					td {
						border: none;
					}
				}
			}
		}
	}
	.dokan-product-listing {
		.dokan-product-listing-area {
			padding: 0;
			>.dokan-w12 {
				justify-content: space-between;
				form {
					width: auto;
				}
			}
			.product-listing-top {
				ul.dokan-listing-filter {
					li {
						padding: 0 10px;
					}
				}
			}
			form.dokan-product-search-form {
				button[name='product_listing_search'] {
					width: auto;
				}
			}
		}
	}
	.dokan-dashboard-content {
		fieldset {
			padding: 0;
			margin: 0;
		}
		.payment-field-bank {
			margin-top: 30px;
		}
		.payment-field-paypal {
			margin-top: 30px;
			.dokan-form-group {
				text-align: left;
			}
		}
		.select2-container--default {
			padding: 0;
			font-size: 15px;
			.select2-selection--single {
				margin-top: 0 !important;
				.select2-selection__rendered {
					line-height: 35px;
				}
				.select2-selection__placeholder {
					color: var(--color-body) !important;
				}
			}
		}
		article.dokan-settings-area {
			#payment-form {
				> div.dokan-form-group {
					> div.ajax_prev.dokan-w4 {
						margin-left: 25%;
					}
				}
			}
			.dokan-payment-settings-summary {
				div.payment-methods-listing-header {
					> div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down ul {
						padding-left: 0;
					}
				}
			}
		}
	}
	.dokan-orders-content {
		.dokan-orders-area {
			.dokan-order-filter-serach {
				padding: 0;
				.dokan-left {
					width: 100% !important;
					.dokan-form-group {
						flex-wrap: nowrap;
						.select2-container.select2-container--default {
							margin-bottom: 0;
						}
					}
				}
				.dokan-right {
					width: 100% !important;
					float: none;
				}
			}
		}
	}
	.product-edit-container #edit-slug-box {
		margin-top: 20px;
		#new-post-slug {
			height: 35px;
		}
		#edit-slug-buttons {
			button.save {
				background-color: var(--color-primary);
				padding: 2px 16px;
				color: var(--color-white);
				margin-top: 10px;
				&:hover {
					background-color: #1150c1;
					border-color: #1150c1;
				}
			}
			button.cancel {
				width: auto;
				background-color: transparent;
				font-size: 16px;
				font-weight: 500;
				&:hover {
					color: var(--color-primary);
				}
			}
		}
	}
	button.edit-slug {
		background-color: transparent !important;
		border: none !important;
		padding: 0 15px;
		font-size: 14px;
		&:hover {
			color: var(--color-primary);
		}
	}
	.select2-container.select2-container--default {
		padding: 0;
		margin: 0 0 10px 0;
	}
	.select2-container--open .select2-dropdown {
		left: 0 !important;
		top: 0;
	}
	.select2-container .select2-selection--single {
		.select2-selection__rendered {
			padding-right: 30px;
		}
	}
	.select2-container--open .select2-dropdown--below {
		margin-top: 32px;
	}
	.select2-container--open .select2-dropdown--above {
		margin-top: 32px;
	}
	#delivery-time-calendar {
		.fc-toolbar-chunk {
			display: flex;

		}
	}
	.dokan-reviews-content {
		.dokan-reviews-area {
			.dokan-comments-wrap {
				#dokan_comments-form {
					.dokan-form-group {
						display: flex;
						align-items: center;
					}
					select {
						width: auto;
						min-width: 150px;
						height: 36px;
						padding: 0 15px;
						margin-right: 10px;
						background-position-x: 90%;
					}
				}
			}
		}
	}
	.dokan-order-details-wrap {
		margin-top: 20px;
		input[type="submit"] {
			height: auto;
		}
		.general-details {
			#dokan-order-status-form {
				select.form-control {
					margin-bottom: 10px;
					height: 35px;
				}

			}
		}
	}
	.dokan-form-group {
		.checkbox {
			label {
				display: flex;
				align-items: center;
				cursor: pointer;
				input[type=checkbox], 
				input[type=radio] {
					margin-right: 5px;
				}
			}
		}
		button.insert-media.add_media {
			padding: 10px;
			background-color: transparent;
			border: none;
			&:hover {
				background-color: transparent;
				border: none;
			}
			&:focus,
			&:focus-visible {
				border: none;
				outline: none;
				box-shadow: none;
			}
		}
	}
	input[type=checkbox], 
	input[type=radio] {
		&:not(#declaration) {
			opacity: 1;
			position: initial;
			width: auto;
			height: auto;
		}
	}
	.minitoggle {
		width: 36px;
	}
	.store-open-close {
		.dokan-time-slots {
			margin-left: 25%;
		}
	}
	.dokan-progress {
		background: #d0d0d0;
	}
}

.dokan-table {
	> thead:first-child {
		> tr:first-child {
			> th {
				border-top: 1px solid #EDEDED;
			}
		}
	}
}

.ui-datepicker {
	.ui-datepicker-calendar {
		background-color: var(--color-white);
	}
	&.ui-widget.ui-widget-content {
		height: auto;
		z-index: 10 !important;
	}
}

.product-edit-container #dokan-product-images ul.product_images li.image a.action-delete, 
.product-edit-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder a.action-delete, 
.product-edit-container #dokan-product-images ul.product_images li.add-image a.action-delete {
	padding-top: 0;
	line-height: 1.15;
}

.product-edit-container .dokan-feat-image-upload a.close {
	padding: 15% 0;
}

.dokan-settings-content {
	.dokan-settings-area {
		.dokan-banner {
			display: flex;
			align-items: center;
			justify-content: center;
			.dokan-remove-banner-image {
				padding-top: 50px;
			}
		}
	}
}

.dokan-add-new-product-popup {
	.dokan-product-field-content {
		.dokan-price-container {
			.content-half-part {
				padding: 0;
				width: 100%;
			}
		}
		.sale-price {
			margin-top: 15px;
			label {
				display: flex;
				justify-content: space-between;
				a {
					font-weight: 500;
				}
			}
		}
	}
	.product-form-container {
		.dokan-feat-image-upload {
			a {
				&.close {
					padding: 8% 0;
				}
			}
		}
	}
	#dokan-product-images ul.product_images li.image a.action-delete, 
	#dokan-product-images ul.product_images li.dokan-sortable-placeholder a.action-delete, 
	#dokan-product-images ul.product_images li.add-image a.action-delete {
		padding: 0;
		font-size: 28px;
	}
}


.media-modal.wp-core-ui {
	.load-more-wrapper {
		button.load-more {
			&:after {
				background-color: var(--color-primary);
				color: var(--color-white);
			}
		}
	}
}	

.select2-container--open .select2-dropdown--below {
	margin-top: 0;
}
.select2-container--open .select2-dropdown--above {
	margin-top: 0;
}

// Dokan Strore Admin Style 
.dokan-single-store {
	.dokan-store-tabs {
		margin-top: 20px;
		ul.dokan-list-inline {
			li {
				&:last-child {
					margin-right: 0;
				}
			}
		}
	}
	.profile-frame {
		.profile-info-box {
			.profile-info-summery-wrapper {
				.profile-info-summery {
					.profile-info {
						li {
							@media (min-width: 1200px) {
								color: var(--color-white);
							}
						}
					}
				}
			}
		}
	}
}

.dokan-store-products-filter-area {
	.dokan-store-products-ordeby {
		display: flex;
		@media (max-width: 767px) {
			display: block;
		}
		.dokan-store-products-filter-search {
			margin-right: 10px;
			@media (max-width: 575px) {
				width: 100% !important;
				margin-bottom: 5px;
			}
		}
		.dokan-btn-theme {
			background-color: var(--color-primary);
			border-color: var(--color-primary);
			&:hover {
				background-color: #1150c1;
				border-color: #1150c1;
			}
		}
		select {
			width: auto;
			margin-left: 10px;
			@media (max-width: 767px) {
				float: initial;
				margin-left: 0;
				width: 100%;
				margin-top: 20px !important;
			}
		}
	}
}

.dokan-store-sidebar {
	.dokan-store-widget {
		.parent-cat-wrap {
			>a {
				display: flex;
				align-items: center;
				justify-content: space-between;

			}
		}
	}
}

.axil-cart-table {
	.product-title {
		dl.variation {
			margin-top: 5px;
			dt {
				margin-bottom: 0;
			}
			dd {
				margin: 0;
			}
		}
	}
}

// Media Modal 

.media-modal.wp-core-ui {
	button.button {
		background-color: var(--color-primary);
		color: var(--color-white);
		padding: 0 15px;
	}

	button.button:disabled, 
	button.button:disabled[disabled] {
		padding: 0 15px;
	}
	.media-frame-tab-panel {
		.media-router {
			.media-menu-item {
				width: auto;
			}
		}
	}
	.media-modal-content {
		.media-frame {
			select.attachment-filters {
				height: auto;
			}
		}
	}
	.media-toolbar {
		button.button {
			margin-top: 7px;
		}
	}
}

/*** Ask a question custom popup ***/

.popup-aska-question {
    padding: 0;
    max-width: 480px;
    border-radius: 5px 5px 0 0;
}

.popup-aska-question {
	border-radius: 5px;
	margin: 0 auto;
	background: #ffffff;
	.model-product {
		background: #f9f9f9;
		padding: 20px 30px 20px;
		margin: 0 0 30px
	}
}

.popup-aska-question .model-product .product-info .name {
    color: #323232;
    font-size: 18px;
    line-height: 30px;
    font-weight: 400;
    height: auto;
    margin: 0 0 5px;
    text-align: left
}

.popup-aska-question .model-product .product-info .price del {
    color: #ccc;
    margin-left: 4px;
    letter-spacing: .3px;
    font-weight: 400
}

.popup-aska-question .model-product .image {
    width: 90px;
    padding-right: 20px
}

.popup-aska-question .model-wrap {
    padding: 0 30px 20px;	
}

.popup-aska-question .model-wrap .wpcf7 form .wpcf7-response-output {
    margin-top: 0
}

.popup-aska-question .model-wrap .model-headling-popup {
    text-align: center;
    font-weight: normal;
    margin: 25px 0 28px;	
}

.popup-aska-question .model-wrap .product_subject {
    display: none
}

.popup-aska-question .model-wrap input[type="email"]::placeholder,.popup-aska-question .model-wrap input[type="number"]::placeholder,.popup-aska-question .model-wrap input[type="password"]::placeholder,.popup-aska-question .model-wrap input[type="search"]::placeholder,.popup-aska-question .model-wrap input[type="text"]::placeholder,.popup-aska-question .model-wrap textarea::placeholder,.popup-aska-question .model-wrap input[type="tel"]::placeholder {
    color: #232323;
    opacity: .6;
    filter: alpha(opacity=60)
}

.popup-aska-question .model-wrap input[type="submit"] {
    float: none;
    margin: 0 auto
}

.popup-aska-question .model-wrap .form-group-submit {
    text-align: center;
    display: flex;
    flex-direction: column;
    margin-bottom: 0
}

.tbay-button-popup-wrap {
	list-style: none;
	margin: 0;
	padding: 0;
	.model-aska-question {
		.popup-button-open {
			display: flex;
			align-items: center;
			column-gap: 10px;
			color: var(--color-dark);
			transition: all 0.3s ease-in-out;
			i {
				height: 30px;
				width: 30px;
				background-color: var(--color-primary);
				border-radius: 50%;
				color: #ffffff;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease-in-out;
			}
			&:hover {
				color: var(--color-primary);
				i {
					background-color: var(--color-secondary);
				}
			}
		}
	}
}
.mfp-content {
	.model-popup-content.popup-aska-question {
		position: relative;
		.mfp-close {
			position: absolute;
			top: 0;
			right: 0;
			color: #cbcbcb;
			&:hover {
				background-color: transparent;
				color: #ffffff;
			}
		}
		.model-headling-popup {
			text-align: center;
			background-color: #2b3034;
			color: #ffffff;
			padding: 15px 30px;
			margin: 0;
			border-radius: 5px 5px 0 0;
		}
		.model-product.media {
			display: flex;
			.price {
				font-weight: 600;
				ins {
					background-color: transparent;
					color: var(--color-dark);
				}
			}
		}
		.form-group textarea {
			height: 50px;
		}
	}
}
/** add to cart and wishlist button **/
.single-product-content .product-action-wrapper {
	.product-action li {
		display: flex;
		column-gap: 10px;
		align-items: center;
		a {
			width: 60px;
			height: 60px;
			line-height: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 10px;
			border: 2px solid var(--color-light);
			transition: all 0.3s ease-in-out;
			&:hover {
				color: #ffffff;
				border: 2px solid var(--color-primary);
			}
		}
	}
}

.single-product-content .product-action-wrapper .button.single_add_to_cart_button {
	margin-right: 0;
}

.single-product-content .product-social {
	margin-top: 15px;
	display: flex;
	align-items: center;
	column-gap: 15px;
	.product-social-title {
		font-weight: 600;
	}
	.product-social-items {
		display: flex;
		column-gap: 12px;
		flex-wrap: wrap;
		align-items: center;
		list-style: none;
		margin: 0;
		padding: 0;
		li {
			a {
				color: #ffffff;
				font-size: 16px;
				height: 40px;
				width: 40px;
				border-radius: 50%;
				background-color: var(--color-primary);
				display: flex;
				align-items: center;
				justify-content: center;
				&:hover {
					background-color: var(--color-secondary);
				}
			}
		}
	}
}

/** Video button **/


.single-product-3 {
	.nm-featured-video-link {
		top: 30px;
		left: 30px;
		transform: translateX(0) translateY(0);	
	}
}
/** Discount box **/
.single-product-3 .single-product-content {
	.price-wrp {
		display: flex;
		align-items: center;
		.product-badget {
			margin-left: 10px;
			margin-bottom: 20px;
			background-color: var(--color-chart03);
			height: 48px;
			line-height: 40px;
			padding: 5px 20px;
			font-size: 16px;
			font-weight: 500;
			color: var(--color-white);
			border-radius: 24px;
			font-family: var(--font-secondary);
		}
	}
}

.single-product-thumb {
	.label-block {
		display: flex;
		align-items: center;
		flex-direction: row-reverse;
		justify-content: space-between;
		width: 100%;
		top: 20px;
		left: 0;
		right: 0;
		padding: 10px 50px;
		.nm-featured-video-link {
			.axil-featured-video-label {
				display: flex;
				align-items: center;
				column-gap: 10px;
				color: #ffffff;
				i {
					height: 50px;
					width: 50px;
					background-color: var(--color-primary);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.3s ease-in-out;
				}
			}
			&:hover {
				.axil-featured-video-label {
					i {
						background-color: var(--color-secondary);
					}
				}
			}
		}
	}
}

.single-product-3,
.single-product-5,
.single-product-7,
.single-product-8 {
	.single-product-thumb {
		.nm-featured-video-link {	
			position: absolute;
			z-index: 1;		
			.axil-featured-video-label {
				display: flex;
				align-items: center;
				column-gap: 10px;
				color: #ffffff;
				i {
					height: 50px;
					width: 50px;
					background-color: var(--color-primary);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.3s ease-in-out;
				}
			}
			&:hover {
				.axil-featured-video-label {
					i {
						background-color: var(--color-secondary);
					}
				}
			}
		}
	}
}

.single-product-3 {
	.single-product-thumb {
		.nm-featured-video-link {
			top: 30px;
			left: 30px;
			transform: translateX(0) translateY(0);	
		}
	}
}
.single-product-5,
.single-product-7 {
	.single-product-thumb {
		.nm-featured-video-link {			
			top: 50%;
			left: 50%;
			transform: translateX(-50%) translateY(-50%);	
		}
	}
}

.single-product-8 {
	.axil-product-lable {
		position: relative;
		.nm-featured-video-link {			
			top: 30px;
			left: 55px;
			transform: translateX(0) translateY(0);		
		}
	}
}