.fw-option-type-addable-option .fw-option-type-addable-option-option:not(.ui-sortable-helper) td {
	max-width: 383px; /* .fw-backend-option-fixed-width - 45px */
}

.fw-option-type-addable-option .fw-option-type-addable-option-option td,
#edittag .form-table .fw-option-type-addable-option .fw-option-type-addable-option-option td,
#edittag .form-table .fw-option-type-addable-option tr.sortable-placeholder td {
	padding: 0 0 15px 0;
	vertical-align: top;
}

.fw-option-type-addable-option .fw-option-type-addable-option-option td.td-option,
#edittag .form-table .fw-option-type-addable-option .fw-option-type-addable-option-option td.td-option {
	padding-right: 10px;
}
body.rtl .fw-option-type-addable-option .fw-option-type-addable-option-option td.td-option,
body.rtl #edittag .form-table .fw-option-type-addable-option .fw-option-type-addable-option-option td.td-option {
	padding-right: 0;
	padding-left: 10px;
}

.fw-option-type-addable-option .fw-option-type-addable-option-option td.td-remove,
#edittag .form-table .fw-option-type-addable-option .fw-option-type-addable-option-option td.td-remove {
	width: 10px;
	padding-top: 4px;
}

.fw-option-type-addable-option .fw-option-type-addable-option-option td.td-remove .dashicons {
	font-size: 16px;
	line-height: 1.3;
	color: #AAAAAA;
}

.fw-option-type-addable-option .fw-option-type-addable-option-option td.td-remove .dashicons:hover {
	color: #CC0000;
}

.fw-option-type-addable-option .fw-option-type-addable-option-option td.td-move,
#edittag .form-table .fw-option-type-addable-option .fw-option-type-addable-option-option td.td-move {
	cursor: move;
	width: 15px;
	padding-top: 4px;
}

.fw-option-type-addable-option:not(.is-sortable) .fw-option-type-addable-option-option td.td-move {
	display: none;
}

.fw-option-type-addable-option tr.fw-option-type-addable-option-option.ui-sortable-helper {
	display: block;
}

.fw-option-type-addable-option tr.fw-option-type-addable-option-option.ui-sortable-helper:before,
.fw-option-type-addable-option tr.fw-option-type-addable-option-option.ui-sortable-helper:after {
	display: table;
	content: " ";
}

.fw-option-type-addable-option tr.fw-option-type-addable-option-option.ui-sortable-helper > td {
	display: block;
	float: left;

	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
body.rtl .fw-option-type-addable-option tr.fw-option-type-addable-option-option.ui-sortable-helper > td {
	float: right;
}

.fw-option-type-addable-option tr.fw-option-type-addable-option-option.ui-sortable-helper td.td-option {
	width: calc(100% - 35px);
}


.fw-backend-option-input-type-addable-option .fw-option-help-in-input {
	top: 4px !important;
}