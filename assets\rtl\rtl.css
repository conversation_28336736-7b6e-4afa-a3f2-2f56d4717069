/*------------------------------
    R<PERSON> Styles  
-------------------------------*/
html {
  direction: rtl;
}

a.axil-btn.right-icon i, button.axil-btn.right-icon i {
  margin-left: 0;
  margin-right: 5px;
}

a.axil-btn i, button.axil-btn i {
  margin-right: 0;
  margin-left: 10px;
}

input[type=checkbox] ~ label:before,
input[type=radio] ~ label:before {
  left: auto;
  right: 0;
}
input[type=checkbox] ~ label:after,
input[type=radio] ~ label:after {
  left: auto;
  right: 3px;
}

select, .select2 {
  background: url(../images/icons/arrow-icon.png) 5% center no-repeat #00000000;
}

.section-title-wrapper {
  padding-right: 0;
  padding-left: 100px;
}
.section-title-wrapper.section-title-border {
  padding-left: 0;
}

.title-highlighter i {
  margin-right: 0;
  margin-left: 10px;
}

@media only screen and (max-width: 991px) {
  .flash-sale-section .section-title-wrapper {
    padding-right: 0;
  }
}

.section-title-border.slider-section-title .title {
  padding-right: 0;
  padding-left: 100px;
}
.section-title-border .title {
  padding-right: 0;
  padding-left: 20px;
}

.is-cat-slider .section-title-border, .title-border-style .section-title-border {
  padding-right: 0;
  padding-left: 115px;
}

.header-top-dropdown .dropdown {
  margin-right: 0;
  margin-left: 20px;
}
.header-top-dropdown .dropdown:last-child {
  margin-left: 0;
}
.header-top-dropdown .dropdown .dropdown-toggle:after {
  margin-left: 0;
  margin-right: 5px;
}

.header-top-campaign .campaign-countdown {
  padding-right: 0;
  padding-left: 30px;
}

.mainmenu > .menu-item-has-children .axil-submenu {
  left: auto;
  right: 0;
}

.axil-mainmenu.aside-category-menu .header-main-nav {
  margin-left: 0;
  margin-right: 40px;
}
.axil-mainmenu.aside-category-menu .mainmenu > li:last-child {
  margin-left: 0;
  margin-right: 24px;
}
.axil-mainmenu.aside-category-menu .header-department .header-department-text .icon {
  margin-right: 0;
  margin-left: 20px;
}
.axil-mainmenu.aside-category-menu .header-department .department-nav-menu .nav-link.has-megamenu:after {
  right: auto;
  left: 0;
}
.axil-mainmenu.aside-category-menu .header-department .department-nav-menu .nav-link.has-megamenu:hover:after {
  transform: translateY(-50%) rotate(90deg);
}
.axil-mainmenu.aside-category-menu .header-department .department-nav-menu .nav-link .menu-icon {
  margin-right: 0;
  margin-left: 14px;
}
@media only screen and (max-width: 1199px) {
  .axil-mainmenu.aside-category-menu .header-department .department-nav-menu {
    left: auto;
    right: -260px;
  }
}
.axil-mainmenu.aside-category-menu .header-department .department-nav-menu.open {
  left: auto;
  right: 0;
}
.axil-mainmenu.aside-category-menu .header-department .department-megamenu {
  left: auto;
  right: 100%;
}
.axil-mainmenu.aside-category-menu .header-department .department-megamenu .department-submenu-wrap {
  border-right: none;
  border-left: 2px solid #f6f7fb;
}
@media only screen and (max-width: 1199px) {
  .axil-mainmenu.aside-category-menu .header-department .department-megamenu .department-submenu-wrap {
    border-left: none;
  }
}

.cart-dropdown {
  right: auto;
  left: -600px;
}
@media only screen and (max-width: 767px) {
  .cart-dropdown {
    left: -100%;
  }
}
.cart-dropdown .cart-item .item-img {
  margin-right: 0;
  margin-left: 30px;
}
@media only screen and (max-width: 479px) {
  .cart-dropdown .cart-item .item-img {
    margin-left: 15px;
  }
}
.cart-dropdown .cart-item .item-content {
  padding-left: 110px;
  padding-right: 0;
}
@media only screen and (max-width: 479px) {
  .cart-dropdown .cart-item .item-content {
    padding-left: 0;
  }
}
.cart-dropdown .cart-item .item-quantity {
  right: auto;
  left: 0;
}
.cart-dropdown.open {
  right: auto;
  left: 0;
}

.axil-mainmenu.mainmenu-bottom .header-department .department-nav-menu .department-megamenu-wrap .elementor-widget-container ul {
  padding-right: 0;
}

.axil-header .mainmenu .menu-item-has-children .axil-submenu .menu-item-has-children .axil-submenu {
  left: auto;
  right: 100%;
}

@media only screen and (max-width: 575px) {
  .header-style-1 .header-brand {
    margin-right: 0;
    margin-left: 10px;
  }
}

@media only screen and (max-width: 991px) {
  .axil-header.header-style-2 .axil-header-top .axil-search {
    margin-left: 20px;
    margin-right: 0;
  }
}
.axil-header.header-style-2 .axil-header-top .axil-search input {
  padding-right: 20px;
}
.axil-header.header-style-2 .product-search {
  margin-right: 0;
  margin-left: 20px;
}
@media (max-width: 767px) {
  .axil-header.header-style-2 .product-search {
    margin-left: 0;
  }
}
.axil-header.header-style-2 .product-search .search_box_wrapper .search_category {
  background-position-x: 10%;
}

.axil-header.header-style-7 .mainmenu > li > a .menu-icon {
  margin-right: 0;
  margin-left: 10px;
}
.axil-header.header-style-7 .mainmenu > li > a:after {
  right: auto;
  left: 0;
}
.axil-header.header-style-7 .mainmenu > li.dropdown .dropdown-toggle:after {
  margin-left: 0;
  margin-right: 8px;
}
.axil-header.header-style-7 .mainmenu > li.is-active > a, .axil-header.header-style-7 .mainmenu > li.active-menu > a {
  padding: 8px 8px 8px 28px !important;
}
.axil-header.header-style-7 .mainmenu > li.is-active > a:after, .axil-header.header-style-7 .mainmenu > li.active-menu > a:after {
  right: auto;
  left: 10px;
}
.axil-header.header-style-7 .mainmenu > li.menu-item-has-children > a {
  padding-right: 8px;
  padding-left: 18px;
}
.axil-header.header-style-7 .mainmenu > li.menu-item-has-children .axil-submenu li a .menu-icon {
  margin-right: 0;
  margin-left: 8px;
}
.axil-header.header-style-7 .header-action .axil-search input {
  padding-right: 15px;
  padding-left: 35px;
}
.axil-header.header-style-7 .header-action .axil-search .icon {
  left: 16px;
  right: auto;
}

.header-action .my-account .my-account-dropdown {
  right: auto;
  left: 0;
}
.header-action .my-account .my-account-dropdown .reg-footer .btn-link {
  margin-left: 0;
  margin-right: 7px;
}

@media only screen and (max-width: 991px) {
  .header-main-nav .mainmenu-nav .mainmenu > li.menu-item-has-children a::after {
    right: auto;
    left: -18px;
  }
}
.mobile-close-btn {
  right: auto;
  left: 15px;
}

@media only screen and (max-width: 991px) {
  .header-search-modal {
    right: auto;
  }
}
@media only screen and (max-width: 575px) {
  .header-search-modal .psearch-results .axil-product-list {
    text-align: right;
  }
}
@media only screen and (max-width: 575px) {
  .header-search-modal .psearch-results .axil-product-list .thumbnail {
    margin-right: 0;
    margin-left: 15px;
  }
}

.axil-footer-widget .inner ul {
  padding: 0;
}
.axil-footer-widget .support-list-item li {
  padding-left: 0;
  padding-right: 26px;
}
.axil-footer-widget .support-list-item li i {
  left: auto;
  right: 0;
  padding-right: 0;
  padding-left: 5px;
}
.axil-footer-widget.footer-widget-newsletter {
  padding-right: 0;
  padding-left: 50px;
}
.axil-footer-widget.footer-widget-newsletter .widget-title {
  text-align: right;
}
.axil-footer-widget.footer-widget-newsletter .input-group .form-control {
  text-align: right;
}

.footer-style-2 .axil-footer-widget .inner .download-btn-group .qr-code {
  margin-right: 0;
  margin-left: 20px;
}

.copyright-default .quick-link li:after {
  right: auto;
  left: -3px;
}

.copyright-default .copyright-left ul + ul {
  margin-left: 0;
  margin-right: 15px;
}

.footer-style-3 .axil-footer-widget .widget-title {
  border-right: none;
  border-left: 1px solid rgba(119, 119, 119, 0.4);
  padding-right: 0;
  margin-right: 0;
  padding-left: 22px;
  margin-left: 22px;
}
.footer-style-3 .axil-footer-widget .download-btn-group .qr-code {
  margin-right: 0;
  margin-left: 20px;
}
.footer-style-3 .axil-footer-widget.footer-widget-newsletter {
  padding-right: 0;
  padding-left: 50px;
}
.footer-style-3 .axil-footer-widget.footer-widget-newsletter .widget-title {
  text-align: right;
}
.footer-style-3 .payment-method .title {
  padding-right: 0;
  padding-left: 24px;
}
.footer-style-3 .payment-method ul {
  border-left: none;
  border-right: 1px solid rgba(119, 119, 119, 0.4);
  padding-left: 0;
  padding-right: 18px;
}
.footer-style-3 .footer-social-link ul {
  border-right-color: rgba(51, 120, 240, 0.4);
}

.main-slider-content .subtitle i {
  margin-right: 0;
  margin-left: 10px;
}
.main-slider-content .item-rating {
  margin-left: 0;
  margin-right: 30px;
}
.main-slider-content .item-rating .content {
  margin-left: 0;
  margin-right: 15px;
}

.main-slider-large-thumb .axil-slick-dots .slick-dots {
  text-align: right;
}

.main-slider-style-1 .main-slider-content .slick-list .slick-slide {
  direction: rtl;
}
.main-slider-style-1 .shape-group li.shape-1 {
  right: auto;
  left: 33%;
}
.main-slider-style-1 .shape-group li.shape-2 {
  right: auto;
  left: 2%;
}

.main-slider-style-2 .slider-offset-left {
  margin-left: 0;
  margin-right: 290px;
}
@media only screen and (max-width: 1199px) {
  .main-slider-style-2 .slider-offset-left {
    margin-right: 0;
  }
}
.main-slider-style-2 .slick-list .slick-slide {
  direction: rtl;
}
.main-slider-style-2 .main-slider-thumb {
  margin-left: 0;
  margin-right: 30px;
  text-align: left;
}
.main-slider-style-2 .main-slider-thumb:after {
  left: auto;
  right: -100px;
}
.main-slider-style-2 .main-slider-content .axil-btn i {
  margin: 0 16px 0 0;
}
.main-slider-style-2 .main-slider-content .axil-btn:hover i {
  margin: 0 10px 0 0;
}

.main-slider-style-5 .slick-list .slick-slide {
  direction: rtl;
}

.main-slider-style-7 {
  background-position-x: -1;
}

.blog-grid .content .axil-btn i {
  padding-left: 0;
  padding-right: 6px;
}

.content-blog.post-list-view .thumbnail {
  margin-right: 0;
  margin-left: 20px;
}

.axil-post-meta .post-author-avatar {
  margin-right: 0;
  margin-left: 20px;
}

.post-meta-list li::after {
  right: auto;
  left: 0;
}

.form-group label {
  left: auto;
  right: 20px;
}

.comment-list .comment .single-comment .comment-img {
  margin-right: 0;
  margin-left: 20px;
}
.comment-list .comment .reply-edit a.comment-reply-link {
  margin-left: 0;
  padding-left: 0;
  margin-right: 8px;
  padding-right: 8px;
}
.comment-list .comment .reply-edit a.comment-reply-link:before {
  left: auto;
  right: -2px;
}

.pro-desc-commnet-area .comment-list .comment .commenter .commenter-rating {
  margin-left: 0;
  margin-right: 15px;
}

.pro-des-commend-respond .form-group textarea {
  padding-right: 30px;
}

.reating-inner.ml--20 {
  margin-left: 0 !important;
  margin-right: 20px !important;
}

.arrow-top-slide .slide-arrow.prev-arrow {
  right: auto;
  left: 58px;
}
.arrow-top-slide .slide-arrow.next-arrow {
  right: auto;
  left: 0;
}

.angle-top-slide .slide-arrow.prev-arrow {
  right: auto;
  left: 55px;
}
.angle-top-slide .slide-arrow.next-arrow {
  right: auto;
  left: 0;
}

.testimonial-style-one .media .thumbnail {
  margin-right: 0;
  margin-left: 20px;
}

.testimonial-custom-nav .slide-custom-nav button.prev-custom-nav {
  border-right: none;
  border-left: 1px solid #d6d6d6;
}
.testimonial-custom-nav .slide-custom-nav button.prev-custom-nav i {
  margin-right: 0;
  margin-left: 8px;
}
.testimonial-custom-nav .slide-custom-nav button i {
  margin-left: 0;
  margin-right: 8px;
}

.testimonial-video-box {
  margin-right: 0;
  margin-left: 22px;
}
.testimonial-video-box .thumbnail img {
  border-radius: 0 8px 8px 0;
}

.newsletter-content {
  text-align: right;
}

.newsletter-form {
  justify-content: flex-start;
  flex-direction: row-reverse;
}
.newsletter-form > p {
  display: flex;
  flex-direction: row-reverse;
}
.newsletter-form .newsletter-inner {
  margin-right: 0;
  margin-left: 15px;
}
.newsletter-form .newsletter-inner:before {
  left: auto;
  right: 30px;
}
.newsletter-form .newsletter-inner .wpcf7-form-control {
  direction: rtl;
  padding-left: 30px;
  padding-right: 66px;
}

.axil-product-list .thumbnail {
  margin-right: 0;
  margin-left: 30px;
}
@media only screen and (max-width: 575px) {
  .axil-product-list .thumbnail {
    margin-left: 0;
  }
}
.axil-product-list .product-content {
  padding-right: 0;
  padding-left: 60px;
}
@media only screen and (max-width: 575px) {
  .axil-product-list .product-content {
    padding-left: 0;
  }
}
.axil-product-list .product-content .product-cart {
  right: auto;
  left: 0;
}
.axil-product-list .product-content .product-rating .star-rating {
  width: 84px;
}
.axil-product-list .product-content .product-rating .rating-number {
  margin-left: 0;
  margin-right: 10px;
}

.verified-icon {
  padding-left: 0;
  padding-right: 2px;
}

.axil-product.product-style-seven .product-content .cart-btn {
  right: auto;
  left: 20px;
}
.axil-product.product-style-eight .cart-action li.select-option a i {
  margin-right: 0;
  margin-left: 10px;
}
.axil-product .product-content .product-rating .rating-number {
  margin-right: 0;
  margin-right: 5px;
}
.axil-product .product-content .cart-action li.add-to-cart a.loading:after {
  right: auto;
  left: 44%;
}
.axil-product .product-hover-action .cart-action li.add-to-cart a.loading:after {
  right: auto;
  left: 44%;
}

.yith-wcqv-button .blockUI.blockOverlay:before {
  left: auto;
  right: 14px;
}

.single-product-content .inner .price-amount ins {
  margin-left: 0;
  margin-right: 10px;
}
.single-product-content .inner .product-rating .star-rating {
  margin-right: 0;
  margin-left: 8px;
}
.single-product-content .inner .product-meta li i {
  padding-right: 0;
  padding-left: 15px;
}
.single-product-content .inner .nft-category label, .single-product-content .inner .nft-verified-option label {
  margin-right: 0;
  margin-left: 10px;
}
.single-product-content .axiltheme-star-rating {
  width: 88px;
  margin: 0 0 0 6px;
}
.single-product-content .axiltheme-star-rating .star-rating {
  width: 88px;
}
.single-product-content .pro-qty {
  margin-right: 0;
  margin-left: 20px;
}

.single-product-5 .single-product-content .price-wrp .product-badget {
  margin-left: 0;
  margin-right: 10px;
}

.product-stock-meta li i {
  margin-right: 0;
  margin-left: 5px;
}

.product-action-wrapper .product-action.action-style-two {
  padding-right: 0;
  padding-left: 220px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-action-wrapper .product-action.action-style-two {
    padding-left: 0;
  }
}
@media only screen and (max-width: 575px) {
  .product-action-wrapper .product-action.action-style-two {
    padding-left: 0;
  }
}

.product_list_widget li .thumbnail {
  margin-right: 0;
  margin-left: 20px;
}

.pro-desc-commnet-area {
  padding-right: 0;
  padding-left: 110px;
}

.woocommerce-tabs.nft-info-tabs ul.tabs {
  padding-left: 0;
}
@media only screen and (max-width: 1199px) {
  .woocommerce-tabs.nft-info-tabs ul.tabs {
    padding-left: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .woocommerce-tabs.nft-info-tabs ul.tabs {
    padding-left: 0;
  }
}

.woocommerce-tabs.wc-tab-style-two .tabs-wrap ul.tabs li {
  margin-right: 0;
  margin-left: 20px;
}

.product-collection-three {
  padding: 30px 20px 0 35px;
}

.product-collection.product-collection-two .collection-content {
  right: 50px;
  left: 0;
}
.product-collection .collection-content {
  left: 0;
  right: 30px;
}
.product-collection .collection-thumbnail img {
  transform: scaleX(-1);
}
.product-collection .label-block.label-right {
  right: auto;
  left: 20px;
}

.single-product-modern .single-product-content .inner .quantity-variant-wrapper .pro-qty {
  display: flex;
}

.single-product-features .single-features .icon {
  margin-right: 0;
  margin-left: 16px;
}

#yith-quick-view-content .single-product-content {
  text-align: right;
}

ul {
  padding-left: 0;
  padding-right: 20px;
}

.axil-product-table th:last-child, .axil-product-table td:last-child {
  text-align: left;
}

.axil-product-cart-wrap .product-cupon .product-cupon-btn {
  margin-left: 0 !important;
  margin-right: 20px !important;
}

.axil-checkout-notice .toggle-bar a i {
  margin-left: 0;
  margin-right: 5px;
}
.axil-checkout-notice .toggle-bar i {
  margin-right: 0;
  margin-left: 8px;
}
.axil-checkout-notice .axil-checkout-coupon input {
  margin-right: 0;
  margin-left: 10px;
}

.axil-order-summery.order-checkout-summery .summery-table th:last-child,
.axil-order-summery.order-checkout-summery .summery-table td:last-child {
  text-align: left;
}

.axil-checkout-billing .form-group.input-group label {
  left: auto;
  right: 0;
}

.axil-dashboard-aside .nav-link {
  padding: 9px 55px 10px 9px;
}
.axil-dashboard-aside .nav-link i {
  left: auto;
  right: 24px;
}

.axil-dashboard-order .table tbody tr td:last-child,
.axil-dashboard-order .table tbody tr th:last-child {
  text-align: left;
}

.signin-header .singin-header-btn .sign-up-btn {
  margin-left: 0;
  margin-right: 40px;
}

.axil-shop-top .category-select .woocommerce-ordering select {
  padding-right: 20px;
  padding-left: 35px;
  background-position-x: 9%;
}
.axil-shop-top .axil-shop-widget-content .price_slider_wrapper .price_slider_amount {
  margin-left: 0;
  margin-right: 20px;
  text-align: right;
}
.axil-shop-top .axil-shop-widget-content .price_slider_wrapper .price_slider_amount .price_label {
  left: auto;
  right: 0;
}

.category-select .orderby,
.category-select .select2-container.select2-container--default {
  background-position-x: 9%;
}

.woocommerce .woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item a {
  padding-left: 0;
  padding-right: 28px;
}

.woocommerce .widget_layered_nav ul li a .wooc-pa-color {
  left: auto;
  right: -10px;
}

.axil-shop-sidebar .title:before {
  right: auto;
  left: 0;
}
.axil-shop-sidebar .product-categories ul li a {
  padding-left: 0;
  padding-right: 28px;
}
.axil-shop-sidebar .product-categories ul li a:before {
  left: auto;
  right: 0;
}
.axil-shop-sidebar .product-price-range li {
  margin: 0 0 0 15px;
}

.input-range {
  padding-right: 0;
  padding-left: 3px;
}

.categrie-product-2 img {
  margin-right: 0;
  margin-left: 10px;
}

.service-box.service-style-2 {
  text-align: right;
}
.service-box.service-style-2 .icon {
  margin-right: 0;
  margin-left: 20px;
}

.countdown .countdown-section:last-child {
  margin-right: 15px;
}
.countdown .countdown-section:first-child {
  margin-right: 0;
}

.sale-countdown .countdown-section:after {
  right: auto;
  left: -14px;
}
.sale-countdown .countdown-section:last-child {
  margin-right: 25px;
}

.axil-breadcrumb-area .inner .bradcrumb-thumb {
  text-align: left;
}
.axil-breadcrumb-area .inner .bradcrumb-thumb:after {
  right: auto;
  left: 60px;
}

.pv-banner-area {
  padding-left: 0;
  padding-right: calc((100% - 1290px) / 2);
}

.pv-main-wrapper .section-title-wrapper {
  padding-left: 0;
}

.pv-support .inner .icon {
  margin-right: 0;
  margin-left: 20px;
}
.pv-support .inner .content .axil-btn i {
  margin-right: 10px;
  margin-left: 0;
}

.axil-new-arrivals-product-area.fullwidth-container {
  margin-right: calc((100% - 1320px) / 2);
  margin-left: 0;
}
@media only screen and (max-width: 1349px) {
  .axil-new-arrivals-product-area.fullwidth-container {
    margin-right: 0;
  }
}

@media only screen and (min-width: 1350px) {
  .ml--xxl-0 {
    margin-left: auto;
    margin-right: 0;
  }
}

/*# sourceMappingURL=rtl.css.map */
