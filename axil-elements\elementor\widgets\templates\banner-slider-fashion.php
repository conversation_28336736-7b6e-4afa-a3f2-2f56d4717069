<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
use Elementor\Icons_Manager;
use Elementor\Group_Control_Image_Size;
$allowed_tags = wp_kses_allowed_html( 'post' ); 
 
?> 
<div class="axil-main-slider-area main-slider-style-2">
    <div class="container-full">
        <div class="slider-box-wrap">
            <div class="slider-activation-one"> 
            <?php 
                foreach ( $settings['list'] as $plist ): 

                    if ('1' == $plist['axil_link_type']) {
                        if ( !empty( $plist['url']['url'] ) ) {
                            $attr  = 'href="' . $plist['url']['url'] . '"';
                            $attr .= !empty( $plist['url']['is_external'] ) ? ' target="_blank"' : '';
                            $attr .= !empty( $plist['url']['nofollow'] ) ? ' rel="nofollow"' : '';
                             
                        }
                        if ( !empty( $plist['btntext'] ) ) {
                            $btn = '<a class="axil-btn" ' . $attr . '>' . $plist['btntext'] . ' <i class="fal fa-long-arrow-right"></i></a>';
                        }
                        }else {
                        $attr  = 'href="' . get_permalink($plist['axil_page_link']) . '"';
                        $attr .= ' target="_self"';
                        $attr .= ' rel="nofollow"';                        
                        $btn = '<a class="axil-btn" ' . $attr . '>' . $plist['btntext'] . ' <i class="fal fa-long-arrow-right"></i></a>';
                    }  
                ?> 
                <div class="single-slide slick-slide">
                    <div class="main-slider-content">                      
                        <?php if ( $plist['list_before'] ) {?>
                            <span class="subtitle title-highlighter highlighter-<?php echo esc_attr( $plist['beforetitlestyle'] );?>">
                                <?php Icons_Manager::render_icon( $plist['before_icon'] ); ?>
                                <?php echo wp_kses( $plist['list_before'], $allowed_tags );?>
                            </span>
                        <?php } ?>   
                        <h1 class="title"><?php echo wp_kses( $plist['list_title'], $allowed_tags );?></h1> 
                        <?php if ( $plist['list_btntext'] ) {?>
                            <div class="shop-btn">
                                <?php echo wp_kses( $btn, $allowed_tags );?>
                            </div>
                        <?php } ?>    
                    </div>
                    <div class="main-slider-thumb">
                      <?php echo Group_Control_Image_Size::get_attachment_image_html( $plist, 'image_size', 'image' );?> 
                    </div>
                </div>
                <?php endforeach;?> 
            </div>
        </div>
    </div>
</div> 