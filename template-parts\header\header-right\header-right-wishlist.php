<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

if ( defined( 'YITH_WCWL' ) ) { 
 
$axil_options = Helper::axil_get_options();
$count = yith_wcwl_count_all_products();
$className = 'wishlist-icon-style';
$class_name =  $count > 0 ? $className : '';
$countview =  $count > 0 ? $count : '';
?>
   <?php if( $axil_options['axil_enable_wishlist']  ):?>
     <?php if ( defined( 'YITH_WCWL' ) && ! function_exists( 'yith_wcwl_get_items_count' ) ) { ?>
       <li class="wishlist"> 
        <a href="<?php echo esc_url( YITH_WCWL()->get_wishlist_url() );?>" title="<?php echo esc_html__('Wish List', 'etrade'); ?> (<?php echo esc_attr($count); ?>) ">
           <i class="fal fa-heart"></i> 
            <span class="wishlist-icon-num <?php echo esc_attr($class_name);?>"><?php echo esc_html( $countview );?></span>
        </a>
      </li>
      <?php  } ?> 
  <?php endif ?> 
 <?php  } ?> 