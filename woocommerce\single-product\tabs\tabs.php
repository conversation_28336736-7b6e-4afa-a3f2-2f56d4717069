<?php
/**
 * Single Product tabs
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/tabs/tabs.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.8.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
$allowed_tags = wp_kses_allowed_html( 'post' );
/**
 * Filter tabs and allow third parties to add their own.
 * 
 *
 * Each tab is an array containing title, callback and priority.
 *
 * @see woocommerce_default_product_tabs()
 */
$product_tabs = apply_filters( 'woocommerce_product_tabs', array() );

if ( ! empty( $product_tabs ) ) : 

$axil_options           = Helper::axil_get_options(); 
$layout                 = Helper::axil_product_layout_style(); 
  

if ($layout == '3' || $layout == '2' || $layout == '4'  ) { ?>	
<div class="product-desc-wrapper mb--40 pt--60 pt_sm--60">
	<?php foreach ( $product_tabs as $key => $product_tab ) : ?>
        <h4 class="primary-color mb--20 desc-heading">
        	<?php echo wp_kses( apply_filters( 'woocommerce_product_' . $key . '_tab_title', $product_tab['title'], $key ), $allowed_tags ); ?>        	
        </h4>
            <div class="single-desc mb--30">
                        <?php
				if ( isset( $product_tab['callback'] ) ) {
					call_user_func( $product_tab['callback'], $key, $product_tab );
				}
				?>                    
			</div>
		
	<?php endforeach; ?>
<?php do_action( 'woocommerce_product_after_tabs' ); ?>
</div>

<?php }elseif( $layout == '5'){ ?> 
<div class="product-desc-wrapper">
    <?php foreach ( $product_tabs as $key => $product_tab ) : ?>
        <h4 class="mb--40 desc-heading">
            <?php echo wp_kses( apply_filters( 'woocommerce_product_' . $key . '_tab_title', $product_tab['title'], $key ), $allowed_tags ); ?>           
        </h4>
            <div class="single-desc mb--60">
                        <?php
                if ( isset( $product_tab['callback'] ) ) {
                    call_user_func( $product_tab['callback'], $key, $product_tab );
                }
                ?>                    
            </div>
        
    <?php endforeach; ?>
<?php do_action( 'woocommerce_product_after_tabs' ); ?>
</div>

<?php }elseif( $layout == '7'){ ?> 

    <div class="woocommerce-tabs wc-tabs-wrapper bg-vista-white nft-info-tabs layout-7">
        <ul class="nav tabs wc-tabs" role="tablist">
            <?php foreach ( $product_tabs as $key => $product_tab ) : ?>
                <li class="<?php echo esc_attr( $key ); ?>_tab nav-item" id="tab-title-<?php echo esc_attr( $key ); ?>" role="tab" aria-controls="tab-<?php echo esc_attr( $key ); ?>">
                    <a  href="#tab-<?php echo esc_attr( $key ); ?>">
                        <?php echo wp_kses( apply_filters( 'woocommerce_product_' . $key . '_tab_title', $product_tab['title'], $key ), $allowed_tags ); ?>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
        <?php foreach ( $product_tabs as $key => $product_tab ) : ?>
            <div class="tab-content woocommerce-Tabs-panel woocommerce-Tabs-panel--<?php echo esc_attr( $key ); ?> panel entry-content wc-tab" id="tab-<?php echo esc_attr( $key ); ?>" role="tabpanel" aria-labelledby="tab-title-<?php echo esc_attr( $key ); ?>">
                <div class="product-additional-info">
                    <?php
                    if ( isset( $product_tab['callback'] ) ) {
                        call_user_func( $product_tab['callback'], $key, $product_tab );
                    }
                    ?>
                </div>
            </div>
        <?php endforeach; ?>

        <?php do_action( 'woocommerce_product_after_tabs' ); ?>
    </div>


<?php }else{ ?>

	<div class="woocommerce-tabs-wrp">
		<ul class="nav tabs wc-tabs" role="tablist">
			<?php foreach ( $product_tabs as $key => $product_tab ) : ?>
				<li class="<?php echo esc_attr( $key ); ?>_tab nav-item" id="tab-title-<?php echo esc_attr( $key ); ?>" role="tab" aria-controls="tab-<?php echo esc_attr( $key ); ?>">
					<a  href="#tab-<?php echo esc_attr( $key ); ?>">
						<?php echo wp_kses( apply_filters( 'woocommerce_product_' . $key . '_tab_title', $product_tab['title'], $key ), $allowed_tags ); ?>
					</a>
				</li>
			<?php endforeach; ?>
		</ul>
		<?php foreach ( $product_tabs as $key => $product_tab ) : ?>
			<div class="woocommerce-Tabs-panel woocommerce-Tabs-panel--<?php echo esc_attr( $key ); ?> panel entry-content wc-tab" id="tab-<?php echo esc_attr( $key ); ?>" role="tabpanel" aria-labelledby="tab-title-<?php echo esc_attr( $key ); ?>">
				<?php
				if ( isset( $product_tab['callback'] ) ) {
					call_user_func( $product_tab['callback'], $key, $product_tab );
				}
				?>
			</div>
		<?php endforeach; ?>

		<?php do_action( 'woocommerce_product_after_tabs' ); ?>
	</div>
<?php 

}

endif; ?>
