<?php
/**
 * The template for displaying product content in the single-product.php template
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce/Templates
 * @version 3.6.0
 */

defined( 'ABSPATH' ) || exit;

global $product;

/**
 * Hook: woocommerce_before_single_product.
 */

$axil_options = Helper::axil_get_options();
$layout = Helper::axil_product_layout_style();
?>
<div class="container">
    <?php do_action( 'woocommerce_before_single_product' );?>
</div>
<?php if ( post_password_required() ) {
    echo get_the_password_form();
    return;
}
$layout = $layout ? $layout : '1';
?>
<div id="product-<?php the_ID();?>" <?php wc_product_class( '', $product );?>>
    <?php wc_get_template( "custom/single/content-{$layout}.php" );?>
</div> 
<?php do_action( 'woocommerce_after_single_product' );?>