<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;
use Elementor\Group_Control_Image_Size;
$allowed_tags = wp_kses_allowed_html( 'post' );  
?> 
<div class="axil-main-slider-area main-slider-style-4"> 
     <?php   
        $btn = $attr = ""; 
         if ('2' == $settings['axil_link_type']) {
            
                $attr  = 'href="' . get_permalink($settings['axil_page_link']) . '"';
                $attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
                $attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : ''; 
                $btn = '<a class="axil-btn btn-bg-primary" ' . $attr . '>'.$settings['btntext'] .'</a>';

           } else {
            if ( $settings['url']['url'] ) {
                if ( !empty( $settings['url']['url'] ) ) {
                    $attr  = 'href="' . $settings['url']['url'] . '"';
                    $attr .= !empty( $settings['url']['is_external'] ) ? ' target="_blank"' : '';
                    $attr .= !empty( $settings['url']['nofollow'] ) ? ' rel="nofollow"' : '';
                } 
                $btn = '<a class="axil-btn btn-bg-primary" ' . $attr . '>'.$settings['btntext'] .'</a>';
                    
                }
            }
        ?>    
        <div class="container">
            <div class="row align-items-lg-center">
                <div class="col-md-6 order-sm-1">
                    <div class="main-slider-content">
                        <?php if ( $settings['list_title'] ) { ?>   
                            <h1 class="title"><?php echo wp_kses( $settings['list_title'], $allowed_tags );?>  <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'title_image_size', 'titleimage' );?></h1>
                        <?php } ?>    
                         <?php if ( $settings['btntext'] ) { ?>   
                            <div class="shop-btn">
                                <?php echo wp_kses( $btn, $allowed_tags );?> 
                            </div>
                        <?php  } ?>    
                    </div>
                </div>
                <div class="col-md-6 order-sm-2">
                    <div class="slide-thumb-area">
                        <div class="main-thumb">
                             <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?> 
                        </div>  
                        <?php if ( $settings['plus_icon_display'] ): 
                         foreach ( $settings['list'] as $plist ):   
                            $plus_active = $plist['plus_icon_active'] ? 'icon-active' : '';
                            $posts = array_map( 'trim' , explode( ',', $plist['product_ids'] ) );
                            $args   = array(
                                'post_type'      => 'product',
                                'ignore_sticky_posts' => true,
                                'nopaging'       => true,
                                'post__in'       => $posts,
                                'orderby'        => 'post__in',
                                'posts_per_page' => 1,
                            ); 
                            $query =  new \WP_Query( $args ); ?>

                               <div class="banner-product plus-hover-items elementor-repeater-item-<?php echo esc_attr($plist['_id']);?> <?php echo esc_attr( $plus_active );?> axil-pos-<?php echo esc_attr( $plist['pos_y_type'] );?> axil-pos-<?php echo esc_attr( $plist['pos_x_type'] );?>">  
                                    <?php if ( $query->have_posts() ) :  
                                        while ( $query->have_posts() ) : $query->the_post(); 
                                            $id = get_the_ID();
                                            $product = wc_get_product( $id );
                                            $price = $product->get_price_html();
                                            $title = get_the_title();   

                                            if ( $plist['sale_price_only'] ) {
                                                $price_html = wc_price( wc_get_price_to_display( $product ) ) . $product->get_price_suffix();
                                            }
                                            else {
                                                $price_html = $product->get_price_html();
                                            }   
                                            ?>
                                             <div class="product-details">
                                                <h4 class="title"><a href="<?php the_permalink();?>"><?php echo wp_kses_post( $title ); ?></a></h4>
                                                <div class="price"><?php echo wp_kses_post( $price_html ); ?></div>
                                               
                                               <?php
                                                if ( $plist['rating_display'] == "yes" ) {
                                                    wc_get_template( 'loop/rating4.php' );
                                                }
                                            ?>   
                                        </div>  
                                        <?php endwhile;?>    
                                    <?php endif;?>   
                                  <?php wp_reset_postdata();?>   
                                    <div class="plus-icon">
                                        <i class="far fa-plus"></i>
                                    </div>
                                </div>  
                            <?php endforeach;?>  
                        <?php endif; ?>  

                        <?php if( $settings['shape_style_on']  == 'yes' ){ ?>
                            <ul class="shape-group">
                                <li class="shape-1">
                                    <svg width="717" height="569" viewBox="0 0 717 569" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M700.635 568.176C593.701 653.555 569.268 645.843 418.418 256.006C229.855 -231.289 -105.017 93.7591 62.1304 620.614" stroke="url(#paint0_linear_3774_13416)" stroke-width="32" stroke-linecap="round" />
                                        <defs>
                                            <linearGradient id="paint0_linear_3774_13416" x1="359.308" y1="-263.741" x2="-235.553" y2="631.772" gradientUnits="userSpaceOnUse">
                                                <stop offset="0.258739" stop-color="<?php echo esc_attr($settings['svg1_color1']);?>" />
                                                <stop offset="1" stop-color="<?php echo esc_attr($settings['svg1_color2']);?>" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </li>
                                <li class="shape-2">
                                    <svg width="806" height="605" viewBox="0 0 806 605" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <g clip-path="url(#clip0_1478_3882)">
                                            <path d="M786.673 3C806.703 135.413 745.738 384.513 341.63 321.606C-163.504 242.971 -51.9045 685.856 476.273 802" stroke="url(#paint0_linear_1478_3882)" stroke-width="32" stroke-linecap="round" />
                                        </g>
                                        <defs>
                                            <linearGradient id="paint0_linear_1478_3882" x1="-232.181" y1="-67.0641" x2="659.844" y2="1032.81" gradientUnits="userSpaceOnUse">
                                                <stop offset="0.525282" stop-color="<?php echo esc_attr($settings['svg2_color1']);?>" />
                                                <stop offset="1" stop-color="<?php echo esc_attr($settings['svg2_color1']);?>" />
                                            </linearGradient>
                                            <clipPath id="clip0_1478_3882">
                                                <rect width="806" height="605" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                </li>
                            </ul>
                        <?php } ?>   
                    </div>
                </div>
            </div> 
        </div>    
</div>  