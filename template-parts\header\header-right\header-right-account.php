<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options = Helper::axil_get_options();
$axil_heaedr_top_menu_args = Helper::axil_heaedr_top_menu_args();
$login_url = '#';
$register_url = '#';
$profile_url = '#'; 
/* Active woocommerce */
if (WOOC_WOO_ACTIVED) {
    $myaccount_page_id = get_option('woocommerce_myaccount_page_id');
    if ($myaccount_page_id) {
        $login_url = get_permalink($myaccount_page_id);
        $register_url = $login_url;
        $profile_url = $login_url;
    }
} else {
    $login_url = wp_login_url();
    $register_url = wp_registration_url();
    $profile_url = admin_url('profile.php');
}
if ($axil_options['axil_enable_acount']) {
?>    
 <li class="my-account"> 
    <a href="javascript:void(0)" class="">
        <i class="fal fa-user"></i> 
    </a> 
    <div class="my-account-dropdown">
        <span class="title"><?php echo esc_html__('Quick Link', 'etrade');?></span>
        <?php 
            $menu_name = 'myaccount';
            $locations = get_nav_menu_locations();  
            if( $locations && isset( $locations[ $menu_name ] ) ){ 
                $menu_items = wp_get_nav_menu_items( $locations[ $menu_name ] ); 
                ?> 
                <ul id="menu-<?php echo esc_html( $menu_name );?>">
                      <?php foreach ( (array) $menu_items as $key => $menu_item ){ ?> 
                        <li>
                            <a href="<?php echo esc_url( $menu_item->url ); ?>"> <?php echo esc_html( $menu_item->title  );?></a>
                        </li>
                  <?php  } ?>
                </ul>
            <?php }else{ ?>
            <ul><li><?php echo esc_html__('Menu location', 'etrade') ;?> <?php echo esc_html( $menu_name );?> <?php echo esc_html__('Active', 'etrade') ;?></li></ul>
            <?php } ?>

        <?php
        if (!WOOC_CORE_USER_LOGGED) {     
             $icon = "";
            $login_ajax = (!isset($axil_options['login_ajax']) || $axil_options['login_ajax'] == 1) ? '1' : '0';
            $span = $icon ? $icon_user : '';       
            ?>  
            <div class="login-btn">
                <a href="<?php echo esc_url($login_url); ?>" class="axil-btn btn-bg-primary"><?php echo esc_html__('Login', 'etrade'); ?></a>
            </div>
            <div class="reg-footer text-center"><?php echo esc_html__('No account yet?', 'etrade') ;?> 
                <a href="<?php echo esc_url($register_url); ?>" class="btn-link"><?php echo esc_html__('REGISTER HERE.', 'etrade') ;?></a>
            </div>  
       <?php }else { ?> 
        <div class="login-btn">
            <a href="<?php echo wp_logout_url( get_permalink() ); ?>" class="axil-btn btn-bg-primary"><?php echo esc_html__('Logout', 'etrade'); ?></a>
        </div> 
        <div class="reg-footer text-center"><?php echo esc_html__('No account yet?', 'etrade') ;?> 
            <a href="<?php echo esc_url($profile_url); ?>" class="btn-link"><?php echo esc_html__('My Account', 'etrade') ;?></a>
        </div> 
        <?php  } ?> 
    </div>
</li>
<?php } ?>
