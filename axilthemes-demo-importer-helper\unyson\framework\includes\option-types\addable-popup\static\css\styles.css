.fw-option-type-addable-popup .item {
	position: relative;
	background-color: #fff;
	min-height: 40px;
	cursor: pointer;
}

.fw-option-type-addable-popup .item .content {
	padding: 12px 30px;
	word-wrap: break-word;
}

.fw-option-type-addable-popup .item + .item {
	border-top: 1px dashed #E1E1E1;
}

.fw-option-type-addable-popup .items-wrapper {
	border: 1px solid #e1e1e1;
	margin-bottom: 20px;
}

.fw-option-type-addable-popup .delete-item {
	position: absolute;
	right: 7px;
	top:50%;
	margin-top: -10px;
}

.fw-option-type-addable-popup .clone-item {
	position: absolute;
	right: 25px;
	top:50%;
	margin-top: -8px;
	font-size:16px;
	opacity:0.4;
}
.fw-option-type-addable-popup .clone-item:hover{
	opacity:0.7;
}

.fw-option-type-addable-popup .sort-item {
	position: absolute;
	left: 11px;
	top:50%;
	margin-top: -4px;
	width: 7px;
}

.fw-option-type-addable-popup .default-item,
.fw-option-type-addable-popup:not(.is-sortable) .sort-item,
.fw-option-type-addable-popup .items-wrapper.hide-clone .clone-item {
	display: none;
}

.fw-option-type-addable-popup:not(.is-sortable) .item .content {
	padding-left: 15px;
}