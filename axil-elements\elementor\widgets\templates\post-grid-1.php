<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
?>
<div class="axil-section-blog"> 
    <div class="row g-5"> 
	<?php
		$post_sorting = $settings['post_sorting'];
		$post_ordering = $settings['post_ordering'];
		$title_limit = $settings['post_title_length'];
		 					
		$post_details_text = $settings['post_details_text'];	

		// number
		$number_of_post = $settings['number_of_post'];
        $p_ids = array();

        if ( !empty($settings['posts_not_in'])){
            foreach ( $settings['posts_not_in'] as $p_idsn ) {
                $p_ids[] = $p_idsn;
            }
        }
		$cat_single_list = $settings['cat_single_list'];			
		$args = array(
			'cat' => $cat_single_list,
			'post_status' => 'publish',
			'order' => $post_ordering,
			'posts_per_page' => $number_of_post, 
			'suppress_filters'    => false,
        	'ignore_sticky_posts' => true,	
			'post__not_in'   => $p_ids

		);
			
		if ( $post_sorting == 'view' ) {
			$args['orderby']  = 'meta_value_num';
			$args['meta_key'] = 'axil_views';
		} else {
			$args['orderby'] = $post_sorting;
		}

		$query = new WP_Query( $args );	

		$temp = Helper::wp_set_temp_query( $query );	

		if ( $query->have_posts() ) {	
			while ( $query->have_posts() ) { 
			$query->the_post(); 
			$post_id = get_the_ID();
			 
			$ptitle = wp_trim_words(get_the_title(), $title_limit, '');	
				
			?>
            <div class="col-lg-4">
	            <div class="content-blog blog-grid">
	                <div class="inner"> 
                	  <?php if(has_post_thumbnail()){ ?>
				            <div class="post-thumbnail thumbnail">
				                <a href="<?php the_permalink(); ?>">
				                    <?php the_post_thumbnail('axil-blog-sm') ?>
				                </a>
								<?php if ( $settings['show_post_categories'] !== 'no' && has_category()) { ?>
									<div class="blog-category"><?php the_category(', &nbsp;'); ?></div>
								<?php } ?> 
				            </div>   
							
			        	<?php } ?>     

	                    <div class="content">
	                        <h5 class="title"><a href="<?php the_permalink(); ?>"><?php echo esc_attr( $ptitle );?></a></h5> 
	                         <?php if( $settings['post_details'] ){ ?>
	                         	<div class="read-more-btn">
	                                <a href="<?php the_permalink(); ?>" class="axil-btn right-icon">
	                                	<?php echo esc_attr( $post_details_text );?>
	                                 	<i class="fal fa-long-arrow-right"></i>
	                             	</a>
	                         	</div>
                           <?php } ?>  
	                    </div>
	                </div>
	            </div>
            </div> 
            <?php } ?>			
      	</div> 
      	<?php 
      		Helper::wp_reset_temp_query( $temp ); 
      		} 
      	?>
    </div>
 


