<?php
/**
 * @package etrade
 */

if( !class_exists('etrade_Info_Widget') ){
    class etrade_Info_Widget extends WP_Widget{
    	/**
         * Register widget with WordPress.
         */
        function __construct(){

            $widget_options = array(
                'description'                   => esc_html__('etrade: Info here', 'etrade-elements'),
                'customize_selective_refresh'   => true,
            );

            parent:: __construct('etrade_Info_Widget', esc_html__( 'etrade: Support', 'etrade-elements'), $widget_options );

        }
        /**
         * Front-end display of widget.
         *
         * @see WP_Widget::widget()
         *
         * @param array $args     Widget arguments.
         * @param array $instance Saved values from database.
         */
        public function widget( $args, $instance ){
        	echo wp_kses_post( $args['before_widget'] );
        	if ( ! empty( $instance['title'] ) ) {
        		echo wp_kses_post( $args['before_title'] ) . apply_filters( 'widget_title', esc_html( $instance['title'] ) ) . wp_kses_post( $args['after_title'] );
        	}

    	    $address = isset( $instance['address'] ) ? $instance['address'] : ''; 
            $email = isset( $instance['email'] ) ? $instance['email'] : '';
        	$phone = isset( $instance['phone'] ) ? $instance['phone'] : '';
             ?> 


            <div class="inner">
                <?php if ( !empty($address) ): ?>
                    <p class="address">
                        <?php echo esc_attr( $address ); ?>
                    </p>
                <?php endif ?> 

                <ul class="support-list-item"> 
                     <?php if ( !empty($email) ): ?>
                        <li><a href="mailto:<?php echo wpautop( $email ); ?>"><i class="fal fa-envelope-open"></i> <?php echo wpautop( $email ); ?></a></li>
                     <?php endif ?>  
                    <?php if ( !empty($phone) ): ?>
                        <li><a href="tel:<?php echo wpautop( $phone ); ?>"><i class="fal fa-phone-alt"></i> <?php echo wpautop( $phone ); ?></a></li>
                    <?php endif ?>  
                </ul>
            </div> 

        	<?php
        	echo wp_kses_post( $args['after_widget'] );
        }

        /**
         * Sanitize widget form values as they are saved.
         *
         * @see WP_Widget::update()
         *
         * @param array $new_instance Values just sent to be saved.
         * @param array $old_instance Previously saved values from database.
         *
         * @return array Updated safe values to be saved.
         */
        public function update( $new_instance, $old_instance ){
        	$instance               = array();
        	$instance['title']      = ( ! empty( $new_instance['title'] ) ) ? sanitize_text_field( $new_instance['title'] ) : '';
        	$instance['address']    = ( ! empty( $new_instance['address'] ) ) ? strip_tags ( $new_instance['address'] ) : '';
            $instance['phone']      = ( ! empty( $new_instance['phone'] ) ) ? strip_tags ( $new_instance['phone'] ) : '';
            $instance['email']      = ( ! empty( $new_instance['email'] ) ) ? strip_tags ( $new_instance['email'] ) : '';
        	 
        	
            if ( current_user_can( 'unfiltered_html' ) ) {
			        $instance['address'] = $new_instance['address'];
			} else {
			        $instance['address'] = wp_kses_post( $new_instance['address'] );
			}
        	return $instance;
        }

        /**
         * Back-end widget form.
         *
         * @see WP_Widget::form()
         *
         * @param array $instance Previously saved values from database.
         */
        public function form($instance){ 
        	$title      = !empty( $instance['title'] ) ? $instance['title'] : '';
        	 
        	$address    = !empty( $instance['address'] ) ? $instance['address'] : ''; 
            $phone      = !empty( $instance['phone'] ) ? $instance['phone'] : ''; 
            $email      = !empty( $instance['email'] ) ? $instance['email'] : ''; 
        	 
        	?>
			<p>
				<label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php echo esc_html__('Title:' ,'etrade-elements') ?></label>
				<input id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" class="widefat" value="<?php echo esc_textarea( $title ); ?>">
			</p>
			 <p>
                <label for="<?php echo esc_attr($this->get_field_id('address')); ?>"><?php echo esc_html__('address:' ,'etrade-elements') ?></label>
                <textarea  id="<?php echo esc_attr($this->get_field_id('address')); ?>" name="<?php echo esc_attr($this->get_field_name('address')); ?>" rows="7" class="widefat" ><?php echo esc_textarea( $address ); ?></textarea>
            </p>

            <p>
                <label for="<?php echo esc_attr($this->get_field_id('email')); ?>"><?php echo esc_html__('Email:' ,'etrade-elements') ?></label>
                <input id="<?php echo esc_attr($this->get_field_id('email')); ?>" name="<?php echo esc_attr($this->get_field_name('email')); ?>" type="text" class="widefat" value="<?php echo esc_textarea( $email ); ?>">
            </p>
            <p>
                <label for="<?php echo esc_attr($this->get_field_id('phone')); ?>"><?php echo esc_html__('Phone:' ,'etrade-elements') ?></label>
                <input id="<?php echo esc_attr($this->get_field_id('phone')); ?>" name="<?php echo esc_attr($this->get_field_name('phone')); ?>" type="text" class="widefat" value="<?php echo esc_textarea( $phone ); ?>">
            </p>
 
			
			 
        	<?php
        }
	}
}
function etrade_Info_Widget(){
    register_widget('etrade_Info_Widget');
}
add_action('widgets_init','etrade_Info_Widget');