<?php
/**
 * Template part for displaying header layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options = Helper::axil_get_options();
$header_layout = Helper::axil_header_layout();
$header_sticky = $header_layout['header_sticky'];
$header_sticky = ( "1" !== $header_sticky && "0" !== $header_sticky ) ? " header-sticky-true " : "";
$axil_nav_menu_args = Helper::axil_nav_menu_args();
?>
<header class="header axil-header header-style-2">
    <?php get_template_part( 'template-parts/header/notification/header', 'notification-count' );?>
    <div class="axil-header-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-2 col-md-3 col-12">
                     <?php get_template_part( 'template-parts/header/header-logo/header', 'logo' );?>
                </div>
                <div class="col-lg-10 col-md-9 col-12">
                    <div class="header-top-dropdown dropdown-box-style">
                        <?php if ( $axil_options['axil_enable_header_search'] ): ?>
                            <?php get_template_part( 'template-parts/header/header-cat', 'search' );?>
                        <?php endif?>
                        <?php if ( $axil_options['axil_enable_currency'] ): ?>
                            <?php get_template_part( 'template-parts/header/header-top/currency', 'switcher' );?>
                        <?php endif?>
                        <?php if ( $axil_options['axil_enable_language'] ): ?>
                            <?php get_template_part( 'template-parts/header/header-top/language', 'toggle-2' );?>
                        <?php endif?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="axil-mainmenu mainmenu-bottom aside-category-menu">
        <div class="container">
            <div class="header-navbar">
            <?php if ( $axil_options['axil_enable_department_menu'] ): ?>
                <div class="header-nav-department">
                        <?php
                        $icon_display = true;
                        $text_display = true;
                        ?>
                     <aside class="header-department">
                        <button class="header-department-text department-title">
                        <span class="icon"><i class="fal fa-bars"></i></span>
                        <span class="text"><?php echo esc_html__( 'Categories', 'etrade' ); ?></span>
                        </button>
                        <?php
                            wp_nav_menu( array(
                                'theme_location'  => 'categoryiconmenu',
                                'container'       => 'nav',
                                'container_class' => 'department-nav-menu',
                                'menu_class'      => 'nav-menu-list',
                                'fallback_cb'     => false,
                                'walker'          => new \AxilNavWalker( $icon_display, $text_display ),

                            ) );
                    ?>
                   </aside>
                </div>
                <?php endif?>
               <div class="header-main-nav">
                    <?php if ( has_nav_menu( 'primary' ) ) {
                        wp_nav_menu( $axil_nav_menu_args );
                    }?>
                </div>
                <div class="header-action">
                    <ul class="action-list">
                        <?php get_template_part( 'template-parts/header/header-right/header-right', 'wishlist' );?>
                        <?php get_template_part( 'template-parts/header/header-right/header-right', 'cart' );?>
                        <?php get_template_part( 'template-parts/header/header-right/header-right', 'account' );?>
                        <?php get_template_part( 'template-parts/header/header-right/header-right', 'mobile-nav-toggler' );?>
                    </ul>
                </div> 
            </div>
        </div>
    </div>
</header> 