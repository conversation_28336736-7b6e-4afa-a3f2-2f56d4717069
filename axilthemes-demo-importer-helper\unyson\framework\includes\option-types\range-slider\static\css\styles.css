.fw-option-type-range-slider.short-slider {
	max-width: 280px;
}

.fw-option-type-range-slider .irs {
	height: 30px;
}

.fw-option-type-range-slider .irs-with-grid {
	height: 40px;
}

.fw-option-type-range-slider .irs-line {
	height: 5px;
	top: 5px;
	background: #ffffff;
	border: 1px solid #ddd;
	-webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
	border-radius: 16px;
	-moz-border-radius: 16px;
	-webkit-border-radius: 16px;
}

.fw-option-type-range-slider .irs-line-left {
	height: 8px;
}

.fw-option-type-range-slider .irs-line-mid {
	height: 8px;
}

.fw-option-type-range-slider .irs-line-right {
	height: 8px;
}

.fw-option-type-range-slider .irs-bar {
	height: 7px;
	top: 5px;
	background: #d9ecf4;
	-moz-box-shadow: inset 0 0 2px 1px #c4d5dc;
	-webkit-box-shadow: inset 0 0 2px 1px #c4d5dc;
	box-shadow: inner 0 0 1px 2px #c4d5dc;
	border-radius: 16px 0 0 16px;
	-moz-border-radius: 16px 0 0 16px;
	-webkit-border-radius: 16px 0 0 16px;
}

.fw-option-type-range-slider .irs-bar-edge {
	visibility: hidden;
}

.fw-option-type-range-slider .irs-shadow {
	height: 2px;
	top: 38px;
	background: #000;
	opacity: 0.3;
	border-radius: 5px;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
}

.fw-option-type-range-slider .lt-ie9 .irs-shadow {
	filter: alpha(opacity=30);
}

.fw-option-type-range-slider #irs-active-slider, .fw-option-type-range-slider .irs-slider:hover {
	background: #00709f;
}

.fw-option-type-range-slider .irs-min, .fw-option-type-range-slider .irs-max {
	display: none;
}

.fw-option-type-range-slider .lt-ie9 .irs-min, .fw-option-type-range-slider .lt-ie9 .irs-max {
	background: #ccc;
}

.fw-option-type-range-slider .irs-from, .fw-option-type-range-slider .irs-to, .fw-option-type-range-slider .irs-single {
	top: 0;
	border: none;
	border-radius: 10px;
	-moz-border-radius: 10px;
	-webkit-border-radius: 10px;
	cursor: pointer;
	background-color: #00709f;
	color: #ffffff;
	text-align: center;
	font-size: 11px;
	padding: 5px;
	min-width: 16px;
	height: 8px;
	line-height: 8px;
	z-index: 2;
}

.fw-option-type-range-slider .lt-ie9 .irs-from, .fw-option-type-range-slider .lt-ie9 .irs-to, .fw-option-type-range-slider .lt-ie9 .irs-single {
	background: #999;
}

.fw-option-type-range-slider .irs-grid {
	height: 19px;
}

.fw-option-type-range-slider .irs-grid-pol {
	opacity: 0.5;
	background: #428bca;
}

.fw-option-type-range-slider .irs-grid-pol.small {
	background: #999;
}

.fw-option-type-range-slider .irs-grid-text {
	color: #99a4ac;
}

.fw-option-type-range-slider .irs-disabled {
}