<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Icons_Manager;

$row_col_class  = "row-cols-xl-{$settings['col_xl']} row-cols-lg-{$settings['col_lg']} row-cols-md-{$settings['col_md']} row-cols-sm-{$settings['col_sm']} row-cols-{$settings['col_mobile']}";
$col_class  = "col ";
$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
if ( !empty( $settings['cat'] ) ) {
	$id = intval( $settings['cat'] );
	$shop_permalink = get_term_link( $id );
}
$block_data = array(
	'layout'         			=> $settings['style'],
	 
	'rating_display' 			=> $settings['rating_display'] ? true : false,
	'display_attributes' 		=> $settings['display_attributes'] ? true : false,
	'wishlist' 		 			=> $settings['wishlist'] ? true : false,
	'quickview' 	 			=> $settings['quickview'] ? true : false,
	'display_title_badge_check' => $settings['display_title_badge_check'] ? true : false,
	'v_swatch'       			=> true,	
	
);
$uniqueid = time().rand( 1, 99 );
$query_ids = array();
foreach ( $settings['queries'] as $key => $query ) {
	$class = $uniqueid.$key;
	foreach ( $query->posts as $post ) {
		$id = $post->ID;
		if ( array_key_exists( $id , $query_ids ) ) {
			$query_ids[$id] .= ' '. $class;
		}
		else {
			$query_ids[$id] = $class;
		}		
	}
}
global $post;
?>
 <div class="axil-product-area bg-color-white axil-section-gap pb--0">
    <div class="container">
        <div class="product-area pb--40">
            <div class="axil-isotope-wrapper">
                <div class="product-isotope-heading">
                   <?php if ( $settings['section_title_display'] ):  ?> 
	                    <div class="section-title-wrapper">
	                    		<?php  if($settings['sub_title']){ ?>
	                    	        <span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] );?> sub-title"><?php Icons_Manager::render_icon( $settings['icon'] ); ?><?php echo wp_kses_post( $settings['sub_title'] );?></span>
	                    	    <?php  } ?>    
	                        <h3 class="title"><?php echo wp_kses_post( $settings['title'] );?></h3>
	                    </div>
					<?php endif; ?> 
					 <?php if ( $settings['section_filter_display'] ):  ?>
						<div class="isotope-button">  
							<?php if ( $settings['filter_all_display'] == "yes"): ?>
								<button data-filter="all" class="is-checked"><span class="filter-text"><?php echo wp_kses_post( $settings['all_tab_text'] );?></span></button>
							<?php endif; ?> 
							<?php foreach ( $settings['navs'] as $key => $value ): ?>
								<button data-filter="<?php echo esc_attr( $uniqueid.$key );?>" class=""><span class="filter-text"><?php echo esc_html( $value );?></span>
								</button> 
							<?php endforeach; ?> 
						</div> 
					<?php endif; ?> 
                	</div>
                <div class="row row--15 isotope-list <?php echo esc_attr( $row_col_class );?>">
				<?php foreach ( $query_ids as $id => $class ):
					$post    = get_post( $id );
					$product = wc_get_product( $id );
					setup_postdata( $post );
			?>
			<div <?php wc_product_class( $col_class.$class, $product ); ?>>
				<?php wc_get_template( "custom/product-block/blocks.php" , compact( 'product', 'block_data' ) ); ?>
			</div>
			<?php
			wp_reset_postdata();
			endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div> 