<?php

final class Ajax_handler {
	private static $_instance = null;

	public function __construct() {
		
		add_action( 'wp_ajax_prod_search', [ $this, 'prod_search' ] );
		add_action( 'wp_ajax_product_search', [ $this, 'product_search' ] );
		add_action( 'wp_ajax_nopriv_prod_search', [ $this, 'prod_search' ] );
		add_action( 'wp_ajax_nopriv_product_search', [ $this, 'product_search' ] );
	}

	public static function instance() {

		if ( is_null( self::$_instance ) ) {
			self::$_instance = new self();
		}

		return self::$_instance;
	}

	public function prod_search() {
        global $wpdb;  
		$search_keyword       = esc_attr( $_REQUEST['keyword'] );
		$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
		if(strlen($search_keyword) == 0){?>
 			<div class="search-result-header">
                <h6 class="title">Recent Product</h6>
                <a href="<?php echo esc_url($shop_permalink);?>" class="view-all"><?php echo esc_html__( 'View All', 'etrade-elements' ); ?></a>
            </div>
			  <div class="psearch-results"> 
                <?php axil_is_recent_product();?>  
            </div> 
            <?php
			die();
		}else{
			$posts = $wpdb->get_results("select * from {$wpdb->posts} where post_type = 'product' and post_status = 'publish' and post_title like '%{$search_keyword}%'");
			if(!empty($posts)){ ?>
				<div class="search-result-header">
					<h6 class="title"><?php echo count($posts) ?> Result Found</h6>
					<a href="<?php echo esc_url($shop_permalink);?>" class="view-all"><?php echo esc_html__( 'View All', 'etrade-elements' ); ?></a>
				</div>
				
				<div class="psearch-results">
				<?php
				foreach ($posts as $post):
					setup_postdata($post);
					$id = $post->ID;
					$product = wc_get_product( $id );
					$price_html = wc_price( wc_get_price_to_display( $product ) ) . $product->get_price_suffix();
					$name = $product->name; ?>
					<div class="axil-product-list">
						<?php if ( $product->image_id ) { ?>
							<div class="thumbnail">
								<a href="<?= $product->get_permalink(); ?>">
									<?php echo wp_get_attachment_image( $product->image_id ); ?>
								</a>
							</div>
						<?php } ?> 
		                <div class="product-content">
							<?php wc_get_template( 'loop/rating4.php' ); ?> 
		                    <h6 class="product-title">
								<a href="<?= $product->get_permalink(); ?>">
									<?php echo preg_replace("#".preg_quote($search_keyword)."#i", "<span style='color: var(--color-secondary);'>$0</span>", $name);?>
								</a>
							</h6>
		                    <div class="product-price-variant">
		                        <?php echo wp_kses( $price_html, 'alltext_allow' );?>
		                    </div>
		                    <div class="product-cart add-to-cart">
								<?php WooC_Functions::wooc_print_add_to_cart_icon( true, false, false );?>   
								<?php WooC_Functions::axil_add_to_wishlist_icon(); ?>    
		                    </div>
		                </div>
		            </div>  <?php
					wp_reset_postdata();
				endforeach;
			} 
			else{ ?> 
				<div class="search-result-header">
					<h6 class="title">No Result Found</h6>
				</div>
			<?php }
			die();
		}

	}

	public function product_search() {

		$search_post_category = esc_attr( $_REQUEST['category_val'] );
		$search_keyword       = esc_attr( $_REQUEST['keyword'] );

		$args = array(
			's'              => $search_keyword,
			'post_type'      => 'product',
			'posts_per_page' => - 1,
		);

		if ( ! empty( $search_post_category ) ) {
			$args['tax_query'] = array(
				array(
					'taxonomy' => 'product_cat',
					'field'    => 'term_id',
					'terms'    => $search_post_category,
				),

			);
		}
		$query = new WP_Query( $args );

		if ( ! empty( $query ) ) {
			if ( $query->have_posts() ) :
				 
				while ( $query->have_posts() ) : 
					$query->the_post();
					$id = get_the_ID();
					$product = wc_get_product( $id );
					$price_html = wc_price( wc_get_price_to_display( $product ) ) . $product->get_price_suffix();
					$name = $product->name; 
					?>
					<div class="axil-product-list mb-0"> 
						<?php if ( $product->image_id ) { ?>
							<div class="thumbnail">
								<a href="<?php the_permalink();?>">
									<?php the_post_thumbnail('thumbnail'); ?>
								</a>
							</div> 
						<?php } ?> 
						<div class="product-content">
							<?php
								wc_get_template( 'loop/rating4.php' );
							?>		
							<h6 class="product-title">
								<a href="<?php the_permalink();?>">
									<?php echo preg_replace("#".preg_quote($search_keyword)."#i", "<span style='color: var(--color-secondary);'>$0</span>", $name);?>
								</a>
							</h6>	 
							<div class="product-price-variant">
								<?php echo wp_kses( $price_html, 'alltext_allow' );?>
							</div>      
							<div class="product-cart add-to-cart">			                  
								<?php WooC_Functions::wooc_print_add_to_cart_icon( true, false, false );?>	
								<?php WooC_Functions::axil_add_to_wishlist_icon(); ?>	
							</div>
						</div>
					</div>
				<?php endwhile;
				 
			else : ?>
				<div class="error_message"><?= esc_html( 'No Product Found.', 'etrade' ) ?></div>;

			<?php endif;
		}
		die(); 

	}
}

Ajax_handler::instance();