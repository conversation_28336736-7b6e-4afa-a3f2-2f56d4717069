<?php
/**
 * Template Name: Register Page
 *
 * @package etrade
 */

$axil_options = Helper::axil_get_options();
$banner_img = $axil_options['acount_register_img'];
$banner_img = "background-image:url({$banner_img['url']});";
$acount_register_title = $axil_options['acount_register_title'];
$allowed_tags   = wp_kses_allowed_html('post');
if ( !WOOC_CORE_USER_LOGGED ) {
    get_header( 'login' );?>
	    <div class="row">
		    <div class="col-xl-4 col-lg-6">
				<?php if ( isset( $_GET['action'] ) ) {
       				 if ( in_array( $_GET['action'], array( 'lostpassword', 'retrievepassword' ) ) ) {?>
				         <div class="axil-signin-banner bg_image bg_image--9" style="<?php echo wp_kses( $banner_img, $allowed_tags ); ?>">
				                <h3 class="title"><?php echo esc_html( $axil_options['acount_register_title'] ); ?></h3>
				          </div>
				    <?php }
    				} else {?>
				 <div class="axil-signin-banner bg_image bg_image--9" style="<?php echo wp_kses( $banner_img, $allowed_tags ); ?>">
				         <h3 class="title"><?php echo esc_html( $axil_options['acount_register_title'] ); ?></h3>
				     </div>
				 <?php }?>
            </div>
        <div class="col-lg-6 offset-xl-2">
            <div class="axil-signin-form-wrap">
                <div class="axil-signin-form"> 
					<?php while ( have_posts() ):
						the_post();
						the_content();
					endwhile; // End of the loop. ?>
                </div>
            </div>
        </div>
	</div>
	<?php } else {
    get_header();?>
			<div class="axil-dashboard-area axil-section-gap">
				<div class="container">
				<?php while ( have_posts() ):
					the_post();
					the_content();
				endwhile; // End of the loop. ?>
		</div>
	</div>
<?php get_footer();?>
<?php }?>