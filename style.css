/*!
Theme Name: eTrade
Theme URI: http://axilthemes.com/themes/etrade/
Author: Axilthemes
Author URI: https://themeforest.net/user/axilthemes/portfolio
Description: eTrade is a simple, highly customizable, easy-to-use eCommerce Template for WooCommerce. It has been designed to create highly functional online shopping stores like fashion, cosmetics, business, electronics, food, furniture, jewelry, medical, and retail.
Version: 1.1.7
License: GNU General Public License v2 or later
License URI: LICENSE
Text Domain: etrade
Tags: custom-background, custom-logo, custom-menu, featured-images, threaded-comments, translation-ready

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned with others.

Etrade is based on Underscores https://underscores.me/, (C) 2012-2017 Automattic, Inc.
Underscores is distributed under the terms of the GNU GPL v2 or later.

Normalizing styles have been helped along thanks to the fine work of
<PERSON> and <PERSON> https://necolas.github.io/normalize.css/

*/
/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Normalize
# Typography
# Elements
# Forms
# Navigation
	## Links
	## Menus
# Accessibility
# Alignments
# Clearings
# Widgets
# Content
	## Posts and pages
	## Comments
# Infinite scroll
# Media
	## Captions
	## Galleries
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Normalize
--------------------------------------------------------------*/
/* normalize.css v8.0.0 | MIT License | github.com/necolas/normalize.css */


/*--------------------------------------------------------------
# Accessibility
--------------------------------------------------------------*/

/* Text meant only for screen readers. */
.screen-reader-text {
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute !important;
	width: 1px;
	word-wrap: normal !important;
	/* Many screen reader and browser combinations announce broken words as they would appear visually. */
}

.screen-reader-text:focus {
	background-color: #f1f1f1;
	border-radius: 3px;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	clip: auto !important;
	clip-path: none;
	color: #21759b;
	display: block;
	font-size: 14px;
	font-size: 0.875rem;
	font-weight: bold;
	height: auto;
	left: 5px;
	line-height: normal;
	padding: 15px 23px 14px;
	text-decoration: none;
	top: 5px;
	width: auto;
	z-index: 100000;
	/* Above WP toolbar. */
}

/* Do not show the outline on the skip link target. */
#content[tabindex="-1"]:focus {
	outline: 0;
}

/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Clearings
--------------------------------------------------------------*/
.clear:before,
.clear:after,
.entry-content:before,
.entry-content:after,
.comment-content:before,
.comment-content:after,
.site-header:before,
.site-header:after,
.site-content:before,
.site-content:after,
.site-footer:before,
.site-footer:after {
	content: "";
	display: table;
	table-layout: fixed;
}

.clear:after,
.entry-content:after,
.comment-content:after,
.site-header:after,
.site-content:after,
.site-footer:after {
	clear: both;
}

/*--------------------------------------------------------------
# Content
--------------------------------------------------------------*/


/*--------------------------------------------------------------
## Comments
--------------------------------------------------------------*/
.comment-content a {
	word-wrap: break-word;
}

.bypostauthor {
	display: block;
}


/*--------------------------------------------------------------
## Posts and pages
--------------------------------------------------------------*/
.sticky {
	display: block;
}

/* .post,
.page {
	margin: 0 0 1.5em;
} */

.updated:not(.published) {
	display: none;
}

.page-content,
.entry-content,
.entry-summary {
	margin: 1.5em 0 0;
}

.page-links {
	clear: both;
	margin: 0 0 1.5em;
}





/*--------------------------------------------------------------
# Infinite scroll
--------------------------------------------------------------*/
/* Globally hidden elements when Infinite Scroll is supported and in use. */
.infinite-scroll .posts-navigation,
.infinite-scroll.neverending .site-footer {
	/* Theme Footer (when set to scrolling) */
	display: none;
}

/* When Infinite Scroll has reached its end we need to re-display elements that were hidden (via .neverending) before. */
.infinity-end.neverending .site-footer {
	display: block;
}

/*--------------------------------------------------------------
# Media
--------------------------------------------------------------*/
.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
	border: none;
	margin-bottom: 0;
	margin-top: 0;
	padding: 0;
}

/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
	max-width: 100%;
}

/* Make sure logo link wraps around logo image. */
.custom-logo-link {
	display: inline-block;
}

/*--------------------------------------------------------------
## Captions
--------------------------------------------------------------*/
.wp-caption {
	margin-bottom: 1.5em;
	max-width: 100%;
}

.wp-caption img[class*="wp-image-"] {
	display: block;
	margin-left: auto;
	margin-right: auto;
}

.wp-caption .wp-caption-text {
	margin: 0.8075em 0;
}

.wp-caption-text {
	text-align: center;
}

/*--------------------------------------------------------------
## Galleries
--------------------------------------------------------------*/
.gallery {
	margin-bottom: 1.5em;
}

.gallery-item {
	display: inline-block;
	text-align: center;
	vertical-align: top;
	width: 100%;
}

.gallery-columns-2 .gallery-item {
	max-width: 50%;
}

.gallery-columns-3 .gallery-item {
	max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
	max-width: 25%;
}

.gallery-columns-5 .gallery-item {
	max-width: 20%;
}

.gallery-columns-6 .gallery-item {
	max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
	max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
	max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
	max-width: 11.11%;
}

.gallery-caption {
	display: block;
}

/*-------------------------------------
	Admin Bar 
------------------------------------*/
@media only screen and (min-width: 779px) {

	body.admin-bar header.axil-header.header-sticky.sticky,
	body.admin-bar .popup-mobile-manu {
		top: 32px
	}
}

@media only screen and (max-width: 779px) and (min-width: 601px) {
	body.admin-bar header.axil-header.header-sticky.sticky {
		top: 46px
	}
}

@media only screen and (max-width: 992px) and (min-width: 779px) {
	body.admin-bar .popup-mobile-manu {
		top: 0
	}
}

@media only screen and (max-width: 779px) and (min-width: 601px) {
	body.admin-bar .popup-mobile-manu {
		top: 0
	}
}

@media only screen and (max-width: 600px) {
	body.admin-bar .popup-mobile-manu {
		top: 0
	}
}

/*-- Preloader --*/
#preloader {
	background-color: #fff;
	background-repeat: no-repeat;
	background-position: center;
	background-attachment: fixed;
	height: 100%;
	left: 0;
	overflow: visible;
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 9999999;
}


/* Text Loading*/
.text-loading .button-text {
	position: relative;
}

.text-loading .button-text::after {
	content: " Please wait... ";
	display: block;
	position: absolute;
	bottom: -45px;
	left: 50%;
	width: 100%;
	font-size: 17px;
	color: #666666;
	font-weight: normal;
	text-transform: none;
	text-align: center;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	transform: translateX(-50%);
}

.disabled {
	pointer-events: none;
	opacity: .20;
}



/* Custom Code */
.content-block.thumb-border-rounded .post-thumbnail a img {
	border-radius: 100%;
	min-width: 250px;
	max-width: 250px;
	max-height: 250px;
	min-height: 250px;
	object-fit: cover;
}

.axil-trending-post-area .trend-post .post-content {
	width: 100%;
}

/* Caregory widget */
.list-categories .single-cat .inner {
	min-height: 180px;
	width: 100%;
	background: var(--color-primary);
}

.list-categories .single-cat .thumbnail img {
	width: 100%;
	min-height: 180px;
	object-fit: cover;
}

/* Social Networks */
ul.social-with-text li:hover a i {
	color: var(--color-white);
}


/* Creative Slider */
.axil-slide.slider-style-3 .content-block {
	display: flex;
}

/* Audio Post */
.axil-post-details audio {
	display: block;
	width: 100%;
}

.ads-container>a {
	display: inline-block;
}


.vertical-tab-with-post-area {
	display: flex;
	margin: 0;
}

.axil-vertical-nav {
	min-width: 279px;
	width: auto;
	border-right: 1px solid #CED0D4;
	padding: 30px;
}

.axil-vertical-nav-content {
	width: 83.589%;
	padding: 30px;
}
 

.woocommerce-store-notice, p.demo_store {
    position: initial;
    top: 0;
    left: 0;
    right: 0;
    margin: 0;
    width: 100%;
    font-size: 1em;
    padding: 0;
    text-align: center;
    background-color: transparent;
    color: #fff;
    z-index: 9;
    box-shadow: none;
    display: none;
}





.hover_overlay_body, .hover_overlay_content, .hover_overlay_footer, .hover_overlay_header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99;
    opacity: 0;
    background: rgba(0,  0,  0,  0.1);
    -webkit-transition: opacity .3s ease-in-out;
    -o-transition: opacity .3s ease-in-out;
    transition: opacity .3s ease-in-out;
    -webkit-transition-delay: .2s;
    -o-transition-delay: .2s;
    transition-delay: .2s;
    -webkit-animation: no_space_and_hidden 0s;
    animation: no_space_and_hidden 0s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-delay: .6s;
    animation-delay: .6s;
    visibility: hidden;
}
.hover_overlay_body.visible, .hover_overlay_content.visible, .hover_overlay_footer.visible, .hover_overlay_header.visible {
    opacity: 1;
    -webkit-animation: full_and_visible 0s;
    animation: full_and_visible 0s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
}
.hover_overlay_content {
    max-width: 100%;
    margin: 0;
}

.main-wrapper .site-content-wrapper {
    position: relative;
    overflow-x: hidden;
}


.main-content {
    position: relative;
}
.service-box .icon img {
    max-height: 60px;
    width: auto;
}

.footer-style-1.bg-color-white .logo.footer-logo-dark{
	display: none;

}
.axil-footer-widget .inner ul li a {
    
    display: inline-block;
     
}
.footer-style-2 .logo.footer-logo-dark{
	display: none;
}
.axil-product .product-content .product-rating { 
    display: inline-flex;
}
 
 .single-product-layout-2 .product-additional-info,

 .single-product-layout-4 .product-additional-info {
    background: #fff;
    padding: 0; 
    border-radius: 6px;
    margin-bottom: 40px;
}

 .single-product-layout-5 .product-desc-wrapper.pt--60{
 	padding-top: 0;
} 
.single-product-layout-5 .product-desc-wrapper.mb--40{
 	margin-bottom: 0;
}
.single-product-layout-5 .reviews-wrapper  .mb--40{
 	margin-bottom: 0;
}

.woocommerce-tabs.nft-info-tabs .tab-content {
    background-color: var(--color-white) !important;
    padding: 30px !important;
    border-radius: 6px !important;
}

.single-product-layout-7 .woocommerce-tabs.nft-info-tabs ul.tabs li a {
    font-size: 18px;
    background-color: rgba(255, 255, 255, 0.7) !important;
    border-radius: 6px !important;
    padding: 10px 25px !important;
    position: relative !important;
}
.woocommerce div.product .action-style-two  form.cart {
    margin-bottom: 0;
}
.overflow-visible .main-wrapper .site-content-wrapper {
 overflow-x: visible !important
}

.woocommerce div.product .woocommerce-tabs.nft-info-tabs.layout-7 ul.tabs {
  
    padding: 0;
    margin: 0; 
}

  .offer-badge {
    background-color: var(--color-chart03);
    height: 48px;
    line-height: 40px;
    padding: 5px 20px;
    font-size: 16px;
    color: var(--color-white);
    border-radius: 24px;
    font-family: var(--font-secondary);
}


/*//custom-logo-link*/

.axil-mainmenu.aside-category-menu .header-department .department-nav-menu {
   
    padding: 4px 0;
   
}
.main-slider-style-2 .slider-product-box {
    
    padding: 54px 20px;
   
}
.main-slider-style-2 .slider-box-wrap {
    
    padding: 57px 50px;
}
.axil-shop-top .widget_layered_nav_filters {
	margin-top:10px;
}
.axil-shop-top .widget_layered_nav_filters ul{
	display: flex;
	
}

.widget_layered_nav_filters .d-none.axil-widget-title.widget-title{
	display: block !important;
	font-size:16px;
	    margin: -1px 16px 0px 0;
	
}
.axil-shop-top .widget_layered_nav_filters {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.header-action .wishlist .wishlist-icon-style {
    text-align: center;
    background-color: var(--color-primary);
    border: 2px solid var(--color-white);
    font-size: 12px;
    font-weight: 500;
    color: var(--color-white);
    border-radius: 50%;
    height: 22px;
    width: 22px;
    line-height: 19px;
    position: absolute;
    top: -23px;
    right: -12px;
}



#axil-login-wrap,
#axil-register-wrap {
	position: absolute;
	top: 10px;
	left: 0;
	z-index: -1;
	width: 100%;
	opacity: 0;
	-webkit-transition: all 0.2s ease; transition: all 0.2s ease;
}
#axil-already-member,
#axil-not-member {
	position: absolute;
	top: 10px;
	left: 0;
	z-index: -1;
	width: 100%;
	opacity: 0;
	-webkit-transition: all 0.2s ease; transition: all 0.2s ease;
}

#axil-login-wrap.inline,
#axil-register-wrap.inline {
	position: relative;
	z-index: 100;
}
#axil-login-wrap.fade-in,
#axil-register-wrap.fade-in {
	opacity: 1;
}
#axil-login-wrap.slide-up,
#axil-register-wrap.slide-up {
	top: 0px;
}



#axil-already-member.inline,
#axil-not-member.inline {
	position: relative;
	z-index: 100;
}
#axil-already-member.fade-in,
#axil-not-member.fade-in {
	opacity: 1;
}
#axil-already-member.slide-up,
#axil-not-member.slide-up {
	top: 0px;
}

#axil-not-member,
#axil-already-member{
	display: flex;
	justify-content: end;
	align-items: center;
}

.is-no-active.is-active > a{
	    color: var(--color-heading) !important;
}

div.zoom-gallery-cursor img:hover{
	cursor: zoom-in;
}

.header-search-modal .card-header .form-control {
    border: 1px solid #F6F7FB;
    border-radius: 8px !important; 
    background: #F6F7FB;
}
.header-style-7 .header-action .axil-search input,.header-style-5 .header-action .axil-search input{
    border: 1px solid #F6F7FB; 
    background: #F6F7FB;
}

.axil-mainmenu.aside-category-menu.axil-sticky .header-department .department-nav-menu{
	opacity: 0;
	visibility: hidden;
}

.axil-mainmenu.aside-category-menu.axil-sticky .header-department:hover .department-nav-menu{
	opacity: 1;
	visibility: visible;
}



.dokan-dashboard-wrap .entry-title {
	font-size: 32px;
	margin-bottom: 25px;
	line-height: 1.5
}

.dokan-dashboard-wrap input {
	padding: 4px 6px
}

.dokan-btn,
a.dokan-btn,
input[type=submit].dokan-btn {
	background-color: #f26c4f;
	border: none;
	border-radius: 4px;
	-webkit-box-shadow: none;
	box-shadow: none;
	color: #fff!important;
	cursor: pointer;
	display: inline-block;
	font-size: 16px;
	line-height: 1;
	font-weight: 600;
	padding: 10px 35px
}

.dokan-btn:active,
.dokan-btn:focus,
.dokan-btn:hover,
a.dokan-btn:active,
a.dokan-btn:focus,
a.dokan-btn:hover,
input[type=submit].dokan-btn:active,
input[type=submit].dokan-btn:focus,
input[type=submit].dokan-btn:hover {
	background-color: #f26c4f;
	color: #fff;
	opacity: .8
}

.dokan-product-listing .dokan-product-listing-area .product-listing-top {
	line-height: inherit;
	padding-bottom: 15px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between
}

@media all and (max-width:575.98px) {
	.dokan-product-listing .dokan-product-listing-area .product-listing-top {
		display: block
	}
}

@media all and (max-width:767.98px) {
	.dokan-product-listing .dokan-product-listing-area .product-listing-top {
		margin-top: 15px
	}
}

.dokan-product-listing .dokan-product-listing-area .product-listing-top ul.dokan-listing-filter {
	float: none!important;
	-webkit-box-flex: 1;
	-ms-flex: 1 1 auto;
	flex: 1 1 auto;
	width: inherit;
	margin: 0
}

.dokan-product-listing .dokan-product-listing-area .product-listing-top ul.dokan-listing-filter li a {
	color: #646464
}

.dokan-product-listing .dokan-product-listing-area .product-listing-top ul.dokan-listing-filter li a:hover,
.dokan-product-listing .dokan-product-listing-area .product-listing-top ul.dokan-listing-filter li.active a {
	color: #f26c4f
}

.dokan-product-listing .dokan-product-listing-area .product-listing-top span.dokan-add-product-link {
	display: block;
	float: none;
	width: inherit;
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto
}

@media all and (max-width:575.98px) {
	.dokan-product-listing .dokan-product-listing-area .product-listing-top span.dokan-add-product-link {
		text-align: left;
		margin-top: 10px
	}
}

.dokan-product-listing .dokan-product-listing-area .product-listing-top span.dokan-add-product-link a.dokan-btn {
	height: 45px;
	line-height: 25px
}

@media all and (max-width:575.98px) {
	.dokan-product-listing .dokan-product-listing-area .product-listing-top span.dokan-add-product-link a.dokan-btn {
		height: inherit;
		padding: 8px 20px
	}
}

.dokan-product-listing-area>.dokan-w12 {
	float: none;
	clear: both;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex
}

@media all and (max-width:1199.98px) {
	.dokan-product-listing-area>.dokan-w12 {
		display: block
	}
}

.dokan-product-listing-area>.dokan-w12 button {
	vertical-align: top
}

.dokan-product-listing-area>.dokan-w12 form {
	float: none;
	width: inherit
}

.dokan-product-listing-area>.dokan-w12 form .dokan-form-group {
	float: none!important;
	display: inline-block
}

@media all and (max-width:575.98px) {
	.dokan-product-listing-area>.dokan-w12 form .dokan-form-group {
		display: block;
		width: 100%
	}
}

.dokan-product-listing-area>.dokan-w12 form .dokan-form-group #filter-by-date {
	width: 100px
}

@media all and (max-width:575.98px) {
	.dokan-product-listing-area>.dokan-w12 form .dokan-form-group #filter-by-date {
		width: 100%
	}
}

@media all and (max-width:575.98px) {
	.dokan-product-listing-area>.dokan-w12 form.dokan-product-date-filter {
		display: block!important;
		margin-bottom: 10px
	}
}

@media all and (max-width:575.98px) {
	.dokan-product-listing-area>.dokan-w12 form.dokan-product-date-filter .dokan-btn {
		width: 100%
	}
}

@media all and (max-width:575.98px) {
	.dokan-product-listing-area>.dokan-w12 form.dokan-product-date-filter .dokan-form-group {
		margin-bottom: 5px!important
	}
}

.dokan-product-listing-area>.dokan-w12 form.dokan-product-search-form {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: reverse;
	-ms-flex-direction: row-reverse;
	flex-direction: row-reverse
}

@media all and (max-width:1199.98px) {
	.dokan-product-listing-area>.dokan-w12 form.dokan-product-search-form {
		-webkit-box-pack: end;
		-ms-flex-pack: end;
		justify-content: flex-end
	}
}

.dokan-product-listing-area>.dokan-w12 form.dokan-product-search-form .dokan-btn {
	float: none!important;
	height: 36px
}

#product-filter div.dokan-form-group {
	font-size: 5px;
	float: none;
	display: inline-block;
	vertical-align: top;
	margin: 0 5px 10px 0
}

@media all and (max-width:575.98px) {
	#product-filter div.dokan-form-group {
		margin: 13px 10px 0 0!important
	}
}

a.dokan-btn-theme.vendor-dashboard {
	padding: 15px 35px;
	color: #fff
}

#dokan-form-inline .dokan-form-group:last-child {
	float: none;
	display: none
}

#dokan-product-list-table {
	margin-top: 30px
}

#dokan-product-list-table td,
#dokan-product-list-table th {
	border: none
}

#dokan-product-list-table th {
	padding: 10px 8px;
	vertical-align: middle;
	color: #111;
	font-weight: 500
}

#dokan-product-list-table th label {
	display: none
}

#dokan-product-list-table th input {
	vertical-align: middle;
	display: block;
	cursor: pointer
}

#dokan-product-list-table td.dokan-product-select label {
	display: none
}

#dokan-product-list-table td.dokan-product-select input {
	cursor: pointer;
	margin-top: 10px
}

#dokan-product-list-table abbr[title] {
	border: none
}

.dokan-form-control,
input.dokan-form-control {
	padding: 8px 12px;
	font-size: 15px;
	line-height: 1.5;
	border: 1px solid #ededed;
	height: 36px;
	color: #444;
	background-color: #f2f2f2;
	border-radius: 4px
}

select.dokan-form-control {
	cursor: pointer
}

.dokan-product-listing .dokan-product-listing-area table.product-listing-table mark.instock {
	color: #444;
	font-weight: 400
}

#order_date_filter {
	height: 33px;
	vertical-align: middle;
	padding: 5px 10px!important;
	border-color: #ededed
}

.edit-account input[type=submit].dokan-btn-theme {
	margin-top: 20px;
	padding: 15px 35px
}

.dokan-add-new-product-popup #dokan-product-images ul.product_images {
	list-style-type: none
}

.mfp-close:hover {
	background-color: inherit
}

#dokan-add-new-product-popup h2 {
	color: #111
}

.dokan-single-store.dokan-w8 {
	width: 100%
}

.dokan-product-listing-area .pagination-wrap,
.pagination-area {
	margin-top: 30px
}

.dokan-product-listing-area .pagination-wrap ul,
.pagination-area ul {
	list-style-type: none;
	margin: 0;
	padding: 0;
	font-size: 0;
	text-align: center
}

.dokan-product-listing-area .pagination-wrap ul li:not(.pagi),
.pagination-area ul li:not(.pagi) {
	display: inline-block
}

.dokan-product-listing-area .pagination-wrap ul li:not(.pagi) a,
.dokan-product-listing-area .pagination-wrap ul li:not(.pagi) span,
.pagination-area ul li:not(.pagi) a,
.pagination-area ul li:not(.pagi) span {
	font-size: 18px;
	line-height: 1;
	color: #ababab;
	border: 1px solid #ccc;
	padding: 10px 15px;
	-webkit-transition: .3s ease-in-out;
	transition: .3s ease-in-out;
	display: block;
	margin-right: -1px;
	outline: 0;
	border-radius: 2px
}

@media all and (max-width:767.98px) {

	.dokan-product-listing-area .pagination-wrap ul li:not(.pagi) a,
	.dokan-product-listing-area .pagination-wrap ul li:not(.pagi) span,
	.pagination-area ul li:not(.pagi) a,
	.pagination-area ul li:not(.pagi) span {
		font-size: 14px;
		padding: 10px 13px
	}
}

@media all and (max-width:575.98px) {

	.dokan-product-listing-area .pagination-wrap ul li:not(.pagi) a,
	.dokan-product-listing-area .pagination-wrap ul li:not(.pagi) span,
	.pagination-area ul li:not(.pagi) a,
	.pagination-area ul li:not(.pagi) span {
		padding: 8px 12px
	}
}

.dokan-product-listing-area .pagination-wrap ul li:not(.pagi) a:hover,
.dokan-product-listing-area .pagination-wrap ul li:not(.pagi) span,
.pagination-area ul li:not(.pagi) a:hover,
.pagination-area ul li:not(.pagi) span {
	background-color: #f26c4f;
	border-color: #f26c4f;
	color: #fff
}

.dokan-product-listing-area .pagination-wrap ul li:not(.pagi).active+li a,
.pagination-area ul li:not(.pagi).active+li a {
	border-left-color: transparent;
	margin-left: 0
}

.dokan-product-listing-area .pagination-wrap ul li:not(.pagi).dot span,
.pagination-area ul li:not(.pagi).dot span {
	background: 0 0 !important;
	border: none !important;
	color: #ababab
}

.dokan-product-listing-area .pagination-wrap ul li.pagi a,
.dokan-product-listing-area .pagination-wrap ul li.pagi span,
.pagination-area ul li.pagi a,
.pagination-area ul li.pagi > span {
	color: #111;
	border-bottom: 2px solid #c8c8c8;
	padding: 10px 0 8px
}

@media all and (max-width:767.98px) {

	.dokan-product-listing-area .pagination-wrap ul li.pagi a,
	.dokan-product-listing-area .pagination-wrap ul li.pagi span,
	.pagination-area ul li.pagi a,
	.pagination-area ul li.pagi span {
		font-size: 14px;
		line-height: 1;
		padding: 5px 0
	}
}

@media all and (max-width:575.98px) {

	.dokan-product-listing-area .pagination-wrap ul li.pagi a,
	.dokan-product-listing-area .pagination-wrap ul li.pagi span,
	.pagination-area ul li.pagi a,
	.pagination-area ul li.pagi span {
		border: none
	}
}

.dokan-product-listing-area .pagination-wrap ul li.pagi a:hover,
.dokan-product-listing-area .pagination-wrap ul li.pagi span:hover,
.pagination-area ul li.pagi a:hover,
.pagination-area ul li.pagi span:hover {
	color: #444
}

.dokan-product-listing-area .pagination-wrap ul li.pagi i,
.pagination-area ul li.pagi i,
.pagination-area ul li.pagi span.fa {
	font-size: 20px;
	line-height: 1;
	vertical-align: text-bottom;
	font-weight: 700
}

@media all and (max-width:767.98px) {

	.dokan-product-listing-area .pagination-wrap ul li.pagi i,
	.pagination-area ul li.pagi i {
		font-size: 18px
	}
}

.dokan-product-listing-area .pagination-wrap ul li.pagi em,
.pagination-area ul li.pagi em {
	font-style: normal
}

@media all and (max-width:575.98px) {

	.dokan-product-listing-area .pagination-wrap ul li.pagi em,
	.pagination-area ul li.pagi em {
		display: none
	}
}

.dokan-product-listing-area .pagination-wrap ul li.pagi.disabled i,
.dokan-product-listing-area .pagination-wrap ul li.pagi.disabled span,
.pagination-area ul li.pagi.disabled i,
.pagination-area ul li.pagi.disabled span {
	color: #ccc
}

.dokan-product-listing-area .pagination-wrap ul li.pagi-previous,
.pagination-area ul li.pagi-previous {
	float: left
}

.dokan-product-listing-area .pagination-wrap ul li.pagi-previous i,
.pagination-area ul li.pagi-previous i,
.pagination-area ul li.pagi-previous span.fa {
	margin-right: 8px
}

.dokan-product-listing-area .pagination-wrap ul li.pagi-next,
.pagination-area ul li.pagi-next {
	float: right
}

.dokan-product-listing-area .pagination-wrap ul li.pagi-next i,
.pagination-area ul li.pagi-next i,
.pagination-area ul li.pagi-next span.fa {
	margin-left: 8px
}
.axil-product > .thumbnail .label-block .onsale {
    background-color: var(--color-primary);
    line-height: 1;
    padding: 6px 10px 5px;
    font-size: 12px;
    font-weight: 700;
    color: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0 8px 16px 0 rgba(53, 119, 240, 0.3);
    border: none;
    height: inherit;
}

/* Remove default "clear" at position 5, 9, etc. This is for 4 columns */
 
.woocommerce-product-gallery .flex-control-thumbs li:nth-child(5n+1) {
    clear: none;
}
 
/* Add new "clear" at position 6, 11, etc. This is for 5 columns */
 
.woocommerce-product-gallery .flex-control-thumbs li:nth-child(6n+1) {
    clear: left;
}
/* Add new CSS for 5 columns */
 
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-6 .flex-control-thumbs li {
	width: 15.1%;
	margin-right: 10px;
	margin-top: 10px;
	float: left;
	border-radius: 4px;
    overflow: hidden;
}
 
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-6 .flex-control-thumbs li:nth-child(6n) {
    margin-right: 0;
}

  .woocommerce  div.product .single-product-8 .woocommerce-product-gallery__image {
    overflow: hidden;
    border-radius: 4px;
}
 
 .woocommerce  div.product .single-product-8 div.images img {
    display: block;
    width: 100%;
    height: auto;
    box-shadow: none;
    border-radius: 4px;
}
.wpcpv-item { 
	background: var(--color-secondary);
	border-radius: 100%;
 
}
 .woocommerce  div.product .single-product-8 .axil-product-lable .woocommerce-product-gallery__wrapper .wpcpv-item { 
	background: var(--color-secondary);
	border-radius: 100%;
	cursor: pointer;
	z-index: 9;
	overflow: hidden;
	width: 34px;
    height: 34px;
    line-height: 32px;
}
 .woocommerce  div.product .single-product-8 .axil-product-lable .woocommerce-product-gallery__wrapper .wpcpv-item:before {
    font-size: 20px;
	width: 34px;
	height: 34px;
	line-height: 32px;
    
}
.wpcpv-item.wpcpv-item-image{
	background: var(--color-secondary);
    border-radius: 100%;
    color: #fff;
}
#chaty-widget-0 .chaty-widget {
    right: auto !important;
    left: 71px !important;
}
#chaty-widget-0 .chaty-widget {
    bottom: 11px !important;
}


