<?php
/*
Plugin Name: Axil Elements
Plugin URI: https://www.axilthemes.com/axil-elements
Description: Axil Elements Plugin for eTrade Theme
Version: 1.0
Author: AxilThemes
Author URI: https://www.axilthemes.com
*/

if (!defined('ABSPATH')) exit;

if (!defined('ETRADE_ELEMENTS')) {
    $plugin_data = get_file_data(__FILE__, array('version' => 'Version'));
    define('ETRADE_ELEMENTS', $plugin_data['version']);
    define('ETRADE_ELEMENTS_SCRIPT_VER', (WP_DEBUG) ? time() : ETRADE_ELEMENTS);
    define('ETRADE_ELEMENTS_THEME_PREFIX', 'etrade');
    define('ETRADE_ELEMENTS_BASE_DIR', plugin_dir_path(__FILE__));
    
    /* Check if WooCommerce active */
    defined('ETRADE_ELEMENTS_ACTIVED') or define('ETRADE_ELEMENTS_ACTIVED', (bool) function_exists('WC'));
     define('ETRADE_ELEMENTS_BASE_URL', plugins_url('/', __FILE__));
}

class etrade_elements
{

    public $plugin = 'etrade-elements';
    public $action = 'etrade_theme_init';
    protected static $instance;

    public function __construct()
    {
        add_action('plugins_loaded', array($this, 'load_textdomain'), 20);
        add_action($this->action, array($this, 'axil_after_theme_loaded')); 
    }

    public static function instance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }
        return self::$instance;
    }
 

    public function axil_after_theme_loaded()
    {
       
        require_once ETRADE_ELEMENTS_BASE_DIR . 'lib/wp-svg/init.php'; // SVG support
        require_once ETRADE_ELEMENTS_BASE_DIR . 'lib/navmenu-icon/init.php'; // Navmenu icon support
        include_once(ETRADE_ELEMENTS_BASE_DIR. '/include/custom-post.php');
        include_once(ETRADE_ELEMENTS_BASE_DIR. '/include/social-share.php');
        include_once(ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/custom-widget-register.php');
        include_once(ETRADE_ELEMENTS_BASE_DIR. '/include/common-functions.php');
        include_once(ETRADE_ELEMENTS_BASE_DIR. '/include/allow-svg.php');
        include_once(ETRADE_ELEMENTS_BASE_DIR. '/ajax_handler.php');
    
        if (did_action('elementor/loaded')) {
            require_once ETRADE_ELEMENTS_BASE_DIR . 'elementor/init.php'; // Elementor
            require_once ETRADE_ELEMENTS_BASE_DIR . 'elementor/helper.php'; // Elementor
        } 
       
      include ETRADE_ELEMENTS_BASE_DIR . 'include/scripts.php';
      include ETRADE_ELEMENTS_BASE_DIR . 'include/ajax_requests.php';
 
        if ( class_exists( 'WooCommerce' ) ) {
                 require_once ETRADE_ELEMENTS_BASE_DIR . 'lib/woocommerce-attribute-functions.php';
        }
             
        
    }
 

    public function load_textdomain()
    {
        load_plugin_textdomain($this->plugin, false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
}
etrade_elements::instance();