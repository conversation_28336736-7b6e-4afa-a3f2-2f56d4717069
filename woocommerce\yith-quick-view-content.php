<?php
/**
 * Quick view content.
 *
 * <AUTHOR>
 * @package YITH WooCommerce Quick View
 * @version 1.0.0
 */

defined( 'YITH_WCQV' ) || exit; // Exit if accessed directly.

while ( have_posts() ):
    the_post();
    ?> 
	<div class="product">
		<div id="product-<?php the_ID();?>" <?php post_class( 'product' );?>>
			<div class="axil-single-product-area bg-color-white single-product-1">
				<div class="single-product-thumb mb--40">
					<div class="container">
						<div class="row">
							<div class="col-lg-6 mb--40">
								<div class="row">
									<?php
										woocommerce_show_product_popup_images();
										woocommerce_show_product_popup_thumbnails();
										?>
								</div>
							</div>
							<div class="col-lg-6 mb--40">
								<div class="single-product-content">
									<div class="inner">
										<?php
											woocommerce_template_single_title();
											woocommerce_template_single_rating();
											woocommerce_template_single_price();
											woocommerce_template_single_meta();
											woocommerce_template_single_excerpt();
											woocommerce_template_single_add_to_cart();
											?>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div> 
<?php
endwhile; // end of the loop.
