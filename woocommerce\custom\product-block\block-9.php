<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
$id = $product->get_id();
// Product variation attributes
$attributes_escaped = function_exists( 'wooc_template_loop_attributes' ) ? wooc_template_loop_attributes() : null;
$has_color_pick = "";
$has_color_pick .= ( $attributes_escaped ) ? ' has-color-pick' : '';
$style = '';
$class = '';
if ( $block_data['product_display_hover'] ) {

    $attachment_ids = $product->get_gallery_image_ids();
    if ( $attachment_ids && isset( $attachment_ids[0] ) ) {
        $product_thumbnail_second = wp_get_attachment_image_src( $attachment_ids[0], 'thumb_size' );
    }
    if ( isset( $product_thumbnail_second[0] ) ) {
        $style = 'background-image:url(' . $product_thumbnail_second[0] . ')';
        $class = ' has-gallery-image';

    }
}
$term_list = wp_get_post_terms( $product->get_id(), 'product_cat', array( 'fields' => 'names' ) );
$cat_id = (int) $term_list[0];

?>
<div class="axil-product product-style-eight product-list-style-3 <?php echo esc_attr( $has_color_pick ); ?> <?php echo esc_attr( $class ); ?>">
    <div class="thumbnail">
        <?php echo WooC_Functions::get_product_thumbnail_link2( $product, $block_data['thumb_size'] ); ?>
     </div>
    <div class="product-content">
        <div class="inner">

            <div class="product-cate"><?php echo WooC_Functions::get_loop_category_name( $product ); ?></div>
             <?php
                if ( $block_data['display_attributes'] ):
                    if ( $attributes_escaped ):
                        $allowed_tags = wp_kses_allowed_html( 'post' );
                        echo wp_kses( $attributes_escaped, $allowed_tags );
                    endif;
                endif;
                ?>
                                <?php if ( WooC_Functions::is_product_archive() ) {
                    do_action( 'woocommerce_before_shop_loop_item_title' );
                }
                ?>
                                <h5 class="title">
                                    <a href="<?php the_permalink();?>"><?php the_title();?></a>
                                </h5>
                                <?php if ( WooC_Functions::is_product_archive() ) {
                    do_action( 'woocommerce_after_shop_loop_item_title' );
                }
                ?>
            <?php
                if ( $block_data['rating_display'] ) {
                    wc_get_template( 'loop/rating3.php' );
                }
                ?>
            <div class="product-price-variant">
                <?php if ( $price_html = $product->get_price_html() ): ?>
                    <span class="price-text">Price</span>
                    <span class="price current-price">
                        <?php echo wp_kses( $price_html, 'alltext_allow' ); ?>
                    </span>
                <?php endif;?>
            </div>
        </div>
    </div>
    <div class="label-block label-right">
        <?php wc_get_template( 'loop/sale-flash2.php' );?>
    </div>
</div>
