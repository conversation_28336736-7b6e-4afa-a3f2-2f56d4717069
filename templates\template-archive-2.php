<?php
/**
 * Template Name: Blog Archive Grid
 *
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

$args = array(
    'post_type' => "post",
);
if ( get_query_var( 'paged' ) ) {
    $args['paged'] = get_query_var( 'paged' );
} elseif ( get_query_var( 'page' ) ) {
    $args['paged'] = get_query_var( 'page' );
} else {
    $args['paged'] = 1;
}
$query = new WP_Query( $args );
global $wp_query;
$wp_query = NULL;
$wp_query = $query;
$axil_options = Helper::axil_get_options();
$axil_blog_sidebar_class = ( $axil_options['axil_blog_sidebar'] === 'no' ) || !is_active_sidebar( 'sidebar-1' ) ? 'col-lg-12 axil-post-wrapper  blog-grid-layout' : 'col-lg-8 axil-post-wrapper blog-grid-layout';
$axil_blog_grid_post_class = ( $axil_options['axil_blog_sidebar'] === 'no' ) || !is_active_sidebar( 'sidebar-1' ) ? 'col-lg-4 axil-post-wrapper' : 'col-md-6';

$axil_is_post_archive = is_home() || ( is_archive() && get_post_type() == 'post' ) ? true : false;
get_header();
?>
<div class="axil-blog-area axil-section-gapTop  axil-section-gapBottom">
    <div class="container">
        <div class="row">
            <?php if ( is_active_sidebar( 'sidebar-1' ) && $axil_options['axil_blog_sidebar'] == 'left' ) {?>
                <div class="col-lg-4 col-xl-4 mt_md--40 mt_sm--40">
                    <aside class="axil-sidebar-area">
                            <?php dynamic_sidebar();?>
                    </aside>
                </div>
            <?php }?>
            <div class="<?php echo esc_attr( $axil_blog_sidebar_class ); ?>">
                <?php
                if ( have_posts() ):
                    echo '<div class="row g-5">';
                    while ( have_posts() ): the_post();
                        echo '<div class="' . $axil_blog_grid_post_class . '">';
                        get_template_part( 'template-parts/post/content-grid', get_post_format() );
                        echo '</div>';
                    endwhile;
                    echo '</div>';
                    axil_blog_pagination();
                else:
                    get_template_part( 'template-parts/content', 'none' );
                endif;
                ?>
            </div>
            <?php if ( is_active_sidebar( 'sidebar-1' ) && $axil_options['axil_blog_sidebar'] == 'right' ) {?>
                <div class="col-lg-4 col-xl-4 mt_md--40 mt_sm--40">
                    <aside class="axil-sidebar-area">
                        <?php dynamic_sidebar();?>
                    </aside>
                </div>
            <?php }?>
        </div>
    </div>
</div>
<?php get_footer();