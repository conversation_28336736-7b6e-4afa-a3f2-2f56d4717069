/*
Included on pages where backend options are rendered
*/


/* General */

.postbox-with-fw-options>.inside,
.fw-postbox>.inside {
	padding: 0 0 5px !important;
	margin: 10px 0 0 !important;
	position: relative;
}

.fw-backend-option {
	opacity: 0;

	transition: opacity ease 0.3s;
	-webkit-transition: opacity ease 0.3s;
}

.fw-backend-option.initialized {
	opacity: 1;
}

.fw-backend-option .fw-backend-option-input input[type="text"],
.fw-backend-option .fw-backend-option-input input[type="password"],
.fw-backend-option .fw-backend-option-input select,
.fw-backend-option .fw-backend-option-input textarea {
	width: 100%;
	max-width: 100%;
}

.fw-backend-postboxes.metabox-holder,
#wpbody-content .fw-backend-postboxes.metabox-holder {
	padding-top: 0;
}


/* Tabs */

.fw-options-tabs-list ul,
.fw-options-tabs-list ul li {
	margin: 0;
	padding: 0;
}

.fw-options-tabs-wrapper {
	opacity: 0;

	transition: opacity ease 0.3s;
	-webkit-transition: opacity ease 0.3s;
}

.fw-options-tabs-wrapper.ui-tabs {
	opacity: 1;
}

.fw-options-tabs-wrapper>.fw-options-tabs-list {
	border-bottom: 1px solid #CCC;
	/*padding: 0 10px;*/
}

.fw-options-tabs-wrapper>.fw-options-tabs-list ul,
.fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
	padding: 0;
	margin: 0;
}

.post-type-product .fw-options-tabs-wrapper {
	border: none;
	padding: 10px 0;
}

.post-type-product .fw-options-tabs-wrapper>.fw-options-tabs-list ul,
.post-type-product .fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
	padding: 0 !important;
	margin: 0 !important;
	background: none !important;
	border: none !important;
}

.fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
	float: left;
}

body.rtl .fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
	float: right;
	margin-right: 0px;
	margin-left: 6px;
}

.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab {
	padding: 6px 12px;
	font-weight: 600;
	font-size: 15px;
	line-height: 24px;
	text-decoration: none;
	color: #000;
	border-bottom: 1px solid #ccc;
	margin-top: 0;
	margin-bottom: -1px;
}

.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab:hover {
	color: #000;
	background-color: #f1f1f1;
}

.postbox .fw-options-tabs-wrapper>.fw-options-tabs-list {
	padding: 10px 10px 0 10px;
}

.postbox .fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab {
	background-color: #f1f1f1;
}

.postbox .fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab:hover {
	background-color: #f8f8f8;
}

.postbox .fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
	border-bottom-color: #fff;
}

.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
	color: #222;
	background: 0 0;
	border-bottom-color: #f1f1f1;
}

.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab:hover {
	background: 0 0;
}

.fw-options-tabs-list ul li a.nav-tab:focus {
	outline: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents,
#wpbody-content .metabox-holder.fw-options-tabs-contents {
	padding-top: 0;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents {
	margin-top: 20px;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list {
	padding: 0 0 0 5px;
	border-bottom-width: 0;
}

body.rtl .fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list {
	padding: 0 5px 0 0;
}

.postbox .fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list {
	padding: 0 0 0 25px;
}

body.rtl .postbox .fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list {
	padding: 0 25px 0 0;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li:after {
	content: '|';
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li:last-child:after {
	content: none;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul>li {
	float: left;
	color: #666;
	margin-right: 0;
}

body.rtl .fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul>li {
	float: right;
	margin-left: 0;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab {
	background: none;
	border: none;
	padding: 0;
	margin: 0 5px;
	font-size: 13px;
	line-height: 16px;
	font-weight: normal;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab.fw-wp-link {
	color: #0074a2;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li:first-child a.nav-tab {
	margin-left: 0;
}

body.rtl .fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li:first-child a.nav-tab {
	margin-left: 5px;
	margin-right: 0;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
	background: none;
	padding: 0;
	border: none;
	color: #000;
}

.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab {
	position: relative;
}

@media (max-width: 782px) {
	.fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
		float: none;
	}

	.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab {
		display: block;
		text-align: center;
	}

	.fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab {
		display: inline;
	}
}


/* Postbox */

.fw-postbox {
	opacity: 0;

	transition: opacity ease 0.3s;
	-webkit-transition: opacity ease 0.3s;
}

.fw-postbox.initialized {
	opacity: 1;
}

/* end: Postbox */


/* Side tabs */

form.fw-settings-form.fw-backend-side-tabs {
	background-color: #fff;
	-webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .05);
	box-shadow: 0 1px 2px rgba(0, 0, 0, .05);
}

.fw-backend-side-tabs .fw-options-tabs-first-level {
	background:
		#fff url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAfQAAAABCAYAAAAo/lyUAAAAIklEQVQ4T2N8/Pjxf4ZRMBoCoyEwGgKjITAaAqMhMKRDAACcIAOqH7nZlwAAAABJRU5ErkJgggAA')
		/* #e3e3e3 500x1 */
		-302px
		/* 198-500 */
		0 repeat-y;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list {
	width: 198px;
	margin: 0;
	padding: 0 0 70px;
	float: left;
	border-bottom: none;
}

body.rtl .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list {
	float: right;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents {
	margin-left: 198px !important;
	background: #fff;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner {
	width: 100%;
	min-height: 100%;
	border-right: 1px solid #e5e5e5;
	display: table;
	padding-bottom: 70px;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li {
	float: none;
	margin-right: 0;
	border: 1px solid #d3d3d3;
	border-width: 0 0 1px;
	overflow: hidden;
}

body.rtl .fw-backend-side-tabs .fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list {
	padding: 0;
}

body.rtl .fw-backend-side-tabs .fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li:first-child a.nav-tab {
	margin: 0;
}

body.rtl .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
	border-left: 1px solid #e5e5e5;
	border-right: none;
}

body.rtl .fw-backend-side-tabs .fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
	float: none;
	margin-left: 0;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li a.nav-tab {
	position: relative;
	outline: none;
	margin: 0;
	padding: 0 10px 0 20px;
	display: block;
	box-sizing: border-box;
	width: 100%;
	max-width: 100%;
	height: 43px;
	line-height: 43px;
	border: none;
	font-size: 14px;
	font-weight: 400;
	color: #333;
	-webkit-box-shadow: none;
	box-shadow: none;
}

body.branch-4-0 .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li a.nav-tab,
body.branch-4-1 .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li a.nav-tab,
body.branch-4-2 .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li a.nav-tab,
body.branch-4-3 .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li a.nav-tab {
	height: 44px;
	line-height: 44px;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list .ui-tabs-nav a:hover {
	background: #dadada;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li.ui-state-active {
	border-color: #b1b1b1;
	margin-top: -1px;
	border-width: 1px 0;
	position: relative;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li.ui-state-active:first-child {
	margin-top: 0;
	border-top-width: 0;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
	font-weight: 600;
	margin: 0;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li.ui-state-active a.nav-tab,
.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li.ui-state-active a.nav-tab:hover {
	background: #bdbdbd;
	color: #fff;
}

body.rtl .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents {
	margin-left: 0 !important;
	margin-right: 198px !important;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents .fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
	border-bottom-color: #fff;
}

.fw-backend-side-tabs .fw-options-tabs-list ul li:focus {
	outline: none;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list {
	padding: 0;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li:after {
	content: none;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul {
	padding: 0;
	background: #f7f7f7;
	border-width: 1px 0;
	clear: both;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul:before,
.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul:after {
	display: table;
	content: " ";
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul:after {
	clear: both;
	margin-bottom: -1px;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul {
	border-bottom: 1px solid #e5e5e5;
}

body.branch-4-0 .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li,
body.branch-4-1 .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li,
body.branch-4-2 .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li,
body.branch-4-3 .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
	border-bottom: 1px solid #e5e5e5;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
	position: relative;
	border-right: 1px solid #e5e5e5;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active:after {
	height: 1px;
	background: #fff;
	width: 100%;
	display: block;
	content: ' ';
	position: absolute;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab,
.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
	margin: 0;
	padding: 0 24px;
	border: none;
	height: 44px;
	line-height: 44px;
	font-size: 14px;
	-webkit-box-shadow: none;
	box-shadow: none;
	outline: none;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab {
	background: transparent;
	color: #000;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
	background: #fff;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul>li a.nav-tab:hover {
	color: #0074a2;
}

.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents,
.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-contents {
	margin-top: 0;
}

/* hide last option border */
.fw-backend-side-tabs .fw-options-tabs-wrapper>.fw-options-tabs-contents>.fw-inner:after {
	content: ' ';
	display: block;
	width: 100%;
	height: 1px;
	border-bottom: 1px solid #FFFFFF;
	margin-top: -2px;
	position: relative;
}

.fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox {
	border: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox.closed {
	margin-bottom: 0;
}

.fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox>.hndle {
	position: relative;
	padding: 0 25px;
	height: 46px;
	line-height: 45px;
	border-bottom: none !important;
	font-size: 17px;
	color: #0074a2;
}

body.branch-4-0 .fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox>.hndle,
body.branch-4-1 .fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox>.hndle,
body.branch-4-2 .fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox>.hndle,
body.branch-4-3 .fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox>.hndle {
	height: 47px;
}

.fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox>.hndle:after {
	content: "";
	display: block;
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	height: 1px;
	border-top: 1px solid #eee;
	border-bottom: 1px solid #eee;
	z-index: 1;
}

/*.fw-backend-side-tabs .fw-backend-postboxes > .fw-postbox > .handlediv {
	display: none;
}*/

.fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox>.handlediv:before {
	top: 4px;
}

.fw-backend-side-tabs input[type="submit"]:focus {
	outline: none;
}

@media (max-width: 782px) {
	.fw-backend-side-tabs .fw-options-tabs-first-level {
		background-image: none;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list {
		width: 100%;
		text-align: center;
		float: none;
		padding: 0;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-list ul li {
		margin-top: 0;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents {
		float: none;
		margin-left: 0 !important;
	}

	body.rtl .fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents {
		margin-right: 0 !important;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner {
		padding-bottom: 0;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul {
		height: auto;
		border-bottom-width: 0;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
		float: none;
		width: 100%;
		text-align: center;
		border-right-width: 0;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active:after {
		display: none;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
		margin-bottom: 0;
		border-bottom-width: 0;
		display: block;
	}

	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li a.nav-tab,
	.fw-backend-side-tabs .fw-options-tabs-first-level>.fw-options-tabs-contents>.fw-inner>.fw-options-tab>.fw-options-tabs-wrapper>.fw-options-tabs-list ul li.ui-state-active a.nav-tab {
		width: 100%;
		height: auto;
		padding: 0;
		display: block;
	}

	.fw-backend-side-tabs .fw-backend-postboxes>.fw-postbox>.handlediv:before {
		top: 2px;
	}
}

@media (max-width: 782px) {
	body.rtl .fw-options-tabs-wrapper>.fw-options-tabs-list ul li {
		float: none;
	}
}


/* form header */

.fw-backend-side-tabs .fw-settings-form-header {
	background-color: #0074a2;
	margin-top: 20px;
	opacity: 0;
	transition: opacity ease 0.3s;
	-webkit-transition: opacity ease 0.3s;
}

/* make it match with admin color themes */
body.admin-color-light .fw-backend-side-tabs .fw-settings-form-header {
	background-color: #777
}

body.admin-color-light .fw-backend-side-tabs .fw-options-tabs-first-level {
	background: #ddd
}

body.admin-color-blue .fw-backend-side-tabs .fw-settings-form-header {
	background-color: #4796b3
}

body.admin-color-coffee .fw-backend-side-tabs .fw-settings-form-header {
	background-color: #46403c
}

body.admin-color-ectoplasm .fw-backend-side-tabs .fw-settings-form-header {
	background-color: #413256
}

body.admin-color-midnight .fw-backend-side-tabs .fw-settings-form-header {
	background-color: #26292c
}

body.admin-color-ocean .fw-backend-side-tabs .fw-settings-form-header {
	background-color: #627c83
}

body.admin-color-sunrise .fw-backend-side-tabs .fw-settings-form-header {
	background-color: #be3631
}

.fw-backend-side-tabs .fw-settings-form-header.initialized {
	opacity: 1;
}

.fw-backend-side-tabs .fw-settings-form-header a:focus {
	outline: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.fw-backend-side-tabs .fw-settings-form-header>div {
	padding: 20px 20px 20px 30px;
}

.fw-backend-side-tabs .fw-settings-form-header h2 {
	position: relative;
	font-size: 27px;
	font-weight: 300;
	line-height: 1;
	color: #fff;
	padding: 0;
	margin: 0;
}

.fw-backend-side-tabs .fw-settings-form-header h2 small {
	margin-left: 9px;
	font-size: 12px;
	font-weight: 300;
	color: #fff;
	opacity: .6;
	white-space: nowrap;
}

.fw-backend-side-tabs .fw-settings-form-header h2 a,
.fw-backend-side-tabs .fw-settings-form-header h2 a:hover {
	text-decoration: none;
}

.fw-backend-side-tabs .fw-settings-form-header h2 a:hover small {
	opacity: 1;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons {
	text-align: right;
}

body.rtl .fw-backend-side-tabs .fw-settings-form-header .form-header-buttons {
	text-align: left;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons input {
	-webkit-box-shadow: none;
	box-shadow: none;
	text-shadow: none;
	vertical-align: middle;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons input:active {
	vertical-align: middle;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons .submit-button-separator {
	display: inline-block;
	vertical-align: middle;
	margin: 0 12px;
	color: #358eb6;
	background-color: rgb(255, 255, 255);
	background-color: rgba(255, 255, 255, 0.5);
	height: 14px;
	width: 1px;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons>* {
	line-height: 26px;
	vertical-align: middle;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons .submit-button-reset {
	padding: 0;
	height: 26px;
	color: #fff;
	background-color: transparent;
	border-width: 0;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons .submit-button-reset:hover {
	text-decoration: underline;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons .submit-button-save {
	padding: 0 18px;
	height: 26px;
	line-height: 24px;
	background: #fff;
	border-color: transparent;
	color: #0074a2;
	-webkit-border-radius: 2px;
	border-radius: 2px;
}

.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons .submit-button-save:hover {
	background: #f5f5f5;
}

@media (max-width: 782px) {
	.fw-backend-side-tabs .fw-settings-form-header {
		text-align: center;
	}

	.fw-backend-side-tabs .fw-settings-form-header .form-header-buttons,
	body.rtl .fw-backend-side-tabs .fw-settings-form-header .form-header-buttons {
		text-align: inherit;
	}

	.fw-backend-side-tabs .fw-settings-form-header>div:first-child {
		padding-bottom: 0;
	}
}

/* end: form header */

/* footer buttons */

.fw-backend-side-tabs .form-footer-buttons {
	position: relative;
	min-height: 70px;
	border-top: 1px solid #e5e5e5;
	background: #f7f7f7;

	-webkit-animation: fwFadeIn 3s ease-in;
	-moz-animation: fwFadeIn 3s ease-in;
	-ms-animation: fwFadeIn 3s ease-in;
	-o-animation: fwFadeIn 3s ease-in;
	animation: fwFadeIn 3s ease-in;
}

.fw-backend-side-tabs .fw-options-tabs-wrapper+.form-footer-buttons {
	margin: -71px 0 0 198px;
}

.fw-backend-side-tabs .fw-backend-option+.form-footer-buttons,
.fw-backend-side-tabs .fw-backend-postboxes+.form-footer-buttons {
	margin-top: -1px;
	/* hide last option border */
}

.fw-backend-side-tabs .form-footer-buttons input[type="submit"] {
	margin: 20px 0 0;
	float: left;
	-webkit-box-shadow: none;
	box-shadow: none;
	-webkit-border-radius: 0;
	border-radius: 2px;
}

body.rtl .fw-backend-side-tabs .form-footer-buttons input[type="submit"] {
	float: right;
	margin-left: 0;
	margin-right: 26px;
}

.fw-backend-side-tabs .form-footer-buttons input[type="submit"].button-primary {
	margin-left: 26px;
}

.fw-backend-side-tabs .form-footer-buttons input[type="submit"].button-secondary {
	margin-left: 13px;
}


body.rtl .fw-backend-side-tabs .fw-options-tabs-wrapper+.form-footer-buttons {
	margin: -71px 198px 0 0;
}

@media (max-width: 782px) {
	.fw-backend-side-tabs .fw-options-tabs-wrapper+.form-footer-buttons {
		margin: -1px 0 0;
		/* hide last option border */
	}

	body.rtl .fw-backend-side-tabs .fw-options-tabs-wrapper+.form-footer-buttons {
		margin: -1px 0 0;
	}
}

/* end: footer buttons */

/* end: Side tabs */

/* end: Tabs */


/* Boxes */

/* Fixes for edit post page */

form#post .fw-options-tabs-wrapper>.fw-options-tabs-contents .fw-backend-postboxes {
	padding: 0 10px;
}

/* end: Fixes for edit post page */

/* Fixes postboxes */

.fw-postbox-without-name>.hndle,
.fw-postbox-without-name>.handlediv,
.js .fw-postbox-without-name>.hndle,
.js .fw-postbox-without-name>.handlediv {
	display: none;
}

.fw-postbox .hndle {
	cursor: pointer !important;
	/* to rewrite .js .postbox .hndle */
}

.fw-postbox:not(.initialized) .hndle {
	display: none;
}

.fw-postbox .hndle .fw-html-before-title small:not(.dashicons),
.fw-postbox .hndle .fw-html-after-title small:not(.dashicons) {
	font-size: medium !important;
}

.fw-postbox .hndle .fw-html-before-title .dashicons,
.fw-postbox .hndle .fw-html-after-title .dashicons {
	width: auto;
	height: auto;
	color: #AAA;
	/* copied from .handlediv */
}

.fw-postbox .hndle .fw-html-before-title .dashicons:hover,
.fw-postbox .hndle .fw-html-after-title .dashicons:hover {
	color: #777;
	/* copied from .handlediv */
}

/* we do not use .meta-box-sortables because of the glitches that appears from /wp-admin/js/postbox.js:130 */
/* begin copy from: .js .meta-box-sortables .postbox .handlediv */

.fw-postbox .handlediv:before {
	top: 0;
	right: 0;
	padding: 8px 9px 8px 7px;
	font: 400 20px/1 dashicons;
	speak: none;
	display: inline-block;
	position: relative;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-decoration: none !important;
}

body.rtl .fw-postbox .handlediv:before {
	left: 12px;
	right: auto;
	padding: 8px 21px 12px 7px;
}

.fw-postbox .handlediv:before {
	content: '\f142';
	color: #ccc;
}

.fw-postbox .handle-order-higher,
.fw-postbox .handle-order-lower {
	display: none !important;
}

.rtl .fw-postbox .postbox-header .hndle {
	justify-content: right;
}

.rtl .fw-option-type-addable-box>.fw-option-boxes>.fw-option-box .hndle .fw-html-after-title {
	right: auto;
	left: 43px;
	background: transparent;
}

.fw-postbox.closed .handlediv:before {
	content: '\f140';
}

.fw-postbox .toggle-indicator {
	display: none;
	/* fixes https://github.com/ThemeFuse/Unyson/issues/1261 */
}

/* end copy from: .js .meta-box-sortables .postbox .handlediv */

/* end: Boxes */


/* Input Options */

.fw-backend-option-design-default {
	position: relative;
	padding: 24px 27px 21px;
	border: 0px solid #eeeeee;
	border-bottom: 1px solid #eeeeee;
}

.fw-backend-option-design-default.fw-bottom-border-hidden {
	border-bottom-color: transparent;
}

.fw-force-xs .fw-backend-option-design-default {
	padding: 15px 12px;
}

.postbox-with-fw-options>.inside>.fw-backend-options-last-border-hider,
.fw-postbox>.inside>.fw-backend-options-last-border-hider {
	position: absolute;
	bottom: 5px;
	/* padding from: .fw-postbox > .inside */
	left: 0;
	width: 100%;
	border-bottom: 1px solid #FFFFFF;
}

body.rtl .postbox-with-fw-options>.inside>.fw-backend-options-last-border-hider,
body.rtl .fw-postbox>.inside>.fw-backend-options-last-border-hider {
	right: 0;
	left: auto;
}

.fw-backend-option-design-default>.fw-backend-option-input>.fw-inner,
.fw-backend-option-design-customizer>.fw-backend-option-input>.fw-inner {
	position: relative;
	max-width: 100%;

	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.fw-backend-option-design-default>.fw-backend-option-input.width-type-fixed>.fw-inner,
.fw-backend-option-design-customizer>.fw-backend-option-input.width-type-fixed>.fw-inner,
.fw-backend-option-fixed-width {
	width: 100%;
	max-width: 428px;
}

.fw-backend-option-design-default>.fw-backend-option-input.width-type-full>.fw-inner,
.fw-backend-option-design-customizer>.fw-backend-option-input.width-type-full>.fw-inner {
	width: 100%;
}

.fw-backend-option-design-default>.fw-backend-option-label label {
	font-weight: 600;
	float: left;
	margin-top: 0.1em;
	font-size: 14px;
}

body.rtl .fw-backend-option-design-default>.fw-backend-option-label label {
	float: right;
}

.fw-backend-option-design-default>.fw-backend-option-desc {
	font-style: italic;
	color: #666;
}

.fw-backend-option-design-default>.fw-backend-option-desc>.fw-inner {
	font-size: 1em;
	padding-top: 7px;
	padding-left: 1px;
}

body.rtl .fw-backend-option-design-default>.fw-backend-option-desc>.fw-inner {
	padding-left: inherit;
	padding-right: 1px;
}


.fw-backend-option-design-customizer {
	padding: 5px 0 10px;
}

.fw-backend-option-design-customizer>.fw-backend-option-desc {
	padding-bottom: 6px;
}

.fw-backend-option-design-customizer label,
.fw-backend-option-design-customizer label .customize-control-title {
	line-height: inherit;
}

.fw-backend-option-design-customizer>.fw-backend-option-label {
	padding-bottom: 5px;
}

.fw-backend-option-design-customizer>.fw-backend-option-label .fw-option-help {
	display: inline-block;
	vertical-align: middle;
}

.fw-backend-option-design-customizer>.fw-backend-option-label .customize-control-title {
	display: inline-block;
}

/* Options Groups */

.fw-backend-options-group:not(.show-borders) {
	border-bottom: 1px solid #eeeeee;
}

.fw-backend-options-group:not(.show-borders)>.fw-backend-option {
	border-bottom-width: 0;
}

.fw-backend-options-group:not(.show-borders)>.fw-backend-option:not(:last-child) {
	padding-bottom: 0;
}

/* .force-border .force-no-border */


/* end: Options Groups */


@media (max-width: 782px) {
	.fw-backend-option-design-default>.fw-backend-option-label>.fw-inner {
		padding: 0 0 10px;
	}

	.fw-backend-option-design-default.with-help>.fw-backend-option-label label {
		margin-right: 8px;
	}

	body.rtl .fw-backend-option-design-default.with-help>.fw-backend-option-label label {
		margin-right: 0px;
		margin-left: 8px;
	}

	.fw-backend-option-design-default>.fw-backend-option-label .fw-option-help {
		margin-top: 0;
	}
}

@media (min-width: 783px) {
	.fw-backend-option-design-default>.fw-backend-option-label>.fw-inner {
		padding: 3px 20px 0 0;
	}

	body.rtl .fw-backend-option-design-default>.fw-backend-option-label>.fw-inner {
		padding: 3px 0 0 20px;
	}
}


/* Options inside option fixes */

.fw-backend-option-type-html .fw-backend-option-design-default>.fw-backend-option-input {
	max-width: 100%;
}

.fw-backend-option-type-html .fw-backend-option-design-default {
	/* options inside html option have double padding */
	padding: 0;
}

/* end: Options inside option fixes */


/* .fw-force-xs have design like on mobile */

.fw-force-xs .fw-options-tabs-first-level>.fw-options-tabs-list ul li {
	float: none;
}

.fw-force-xs .fw-options-tabs-first-level>.fw-options-tabs-list ul li a {
	display: block;
}

.fw-force-xs .fw-backend-option-design-default>.fw-backend-option-label>.fw-inner,
body.rtl .fw-force-xs .fw-backend-option-design-default>.fw-backend-option-label>.fw-inner {
	padding: 0 0 10px;
}

.fw-force-xs .fw-backend-option-design-default.with-help>.fw-backend-option-label label {
	margin-right: 8px;
}

body.rtl .fw-force-xs .fw-backend-option-design-default.with-help>.fw-backend-option-label label {
	margin-right: 0px;
	margin-left: 8px;
}

.fw-force-xs .fw-backend-option-design-default>.fw-backend-option-label .fw-option-help {
	margin-top: 0;
}

/* end .fw-force-xs */


/* help icon */

.fw-backend-option .fw-option-help {
	cursor: pointer;
	color: #AAAAAA;
	/* copied from .handlediv */
}

.fw-backend-option .fw-option-help:hover,
.fw-backend-option .fw-option-help.active {
	color: #333333;
}

.fw-backend-option-design-default.with-help>.fw-backend-option-input>.fw-inner>.fw-option-help {
	position: absolute;
	top: 0;
	right: 0px;
}

body.rtl .fw-backend-option-design-default.with-help>.fw-backend-option-input>.fw-inner>.fw-option-help {
	right: auto;
	left: 0;
}

.fw-backend-option-design-default.with-help>.fw-backend-option-label>.fw-inner>.fw-option-help {
	float: left;
}

body.rtl .fw-backend-option-design-default.with-help>.fw-backend-option-label>.fw-inner>.fw-option-help {
	float: right;
}

@media (min-width: 783px) {
	.fw-backend-option-design-default.with-help>.fw-backend-option-input>.fw-inner {
		padding-right: 26px;
	}

	body.rtl .fw-backend-option-design-default.with-help>.fw-backend-option-input>.fw-inner {
		padding-right: 0;
		padding-left: 26px;
	}

	.fw-force-xs .fw-backend-option-design-default.with-help>.fw-backend-option-input>.fw-inner {
		padding-right: 0;
	}

	body.rtl .fw-force-xs .fw-backend-option-design-default.with-help>.fw-backend-option-input>.fw-inner {
		padding-right: inherit;
		padding-left: 0;
	}
}

/* end: help icon */


/* Edit Term page */

#edittag td.fw-backend-container-content {
	padding-left: 0;
}


#edittag>.fw-backend-option,
#edittag>.fw-backend-options-group>.fw-backend-option,
#edittag>.fw-backend-option-type-multi .fw-backend-option,
#edittag>.fw-backend-option-type-multi-picker .fw-backend-option {
	padding: 15px 0;
	border-bottom-width: 0;
}

@media (min-width: 783px) {

	#edittag>.fw-backend-option>.fw-backend-option-label,
	#edittag>.fw-backend-options-group>.fw-backend-option>.fw-backend-option-label,
	#edittag>.fw-backend-option-type-multi .fw-backend-option>.fw-backend-option-label,
	#edittag>.fw-backend-option-type-multi-picker .fw-backend-option>.fw-backend-option-label {
		width: 219px;
	}

	#edittag>.fw-backend-option>.fw-backend-option-input,
	#edittag>.fw-backend-options-group>.fw-backend-option>.fw-backend-option-input,
	#edittag>.fw-backend-option-type-multi .fw-backend-option>.fw-backend-option-input,
	#edittag>.fw-backend-option-type-multi-picker .fw-backend-option>.fw-backend-option-input {
		width: calc(100% - 219px);
	}

	#edittag>.fw-backend-option>.fw-backend-option-desc,
	#edittag>.fw-backend-options-group>.fw-backend-option>.fw-backend-option-desc,
	#edittag>.fw-backend-option-type-multi .fw-backend-option>.fw-backend-option-desc {
		margin-left: 219px;
	}
}

#edittag .fw-backend-option input[type=checkbox],
#edittag .fw-backend-option input[type=radio] {
	width: auto;
}

@media (max-width: 782px) {

	#edittag .fw-backend-option input[type=checkbox],
	#edittag .fw-backend-option input[type=radio] {
		height: 25px;
		width: 25px;
	}
}

#edittag .fw-backend-option .description,
#edittag .fw-backend-option .fw-backend-option-desc .fw-inner {
	font-size: 14px;
}

/* end: Edit Term page */

/* Add Term page */

#addtag .fw-backend-option {
	padding-left: 0;
	padding-right: 0;
}

#addtag .fw-postbox .fw-backend-option {
	padding-left: 12px;
	padding-right: 12px;
}

/* end: Add Term page */

/* modal fix https://github.com/ThemeFuse/Unyson/issues/1353 */

@media (min-width: 1200px) {
	.media-frame-content .fw-backend-option>.fw-backend-option-label.fw-col-lg-2 {
		width: 25%;
	}

	.media-frame-content .fw-backend-option>.fw-backend-option-input.fw-col-lg-10 {
		width: 75%;
	}

	.media-frame-content .fw-backend-option>.fw-backend-option-desc.fw-col-lg-offset-2 {
		margin-left: 25%;
		width: 75%;
	}
}

/* end: modal fix */

/* Add Selectize support for all selects with fw-selectize class */

.fw-selectize.selectize-control.single .selectize-input:before {
	height: 0;
	background: none;
}

.fw-selectize.selectize-control.single .selectize-input:after,
.fw-selectize.selectize-control.single .selectize-input.dropdown-active:after {
	right: 8px;
	margin-top: -2px;
	border-width: 6px 3px 0 3px;
	border-color: #000000 transparent transparent transparent;
}

.fw-selectize.selectize-control>.selectize-input,
.fw-selectize.selectize-control>.selectize-input.has-items,
.fw-selectize.selectize-control>.selectize-input.input-active {
	display: block !important;
	padding: 0;
	margin: 1px;
	border-radius: 0;
	border: 1px solid #ddd;
	color: #333333;

	-webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, .07);
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, .07);
}

.fw-selectize.selectize-control>.selectize-input>input {
	padding: 2px 5px !important;
	height: auto;
	font-size: 14px;
	vertical-align: middle;
}

.fw-modal .media-frame .fw-selectize.selectize-control>.selectize-input>input {
	height: 26px;
}

.fw-selectize.selectize-control .selectize-input>div {
	vertical-align: middle;
}

.fw-selectize.selectize-control.single .selectize-input>div {
	padding: 2px 5px;
	font-size: 14px;
}

.fw-modal .media-frame .fw-selectize.selectize-control.single .selectize-input>div {
	padding: 4px 5px;
}

.fw-selectize.selectize-control.multi .selectize-input>div {
	margin: 2px;
	padding: 0 3px;
}

.fw-modal .media-frame .fw-selectize.selectize-control.multi .selectize-input>div {
	padding: 2px 5px;
}

.fw-selectize.selectize-control .selectize-dropdown {
	margin: 0 1px;
	border: none;
	top: 33px;
}

.fw-selectize.selectize-control .selectize-dropdown .selectize-dropdown-content {
	border: 1px solid #ddd;
	max-height: 204px;
}

.fw-selectize.selectize-control .selectize-dropdown .selectize-dropdown-content div {
	height: 30px;
	background-color: #fff;
	cursor: pointer;
	border-bottom: 1px solid #ddd;
	line-height: 30px;
}

.fw-selectize.selectize-control .selectize-dropdown .selectize-dropdown-content div:hover {
	background-color: #f0f0f0;
}

/* end: Selectize */