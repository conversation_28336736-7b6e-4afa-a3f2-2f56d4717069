<?php
class WoocScriptsmain
{

    protected static $instance = null;

    public function __construct()
    {

        add_action('wp_enqueue_scripts', array($this, 'etrade_elements_assets_scripts'), 20);
       // add_action('wp_head', array(&$this, 'custom_cart_functionality'));

    }

    public static function instance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }
        return self::$instance;
    }
  
  public function custom_cart_functionality(){
   
  ?> 
    <div class="wooc-mini-cart-container cart-dropdown">
        <div class="cart-content-wrap">
          <div class="cart-header">
           <h2 class="header-title"><?php echo esc_html__( 'Cart review.', 'etrade-elements' ); ?></h2> 
           <button class="close cart-close sidebar-close"><i class="fas fa-times"></i></button>  
          </div>          
           <div class="cart-body-wrp" id="etrade-content-cart-template"></div>  
        </div>
      </div>
    <div class="wooc-overlay-cart"></div> 
  <?php 
  }

    public function etrade_elements_assets_scripts()
    {


        wp_enqueue_script('etrade-ajax-scripts', ETRADE_ELEMENTS_BASE_URL . 'assets/js/ajax-scripts.js', array('jquery'), '2.0', true);

      $this->etrade_elements_localized_scripts(); // Localization
    }

    private function etrade_elements_localized_scripts()
        {        

        $localize_data = array(
            'ajaxurl'           => admin_url('admin-ajax.php'),      
            'filter_text' => __('Filter by', 'etrade-elements'),     


        );
        wp_localize_script('etrade-ajax-scripts', 'eTradeAjaxObj', $localize_data);        
        
    }


}

WoocScriptsmain::instance();

