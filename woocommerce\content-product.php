<?php
/**
 * The template for displaying product content within loops
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.4.0
 */

defined( 'ABSPATH' ) || exit;
global $product;
// Ensure visibility.
if ( empty( $product ) || !$product->is_visible() ) {
    return;
}
$block_data = axil_product_block_data();

$product_class = axil_product_col_class();
 
?>
<div <?php wc_product_class( $product_class, $product );?>>
    <?php
do_action( 'woocommerce_before_shop_loop_item' );
    wc_get_template( "custom/product-block/blocks.php", compact( 'product', 'block_data' ) );
do_action( 'woocommerce_after_shop_loop_item' );
?>
</div>
