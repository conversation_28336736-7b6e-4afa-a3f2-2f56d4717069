<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="zxx">
<!--<![endif]-->

<head>
	<!-- Basic metas
    ======================================== -->
	<meta charset="utf-8">
	<meta name="author" content="">
	<meta name="description" content="">
	<meta name="keywords" content="">

	<!-- Mobile specific metas
    ======================================== -->
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

	<meta name="msapplication-TileColor" content="#ffffff">
	<meta name="msapplication-TileImage"
		content="https://axilweb.com/wp-content/themes/axilweb/favicon/ms-icon-144x144.png">
	<meta name="theme-color" content="#ffffff">
	<meta property="og:locale" content="en_US" />
	<meta property="og:type" content="website" />
	<meta property="og:title" content="Documentation" />
	<meta property="og:description"
		content="Axilweb is a digital agency that provides back office support services and makes web ecosystems for organizations to modernize their business process." />
	<meta property="og:url" content="https://axilweb.com/" />
	<meta property="og:site_name" content="Axilweb" />
	<meta property="fb:app_id" content="275049943141430" />
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:description"
		content="Axilweb is a digital agency that provides back office support services and makes web ecosystems for organizations to modernize their business process." />
	<meta name="twitter:title" content="Documentation" />
	<meta name="twitter:site" content="@axilweb" />
	<meta name="twitter:image" content="https://axilweb.com/wp-content/uploads/2018/11/Axilweb-Logo-Banner.png" />
	<meta name="twitter:creator" content="@axilweb" />

	<!-- Page Title
    ======================================== -->
	<title>eTrade - WordPress Documentation</title>

	<!-- links for favicon
    ======================================== -->
	<link rel="apple-touch-icon" sizes="57x57" href="assets/favicon/apple-icon-57x57.png">
	<link rel="apple-touch-icon" sizes="60x60" href="assets/favicon/apple-icon-60x60.png">
	<link rel="apple-touch-icon" sizes="72x72" href="assets/favicon/apple-icon-72x72.png">
	<link rel="apple-touch-icon" sizes="76x76" href="assets/favicon/apple-icon-76x76.png">
	<link rel="apple-touch-icon" sizes="114x114" href="assets/favicon/apple-icon-114x114.png">
	<link rel="apple-touch-icon" sizes="120x120" href="assets/favicon/apple-icon-120x120.png">
	<link rel="apple-touch-icon" sizes="144x144" href="assets/favicon/apple-icon-144x144.png">
	<link rel="apple-touch-icon" sizes="152x152" href="assets/favicon/apple-icon-152x152.png">
	<link rel="apple-touch-icon" sizes="180x180" href="assets/favicon/apple-icon-180x180.png">
	<link rel="icon" type="image/png" sizes="192x192" href="assets/favicon/android-icon-192x192.png">
	<link rel="icon" type="image/png" sizes="32x32" href="assets/favicon/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="96x96" href="assets/favicon/favicon-96x96.png">
	<link rel="icon" type="image/png" sizes="16x16" href="assets/favicon/favicon-16x16.png">
	<link rel="manifest" href="assets/favicon/manifest.json">
	<meta name="msapplication-TileColor" content="#ffffff">
	<meta name="msapplication-TileImage" content="assets/favicon/ms-icon-144x144.png">
	<meta name="theme-color" content="#ffffff">

	<!-- Icon fonts
	======================================== -->
	<link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,500i,700,700i,900" rel="stylesheet">
	<link rel="stylesheet" type="text/css" href="assets/css/fontawesome-all.min.css">

	<!-- Bootstrap -->
	<link rel="stylesheet" type="text/css" href="assets/css/vendor/bootstrap.min.css">

	<!-- Custom css -->
	<link rel="stylesheet" type="text/css" href="assets/css/main.css">


</head>

<body>
	<!--[if lte IE 9]>
        <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</p>
	<![endif]-->

	<!-- Header starts -->
	<a href="#top" class="back-to-top"><i class="fal fa-arrow-up"></i></a>
	<header class="page-header sticky-top" id="top">
		<div class="container-fluid">
			<nav class="page-nav">
				<a href="https://axilthemes.com/" class="nav-brand" target="_blank">
					<img src="assets/images/logo.png" alt="logo" height="30">
				</a>

				<div class="nav-list">
					<a href="http://support.axilthemes.com/support/" target="_blank">request support</a>
					<a href="https://themeforest.net/user/axilthemes/portfolio" target="_blank">awesome themes</a>
					<a href="#" class="toggle-navbar"><i class="far fa-bars"></i></a>
				</div>
				<!-- End of .nav-list -->
			</nav>
		</div>
		<!-- End of .container -->
	</header>
	<!-- End of .page-header -->

	<!-- End of .main-content -->
	<div class="main-content-wrapper">
		<div class="container-fluid">
			<div class="main-content p-t-40">
				<aside class="sidebar-navigation sticky-top">
					<nav class="nav">
						<div class="logo-wrapper m-b-30">
							<img src="assets/images/theme-logo.png" alt="logo">
						</div>
						<form action="#" class="search-form m-b-20">
							<div class="input-group">
								<input type="text" class="search-input" placeholder="Search item">
							</div>
						</form>
						<ol class="doc-nav">

							<li>
								<a class="nav-link" href="#section-1">Introduction</a>
								<ol class="sub-nav">
									<li><a class="nav-link" href="#section-1-1">Requirements</a></li>
									<li><a class="nav-link" href="#section-1-2">What's Included</a></li>
								</ol>
							</li>

							<li>
								<a class="nav-link" href="#section-2">WordPress Installation</a>
							</li>

							<li>
								<a class="nav-link" href="#section-3">Upload and Activate Theme</a>
								<ol class="sub-nav">
									<li><a class="nav-link" href="#section-3-1">Using Theme Uploader</a></li>
									<li><a class="nav-link" href="#section-3-2">Using FTP Clients</a></li>
								</ol>
							</li>


							<li>
								<a class="nav-link" href="#section-4">Importing Demo Contents</a>
								<ol>
									<li>
										<a class="nav-link" href="#section-4-1">Manual Demo Import</a>
										<a class="nav-link" href="#section-4-2">Where Is My Purchase Code?</a>
									</li>
								</ol>
							</li>
							<li><a class="nav-link" href="#section-5">Change Site Title and Favicon</a></li>

							<li><a class="nav-link" href="#section-6">Change Global Colors</a></li>


							<li><a class="nav-link" href="#section-7">Change Logo</a></li>


							<li><a class="nav-link" href="#section-8">Pages Default Banner</a></li>

							<li><a class="nav-link" href="#section-9">Set Home and Blog Page</a></li>

							<li><a class="nav-link" href="#section-10">Customize Menu</a></li>


							<li>
								<a class="nav-link" href="#section-11">eTrade Theme Options</a>

								<ol>
									<li>
										<a class="nav-link" href="#section-11">General</a>
										<ol>
											<li><a class="nav-link" href="#section-11">General</a></li>

											<li><a class="nav-link" href="#section-11-1-1">Login / Acount</a></li>

									</li>

								</ol>
							</li>

							<li>
								<a class="nav-link" href="#section-11-3">Header</a>
								<ol>
									<li><a class="nav-link" href="#section-11-2">Header Top</a></li>
									<li><a class="nav-link" href="#section-11-2.5">Header</a></li>
								</ol>
							</li>
							<li>
								<a class="nav-link" href="#section-11-3">Footer</a>
								<ol>
									<li><a class="nav-link" href="#section-11-3">Footer</a></li>
									<li><a class="nav-link" href="#section-11-3-2">Footer Top</a></li>
									<li><a class="nav-link" href="#section-11-3-3">footer Service Policies</a></li>
								</ol>
							</li>

							<li>
								<a class="nav-link" href="#section-11-4">Blog</a>
								<ol>
									<li><a class="nav-link" href="#section-11-4">Archive</a></li>
									<li><a class="nav-link" href="#section-11-4-1">Single</a></li>
								</ol>
							</li>

							<li>
								<a class="nav-link" href="#section-11-5">WooCommerce Settings</a>
								<ol>
									<li><a class="nav-link" href="#section-11-5">Shop</a></li>
									<li><a class="nav-link" href="#section-11-5-1">Single Product</a></li>
								</ol>
							</li>

							<li>
								<a class="nav-link" href="#section-11-8">UnderConstruction Settings</a>
							</li>
							<li>
								<a class="nav-link" href="#section-11-9">Error 404 Page Settings</a>
							</li>
							<li>
								<a class="nav-link" href="#section-11-10">Currency / Language</a>
							</li>
						</ol>
						</li>

						<li>
							<a class="nav-link" href="#section-12">Contact Form 7</a>
						</li>

						<li>
							<a class="nav-link" href="#section-13">MailChimp for WordPress</a>
						</li>

						</ol>
					</nav>
				</aside>

				<main class="main">
					<div class="filterable-wrapper">
						<section id="section-1">
							<h3 class="mt-0">1. Introduction</h3>
							<p>We have like to thank you for choosing eTrade.</p>
							<p>Based on our vast experience of developing functional and aesthetically pleasing themes,
								we are proud to introduce eTrade. It's is a
								state-of-the-art WordPress theme. We made eTrade from the ground up with flexibility in
								mind. Each element of eTrade is easily customizable, where you can make eTrade reflect
								your branding styles. The guide gives you detailed methodologies about how you can
								customize eTrade and make it fit your brand perfectly!</p>


							<p> eTrade is also made with UI/UX in mind. The easy-to-navigate features mean your users
								will always be able to find what they are looking for with much fewer clicks. Bold and
								bright color combinations also ensure that each page is unique, helping your desired
								content stand out from the rest. eTrade is a product of Axilthemes.</p>

						</section>
						<!-- End of #section-1 -->

						<section id="section-1-1">
							<h3>1.1. Requirements</h3>
							<p>There are system requirements to install and set up the eTrade theme and its components
								properly. Make sure that you are running the latest version of WordPress, PHP version
								5.6 or higher, and MySQL version 5.6 or higher. We also recommend the following PHP
								configuration limits.</p>
							<h4>Recommended PHP configuration limits</h4>
							<ul>
								<li>upload_max_filesize = 32M</li>
								<li>post_max_size = 32M</li>
								<li>max_execution_time = 600</li>
								<li>max_input_time = 600</li>
								<li>memory_limit = 256M</li>
							</ul>

						</section>
						<!-- End of #section-1-1 -->

						<section id="section-1-2">
							<h3>1.2. What's Included</h3>
							<p>After purchasing the eTrade theme on themeforest.net with your Envato account, go to your
								Download page. You can choose to download the eTrade theme only (Installable WordPress
								Theme) or the entire eTrade theme package that contains the following files:</p>
							<ul>
								<li><strong>eTrade theme:</strong> An Installable WordPress Theme zip file.</li>
								<li><strong>eTrade child-theme:</strong> A child-theme zip file and a readme.txt note
									about the child-theme.</li>
								<li><strong>Bundled Plug-ins:</strong> Pro Plugins</li>
								<li><strong>Demo Content Files:</strong> You can import demo data manually</li>
								<li><strong>Documentation:</strong> A link to this documentation.</li>
								<li>Licensing.</li>
							</ul>

						</section>
						<!-- End of #section-1-2 -->

						<section id="section-2">
							<h3>2. WordPress Installation</h3>
							<p>Please follow the instructions in the video to see how you can install WordPress on your
								hosting:</p>
							<ol>
								<li>For local host: <a href="https://www.youtube.com/watch?v=snFzbPm_RUE"
										target="_blank">https://www.youtube.com/watch?v=snFzbPm_RUE</a>
								</li>
								<li>For cPanel: <a href="https://www.youtube.com/watch?v=t-YBqV2ReR0"
										target="_blank">https://www.youtube.com/watch?v=t-YBqV2ReR0</a>
								</li>
							</ol>
						</section>
						<!-- End of #section-2 -->

						<section id="section-3">
							<h3>3. Upload and Activate Theme</h3>
							<p>When you are ready to install a theme, you must first upload the theme files and then
								activate the theme itself. The theme files can be uploaded in two ways:</p>

							<p>How to Setup and Demo Import (Video): <a href="https://youtu.be/ClCmn8ueYp0"
									target="_blank">https://youtu.be/ClCmn8ueYp0</a></p>
							<p>Create Home Page Like Demo Without Demo Import (Video): <a
									href="https://youtu.be/albB011W8lg" target="_blank">https://youtu.be/albB011W8lg</a>
							</p>
							<ul>
								<li><a class="nav-link" href="#section-3-1"><strong>Using Theme
											Uploader</strong></a></li>
								<li><a class="nav-link" href="#section-3-2"><strong> Using FTP Clients</strong></a>
								</li>
							</ul>

						</section>
						<!-- End of #section-3 -->


						<section id="section-3-1">
							<h3>3.1. Using Theme
								Uploader</h3>

							<p>Follow the steps as instructed in the images below:</p>
							<img src="assets/images/doc/3.1.1.jpg" alt="3.1.1" class="img-fluid">
							<img src="assets/images/doc/3.1.2.jpg" alt="3.1.2" class="img-fluid">
							<img src="assets/images/doc/3.1.3.jpg" alt="3.1.3" class="img-fluid">
							<img src="assets/images/doc/3.1.4.jpg" alt="3.1.4" class="img-fluid">
							<img src="assets/images/doc/3.1.5.jpg" alt="3.1.5" class="img-fluid">
							<img src="assets/images/doc/3.1.6.jpg" alt="3.1.5" class="img-fluid">
						</section>
						<!-- End of #section-3-1 -->

						<section id="section-3-2">
							<h3>3.2. Using FTP Clients</h3>
							<p>Follow the following steps to upload your theme using FTP clients:</p>
							<ol>
								<li>Use an FTP such as FileZilla and go to your WordPress installation director</li>
								<li>Navigate to /wp-content/themes/ folder and upload the theme folder there</li>
								<li>Log in to your WP Admin Dashboard and open the 'Appearance / Themes' menu</li>
								<li>You will now see the eTrade Theme listed among the other themes. Click on
									Activate and you’re done!</li>
							</ol>
						</section>
						<!-- End of #section-3-2 -->

						<section id="section-4">
							<h3>4. Importing Demo Contents</h3>
							<p>eTrade is an easy solution for the demo content you saw on our preview website.
								Please follow the instructions below:</p>
							<img src="assets/images/doc/4.1.1.jpg" alt="4.1.1" class="img-fluid">
							<img src="assets/images/doc/4.1.2.jpg" alt="4.1.2" class="img-fluid">

							<h4>⏳ After Importing Demo Content Successful 🎉</h4>
							<ol>
								<li>Go to Setting -> Permalinks</li>
								<li>Make Sure That "Post Name" Options is Selected -> Permalinks.</li>
								<li>Hit "Save Changes" Button and ready to go live site.</li>
							</ol>
							<img src="assets/images/doc/4.1.4.jpg" alt="4.1.4" class="img-fluid">
						</section>
						<!-- End of #section-4 -->

						<section id="section-4-1">
							<h3>4.1. Manual Demo Import</h3>
							<h6> 4 steps to manual demo import</h6>
							<ul>
								<li>1# Dashboard -> Tools -> Import: (WordPress) Run Importer</li>
								<li>2# Dashboard -> Plugins -> Add New: search Plugin (Customizer Reset - Export &
									Import) Run Importer</li>
								<li>2# Dashboard -> Plugins -> Add New: search Plugin (Widget Importer & Exporter) Run
									Importer</li>
								<li>2# Dashboard -> Appearance -> Theme Options -> Import / Export -> Upload file: Click
									Import</li>
							</ul>

						</section>

						<!-- End of #section-4-1 -->
						<section id="section-4-2">
							<h3>4.1. Where Is My Purchase Code?</h3>
							<p>When you buy a product from Envato you get a purchase code. To learn about how to find
								your purchase code visit the following link:</p>
							<a href="https://help.market.envato.com/hc/en-us/articles/202822600-Where-Can-I-Find-my-Purchase-Code"
								target="_blank">https://help.market.envato.com/hc/en-us/articles/202822600-Where-Can-I-Find-my-Purchase-Code</a>
						</section>
						<!-- End of #section-4-1 -->

						<section id="section-5">
							<h3>5. Change Site Title and Favicon</h3>
							<p>To change your Site title and Favicon login to wp-admin and navigate to <strong>Dashboard
									> Appearance > Customize > Site
									Identity</strong> and follow the following steps:</p>

							<ol>
								<li>Navigate To Dashbord -> Appearance -> Site Identity then change your Title.</li>
								<li>To change The Site tagline</li>
								<li>To change The favicon hit the change Image button and upload a new image.</li>
							</ol>

							<img src="assets/images/doc/5.jpg" alt="5" class="img-fluid">
						</section>
						<!-- End of #section-5 -->



						<section id="section-6">
							<h3>6. Change Global Colors</h3>
							<p>To change all global colors login to wp-admin and navigate to <strong>Dashboard >
									Appearance > Customize > Colors</strong> and follow the following steps:</p>

							<ol>
								<li>To Change global colors click on any of the select color buttons</li>
								<li>Change any color according to your color</li>
							</ol>
							<img src="assets/images/doc/6.jpg" alt="6" class="img-fluid">
						</section>
						<!-- End of #section-6 -->


						<section id="section-7">
							<h3>7. Change Logo</h3>
							<p>To change the site logo login to your wp-admin and navigate to <strong>Dashboard > Theme
									Options > General</strong> and follow the following steps:</p>

							<ol>
								<li>General Setting</li>
								<li>Select Logo Type -> Image</li>
								<li>Hit The Upload Button To upload the logo </li>
								<li>Hit The Files Button</li>
								<li>Drop your image format logo file here Or Hit The Select Files Button to upload a
									imahe</li>
							</ol>

							<img src="assets/images/doc/7.1.1.jpg" alt="7.1.1" class="img-fluid">
							<img src="assets/images/doc/7.1.2.jpg" alt="7.1.2" class="img-fluid">
 
						</section>
						<!-- End of #section-7 -->

						<section id="section-8">
							<h3>8. Pages Default Banner</h3>
							<p>To change the typography of your site navigate to <strong>Appearance > eTrade
									Options > Pages Default Banner.</strong></p>
							<img src="assets/images/doc/8.jpg" alt="8" class="img-fluid">
						</section> 
						<!-- End of #section-8 --> 

						<section id="section-9">
							<h3>9. Set Home and Blog Page</h3>
							<p>To set the default Home and Blog page please login to wp-admin and navigate to
								<strong>Dashboard > Settings > Reading</strong>
							</p>
							<img src="assets/images/doc/8.1.jpg" alt="8.1" class="img-fluid">
						</section>
						<!-- End of #section-8-1 -->

						<section id="section-10">
							<h3>10. Customize Menu</h3>
							<p>To customize the menu and megamenu please log in to wp-admin and navigate to
								<strong>Appearance >
									Menus</strong> and follow the following steps:
							</p>
							<img src="assets/images/doc/8.2.1.jpg" alt="8.2.1" class="img-fluid">
						</section>
						<!-- End of #section-8-2 -->

						<section id="section-11">
							<h3>11 eTrade Options / General</h3>
							<p>To change eTrade Options navigate to <strong>Appearance > Axil Theme Options.</strong>
							</p>
							<img src="assets/images/doc/11.jpg" alt="11" class="img-fluid">

						</section>
						<!-- End of #section-9 -->


						<section id="section-11-1-1">
							<h3>11.1.1. Login / Acount</h3>
							<p>To change Contact &amp; Social information in eTrade, navigate to <strong>Appearance >
									Axil Theme Options.</strong></p>
							<img src="assets/images/doc/11.1.1.jpg" alt="11.1.1" class="img-fluid">

						</section>
						<!-- End of #section-9 -->

						<section id="section-11-2">
							<h3>11.2.1 Header Top</h3>
							<p>To change the header top in eTrade, navigate to <strong>Appearance > Axil Theme
									Options.</strong></p>
							<img src="assets/images/doc/11.2.1.jpg" alt="11.2.1" class="img-fluid">
						</section>
						<!-- End of #section-9 -->

						<section id="section-11-2.5">
							<h3>11.2.1 Header</h3>
							<p>To change the header in eTrade, navigate to <strong>Appearance > Axil Theme
									Options.</strong></p>
							<img src="assets/images/doc/11.2.2.jpg" alt="11.2.2" class="img-fluid">
						</section>
						<!-- End of #section-9 -->


						<section id="section-11-3">
							<h3>11.3.1 Footer</h3>
							<p>To change the footer in eTrade, navigate to <strong>Appearance > Axil Theme
									Options.</strong></p>
							<img src="assets/images/doc/11.3.1.jpg" alt="11.3.1" class="img-fluid">
						</section>
						<!-- End of #section-11-3-1 -->

						<section id="section-11-3-2">
							<h3>11.3.2 Footer Top</h3>
							<p>To change the copyright notice in eTrade, navigate to <strong>Appearance > Axil Theme
									Options.</strong></p>
							<img src="assets/images/doc/11.3.2.jpg" alt="11.3.2" class="img-fluid">
						</section>
						<!-- End of #section-11-3-1 -->
						<section id="section-11-3-3">
							<h3>11.3.3 Footer Service Policies</h3>
							<p>To change the copyright notice in eTrade, navigate to <strong>Appearance > Axil Theme
									Options.</strong></p>
							<img src="assets/images/doc/11.3.3.jpg" alt="11.3.3" class="img-fluid">
						</section>
						<!-- End of #section-11-3-1 -->

						<section id="section-11-4">
							<h3>11.4.1 Blog/Archive</h3>
							<p>To customize the default Blog options in eTrade, navigate to <strong>Appearance > Axil
									Theme Options.</strong></p>
							<img src="assets/images/doc/11.4.jpg" alt="11.4" class="img-fluid">
						</section>
						<!-- End of #section-11-4 -->

						<section id="section-11-4-1">
							<h3>11.4.2 Blog/Single</h3>
							<p>To customize the default Blog options in eTrade, navigate to <strong>Appearance > Axil
									Theme Options.</strong></p>
							<img src="assets/images/doc/11.4.1.jpg" alt="11.4.1" class="img-fluid">
						</section>
						<!-- End of #section-11-4 -->\


						<section id="section-11-5">
							<h3>11.5 WooCommerce ( Shop )</h3>
							<p>To customize the default Shop page options in eTrade, navigate to <strong>Appearance > Axil
									Theme Options.</strong></p>
							<img src="assets/images/doc/11.5.jpg" alt="11.5" class="img-fluid">
						</section>
						<!-- End of #section-11-4 -->

						<section id="section-11-5-1">
							<h3>11.5.1 WooCommerce (Product)</h3>
							<p>To customize the default Shop page options in eTrade, navigate to <strong>Appearance > Axil
									Theme Options.</strong></p>
							<img src="assets/images/doc/11.5.1.jpg" alt="11.5.1" class="img-fluid">
						</section>
						<!-- End of #section-11-4 -->


						<section id="section-11-8">
							<h3>11.8 Under Construction Page Settings</h3>
							<p>To change the Under Construction page options, navigate to <strong>Appearance > Axil Theme
									Options.</strong></p>
							<img src="assets/images/doc/11.8.jpg" alt="11.8" class="img-fluid">
							<h4>Under Construction Page View</h4>
							<img src="assets/images/doc/11.8.1.jpg" alt="11.8.1" class="img-fluid">
						</section>

						<section id="section-11-9">
							<h3>11.9 Error 404 Page Settings</h3>
							<p>To change the 404 error page options, navigate to <strong>Appearance > Axil Theme
									Options.</strong></p>
							<img src="assets/images/doc/11.9.jpg" alt="11.8.3" class="img-fluid">
						</section>

						<section id="section-11-10">
							<h3>11.10 Currency / Language</h3>
							<p>To change the Currency / Language options, navigate to <strong>Appearance > Axil Theme
									Options.</strong></p>
							<img src="assets/images/doc/11.10.jpg" alt="11.10" class="img-fluid">
						</section>

						<section id="section-12">
							<h3>12. Contact Form 7</h3>
							<p>To customize the contact form visit the official documentation of Contact Form 7: <a
									href="https://contactform7.com/docs/"
									target="_blank">https://contactform7.com/docs/</a></p>

							<img src="assets/images/doc/12.jpg" alt="12" class="img-fluid">
							<img src="assets/images/doc/12.1.jpg" alt="12.1" class="img-fluid">
							<img src="assets/images/doc/12.2.jpg" alt="12.2" class="img-fluid">
							<img src="assets/images/doc/12.3.jpg" alt="12.3" class="img-fluid">
							<img src="assets/images/doc/12.4.jpg" alt="12.4" class="img-fluid">
						</section>
						<!-- End of #section-13 -->

						<section id="section-13">
							<h3>14. MailChimp for WordPress</h3>
							<p>To know how to customize MailChimp options for your website, visit the official
								documentation of MailChimp for WordPress: <a
									href="https://kb.mc4wp.com/#utm_source=wp-plugin&utm_medium=mailchimp-for-wp&utm_campaign=sidebar"
									target="_blank">https://kb.mc4wp.com/#utm_source=wp-plugin&utm_medium=mailchimp-for-wp&utm_campaign=sidebar</a>
							</p>
							<img src="assets/images/doc/13.jpg" alt="14" class="img-fluid">
							<img src="assets/images/doc/13.1.jpg" alt="14" class="img-fluid">
							<img src="assets/images/doc/13.2.jpg" alt="14" class="img-fluid">
						</section>
						<!-- End of #section-14 -->

					</div>
					<!-- End of .filterable-wrapper -->
				</main>
			</div>
			<!-- End of .main-content -->
		</div>
		<!-- End of .container -->
	</div>
	<!-- End of .main-content -->

	<footer class="page-footer">
		<div class="container-fluid">
			<p>Designed by <a href="https://axilthemes.com/">Axilthemes</a>. Powered by <a
					href="https://wordpress.org/download/">WordPress</a>.</p>
		</div>
		<!-- End of .page-footer -->
	</footer>
	<!-- End of .page-footer -->



	<!-- Javascripts
		======================================= -->

	<!-- jQuery -->
	<script src="assets/js/vendor/jquery.min.js"></script>
	<script src="assets/js/vendor/jquery-migrate.min.js"></script>
	<script src="assets/js/vendor/bootstrap.bundle.min.js"></script>

	<!-- jQuery Easing Plugin -->
	<script src="assets/js/vendor/easing-1.3.js"></script>

	<!-- Custom Script -->
	<script src="assets/js/main.js"></script>

	<script>
		if (/MSIE \d|Trident.*rv:/.test(navigator.userAgent)) {
			document.write('<script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@1"><\/script>');
			document.write('<script src="assets/js/ie.js"><\/script>');
		}
	</script>

</body>

</html>