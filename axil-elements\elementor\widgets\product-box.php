<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;


use Elementor\Widget_Base;
use Elementor\Repeater;
use Elementor\Controls_Manager;
use Elementor\Utils;
use Elementor\Group_Control_Typography;

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class axil_Single_Product extends Widget_Base {

 public function get_name() {
        return 'axil-single-product';
    }    
    public function get_title() {
        return __( 'Single Product', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-banner';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }

    protected function register_controls() {        
   

       $this->start_controls_section(
            'axilbanner_content_sec',
                [
                    'label' => __( 'Layout ', 'etrade-elements' ),                      
                              
                ]
            );    
        
         $this->add_control(      
            'product_ids',
                [
                'label' => __( 'Select The Posts that will display', 'etrade-elements' ),
                'type' => Controls_Manager::SELECT,
                'options'       => wooc_product_post_name(),                  
                'label_block'   => true,
                'separator'     => 'before',
                'multiple'      => false,
                ] 
            );
         $this->add_control(
            'sale_price_only',
            [
                
                'type' => Controls_Manager::SWITCHER,
                'label'       => __( 'Display only sale price', 'etrade-elements' ),
                'label_on'    => __( 'On', 'etrade-elements' ),
                'label_off'   => __( 'Off', 'etrade-elements' ),
                'default'     => '',              
                
            ] 
        );      
          
		$this->add_control(
		    'image',
		    [
		        'label' => __('Image','etrade-elements'),
		        'type'=>Controls_Manager::MEDIA,
		        'default' => [
		            'url' => Utils::get_placeholder_image_src(),
		        ],
		        'dynamic' => [
		            'active' => true,
		        ],
		            
		    ]
		);
    $this->end_controls_section();

    }
	

    private function wooc_build_query( $settings ) { 
		$posts = array_map( 'trim' , explode( ',', $settings['product_ids'] ) );
			$args = array(
				'post_type'      => 'product',
				'ignore_sticky_posts' => true,
				'nopaging'       => true,
				'post__in'       => $posts,
				'orderby'        => 'post__in',
				'posts_per_page' => 1,
			);
 
        return new \WP_Query( $args );
    }

    protected function render() {      
        $settings = $this->get_settings();
           
        $settings['query'] = $this->wooc_build_query( $settings );
        $template = 'single-product';
		return wooc_Elements_Helper::wooc_element_template( $template, $settings );
    }

}