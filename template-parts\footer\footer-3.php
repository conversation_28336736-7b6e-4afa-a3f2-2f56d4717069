<?php
/**
 * Template part for displaying footer layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options = Helper::axil_get_options();
$axil_footer_bottom_menu_args = Helper::axil_footer_bottom_menu_args();
$lineclass = ( is_active_sidebar( 'footer-1' ) || is_active_sidebar( 'footer-2' ) || is_active_sidebar( 'footer-3' ) || is_active_sidebar( 'footer-4' ) ) ? "footer-menu-active" : "";
$allowed_tags = wp_kses_allowed_html( 'post' );
?>

<footer class="axil-footer-area footer-style-3 footer-dark">
    <!-- Start Footer Top Area  -->
 <?php if ( is_active_sidebar( 'footer-mailchimp' ) || is_active_sidebar( 'footer-1' ) || is_active_sidebar( 'footer-2' ) || is_active_sidebar( 'footer-3' ) || is_active_sidebar( 'footer-4' ) || is_active_sidebar( 'footer-5' ) ) {?>
    <div class="footer-top">
        <div class="container">
            <div class="footer-widget-warp">
                <div class="row">
                    <div class="col-lg-4">
                        <?php dynamic_sidebar( 'footer-mailchimp' );?>
                    </div>
                    <div class="col-lg-4">
                        <?php dynamic_sidebar( 'footer-1' );?>
                    </div>
                    <div class="col-lg-4">
                        <?php dynamic_sidebar( 'footer-2' );?>
                    </div>
                </div>
            </div>
            <div class="footer-widget-warp">
                <div class="row">
                    <div class="col-lg-4">
                        <?php dynamic_sidebar( 'footer-3' );?>
                    </div>
                    <div class="col-lg-4">
                        <?php dynamic_sidebar( 'footer-4' );?>
                    </div>
                    <div class="col-lg-4">
                        <?php dynamic_sidebar( 'footer-5' );?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php }?>
<!-- End Footer Top Area  -->
 <?php if ( !empty( $axil_options['payment_icons'] ) || !empty( $axil_options['axil_footer_social_enable'] ) ) {?>
    <div class="footer-middle">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <?php if ( !empty( $axil_options['payment_icons'] ) ) {?>
                        <div class="payment-method">
                            <?php if ( !empty( $axil_options['display_Payment_title'] ) ): ?>
                                <h6 class="title"><?php echo wp_kses( $axil_options['display_Payment_title'], $allowed_tags ); ?></h6>
                            <?php endif;?>
                            <?php if ( $axil_options['payment_icons'] ): ?>
                                <ul class="payment-icons">
                                    <?php if ( $axil_options['payment_img'] ): ?>
                                        <?php $axil_cards = explode( ',', $axil_options['payment_img'] );?>
                                        <?php foreach ( $axil_cards as $axil_card ): ?>
                                            <li><?php echo wp_get_attachment_image( $axil_card ); ?></li>
                                        <?php endforeach;?>
                                    <?php else: ?>
                                        <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>"
                                                 src="<?php echo esc_url( Helper::get_img( 'cart-1.svg' ) ); ?>"></li>
                                        <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>"
                                                 src="<?php echo esc_url( Helper::get_img( 'cart-2.svg' ) ); ?>"></li>
                                        <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>"
                                                 src="<?php echo esc_url( Helper::get_img( 'cart-3.svg' ) ); ?>"></li>
                                        <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>"
                                                 src="<?php echo esc_url( Helper::get_img( 'cart-4.svg' ) ); ?>"></li>
                                        <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>"
                                                 src="<?php echo esc_url( Helper::get_img( 'cart-5.svg' ) ); ?>"></li>
                                    <?php endif;?>
                                </ul>
                            <?php endif;?>
                        </div>
                    <?php }?>
                </div>
                <div class="col-lg-4">
                    <?php if ( !empty( $axil_options['axil_footer_social_enable'] ) ) {?>
                        <div class="payment-method footer-social-link">
                            <?php if ( !empty( $axil_options['social_title'] ) ): ?>
                                <h6 class="title"><?php echo wp_kses( $axil_options['social_title'], $allowed_tags ); ?></h6>
                            <?php endif;?>
                            <ul class="social-link">
                                <?php
                                foreach ( $axil_options['axil_social_icons'] as $key => $value ) {

                                    if ( $value != '' ) {
                                        echo '<li><a class="' . esc_attr( $key ) . ' social-icon" href="' . esc_url( $value ) . '" title="' . esc_attr( ucwords( esc_attr( $key ) ) ) . '" target="_blank"><i class="fab fa-' . esc_attr( $key ) . '"></i></a></li>';
                                    }
                                }?>
                            </ul>
                        </div>
                    <?php }?>
                </div>
            </div>
        </div>
    </div>
   <?php }?>

    <!-- Start Copyright Area  -->
    <div class="copyright-area copyright-default">
        <div class="container">
            <div class="copyright-left d-flex flex-wrap justify-content-center">
                <?php if ( !empty( $axil_options['axil_footer_footerbottom'] ) ) {?>
                    <?php if ( has_nav_menu( 'footerbottom' ) ) {?>
                        <?php wp_nav_menu( $axil_footer_bottom_menu_args );?>
                    <?php }?>
                <?php }?>
                <?php if ( !empty( $axil_options['axil_copyright_contact'] ) ) {?>
                    <ul class="quick-link quick-copyright">
                        <li><?php echo wp_kses( $axil_options['axil_copyright_contact'], $allowed_tags ); ?></li>
                    </ul>
                <?php }?>
            </div>
        </div>
    </div>
    <!-- End Copyright Area  -->
</footer>
<!-- End Footer Area  -->


