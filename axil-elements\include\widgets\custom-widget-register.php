<?php 

/**
 * Custom widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */

/**
 * Load Custom Widget
 */
include_once( ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/about-widget.php' );
include_once( ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/download-app.php' );
include_once( ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/recent-posts.php' );
include_once( ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/categories.php' );
include_once( ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/social-media.php' );
include_once( ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/featured-posts.php' );
if ( class_exists( 'WooCommerce' ) ) {
    include_once( ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/woocommerce-price-filter.php' );
    include_once( ETRADE_ELEMENTS_BASE_DIR. '/include/widgets/woocommerce-product-sorting.php' );
}