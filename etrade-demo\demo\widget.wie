{"sidebar-1": {"woocommerce_product_search-2": {"title": "Search"}, "etrade_recent_post-2": {"title": "Latest Posts", "show_item": 3, "num_title_word": 7, "show_date": true}, "woocommerce_products-2": {"title": "Recent Viewed Products", "number": 3, "show": "", "orderby": "date", "order": "desc", "hide_free": 1, "show_hidden": 0}, "tag_cloud-2": {"title": "Tags", "count": 1, "taxonomy": "post_tag"}}, "widgets-shop-header": {"woocommerce_product_categories-3": {"title": "Product categories", "orderby": "name", "dropdown": 1, "count": 0, "hierarchical": 1, "show_children_only": 1, "hide_empty": 1, "max_depth": "1"}, "woocommerce_layered_nav-4": {"title": "Filter by Color", "attribute": "colors", "display_type": "dropdown", "query_type": "and"}, "woocommerce_layered_nav-5": {"title": "Filter by <PERSON><PERSON>", "attribute": "size", "display_type": "dropdown", "query_type": "and"}, "woocommerce_price_filter-5": {"title": "Filter by price"}, "woocommerce_layered_nav_filters-3": {"title": "Active filters"}}, "widgets-shop": {"woocommerce_layered_nav_filters-2": {"title": "Active filters"}, "woocommerce_product_categories-2": {"title": "Product categories", "orderby": "name", "dropdown": 0, "count": 0, "hierarchical": 1, "show_children_only": 1, "hide_empty": 1, "max_depth": "1"}, "woocommerce_layered_nav-6": {"title": "Filter by Gender", "attribute": "gender", "display_type": "list", "query_type": "and"}, "woocommerce_layered_nav-2": {"title": "Filter by Color", "attribute": "colors", "display_type": "list", "query_type": "and"}, "woocommerce_layered_nav-3": {"title": "Filter by <PERSON><PERSON>", "attribute": "size", "display_type": "list", "query_type": "and"}, "woocommerce_rating_filter-2": {"title": "Average rating"}, "woocommerce_price_filter-4": {"title": "Filter by price"}}, "footer-mailchimp": {"text-2": {"title": "", "text": "[mc4wp_form id=25]", "filter": true, "visual": true}}, "footer-1": {"text-3": {"title": "Support", "text": "   <div class=\"inner\"> \r\n   <p> 685 Market Street,\r\n    Las Vegas, LA 95820,\r\n    United States.</p> \r\n    <ul class=\"support-list-item\"> \r\n        <li><a href=\"mailto:<EMAIL>\"><i class=\"fal fa-envelope-open\"></i> <EMAIL></a></li>\r\n        <li><a href=\"tel:(+01)************\"><i class=\"fal fa-phone-alt\"></i> (+01) ************</a></li>\r\n    </ul> \r\n    </div>", "filter": true, "visual": true}}, "footer-2": {"nav_menu-4": {"title": "Account", "nav_menu": 142}}, "footer-3": {"nav_menu-2": {"title": "Quick Link", "nav_menu": 143}}, "footer-4": {"nav_menu-3": {"title": "Shop", "nav_menu": 144}}, "footer-5": {"etrade_download_app-2": {"title": "Download App", "content": "Save $3 With App & New User only", "app": "https://dev.axilthemes.com/themes/etrade/wp-content/uploads/2023/01/app-store.png", "app_link2": "#", "app_link": "", "app2": "https://dev.axilthemes.com/themes/etrade/wp-content/uploads/2023/01/play-store.png", "qr_code": "https://dev.axilthemes.com/themes/etrade/wp-content/uploads/2023/01/qr.png"}}}