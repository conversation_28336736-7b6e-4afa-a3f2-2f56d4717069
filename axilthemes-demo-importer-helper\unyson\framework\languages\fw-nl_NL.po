# 
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015
# Ruud <PERSON> <<EMAIL>>, 2016
# <PERSON>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Unyson\n"
"POT-Creation-Date: 2016-02-02 15:48+0300\n"
"PO-Revision-Date: 2016-04-08 04:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Dutch (Netherlands) (http://www.transifex.com/themefuse/unyson/language/nl_NL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nl_NL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.5.4\n"
"X-Poedit-Basepath: .\n"
"X-Poedit-KeywordsList: _:1;gettext:1;dgettext:2;ngettext:1,2;dngettext:2,3;__:1;_e:1;_c:1;_n:1,2;_n_noop:1,2;_nc:1,2;__ngettext:1,2;__ngettext_noop:1,2;_x:1,2c;_ex:1,2c;_nx:1,2,4c;_nx_noop:1,2,3c;_n_js:1,2;_nx_js:1,2,3c;esc_attr__:1;esc_html__:1;esc_attr_e:1;esc_html_e:1;esc_attr_x:1,2c;esc_html_x:1,2c;comments_number_link:2,3;t:1;st:1;trans:1;transChoice:1,2\n"
"X-Poedit-SearchPath-0: ..\n"
"X-Poedit-SourceCharset: UTF-8\n"

#: ../framework/manifest.php:5
msgid "Unyson"
msgstr "Unyson"

#: ../framework/helpers/class-fw-wp-filesystem.php:43
msgid "Cannot connect to Filesystem directly"
msgstr "Kan geen directe verbinding maken met het bestandssyteem"

#: ../framework/helpers/class-fw-wp-filesystem.php:271
#, php-format
msgid "Cannot create directory \"%s\". It must be inside \"%s\""
msgstr "Kan de map \"%s\" niet aanmaken. Deze map moet aangemaakt worden in \"%s\""

#: ../framework/helpers/class-fw-wp-filesystem.php:273
msgid "\" or \""
msgstr "\" of \""

#: ../framework/helpers/class-fw-flash-messages.php:95
#, php-format
msgid "Invalid flash message type: %s"
msgstr "Ongeldig flitsbericht type: %s"

#: ../framework/helpers/class-fw-wp-list-table.php:185
msgid "No items found."
msgstr "Geen items gevonden"

#: ../framework/helpers/class-fw-wp-list-table.php:309
msgid "Bulk Actions"
msgstr "Bulk acties"

#: ../framework/helpers/class-fw-wp-list-table.php:319
msgid "Apply"
msgstr "Toepassen"

#: ../framework/helpers/class-fw-wp-list-table.php:403
msgid "All dates"
msgstr "Alle datums"

#: ../framework/helpers/class-fw-wp-list-table.php:416
#, php-format
msgid "%1$s %2$d"
msgstr "%1$s %2$d"

#: ../framework/helpers/class-fw-wp-list-table.php:432
msgid "List View"
msgstr "Lijstweergave"

#: ../framework/helpers/class-fw-wp-list-table.php:433
msgid "Excerpt View"
msgstr "Detailweergave"

#: ../framework/helpers/class-fw-wp-list-table.php:459
#, php-format
msgid "%s pending"
msgstr "%s wachtend"

#: ../framework/helpers/class-fw-wp-list-table.php:714
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:33
msgid "Select All"
msgstr "Alles Selecteren"

#: ../framework/helpers/general.php:1150
msgid "year"
msgstr "jaar"

#: ../framework/helpers/general.php:1151
msgid "years"
msgstr "jaren"

#: ../framework/helpers/general.php:1153
msgid "month"
msgstr "maand"

#: ../framework/helpers/general.php:1154
msgid "months"
msgstr "maanden"

#: ../framework/helpers/general.php:1156
msgid "week"
msgstr "week"

#: ../framework/helpers/general.php:1157
msgid "weeks"
msgstr "weken"

#: ../framework/helpers/general.php:1159
msgid "day"
msgstr "dag"

#: ../framework/helpers/general.php:1160
msgid "days"
msgstr "dagen"

#: ../framework/helpers/general.php:1162
msgid "hour"
msgstr "uur"

#: ../framework/helpers/general.php:1163
msgid "hours"
msgstr "uren"

#: ../framework/helpers/general.php:1165
msgid "minute"
msgstr "minuut"

#: ../framework/helpers/general.php:1166
msgid "minutes"
msgstr "minuten"

#: ../framework/helpers/general.php:1168
msgid "second"
msgstr "seconde"

#: ../framework/helpers/general.php:1169
msgid "seconds"
msgstr "seconden"

#: ../framework/helpers/general.php:1558
msgid "Maximum stack depth exceeded"
msgstr "Maximale stack diepte overschreden"

#: ../framework/helpers/general.php:1561
msgid "Underflow or the modes mismatch"
msgstr "Underflow or the modes mismatch"

#: ../framework/helpers/general.php:1564
msgid "Unexpected control character found"
msgstr "Onverwacht controleteken gevonden"

#: ../framework/helpers/general.php:1567
msgid "Syntax error, malformed JSON"
msgstr "Syntaxisfout, onjuist geformateerde JSON"

#: ../framework/helpers/general.php:1570
msgid "Malformed UTF-8 characters, possibly incorrectly encoded"
msgstr "Onjuist geformateerd UTF-8 karakters, mogelijk onjuist versleuteld"

#: ../framework/helpers/general.php:1573
#: ../framework/extensions/backups/class-fw-extension-backups.php:550
msgid "Unknown error"
msgstr "Onbekende fout"

#: ../framework/helpers/class-fw-form.php:80
#, php-format
msgid "Form with id \"%s\" was already defined"
msgstr "Formulier met id \"%s\" was al gedefinieerd"

#: ../framework/helpers/class-fw-form.php:168
msgid "Nonce verification failed"
msgstr "Nonce verificatie mislukt"

#: ../framework/helpers/class-fw-form.php:331
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/views/form.php:13
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:66
msgid "Submit"
msgstr "Verzenden"

#: ../framework/extensions/update/class-fw-extension-update.php:285
#: ../framework/extensions/update/class-fw-extension-update.php:602
msgid "Cannot delete: "
msgstr "Kan niet verwijderen:"

#: ../framework/extensions/update/class-fw-extension-update.php:292
msgid "Cannot create: "
msgstr "Kan niet aanmaken:"

#: ../framework/extensions/update/class-fw-extension-update.php:370
msgid "Cannot remove old temporary directory: "
msgstr "Kan oude tijdelijke map niet verwijderen:"

#: ../framework/extensions/update/class-fw-extension-update.php:376
#: ../framework/extensions/update/class-fw-extension-update.php:572
msgid "Cannot create directory: "
msgstr "Kan map niet aanmaken: "

#: ../framework/extensions/update/class-fw-extension-update.php:381
#, php-format
msgid "Downloading the %s..."
msgstr "Bezig met downloaden van de %s..."

#: ../framework/extensions/update/class-fw-extension-update.php:386
#, php-format
msgid "Cannot download the %s."
msgstr "Kan de %s niet downloaden. "

#: ../framework/extensions/update/class-fw-extension-update.php:396
#, php-format
msgid "Installing the %s..."
msgstr "Bezig met installeren van de %s..."

#: ../framework/extensions/update/class-fw-extension-update.php:402
#: ../framework/extensions/update/class-fw-extension-update.php:431
#: ../framework/extensions/update/class-fw-extension-update.php:531
#: ../framework/extensions/update/class-fw-extension-update.php:552
#: ../framework/extensions/update/class-fw-extension-update.php:582
msgid "Cannot access directory: "
msgstr "Kan geen toegang krijgen tot map:"

#: ../framework/extensions/update/class-fw-extension-update.php:421
msgid "Cannot remove: "
msgstr "Kan niet verwijderen:"

#: ../framework/extensions/update/class-fw-extension-update.php:456
#: ../framework/extensions/update/class-fw-extension-update.php:522
#: ../framework/extensions/update/class-fw-extension-update.php:628
#: ../framework/extensions/update/class-fw-extension-update.php:652
#, php-format
msgid "Cannot move \"%s\" to \"%s\""
msgstr "Kan \"%s\" niet naar \"%s\" verplaatsen"

#: ../framework/extensions/update/class-fw-extension-update.php:472
#, php-format
msgid "Cannot merge \"%s\" with \"%s\""
msgstr "Kan \"%s\" niet samenvoegen met \"%s\""

#: ../framework/extensions/update/class-fw-extension-update.php:485
#, php-format
msgid "The %s has been successfully updated."
msgstr "De %s is succesvol bijgewerkt. "

#: ../framework/extensions/update/class-fw-extension-update.php:492
#, php-format
msgid "Cannot remove temporary directory \"%s\"."
msgstr "Kan tijdelijke map \"%s\" niet verwijderen."

#: ../framework/extensions/update/class-fw-extension-update.php:672
#: ../framework/extensions/update/class-fw-extension-update.php:740
#: ../framework/extensions/update/class-fw-extension-update.php:808
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:930
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1427
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1837
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2054
msgid "Invalid nonce."
msgstr "Ongeldige nonce."

#: ../framework/extensions/update/class-fw-extension-update.php:683
msgid "Framework Update"
msgstr "Framework Update"

#: ../framework/extensions/update/class-fw-extension-update.php:699
msgid "Failed to get framework latest version."
msgstr "Niet gelukt om laatste versie van framework te verkrijgen. "

#: ../framework/extensions/update/class-fw-extension-update.php:716
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:360
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:374
#: ../framework/extensions/update/views/updates-list.php:10
#: ../framework/core/class-fw-manifest.php:353
msgid "Framework"
msgstr "Framework"

#: ../framework/extensions/update/class-fw-extension-update.php:751
msgid "Theme Update"
msgstr "Thema Update"

#: ../framework/extensions/update/class-fw-extension-update.php:767
msgid "Failed to get theme latest version."
msgstr "Niet gelukt om de laatste versie van thema te verkrijgen. "

#: ../framework/extensions/update/class-fw-extension-update.php:784
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:393
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:407
msgid "Theme"
msgstr "Thema"

#: ../framework/extensions/update/class-fw-extension-update.php:817
msgid "Please check the extensions you want to update."
msgstr "Controleer de uitbreidingen die je wilt bijwerken."

#: ../framework/extensions/update/class-fw-extension-update.php:841
msgid "Extensions Update"
msgstr "Uitbreidingen Update"

#: ../framework/extensions/update/class-fw-extension-update.php:879
msgid "No extensions updates found."
msgstr "Geen updates van uitbreidingen gevonden. "

#: ../framework/extensions/update/class-fw-extension-update.php:886
#, php-format
msgid "Extension \"%s\" does not exist or is disabled."
msgstr "Uitbreiding \"%s\" bestaat niet of is uitgeschakeld."

#: ../framework/extensions/update/class-fw-extension-update.php:893
#, php-format
msgid "No update found for the \"%s\" extension."
msgstr "Geen update gevonden voor de \"%s\" uitbreiding."

#: ../framework/extensions/update/class-fw-extension-update.php:915
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:426
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:440
#, php-format
msgid "%s extension"
msgstr "%s uitbreiding"

#: ../framework/extensions/update/manifest.php:5
#: ../framework/extensions/builder/includes/option-types/builder/includes/fullscreen.php:82
msgid "Update"
msgstr "Bijwerken"

#: ../framework/extensions/update/manifest.php:6
msgid "Keep you framework, extensions and theme up to date."
msgstr "Houd je framework, uitbreidingen en thema up-to-date."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:91
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:232
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2439
msgid "Github error:"
msgstr "Github fout:"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:100
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:241
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2446
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (Response code: %d)"
msgstr "Toegang tot Github repository \"%s\" releases mislukt. (Response code: %d)"

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:108
#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:249
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2462
#, php-format
msgid "Failed to access Github repository \"%s\" releases."
msgstr "Toegang tot Github repository \"%s\" releases mislukt."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:122
#, php-format
msgid "No releases found in repository \"%s\"."
msgstr "Geen releases gevonden in repository: \"%s\"."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:143
#, php-format
msgid ""
"%s manifest has invalid \"github_update\" parameter. Please use "
"\"user/repo\" format."
msgstr "%s manifest heeft ongeldige \"github_update\" parameter. Gebruik de \"user/repo\" indeling."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:171
#, php-format
msgid "Failed to fetch %s latest version from github \"%s\"."
msgstr "Niet gelukt om laatste %s versie van github \"%s\" te verkrijgen. "

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:264
#, php-format
msgid "%s github repository \"%s\" does not have the \"%s\" release."
msgstr "%s github repository \"%s\" beschikt niet over de \"%s\" release."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:284
#, php-format
msgid "Cannot download %s zip."
msgstr "Kan %s zip niet downloaden. "

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:297
#, php-format
msgid "Cannot save %s zip."
msgstr "Kan %s zip niet opslaan."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:316
#, php-format
msgid "Cannot remove %s zip."
msgstr "Kan %s zip niet verwijderen."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:325
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2566
msgid "Cannot access the unzipped directory files."
msgstr "Kan de bestanden van de uitgepakte map niet benaderen."

#: ../framework/extensions/update/extensions/github-update/class-fw-extension-github-update.php:341
#, php-format
msgid "The unzipped %s directory not found."
msgstr "De uitgepakte %s map kan niet worden gevonden. "

#: ../framework/extensions/update/views/updates-list.php:12
#, php-format
msgid "You have the latest version of %s."
msgstr "Je hebt de laatste versie van %s."

#: ../framework/extensions/update/views/updates-list.php:25
msgid "Update Framework"
msgstr "Framework Bijwerken"

#: ../framework/extensions/update/views/updates-list.php:37
msgid "Your theme is up to date."
msgstr "Je thema is up-to-date."

#: ../framework/extensions/update/views/updates-list.php:50
msgid "Update Theme"
msgstr "Thema Bijwerken"

#: ../framework/extensions/update/views/updates-list.php:60
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:844
#, php-format
msgid "%s Extensions"
msgstr "%s Uitbreidingen"

#: ../framework/extensions/update/views/updates-list.php:62
#, php-format
msgid "You have the latest version of %s Extensions."
msgstr "Je hebt de laatste versie van %s Uitbreidingen."

#: ../framework/extensions/update/views/updates-list.php:80
#: ../framework/extensions/update/views/updates-list.php:95
#: ../framework/extensions/update/views/updates-list.php:100
msgid "Update Extensions"
msgstr "Uitbreidingen Bijwerken"

#: ../framework/extensions/update/views/updates-list.php:99
msgid "New extensions updates available."
msgstr "Nieuwe uitbreidingen beschikbaar."

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:16
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:14
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:16
msgid "Go to updates page"
msgstr "Ga naar updates pagina"

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:19
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:17
#: ../framework/extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:19
msgid "Return to Updates page"
msgstr "Keer terug naar Updates pagina. "

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:84
#, php-format
msgid "You have version %s installed. Update to %s."
msgstr "Je hebt versie %s geïnstalleerd. Werk bij naar %s."

#: ../framework/extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:126
msgid "No Extensions for update."
msgstr "Geen Uitbreidingen voor de update."

#: ../framework/extensions/portfolio/manifest.php:7
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:260
#: ../framework/core/components/extensions/manager/available-extensions.php:72
msgid "Portfolio"
msgstr "Portfolio"

#: ../framework/extensions/portfolio/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:73
msgid ""
"This extension will add a fully fledged portfolio module that will let you "
"display your projects using the built in portfolio pages."
msgstr "Deze uitbreiding voegt een volledig uitgeruste portfolio module toe, die het mogelijk maakt om je projecten te tonen door middel van de ingebouwde portfolio pagina's."

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:115
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Project"
msgstr "Project"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:116
msgid "Projects"
msgstr "Projecten"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:123
#: ../framework/extensions/learning/class-fw-extension-learning.php:63
#: ../framework/extensions/learning/class-fw-extension-learning.php:127
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:58
#: ../framework/extensions/events/class-fw-extension-events.php:76
#: ../framework/extensions/media/extensions/slider/posts.php:8
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:32
msgid "Add New"
msgstr "Nieuwe Toevoegen"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:124
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:198
#: ../framework/extensions/learning/class-fw-extension-learning.php:64
#: ../framework/extensions/learning/class-fw-extension-learning.php:128
#: ../framework/extensions/events/class-fw-extension-events.php:77
#: ../framework/extensions/events/class-fw-extension-events.php:131
#, php-format
msgid "Add New %s"
msgstr "Nieuwe %s toevoegen"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:125
#: ../framework/extensions/learning/class-fw-extension-learning.php:129
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:89
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:76
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:88
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:86
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:68
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:34
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:50
#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:110
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:45
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:37
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:73
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:81
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:57
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:50
#: ../framework/extensions/events/class-fw-extension-events.php:78
#: ../framework/includes/option-types/popup/class-fw-option-type-popup.php:158
#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:151
#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:177
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:161
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:187
msgid "Edit"
msgstr "Bewerk"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:126
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:196
#: ../framework/extensions/learning/class-fw-extension-learning.php:65
#: ../framework/extensions/learning/class-fw-extension-learning.php:66
#: ../framework/extensions/learning/class-fw-extension-learning.php:130
#: ../framework/extensions/learning/class-fw-extension-learning.php:192
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:59
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:60
#: ../framework/extensions/events/class-fw-extension-events.php:79
#: ../framework/extensions/events/class-fw-extension-events.php:129
#, php-format
msgid "Edit %s"
msgstr "Bewerk %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:127
#: ../framework/extensions/learning/class-fw-extension-learning.php:67
#: ../framework/extensions/learning/class-fw-extension-learning.php:131
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:61
#: ../framework/extensions/events/class-fw-extension-events.php:80
#, php-format
msgid "New %s"
msgstr "Nieuw %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:128
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:193
#: ../framework/extensions/learning/class-fw-extension-learning.php:68
#: ../framework/extensions/learning/class-fw-extension-learning.php:132
#: ../framework/extensions/learning/class-fw-extension-learning.php:189
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:62
#: ../framework/extensions/events/class-fw-extension-events.php:81
#: ../framework/extensions/events/class-fw-extension-events.php:126
#, php-format
msgid "All %s"
msgstr "Alle %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:129
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:130
#: ../framework/extensions/learning/class-fw-extension-learning.php:69
#: ../framework/extensions/learning/class-fw-extension-learning.php:70
#: ../framework/extensions/learning/class-fw-extension-learning.php:133
#: ../framework/extensions/learning/class-fw-extension-learning.php:134
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:63
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:64
#: ../framework/extensions/events/class-fw-extension-events.php:82
#: ../framework/extensions/events/class-fw-extension-events.php:83
#, php-format
msgid "View %s"
msgstr "Bekijk %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:131
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:192
#: ../framework/extensions/learning/class-fw-extension-learning.php:71
#: ../framework/extensions/learning/class-fw-extension-learning.php:135
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:65
#: ../framework/extensions/events/class-fw-extension-events.php:84
#: ../framework/extensions/events/class-fw-extension-events.php:125
#, php-format
msgid "Search %s"
msgstr "Zoek %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:132
#: ../framework/extensions/learning/class-fw-extension-learning.php:72
#: ../framework/extensions/learning/class-fw-extension-learning.php:136
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:66
#: ../framework/extensions/events/class-fw-extension-events.php:85
#, php-format
msgid "No %s Found"
msgstr "Geen %s Gevonden"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:133
#: ../framework/extensions/learning/class-fw-extension-learning.php:73
#: ../framework/extensions/learning/class-fw-extension-learning.php:137
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:67
#: ../framework/extensions/events/class-fw-extension-events.php:86
#, php-format
msgid "No %s Found In Trash"
msgstr "Geen %s Gevonden In Prullenbak"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:136
msgid "Create a portfolio item"
msgstr "Maak een portfolio onderdeel aan"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:183
#: ../framework/extensions/events/class-fw-extension-events.php:116
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:268
msgid "Category"
msgstr "Categorie"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:184
#: ../framework/extensions/events/class-fw-extension-events.php:117
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:168
msgid "Categories"
msgstr "Categorieën"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:194
#: ../framework/extensions/learning/class-fw-extension-learning.php:190
#: ../framework/extensions/events/class-fw-extension-events.php:127
#, php-format
msgid "Parent %s"
msgstr "Ouder %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:195
#: ../framework/extensions/learning/class-fw-extension-learning.php:191
#: ../framework/extensions/events/class-fw-extension-events.php:128
#, php-format
msgid "Parent %s:"
msgstr "Ouder %s:"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:197
#: ../framework/extensions/learning/class-fw-extension-learning.php:193
#: ../framework/extensions/events/class-fw-extension-events.php:130
#: ../framework/core/components/extensions/manager/views/extension.php:234
#: ../framework/core/components/extensions/manager/views/extension.php:272
#, php-format
msgid "Update %s"
msgstr "Bijwerken %s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:199
#: ../framework/extensions/learning/class-fw-extension-learning.php:195
#: ../framework/extensions/events/class-fw-extension-events.php:132
#, php-format
msgid "New %s Name"
msgstr "Nieuwe %s Naam"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:200
#: ../framework/extensions/learning/class-fw-extension-learning.php:196
#: ../framework/extensions/events/class-fw-extension-events.php:133
#, php-format
msgid "%s"
msgstr "%s"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Gallery"
msgstr "Galerij"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:240
msgid "Set project gallery"
msgstr "Stel project galerij in"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:241
msgid "Edit project gallery"
msgstr "Bewerk project gallerij"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:271
msgid "Project Cover Image"
msgstr "Project Cover Foto"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:287
msgid "Edit this item"
msgstr "Bewerk dit onderdeel"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:348
#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:361
#: ../framework/extensions/learning/class-fw-extension-learning.php:320
#: ../framework/extensions/learning/class-fw-extension-learning.php:333
msgid "View all categories"
msgstr "Bekijk alle categorieën"

#: ../framework/extensions/portfolio/class-fw-extension-portfolio.php:381
msgid "Cover Image"
msgstr "Cover Afbeelding"

#: ../framework/extensions/seo/settings-options.php:17
#: ../framework/extensions/social/settings-options.php:11
msgid "General"
msgstr "Algemeen"

#: ../framework/extensions/seo/settings-options.php:21
#: ../framework/extensions/social/settings-options.php:15
msgid "General Settings"
msgstr "Algemene Instellingen"

#: ../framework/extensions/seo/class-fw-extension-seo.php:89
msgid "Site name"
msgstr "Site naam"

#: ../framework/extensions/seo/class-fw-extension-seo.php:95
msgid "Site description"
msgstr "Site omschrijving"

#: ../framework/extensions/seo/class-fw-extension-seo.php:101
msgid "Current time"
msgstr "Huidige tijd"

#: ../framework/extensions/seo/class-fw-extension-seo.php:107
msgid "Current date"
msgstr "Huidige datum"

#: ../framework/extensions/seo/class-fw-extension-seo.php:113
msgid "Current month"
msgstr "Huidige maand"

#: ../framework/extensions/seo/class-fw-extension-seo.php:119
msgid "Current year"
msgstr "Huidig jaar"

#: ../framework/extensions/seo/class-fw-extension-seo.php:125
msgid "Date of the post/page"
msgstr "Datum van bericht/pagina"

#: ../framework/extensions/seo/class-fw-extension-seo.php:131
msgid "Title of the post/page/term"
msgstr "Titel van bericht/pagina/term"

#: ../framework/extensions/seo/class-fw-extension-seo.php:137
msgid "Excerpt of the current post, of auto-generate if it is not set"
msgstr "Samenvatting van het huidige bericht, of auto-genereren indien niet ingesteld"

#: ../framework/extensions/seo/class-fw-extension-seo.php:143
msgid "Excerpt of the current post, without auto-generation"
msgstr "Samenvatting van het huidige bericht, zonder auto-generatie"

#: ../framework/extensions/seo/class-fw-extension-seo.php:149
msgid "Post tags, separated by coma"
msgstr "Bericht tags, gescheiden door komma's"

#: ../framework/extensions/seo/class-fw-extension-seo.php:155
msgid "Post categories, separated by coma"
msgstr "Bericht categorieën, gescheiden door komma's"

#: ../framework/extensions/seo/class-fw-extension-seo.php:161
msgid "Category/tag/term description"
msgstr "Categorie/tag/term beschrijving"

#: ../framework/extensions/seo/class-fw-extension-seo.php:167
msgid "Term title"
msgstr "Term titel"

#: ../framework/extensions/seo/class-fw-extension-seo.php:173
msgid "Post modified time"
msgstr "Aangepaste tijd van bericht"

#: ../framework/extensions/seo/class-fw-extension-seo.php:179
msgid "Post/page id"
msgstr "Bericht/pagina id"

#: ../framework/extensions/seo/class-fw-extension-seo.php:185
msgid "Post/page author \"nicename\""
msgstr "Bericht/pagina auteur \"bijnaam\""

#: ../framework/extensions/seo/class-fw-extension-seo.php:191
msgid "Post/page author id"
msgstr "Bericht/pagina auteur id"

#: ../framework/extensions/seo/class-fw-extension-seo.php:197
msgid "Search phrase in search page"
msgstr "Zoek zin in zoek pagina"

#: ../framework/extensions/seo/class-fw-extension-seo.php:203
#: ../framework/extensions/seo/class-fw-extension-seo.php:209
msgid "Page number"
msgstr "Paginanummer"

#: ../framework/extensions/seo/class-fw-extension-seo.php:215
msgid "Attachment caption"
msgstr "Onderschrift voor bijlage"

#: ../framework/extensions/seo/class-fw-extension-seo.php:435
#: ../framework/extensions/seo/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:120
msgid "SEO"
msgstr "SEO"

#: ../framework/extensions/seo/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:121
msgid ""
"This extension will enable you to have a fully optimized WordPress website "
"by adding optimized meta titles, keywords and descriptions."
msgstr "Deze uitbreiding helpt je een volledig geoptimaliseerde WordPress website te krijgen, door het toevoegen van geoptimaliseerde meta titels, sleutelwoorden en beschrijvingen. "

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:22
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:310
msgid "Titles & Meta"
msgstr "TItels & Meta"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:26
#: ../framework/extensions/breadcrumbs/settings-options.php:6
#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:9
msgid "Homepage"
msgstr "Homepage"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:30
msgid "Homepage Title"
msgstr "Homepage Titel"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:31
msgid "Set homepage title format"
msgstr "Stel de titel indeling voor de homepage in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:36
msgid "Homepage Description"
msgstr "Homepage Beschrijving"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:37
msgid "Set homepage description"
msgstr "Stel de beschrijving voor de homepage in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:44
msgid "Homepage Meta Keywords"
msgstr "Homepage Meta Trefwoorden"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:45
msgid "Set homepage meta keywords"
msgstr "Stel de meta trefwoorden voor de homepage in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:54
msgid "Custom Posts"
msgstr "Custom Posts"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:59
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:111
#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:120
#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:7
#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:15
#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:12
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:15
#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:56
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:480
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:62
msgid "Title"
msgstr "Titel"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:60
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:112
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:287
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:315
msgid "Set title format"
msgstr "Stel titel indeling in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:63
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:74
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:87
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:115
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:126
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:139
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:247
msgid "Here are some tags examples:"
msgstr "Hier zijn enkele voorbeelden van tags:"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:70
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:122
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:320
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:66
msgid "Description"
msgstr "Beschrijving"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:71
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:123
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:293
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:321
msgid "Set description format"
msgstr "Stel beschrijving indeling in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:83
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:135
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:328
msgid "Meta Keywords"
msgstr "Meta Sleutelwoorden"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:84
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:136
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:329
msgid "Set meta keywords"
msgstr "Stel meta trefwoorden in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:96
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:148
msgid "Meta Robots"
msgstr "Meta Robots"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:97
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:149
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:188
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:227
msgid "noindex, follow"
msgstr "noindex, follow"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:105
msgid "Taxonomies"
msgstr "Taxonomieën"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:158
msgid "Other"
msgstr "Andere"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:165
msgid "Author Page Title"
msgstr "Auteurspagina Titel"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:166
msgid "Set author page title format"
msgstr "Stel de titel indeling voor de auteurspagina in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:171
msgid "Author Page Description"
msgstr "Auteurspagina Beschrijving"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:172
msgid "Set author page description"
msgstr "Stel de omschrijving voor de auteurspagina in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:179
msgid "Author Meta Keywords"
msgstr "Auteur Meta Trefwoorden"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:180
msgid "Set author page meta keywords"
msgstr "Stel de meta trefwoorden voor de auteurspagina in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:187
#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:226
msgid "Metarobots"
msgstr "Metarobots"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:193
msgid "Disable Author Archives"
msgstr "Auteursarchieven Uitschakelen"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:194
msgid "Disable Author archives SEO settings"
msgstr "Schakel SEO instellingen voor Auteursarchieven uit"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:204
msgid "Date Achieves Title"
msgstr "Datumarchieven Titel"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:205
msgid "Set date achieves title format"
msgstr "Stel de titel indeling voor de datumarchieven in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:210
msgid "Date Achieves Description"
msgstr "Datumarchieven Beschrijving"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:211
msgid "Set date achieves description"
msgstr "Stel de beschrijving voor de datumarchieven in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:218
msgid "Date achieves Meta Keywords"
msgstr "Datumarchieven Meta Trefwoordden"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:219
msgid "Set date achieves meta keywords"
msgstr "Stel de meta trefwoorden voor de datumarchieven in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:232
msgid "Disable Date Archives"
msgstr "Datumarchieven Uitschakelen"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:233
msgid "Disable date archives SEO settings"
msgstr "Schakel SEO instellingen voor datumarchieven uit"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:243
msgid "Search Page Title"
msgstr "Zoekpagina Titel"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:244
msgid "Set search page title format"
msgstr "Stel de titel indeling voor de zoekpagina in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:259
msgid "404 Page Title"
msgstr "404 Pagina Titel"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:260
msgid "Set 404 page title format"
msgstr "Stel de titel indeling voor de 404 pagina in"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:262
#: ../framework/extensions/breadcrumbs/settings-options.php:8
msgid "404 Not Found"
msgstr "404 Niet Gevonden"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:286
msgid "SEO Title"
msgstr "SEO Titel"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:292
msgid "SEO Description"
msgstr "SEO Beschrijving"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:314
msgid "Page Title"
msgstr "Pagina Titel"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:356
msgid "Use Meta Keywords"
msgstr "Gebruik Meta Trefwoorden"

#: ../framework/extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:357
msgid "Allow the use of meta keywords in posts and taxonomies"
msgstr "Sta het gebruik van meta trefwoorden in berichten en taxonomieën toe"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:33
msgid "Google Webmasters"
msgstr "Google Webmasters"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:34
msgid "Insert Google Webmasters verification code"
msgstr "Voer de Google Webmasters verificatiecode in"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:41
msgid "Bing Webmasters"
msgstr "Bing Webmasters"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:42
msgid "Insert Bing Webmasters verification code"
msgstr "Voer de Bing Webmasters verificatiecode in"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:80
msgid "Webmasters"
msgstr "Webmasters"

#: ../framework/extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:89
#, php-format
msgid "Webmaster %s already exists"
msgstr "Webmaster %s bestaat al"

#: ../framework/extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:634
msgid "Google"
msgstr "Google"

#: ../framework/extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:638
msgid "Bing"
msgstr "Bing"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:19
msgid "Check if you want to exclude this page"
msgstr "Vink aan als je deze pagina wilt uitsluiten"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:40
msgid "Check if you want to exclude this category"
msgstr "Vink aan als je deze categorie wilt uitsluiten"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:57
msgid "Sitemap"
msgstr "Sitemap"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:68
msgid "View Sitemap"
msgstr "Bekijk Sitemap"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:69
msgid "Press button to view sitemap file"
msgstr "Klik op de knop om het sitemap bestand te zien"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:71
msgid "XML Sitemap"
msgstr "XML Sitemap"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:81
msgid "Search Engines"
msgstr "Zoekmachines"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:83
msgid "After adding content the extension will automatically ping to:"
msgstr "Na het toevoegen van de inhoud zal de uitbreiding automatisch contact zoeken met:"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:93
msgid "Exclude Pages"
msgstr "Pagina's Uitsluiten"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:95
msgid "Please check the pages you do not want to include in sitemap"
msgstr "Vink de pagina's aan die je wilt uitsluiten van de sitemap"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:106
msgid "Exclude Categories"
msgstr "Categorieën Uitsluiten"

#: ../framework/extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:108
msgid "Please check the categories you do not want to include in sitemap"
msgstr "Vink de categorieën aan die je wilt uitsluiten van de sitemap"

#: ../framework/extensions/mailer/manifest.php:5
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:187
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:188
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:103
#: ../framework/core/components/extensions/manager/available-extensions.php:240
msgid "Mailer"
msgstr "Mailer"

#: ../framework/extensions/mailer/manifest.php:6
#: ../framework/core/components/extensions/manager/available-extensions.php:241
msgid ""
"This extension will let you set some global email options and it is used by "
"other extensions (like Forms) to send emails."
msgstr "Deze uitbreiding maakt het mogelijk bepaalde globale emailinstellingen te configureren, die ook door andere uitbreidingen (zoals Formulieren) gebruikt worden om e-mails te verzenden."

#: ../framework/extensions/mailer/class-fw-extension-mailer.php:48
msgid "Invalid send method"
msgstr "Ongeldige verzendmethode"

#: ../framework/extensions/mailer/class-fw-extension-mailer.php:81
msgid "The message has been successfully sent!"
msgstr "Het bericht is succesvol verzonden!"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:27
msgid "Invalid email configuration"
msgstr "Ongeldige e-mailconfiguratie"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:145
#: ../framework/extensions/mailer/includes/class-mailer-sender.php:161
msgid "Email sent"
msgstr "E-mail verzonden"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:146
msgid "Could not send via smtp"
msgstr "Kon niet verzenden via SMTP"

#: ../framework/extensions/mailer/includes/class-mailer-sender.php:162
msgid "Could not send via wp_mail"
msgstr "Kon niet verzenden via wp_mail"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:25
msgid "Server Address"
msgstr "Server Adres"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:26
msgid "Enter your email server"
msgstr "Geef de naam van je e-mailserver in"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:31
msgid "Username"
msgstr "Gebruikersnaam"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:32
msgid "Enter your username"
msgstr "Geef je gebruikersnaam in"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:37
msgid "Password"
msgstr "Wachtwoord"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:38
msgid "Enter your password"
msgstr "Geef je wachtwoord in"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:43
msgid "Secure Connection"
msgstr "Veilige Verbinding"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:54
msgid "Custom Port"
msgstr "Aangepaste Poort"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:55
msgid "Optional - SMTP port number to use."
msgstr "Optioneel - te gebruiken poortnummer voor SMTP."

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:56
msgid "Leave blank for default (SMTP - 25, SMTPS - 465)"
msgstr "Laat leeg voor standaard (SMTP - 25, SMTPS - 465)"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:82
msgid "Username cannot be empty"
msgstr "Gebruikersnaam kan niet leeg zijn"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:89
msgid "Password cannot be empty"
msgstr "Wachtwoord kan niet leeg zijn"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:96
msgid "Invalid host"
msgstr "Ongeldige host"

#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:176
#: ../framework/extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-wpmail.php:56
msgid "Could not send the email"
msgstr "De e-mail kon niet worden verzonden"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:76
msgid "Send Method"
msgstr "Verzendmethode"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:77
msgid "Select the send form method"
msgstr "Selecteer de verzendmethode voor het formulier"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:94
msgid "From Name"
msgstr "Afzender Naam"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:95
msgid "The name you'll see in the From filed in your email client."
msgstr "De naam die wordt getoond als afzender in je e-mailclient."

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:102
msgid "From Address"
msgstr "Afzender Adres"

#: ../framework/extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:103
msgid "The form will look like was sent from this email address."
msgstr "Het adres dat zal worden getoond als afzender van het formulier."

#: ../framework/extensions/learning/class-fw-extension-learning.php:56
msgid "Lesson"
msgstr "Les"

#: ../framework/extensions/learning/class-fw-extension-learning.php:57
#: ../framework/extensions/learning/views/content-course.php:14
msgid "Lessons"
msgstr "Lessen"

#: ../framework/extensions/learning/class-fw-extension-learning.php:78
msgid "Create a lesson"
msgstr "Maak een les aan"

#: ../framework/extensions/learning/class-fw-extension-learning.php:120
#: ../framework/extensions/learning/hooks.php:53
msgid "Course"
msgstr "Cursus"

#: ../framework/extensions/learning/class-fw-extension-learning.php:121
#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:108
msgid "Courses"
msgstr "Cursussen"

#: ../framework/extensions/learning/class-fw-extension-learning.php:142
msgid "Create a course"
msgstr "Maak een cursus aan"

#: ../framework/extensions/learning/class-fw-extension-learning.php:181
#: ../framework/extensions/learning/class-fw-extension-learning.php:222
msgid "Course Category"
msgstr "Cursus Categorie"

#: ../framework/extensions/learning/class-fw-extension-learning.php:182
#: ../framework/extensions/learning/class-fw-extension-learning.php:223
msgid "Course Categories"
msgstr "Cursus Categorieën"

#: ../framework/extensions/learning/class-fw-extension-learning.php:188
msgid "Search categories"
msgstr "Zoek categorieën"

#: ../framework/extensions/learning/class-fw-extension-learning.php:194
msgid "Add New category"
msgstr "Nieuwe Categorie Toevoegen"

#: ../framework/extensions/learning/class-fw-extension-learning.php:285
msgid "View all courses"
msgstr "Bekijk alle cursussen"

#: ../framework/extensions/learning/class-fw-extension-learning.php:511
msgid "No courses available"
msgstr "Geen cursussen beschikbaar"

#: ../framework/extensions/learning/class-fw-extension-learning.php:513
msgid "Without Course"
msgstr "Zonder Cursus"

#: ../framework/extensions/learning/class-fw-extension-learning.php:520
msgid "Select Course"
msgstr "Selecteer Cursus"

#: ../framework/extensions/learning/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:216
msgid "Learning"
msgstr "Lerend"

#: ../framework/extensions/learning/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:217
msgid ""
"This extension adds a Learning module to your theme. Using this extension "
"you can add courses, lessons and tests for your users to take."
msgstr "Deze uitbreiding voegt een Leer module toe aan je thema. Met deze uitbreiding kun je cursussen, lessen en toetsen voor je gebruikers toevoegen."

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:118
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:145
msgid "Quiz"
msgstr "Quiz"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:259
msgid "Quiz Elements"
msgstr "Quiz Elementen"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:272
msgid "Quiz settings"
msgstr "Quiz instellingen"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:276
msgid "Quiz Passmark Points"
msgstr "Quiz Slagingspunten"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:278
msgid "The points number at which the test will be passed."
msgstr "Het benodigde aantal punten om voor de toest te slagen."

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:286
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:296
msgid "Lesson Quiz"
msgstr "Les Quiz"

#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:535
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:544
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:552
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:559
#: ../framework/extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:568
msgid "Invalid Quiz"
msgstr "Ongeldige Quiz"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:21
#, php-format
msgid "You require %d points in oder to pass the test"
msgstr "U hebt %d punten nodig om voor de toets te slagen"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:27
msgid "Sorry, you did not pass the test"
msgstr "Helaas, je hebt de toets niet gehaald"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:29
msgid "Congratulation, you passed the test"
msgstr "Gefeliciteerd, je hebt de toets gehaald!"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:43
#, php-format
msgid "You answered correctly %s questions from %s"
msgstr "Je hebt %s vragen van de %s goed beantwoord"

#: ../framework/extensions/learning/extensions/learning-quiz/views/content.php:69
#: ../framework/extensions/learning/views/content-lesson.php:18
msgid "Back to"
msgstr "Terug naar"

#: ../framework/extensions/learning/extensions/learning-quiz/views/start-quiz.php:11
msgid "Start Quiz"
msgstr "Start Quiz"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:20
msgid "Correct answers"
msgstr "Juiste antwoorden"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:21
msgid "Add correct answer variants"
msgstr "Voeg varianten van juiste antwoorden toe"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:24
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:23
msgid "Set Correct Answer"
msgstr "Juist Antwoord Instellen"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:34
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:31
msgid "Wrong answers"
msgstr "Onjuiste antwoorden"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:35
msgid "Add wrong answer variants"
msgstr "Voeg varianten van onjuiste antwoorden toe"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:38
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:35
msgid "Set Wrong Answer"
msgstr "Onjuist Antwoord Instellen"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:42
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:55
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:53
msgid "Creates a"
msgstr "Creëert een"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:59
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:91
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:82
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:180
msgid "Multiple Choice"
msgstr "Meerkeuze"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
msgid "item"
msgstr "onderdeel"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:87
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:73
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:86
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:84
#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:76
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:75
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:80
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:80
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:48
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:75
msgid "Label"
msgstr "Label"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:88
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:75
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:87
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:85
msgid "Add/Edit Question"
msgstr "Vraag toevoegen/bewerken"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:90
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:77
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:90
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:87
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:58
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:51
msgid "Delete"
msgstr "Verwijder"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:92
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:90
msgid "More"
msgstr "Meer"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:93
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:91
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:57
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:57
msgid "Close"
msgstr "Sluiten"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:94
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:78
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:91
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:56
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:55
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:52
msgid "Edit Label"
msgstr "Bewerk Label"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:96
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:80
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:93
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:93
msgid "The question label is empty"
msgstr "Het label van de vraag is leeg"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:97
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:81
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:94
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:94
msgid "Invalid mark point number"
msgstr "Ongeldig markeringspunt nummer"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:98
msgid "There needs to be at least one correct answer"
msgstr "Er moet minimaal één juist antwoord zijn."

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:18
msgid "Correct answer"
msgstr "Juist antwoord"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:19
msgid "The question answer will be true or false"
msgstr "Het antwoord op de vraag zal waar of niet waar zijn"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:22
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:20
msgid "True"
msgstr "Waar"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:26
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:24
msgid "False"
msgstr "Niet Waar"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:45
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:74
msgid "True/False"
msgstr "Waar/Niet Waar"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:19
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:21
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:97
msgid "Text before gap"
msgstr "Tekst vóór de tussenruimte"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:28
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:30
msgid "Gap"
msgstr "Tussenruimte"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:37
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:39
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:98
msgid "Text after gap"
msgstr "Tekst na de tussenruimte"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:58
msgid "Gap Fill"
msgstr "Tussenruimte Vulling"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:89
msgid "Gap _____ Fill"
msgstr "Tussenruimte _____ Vulling"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:96
#, php-format
msgid "At least one of the fields ( %s or %s ) has to ve filled with text"
msgstr "Ten minste één van de velden (%s of %s) moet gevuld zijn met tekst"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:19
msgid "Correct Answer"
msgstr "Juist Antwoord"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:20
msgid "Write the correct answer text"
msgstr "Schrijf de tekst voor het juiste antwoord"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:32
msgid "Add wrong answers variants"
msgstr "Voeg varianten van onjuiste antwoorden toe"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:56
#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:23
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:82
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:180
msgid "Single Choice"
msgstr "Enkele Keuze"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:95
msgid "Correct answer cannot be empty"
msgstr "Het juiste antwoord kan niet leeg zijn"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:96
msgid "There are not any wrong answers set"
msgstr "Er zijn geen onjuiste antwoorden ingesteld"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:51
msgid "Question"
msgstr "Vraag"

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:53
msgid "Type the question..."
msgstr "Type de vraag..."

#: ../framework/extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:58
msgid "Points"
msgstr "Punten"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:18
msgid "Get list of courses"
msgstr "Krijg een lijst van alle cursussen"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:20
msgid "Lesson Courses"
msgstr "Les Cursussen"

#: ../framework/extensions/learning/includes/class-fw-widget-learning.php:126
msgid "Number of courses"
msgstr "Aantal cursussen"

#: ../framework/extensions/analytics/settings-options.php:11
msgid "Google Analytics"
msgstr "Google Analytics"

#: ../framework/extensions/analytics/settings-options.php:12
msgid "Enter your Google Analytics code (Ex: UA-XXXXX-X)"
msgstr "Vul je Google Analytics code in (Ex: UA-XXXXX-X)"

#: ../framework/extensions/analytics/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:192
msgid "Analytics"
msgstr "Analysegegevens"

#: ../framework/extensions/analytics/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:193
msgid ""
"Enables the possibility to add the Google Analytics tracking code that will "
"let you get all the analytics about visitors, page views and more."
msgstr "Maakt het mogelijk om de Google Analytics tracking code toe te voegen, welke je toegang geeft tot statistieken van bezoekers, bekeken pagina's en meer."

#: ../framework/extensions/blog/class-fw-extension-blog.php:36
#: ../framework/extensions/blog/class-fw-extension-blog.php:37
#: ../framework/extensions/breadcrumbs/settings-options.php:7
#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:10
msgid "Blog"
msgstr "Blog"

#: ../framework/extensions/blog/class-fw-extension-blog.php:38
msgid "Add blog post"
msgstr "Blogpost toevoegen"

#: ../framework/extensions/blog/class-fw-extension-blog.php:39
msgid "Add new blog post"
msgstr "Nieuwe blogpost toevoegen"

#: ../framework/extensions/blog/class-fw-extension-blog.php:40
msgid "All blog posts"
msgstr "Alle blogposts"

#: ../framework/extensions/blog/class-fw-extension-blog.php:41
msgid "Edit blog post"
msgstr "Blogpost bewerken"

#: ../framework/extensions/blog/class-fw-extension-blog.php:42
#: ../framework/extensions/blog/class-fw-extension-blog.php:43
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:49
msgid "Blog Post"
msgstr "Blogpost"

#: ../framework/extensions/blog/class-fw-extension-blog.php:44
msgid "New blog post"
msgstr "Nieuw blogpost"

#: ../framework/extensions/blog/class-fw-extension-blog.php:45
msgid "No blog posts found"
msgstr "Geen blogposts gevonden"

#: ../framework/extensions/blog/class-fw-extension-blog.php:46
msgid "No blog posts found in trash"
msgstr "Geen blogposts gevonden in prullenbak"

#: ../framework/extensions/blog/class-fw-extension-blog.php:47
msgid "Search blog posts"
msgstr "Blogposts zoeken"

#: ../framework/extensions/blog/class-fw-extension-blog.php:48
msgid "View blog post"
msgstr "Blogpost bekijken"

#: ../framework/extensions/blog/class-fw-extension-blog.php:67
#: ../framework/extensions/blog/class-fw-extension-blog.php:87
#: ../framework/extensions/blog/manifest.php:7
#: ../framework/extensions/blog/manifest.php:8
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:50
msgid "Blog Posts"
msgstr "Blogposts"

#: ../framework/extensions/blog/class-fw-extension-blog.php:76
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:74
msgid "Blog Categories"
msgstr "Blog Categorieën"

#: ../framework/extensions/styling/class-fw-extension-styling.php:60
#: ../framework/extensions/styling/class-fw-extension-styling.php:61
#: ../framework/extensions/styling/class-fw-extension-styling.php:78
#: ../framework/extensions/styling/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:48
msgid "Styling"
msgstr "Opmaak"

#: ../framework/extensions/styling/class-fw-extension-styling.php:104
#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:278
#: ../framework/core/components/backend.php:357
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2277
msgid "Save"
msgstr "Opslaan"

#: ../framework/extensions/styling/class-fw-extension-styling.php:118
msgid "You have no permission to change Styling options"
msgstr "Je hebt onvoldoende rechten om de opmaakinstellingen te wijzigen."

#: ../framework/extensions/styling/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:49
msgid ""
"This extension lets you control the website visual style. Starting from "
"predefined styles to changing specific fonts and colors across the website."
msgstr "Deze uitbreiding maakt het mogelijk de visuele opmaak van de website te beheren. Van opmaakprofielen tot het wijzigen van specifieke lettertypes en kleuren van de website."

#: ../framework/extensions/styling/extensions/switch-style-panel/manifest.php:7
msgid "Switch Style Panel"
msgstr "Opmaakwisselaar Paneel"

#: ../framework/extensions/styling/extensions/switch-style-panel/manifest.php:8
msgid ""
"Show on the front-end a panel that allows the user to make the switch "
"between predefined styles."
msgstr "Toon een paneel aan de front-end, waarmee de gebruiker in staat wordt gesteld te wisselen tussen vooraf gedefinieerde opmaakprofielen."

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:9
msgid "Frontend Style Switcher"
msgstr "Frontend Opmaakwisselaar"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:10
msgid "Enable frontend style switcher"
msgstr "Activeer frontend opmaakwisselaar"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:13
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:274
#: ../framework/extensions/shortcodes/shortcodes/map/options.php:45
#: ../framework/extensions/shortcodes/shortcodes/button/options.php:24
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:42
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:34
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:48
#: ../framework/includes/option-types/simple.php:454
#: ../framework/includes/option-types/switch/class-fw-option-type-switch.php:151
msgid "Yes"
msgstr "Ja"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:17
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:278
#: ../framework/extensions/shortcodes/shortcodes/map/options.php:49
#: ../framework/extensions/shortcodes/shortcodes/button/options.php:28
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:46
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:38
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:52
#: ../framework/includes/option-types/switch/class-fw-option-type-switch.php:147
msgid "No"
msgstr "Nee"

#: ../framework/extensions/styling/extensions/switch-style-panel/options/settings.php:24
msgid "The text that will be displayed at the top of the panel."
msgstr "De text die wordt getoond aan de bovenzijde van het paneel. "

#: ../framework/extensions/styling/includes/option-types/style/views/settings.php:119
msgid "Background"
msgstr "Achtergrond"

#: ../framework/extensions/styling/includes/option-types/style/views/predefined.php:26
msgid "Predefined Styles"
msgstr "Voorgedefiniëerde opmaakprofielen"

#: ../framework/extensions/styling/includes/option-types/style/views/preview.php:41
msgid "This is a simplified preview, not changes are reflected."
msgstr "Dit is een gesimplificeerde voorvertoning; geen veranderingen zijn te zien."

#: ../framework/extensions/feedback/class-fw-extension-feedback.php:64
#: ../framework/core/components/extensions/manager/available-extensions.php:144
msgid "Feedback"
msgstr "Terugkoppeling"

#: ../framework/extensions/feedback/class-fw-extension-feedback.php:111
msgid "Reviews"
msgstr "Recensies"

#: ../framework/extensions/feedback/settings-options.php:10
#: ../framework/extensions/shortcodes/extensions/page-builder/settings-options.php:11
msgid "Activate for"
msgstr "Activeer voor"

#: ../framework/extensions/feedback/settings-options.php:16
msgid "Select the options you want the Feedback extension to be activated for"
msgstr "Selecteer de opties waarvoor je de Terugkoppeling uitbreiding wilt activeren"

#: ../framework/extensions/feedback/manifest.php:7
msgid "FeedBack"
msgstr "Terugkoppeling"

#: ../framework/extensions/feedback/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:145
msgid ""
"Adds the possibility to leave feedback (comments, reviews and rating) about "
"your products, articles, etc. This replaces the default comments system."
msgstr "Voegt de mogelijkheid toe om terugkoppeling te geven (reacties, recensies en cijfers) over je producten, artikelen etc. Dit vervangt het standaard reactiesysteem. "

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:84
msgid "Rating:"
msgstr "Cijfer:"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:195
msgid "Feedback Stars"
msgstr "Terugkoppeling Sterren"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:221
#: ../framework/extensions/feedback/extensions/feedback-stars/views/rate.php:12
msgid "Rating"
msgstr "Cijfer"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "ERROR"
msgstr "FOUT"

#: ../framework/extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "please rate the post."
msgstr "waardeer het bericht a.u.b."

#: ../framework/extensions/feedback/extensions/feedback-stars/manifest.php:7
msgid "FeedBack Stars"
msgstr "Terugkoppeling Sterren"

#: ../framework/extensions/feedback/extensions/feedback-stars/manifest.php:8
msgid "Allows visitors to appreciate a post using star rating"
msgstr "Laat bezoekers toe een bericht te waarderen met sterren"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
msgid "Pingback:"
msgstr "Pingback:"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
msgid "(Edit)"
msgstr "(Bewerk)"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:37
msgid "Post author"
msgstr "Bericht auteur"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:57
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
#, php-format
msgid "%1$s at %2$s"
msgstr "%1$s op %2$s"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review.php:63
#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:53
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:56
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:122
msgid "Your comment is awaiting moderation."
msgstr "Je reactie wacht op goedkeuring."

#: ../framework/extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:24
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:40
#: ../framework/extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:119
msgid "says"
msgstr "zegt"

#: ../framework/extensions/feedback/extensions/feedback-stars/views/view-rates.php:24
#, php-format
msgid "Based on %s Votes"
msgstr "Gebaseerd op %s stemmen"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:6
msgid "Rating System"
msgstr "Waarderingssysteem"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:9
msgid "Enter the number of stars you want in the rating system"
msgstr "Geef het aantal sterren aan dat je wilt gebruiken in het waarderingssysteem"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:11
msgid "5 stars"
msgstr "5 sterren"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:12
msgid "7 stars"
msgstr "7 sterren"

#: ../framework/extensions/feedback/extensions/feedback-stars/options/settings.php:13
msgid "10 stars"
msgstr "10 sterren"

#: ../framework/extensions/feedback/views/reviews.php:32
#: ../framework/extensions/feedback/views/reviews.php:53
msgid "Comment navigation"
msgstr "Reactienavigatie"

#: ../framework/extensions/feedback/views/reviews.php:35
#: ../framework/extensions/feedback/views/reviews.php:56
msgid "&larr; Older Comments"
msgstr "&larr; Oudere Reacties"

#: ../framework/extensions/feedback/views/reviews.php:36
#: ../framework/extensions/feedback/views/reviews.php:57
msgid "Newer Comments &rarr;"
msgstr "Nieuwere Reacties &rarr;"

#: ../framework/extensions/feedback/views/reviews.php:62
msgid "Comments are closed."
msgstr "Reacties zijn niet beschikbaar."

#: ../framework/extensions/shortcodes/extensions/page-builder/settings-options.php:18
msgid ""
"Select the posts you want the Page Builder extension to be activated for"
msgstr "Selecteer de berichttypes waarvoor u de Pagina Bouwer uitbreiding wilt activeren"

#: ../framework/extensions/shortcodes/extensions/page-builder/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:84
msgid "Page Builder"
msgstr "Pagina Bouwer"

#: ../framework/extensions/shortcodes/extensions/page-builder/manifest.php:9
msgid ""
"Lets you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr "Maakt het mogelijk om makkelijk ontelbare pagina's te bouwen met behulp van de drag-and-drop visuele paginabouwer, welke komt met een groot aantal kant-en-klare shortcodes."

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:51
msgid ""
"There must not be more than one page Editor integrated with the wp post "
"editor per page"
msgstr "Er kan niet meer dan één paginabewerker per pagina geïntegreerd zijn met de wp post editor"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:96
msgid "Visual Page Builder"
msgstr "Visuele Pagina Bouwer"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:97
msgid "Default Editor"
msgstr "Standaard Bewerker"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:126
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:117
#: ../framework/extensions/shortcodes/shortcodes/section/config.php:5
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:22
msgid "Layout Elements"
msgstr "Layout Elementen"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:99
#, php-format
msgid "No Page Builder tab specified for shortcode: %s"
msgstr "Geen Pagina Bouwer tabblad gespecificeerd voor de shortcode: %s"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:111
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:75
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:83
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:57
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:238
#: ../framework/core/components/extensions/manager/views/extension.php:141
#: ../framework/core/components/extensions/manager/views/extension.php:345
msgid "Remove"
msgstr "Verwijder"

#: ../framework/extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:112
#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:74
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:82
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:56
msgid "Duplicate"
msgstr "Dupliceer"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:12
msgid "Testimonials"
msgstr "Recensies"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:9
msgid "Add some Testimonials"
msgstr "Voeg enkele Recensies toe"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/table/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/map/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/button/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:10
#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:10
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:10
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:106
msgid "Content Elements"
msgstr "Inhoudselementen"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:8
msgid "Option Testimonials Title"
msgstr "Optie Recensies TItel"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:13
msgid "Add/Edit Testimonial"
msgstr "Recensie Toevoegen/Bewerken"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:14
msgid "Here you can add, remove and edit your Testimonials."
msgstr "Hier kun je Recensies toevoegen, verwijderen en bewerken."

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:19
msgid "Quote"
msgstr "Citaat"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:20
msgid "Enter the testimonial here"
msgstr "Voer de recensie hier in"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:25
#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:6
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:102
msgid "Image"
msgstr "Afbeelding"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:26
#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:9
msgid ""
"Either upload a new, or choose an existing image from your media library"
msgstr "Upload een nieuwe afbeelding of kies een bestaande afbeelding uit je mediabibliotheek"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:30
msgid "Name"
msgstr "Naam"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:31
msgid "Enter the Name of the Person to quote"
msgstr "Voer de naam in van de persoon die je wilt citeren"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:35
msgid "Position"
msgstr "Positie"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:36
msgid "Can be used for a job description"
msgstr "Kan worden gebruik voor een baan beschrijving"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:40
msgid "Website Name"
msgstr "Website Naam"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:41
msgid "Linktext for the above Link"
msgstr "Linktekst voor de bovenstaande link"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:45
msgid "Website Link"
msgstr "Website Link"

#: ../framework/extensions/shortcodes/shortcodes/testimonials/options.php:46
msgid "Link to the Persons website"
msgstr "Link naar de website van de persoon"

#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:8
msgid "Accordion"
msgstr "Accordion"

#: ../framework/extensions/shortcodes/shortcodes/accordion/config.php:9
msgid "Add an Accordion"
msgstr "Voeg een Accordion toe"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:8
msgid "Tabs"
msgstr "Tabbladen"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:9
msgid "Add/Edit Tabs"
msgstr "Tabbladen Toevoegen/Bewerken"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:10
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:10
msgid "Create your tabs"
msgstr "Maak je tabbladen aan"

#: ../framework/extensions/shortcodes/shortcodes/accordion/options.php:19
#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:24
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/text-block/options.php:10
#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:19
msgid "Content"
msgstr "Inhoud"

#: ../framework/extensions/shortcodes/shortcodes/table/config.php:8
msgid "Table"
msgstr "Tabel"

#: ../framework/extensions/shortcodes/shortcodes/table/config.php:9
msgid "Add a Table"
msgstr "Voeg een Tabel toe"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:64
msgid "table-builder option type must be inside the table shortcode"
msgstr "tabel-bouwer optie type moet binnen de tabel shortcode zitten"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:171
msgid "Table Styling"
msgstr "Tabel Opmaak"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:172
msgid "Choose the table styling options"
msgstr "Kies de opmaakinstellingen voor de tabel"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:174
msgid "Use the table as a pricing table"
msgstr "Gebruik de tabel als prijstabel"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:175
msgid "Use the table to display tabular data"
msgstr "Gebruik de tabel om tabeldata te vertonen"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:197
msgid "Default row"
msgstr "Standaard rij"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:198
msgid "Heading row"
msgstr "Top rij"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:199
msgid "Pricing row"
msgstr "Prijsstelling rij"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:200
msgid "Button row"
msgstr "Knop rij"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:201
msgid "Row switch"
msgstr "Rijwisselaar"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:211
msgid "Default column"
msgstr "Standaard kolom"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:212
msgid "Description column"
msgstr "Beschrijving kolom"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:213
msgid "Highlight column"
msgstr "Geaccentueerde kolom"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:214
msgid "Center text column"
msgstr "Centreer tekst kolom"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:251
msgid "per month"
msgstr "per maand"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:262
#: ../framework/extensions/shortcodes/shortcodes/button/config.php:8
msgid "Button"
msgstr "Knop"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:263
#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:39
#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:148
#: ../framework/includes/option-types/addable-option/class-fw-option-type-addable-option.php:20
#: ../framework/includes/option-types/addable-popup/class-fw-option-type-addable-popup.php:153
#: ../framework/includes/option-types/addable-box/class-fw-option-type-addable-box.php:186
msgid "Add"
msgstr "Toevoegen"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-head-template.php:27
msgid "Add Column"
msgstr "Kolom Toevoegen"

#: ../framework/extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/view.php:161
msgid "Add Row"
msgstr "Rij Toevoegen"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:24
#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:25
msgid "Custom"
msgstr "Aangepast"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:27
msgid "Locations"
msgstr "Locaties"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:28
msgid "Add/Edit Location"
msgstr "Locatie Toevoegen/Bewerken"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:31
msgid "Note: Please set location"
msgstr "Opmerking: Stel een locatie in"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:35
#: ../framework/extensions/events/class-fw-extension-events.php:187
msgid "Location"
msgstr "Locaties"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:39
msgid "Location Title"
msgstr "Locatie Titel"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:40
msgid "Set location title"
msgstr "Stel een locatie titel in"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:44
msgid "Location Description"
msgstr "Locatie Beschrijving"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:45
msgid "Set location description"
msgstr "Stel een locatie beschrijving in"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:49
msgid "Location Url"
msgstr "Locatie Url"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:50
msgid "Set page url (Ex: http://example.com)"
msgstr "Stel pagina url in (Bijv.: http://example.com)"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:53
msgid "Location Image"
msgstr "Locatie Afbeelding"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:54
msgid "Add location image"
msgstr "Voeg een locatie afbeelding toe"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:113
msgid "No location provider specified for map shortcode"
msgstr "Geen locatie provider gespecificeerd voor de kaart shortcode"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:115
msgid "Map Placeholder"
msgstr "Kaart Placeholder"

#: ../framework/extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:122
#, php-format
msgid "Unknown location provider \"%s\" specified for map shortcode"
msgstr "Onbekende locatie provider \"%s\" gespecificeerd voor de kaart shortcode"

#: ../framework/extensions/shortcodes/shortcodes/map/config.php:8
msgid "Map"
msgstr "Kaart"

#: ../framework/extensions/shortcodes/shortcodes/map/config.php:9
msgid "Add a Map"
msgstr "Voeg een kaart toe"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:13
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:214
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:473
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:11
msgid "Population Method"
msgstr "Populatie methode"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:14
msgid "Select map population method (Ex: events, custom)"
msgstr "Selecteer kaartpopulatie methode: (Bijv.: evenementen, aangepast)"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:24
msgid "Map Type"
msgstr "Kaart Type"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:25
msgid "Select map type"
msgstr "Selecteer kaarttype"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:27
msgid "Roadmap"
msgstr "Wegenkaart"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:28
msgid "Terrain"
msgstr "Terrein"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:29
msgid "Satellite"
msgstr "Satelliet"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:30
msgid "Hybrid"
msgstr "Hybride"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:34
msgid "Map Height"
msgstr "Kaart Hoogte"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:35
msgid "Set map height (Ex: 300)"
msgstr "Hoogte van de kaart instellen (Bijv.: 300)"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:41
msgid "Disable zoom on scroll"
msgstr "Schakel zoom tijdens scrollen uit"

#: ../framework/extensions/shortcodes/shortcodes/map/options.php:42
msgid "Prevent the map from zooming when scrolling until clicking on the map"
msgstr "Voorkom zoomen van de kaart door scrolling tot er op de kaart is geklikt"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:76
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:65
msgid "Column"
msgstr "Kolom"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:120
#, php-format
msgid "Add a %s column"
msgstr "Voeg een %s kolom toe"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:12
msgid "Columns"
msgstr "Kolommen"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:47
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:47
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:51
msgid "No Templates Saved"
msgstr "Geen Sjablonen Opgeslagen"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:50
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:50
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:54
msgid "Load Template"
msgstr "Sjabloon Laden"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:91
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:91
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:94
msgid "Template Name"
msgstr "Sjabloon Naam"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:92
msgid "Save Column"
msgstr "Kolom Opslaan"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:93
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:93
msgid "Save as Template"
msgstr "Opslaan als Sjabloon"

#: ../framework/extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:174
#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:174
#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:206
msgid "No Title"
msgstr "Geen Titel"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:6
msgid "Special Heading"
msgstr "Speciale Heading"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/config.php:7
msgid "Add a Special Heading"
msgstr "Voeg een Speciale Heading toe"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:8
msgid "Heading Title"
msgstr "Heading Titel"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:9
msgid "Write the heading title content"
msgstr "Schrijf de inhoud voor de heading titel"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:13
msgid "Heading Subtitle"
msgstr "Heading Ondertitel"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:14
msgid "Write the heading subtitle content"
msgstr "Schrijf de inhoud voor de heading ondertitel"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:18
msgid "Heading Size"
msgstr "Heading Grootte"

#: ../framework/extensions/shortcodes/shortcodes/special-heading/options.php:30
msgid "Centered"
msgstr "Gecentreerd"

#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:8
msgid "Team Member"
msgstr "Teamlid"

#: ../framework/extensions/shortcodes/shortcodes/team-member/config.php:9
msgid "Add a Team Member"
msgstr "Voeg een Teamlid toe"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:7
msgid "Team Member Image"
msgstr "Teamlid Afbeelding"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:12
msgid "Team Member Name"
msgstr "Teamlid Naam"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:13
msgid "Name of the person"
msgstr "Naam van deze persoon"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:18
msgid "Team Member Job Title"
msgstr "Teamlid Functie"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:19
msgid "Job title of the person."
msgstr "Functie van deze persoon."

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:24
msgid "Team Member Description"
msgstr "Teamlid Beschrijving"

#: ../framework/extensions/shortcodes/shortcodes/team-member/options.php:25
msgid "Enter a few words that describe the person"
msgstr "Schrijf een paar woorden die deze persoon omschrijven"

#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:6
#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:8
#: ../framework/extensions/megamenu/class-fw-extension-megamenu.php:115
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:188
msgid "Icon"
msgstr "Icoon"

#: ../framework/extensions/shortcodes/shortcodes/icon/config.php:7
msgid "Add an Icon"
msgstr "Voeg een Icoon toe"

#: ../framework/extensions/shortcodes/shortcodes/icon/options.php:13
msgid "Icon title"
msgstr "Icoon titel"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:6
msgid "Icon Box"
msgstr "Icoon Box"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/config.php:7
msgid "Add an Icon Box"
msgstr "Voeg een Icoon Box toe"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:8
msgid "Box Style"
msgstr "Box Opmaak"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:10
msgid "Icon above title"
msgstr "Icoon boven titel"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:11
msgid "Icon in line with title"
msgstr "Icoon op dezelfde lijnhoogte als de titel"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:16
msgid "Choose an Icon"
msgstr "Kies een Icoon"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:20
msgid "Title of the Box"
msgstr "Titel van de Box"

#: ../framework/extensions/shortcodes/shortcodes/icon-box/options.php:25
msgid "Enter the desired content"
msgstr "Voer de gewenste inhoud in"

#: ../framework/extensions/shortcodes/shortcodes/button/config.php:9
msgid "Add a Button"
msgstr "Voeg een Knop toe"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:7
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:17
msgid "Button Label"
msgstr "Knop Label"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:8
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:18
msgid "This is the text that appears on your button"
msgstr "Dit is de tekst die in de knop wordt getoond"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:13
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:23
msgid "Button Link"
msgstr "Knop Link"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:14
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:24
msgid "Where should your button link to"
msgstr "Waar moet je knop naar verwijzen "

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:20
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:38
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:30
msgid "Open Link in New Window"
msgstr "Open Link in Nieuw Venster"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:21
#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:39
#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:31
msgid "Select here if you want to open the linked page in a new window"
msgstr "Selecteer als de gelinkte pagina in een nieuw venster moet worden geopend"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:32
msgid "Button Color"
msgstr "Knop Kleur"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:33
msgid "Choose a color for your button"
msgstr "Kies een kleur voor de knop"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:36
msgid "Default"
msgstr "Standaard"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:37
msgid "Black"
msgstr "Zwart"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:38
msgid "Blue"
msgstr "Blauw"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:39
msgid "Green"
msgstr "Groen"

#: ../framework/extensions/shortcodes/shortcodes/button/options.php:40
msgid "Red"
msgstr "Rood"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:6
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:108
msgid "Video"
msgstr "Video"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:7
msgid "Add a Video"
msgstr "Voeg een Video toe"

#: ../framework/extensions/shortcodes/shortcodes/media-video/config.php:8
#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:8
#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:10
msgid "Media Elements"
msgstr "Media Elementen"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:8
msgid "Insert Video URL"
msgstr "Voeg Video URL in"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:9
#: ../framework/extensions/shortcodes/shortcodes/section/options.php:24
msgid "Insert Video URL to embed this video"
msgstr "Voeg Video URL in om deze video in te sluiten"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:13
msgid "Video Width"
msgstr "Video Breedte"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:14
msgid "Enter a value for the width"
msgstr "Voer een waarde in voor de breedte"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:19
msgid "Video Height"
msgstr "Video Hoogte"

#: ../framework/extensions/shortcodes/shortcodes/media-video/options.php:20
msgid "Enter a value for the height"
msgstr "Voer een waarde in voor de hoogte"

#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:8
#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:16
msgid "Calendar"
msgstr "Kalender"

#: ../framework/extensions/shortcodes/shortcodes/calendar/config.php:9
msgid "Add a Calendar"
msgstr "Voeg een Kalender toe"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:14
msgid "Select calendar population method (Ex: events, custom)"
msgstr "Stel kalender populatie methode in: (Bijv.: evenementen, aangepast)"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:23
msgid "Calendar Type"
msgstr "Kalender Type"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:24
msgid "Select calendar type"
msgstr "Stel kalender type in"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:27
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:23
msgid "Daily"
msgstr "Dagelijks"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:28
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:22
msgid "Weekly"
msgstr "Wekelijks"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:29
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:21
msgid "Monthly"
msgstr "Maandelijks"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:33
msgid "Start Week On"
msgstr "Begin Week Op"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:34
msgid "Select first day of week"
msgstr "Selecteer eerste dag van de week"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:37
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:110
msgid "Monday"
msgstr "Maandag"

#: ../framework/extensions/shortcodes/shortcodes/calendar/options.php:38
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:109
msgid "Sunday"
msgstr "Zondag"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:29
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:56
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:82
#: ../framework/extensions/events/class-fw-extension-events.php:69
#: ../framework/extensions/events/class-fw-extension-events.php:74
#: ../framework/extensions/events/manifest.php:7
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:77
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:185
#: ../framework/core/components/extensions/manager/available-extensions.php:180
msgid "Events"
msgstr "Evenementen"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:30
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:59
msgid "Add/Edit Date & Time"
msgstr "Voeg toe/Bewerk Datum & Tijd"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:33
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:63
msgid "Note: Please set start & end event datetime"
msgstr "Notitie: Voer een begin- en einddatum en -tijd in voor het evenement"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:37
msgid "Event Title"
msgstr "Evenement Titel"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:38
msgid "Enter the event title"
msgstr "Voer de evenement titel in"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:42
msgid "Event URL"
msgstr "Evenement URL"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:43
msgid "Enter the event URL (Ex: http://your-domain.com/event)"
msgstr "Voer de evenement URL in (Bijv.: http://jouw-domein.com/evenement)"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:47
#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:58
msgid "Date & Time"
msgstr "Datum & Tijd"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:48
msgid "Enter the event date & time"
msgstr "Voer evenement datum en tijd in"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:129
msgid "No events provider specified for calendar shortcode"
msgstr "Geen evenementen provider gespecificeerd voor kalender shortcode"

#: ../framework/extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:138
#, php-format
msgid "Unknown events provider \"%s\" specified for calendar shortcode"
msgstr "Onbekende evenementen provider \"%s\" gespecificeerd voor de kalender shortcode"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:55
#: ../framework/extensions/events/class-fw-extension-events.php:68
#: ../framework/extensions/events/class-fw-extension-events.php:75
msgid "Event"
msgstr "Evenement"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:57
#: ../framework/extensions/shortcodes/shortcodes/calendar/views/view.php:23
msgid "Today"
msgstr "Vandaag"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:66
#, php-format
msgid "Calendar: View %s not found"
msgstr "Kalender: Vertoning %s is niet gevonden"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:67
#, php-format
msgid "Calendar: Wrong date format %s. Should be either \"now\" or \"yyyy-mm-dd\""
msgstr "Kalender: Verkeerde tijdsindeling %s. Moet of \"nu\" of \"jjjj-mm-dd\" zijn."

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:69
msgid "Calendar: Event URL is not set"
msgstr "Kalender: Evenement URL is niet ingesteld"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:70
#, php-format
msgid ""
"Calendar: Wrong navigation direction %s. Can be only \"next\" or \"prev\" or"
" \"today\""
msgstr "Kalender: Verkeerde navigatierichting %s. Kan alleen \"volgende\", \"vorige\" of \"vandaag\" zijn"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:72
msgid ""
"Calendar: Time split parameter should divide 60 without decimals. Something "
"like 10, 15, 30"
msgstr "Kalender: Tijdsplitsing parameter moet 60 verdelen zonder decimalen. Iets als 10, 15, 30"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:74
msgid "No events in this day."
msgstr "Geen evenementen op deze dag."

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:77
#, php-format
msgid "week %s of %s"
msgstr "week %s van %s"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:79
msgid "Week "
msgstr "Week"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:80
msgid "All day"
msgstr "Hele dag"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:81
msgid "Time"
msgstr "Tijd"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:83
msgid "Ends before timeline"
msgstr "Eindigt vóór de tijdlijn"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:84
msgid "Starts after timeline"
msgstr "Start na de tijdlijn"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:85
msgid "January"
msgstr "Januari"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:86
msgid "February"
msgstr "Februari"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:87
msgid "March"
msgstr "Maart"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:88
msgid "April"
msgstr "Apri"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:89
#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:101
msgid "May"
msgstr "Mei"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:90
msgid "June"
msgstr "Juni"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:91
msgid "July"
msgstr "Juli"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:92
msgid "August"
msgstr "Augustus"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:93
msgid "September"
msgstr "September"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:94
msgid "October"
msgstr "Oktober"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:95
msgid "November"
msgstr "November"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:96
msgid "December"
msgstr "December"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:97
msgid "Jan"
msgstr "Jan"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:98
msgid "Feb"
msgstr "Feb"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:99
msgid "Mar"
msgstr "Mar"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:100
msgid "Apr"
msgstr "Apr"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:102
msgid "Jun"
msgstr "Jun"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:103
msgid "Jul"
msgstr "Jul"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:104
msgid "Aug"
msgstr "Aug"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:105
msgid "Sep"
msgstr "Sep"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:106
msgid "Oct"
msgstr "Okt"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:107
msgid "Nov"
msgstr "Nov"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:108
msgid "Dec"
msgstr "Dec"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:111
msgid "Tuesday"
msgstr "Dinsdag"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:112
msgid "Wednesday"
msgstr "Woensdag"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:113
msgid "Thursday"
msgstr "Donderdag"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:114
msgid "Friday"
msgstr "Vrijdag"

#: ../framework/extensions/shortcodes/shortcodes/calendar/static.php:115
msgid "Saturday"
msgstr "Zaterdag"

#: ../framework/extensions/shortcodes/shortcodes/media-image/config.php:7
msgid "Add an Image"
msgstr "Voeg een Afbeelding toe"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:8
msgid "Choose Image"
msgstr "Afbeelding kiezen"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:16
msgid "Width"
msgstr "Breedte"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:17
msgid "Set image width"
msgstr "Stel afbeeldingsbreedte in"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:22
#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:24
msgid "Height"
msgstr "Hoogte"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:23
msgid "Set image height"
msgstr "Stel afbeeldingshoogte in"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:33
msgid "Image Link"
msgstr "Afbeelding Link"

#: ../framework/extensions/shortcodes/shortcodes/media-image/options.php:34
msgid "Where should your image link to?"
msgstr "Waar moet de afbeelding naar verwijzen?"

#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:8
msgid "Notification"
msgstr "Notificatie"

#: ../framework/extensions/shortcodes/shortcodes/notification/config.php:9
msgid "Add a Notification Box"
msgstr "Voeg een Notificatie Box toe"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:7
msgid "Message"
msgstr "Bericht"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:8
msgid "Notification message"
msgstr "Notificatie bericht"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:10
msgid "Message!"
msgstr "Bericht!"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:13
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:444
msgid "Type"
msgstr "Type"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:14
msgid "Notification type"
msgstr "Notificatie type"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:17
msgid "Congratulations"
msgstr "Gefeliciteerd"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:18
msgid "Information"
msgstr "Informatie"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:19
msgid "Alert"
msgstr "Alarm"

#: ../framework/extensions/shortcodes/shortcodes/notification/options.php:20
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:456
msgid "Error"
msgstr "Fout"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:9
msgid "Congratulations!"
msgstr "Gefeliciteerd!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:13
msgid "Information!"
msgstr "Informatie!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:17
msgid "Alert!"
msgstr "Alarm!"

#: ../framework/extensions/shortcodes/shortcodes/notification/views/view.php:21
msgid "Error!"
msgstr "Fout!"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:8
msgid "Widget Area"
msgstr "Widget Gebied"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/config.php:9
msgid "Add a Widget Area"
msgstr "Voeg een Widget Gebied toe"

#: ../framework/extensions/shortcodes/shortcodes/widget-area/options.php:5
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:5
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:7
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:40
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:91
msgid "Sidebar"
msgstr "Zijkolom"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:8
msgid "Call To Action"
msgstr "Oproep Tot Actie"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/config.php:9
msgid "Add a Call to Action"
msgstr "Voeg Oproep Tot Actie toe"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:9
msgid "This can be left blank"
msgstr "Dit kan leeg blijven"

#: ../framework/extensions/shortcodes/shortcodes/call-to-action/options.php:14
msgid "Enter some content for this Info Box"
msgstr "Voer inhoud in voor deze Info Box"

#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:8
msgid "Text Block"
msgstr "Tekstblok"

#: ../framework/extensions/shortcodes/shortcodes/text-block/config.php:9
msgid "Add a Text Block"
msgstr "Voeg een Tekstblok toe"

#: ../framework/extensions/shortcodes/shortcodes/text-block/options.php:11
msgid "Enter some content for this texblock"
msgstr "Voer inhoud in voor dit tekstblok"

#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:6
msgid "Divider"
msgstr "Verdeler"

#: ../framework/extensions/shortcodes/shortcodes/divider/config.php:7
msgid "Add a Divider"
msgstr "Voeg een Verdeler toe"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:13
msgid "Ruler Type"
msgstr "Lineaal Type"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:14
msgid "Here you can set the styling and size of the HR element"
msgstr "Hier kun je de opmaak en afmetingen van het HR element instellen"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:16
msgid "Line"
msgstr "Regel"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:17
msgid "Whitespace"
msgstr "Witruimte"

#: ../framework/extensions/shortcodes/shortcodes/divider/options.php:25
msgid ""
"How much whitespace do you need? Enter a pixel value. Positive value will "
"increase the whitespace, negative value will reduce it. eg: '50', '-25', "
"'200'"
msgstr "Hoeveel witruimte heb je nodig? Voer een pixelwaarde in. Positieve waarde zal de witruimte vergroten, negatieve waarde zal het verkleinen. Voorbeeld: '50', '-25', '200'"

#: ../framework/extensions/shortcodes/shortcodes/section/config.php:6
#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:23
msgid "Section"
msgstr "Sectie"

#: ../framework/extensions/shortcodes/shortcodes/section/config.php:7
msgid "Add a Section"
msgstr "Voeg een Sectie toe"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:7
msgid "Full Width"
msgstr "Volledige Breedte"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:11
msgid "Background Color"
msgstr "Achtergrondkleur"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:12
msgid "Please select the background color"
msgstr "Stel de achtergrondkleur in"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:16
msgid "Background Image"
msgstr "Achtergrondafbeelding"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:17
msgid "Please select the background image"
msgstr "Stel de achtergrondafbeelding in"

#: ../framework/extensions/shortcodes/shortcodes/section/options.php:23
msgid "Background Video"
msgstr "Achtergrondvideo"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:12
msgid "Sections"
msgstr "Secties"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:92
msgid "Save Section"
msgstr "Sectie Opslaan"

#: ../framework/extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:24
msgid "Creates a section"
msgstr "Maakt een sectie aan"

#: ../framework/extensions/shortcodes/shortcodes/tabs/config.php:9
msgid "Add some Tabs"
msgstr "Voeg Tabbladen toe"

#: ../framework/extensions/shortcodes/shortcodes/tabs/options.php:9
msgid "Add/Edit Tab"
msgstr "Tabblad Toevoegen/Bewerken"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcode.php:194
#, php-format
msgid "No default view (views/view.php) found for shortcode: %s"
msgstr "Geen standaard weergave (views/view.php) gevonden voor de shortcode: %s"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:203
#, php-format
msgid "Shortcode \"%s\" from %s was already defined at %s"
msgstr "Shortcode \"%s\" afkomstig van %s is al gedefinieerd bij %s"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:264
#, php-format
msgid "Class file found for shortcode %s but no class %s found"
msgstr "Klassebestand gevonden voor shortcode %s maar geen klasse voor %s gevonden"

#: ../framework/extensions/shortcodes/includes/class-fw-shortcodes-loader.php:269
#, php-format
msgid "The class %s must extend from FW_Shortcode"
msgstr "De klasse %s moet uit FW_Shortcode voortkomen"

#: ../framework/extensions/builder/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:204
msgid "Builder"
msgstr "Bouwer"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:306
msgid "Full Screen"
msgstr "Volledig Scherm"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:307
msgid "Exit Full Screen"
msgstr "Volledig Scherm Verlaten"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:334
msgid "Undo"
msgstr "Ongedaan maken"

#: ../framework/extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:335
msgid "Redo"
msgstr "Opnieuw uitvoeren"

#: ../framework/extensions/builder/includes/option-types/builder/includes/fullscreen.php:81
msgid "Preview Changes"
msgstr "Wijzigingen Bekijken"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/class-fw-ext-builder-templates.php:119
msgid "Templates"
msgstr "Sjablonen"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:12
msgid "Full Templates"
msgstr "Volledig Sjabloon"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:61
msgid "Save Full Template"
msgstr "Volledig Sjabloon Opslaan"

#: ../framework/extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:95
msgid "Save Builder Template"
msgstr "Builder Sjabloon Opslaan"

#: ../framework/extensions/social/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:252
msgid "Social"
msgstr "Sociaal"

#: ../framework/extensions/social/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:253
msgid ""
"Use this extension to configure all your social related APIs. Other "
"extensions will use the Social extension to connect to your social accounts."
msgstr "Gebruik deze uitbreiding om je sociale media gerelateerde API's te configureren. Andere uitbreidingen gebruiken de Sociale Media uitbreiding om verbinding te maken met je sociale media accounts."

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:62
#: ../framework/core/components/backend.php:584
msgid "Facebook"
msgstr "Facebook"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:66
#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:33
msgid "API Settings"
msgstr "API Instellingen"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:72
msgid "App ID/API Key:"
msgstr "ID/API Sleutel Toevoegen"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:73
msgid "Enter Facebook App ID / API Key."
msgstr "Vul Facebook App ID / API sleutel in."

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:78
msgid "App Secret:"
msgstr "App Secret:"

#: ../framework/extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:79
msgid "Enter Facebook App Secret."
msgstr "Vul Facebook App Secret in."

#: ../framework/extensions/social/extensions/social-facebook/manifest.php:7
#: ../framework/extensions/social/extensions/social-facebook/manifest.php:8
msgid "Social Facebook"
msgstr "Sociaal Facebook"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:29
#: ../framework/core/components/backend.php:592
msgid "Twitter"
msgstr "Twitter"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:39
msgid "Consumer Key"
msgstr "Gebruiker Sleutel"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:40
msgid "Enter Twitter Consumer Key."
msgstr "Vul Twitter Gebruiker Sleutel in."

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:45
msgid "Consumer Secret"
msgstr "Gebruiker Secret"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:46
msgid "Enter Twitter App Secret."
msgstr "Vul Twitter App Secret in."

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:51
msgid "Access Token"
msgstr "Access Token"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:52
msgid "Enter Twitter Access Token."
msgstr "Vul Twitter Access Token in."

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:57
msgid "Access Token Secret"
msgstr "Access Token Secret"

#: ../framework/extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:58
msgid "Enter Twitter Access Token Secret."
msgstr "Vul Twitter Access Token Secret in."

#: ../framework/extensions/social/extensions/social-twitter/manifest.php:7
#: ../framework/extensions/social/extensions/social-twitter/manifest.php:8
msgid "Social Twitter"
msgstr "Sociaal Twitter"

#: ../framework/extensions/forms/class-fw-extension-forms.php:112
#: ../framework/extensions/forms/class-fw-extension-forms.php:123
#: ../framework/extensions/forms/class-fw-extension-forms.php:131
#: ../framework/extensions/forms/class-fw-extension-forms.php:142
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:102
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:114
#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:124
msgid "Unable to process the form"
msgstr "Niet in staat het formulier te verwerken"

#: ../framework/extensions/forms/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:228
msgid "Forms"
msgstr "Formulieren"

#: ../framework/extensions/forms/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:229
msgid ""
"This extension adds the possibility to create a contact form. Use the drag &"
" drop form builder to create any contact form you'll ever want or need."
msgstr "Deze uitbreiding voegt de mogelijkheid to om een contactformulier aan te maken. Gebruik de drag & drop formulierenbouwer om alle contactformulieren te creëren die je ooit nodig hebt."

#: ../framework/extensions/forms/extensions/contact-forms/manifest.php:5
msgid "Contact Forms"
msgstr "Contactformulieren"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:134
msgid "Invalid destination email (please contact the site administrator)"
msgstr "Ongeldige bestemmingsemail (neem contact op met de sitebeheerder)"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:158
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:77
msgid "Message sent!"
msgstr "Bericht is verstuurd!"

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:164
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:85
msgid "Oops something went wrong."
msgstr "Oeps, er is iets fout gegaan."

#: ../framework/extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:190
msgid "Please configure the {mailer_link} extension."
msgstr "Configureer eerst de {mailer_link} uitbreiding."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:8
#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:107
msgid "Contact form"
msgstr "Contactformulier"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:9
msgid "Build contact forms"
msgstr "Bouw contactformulieren"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:15
msgid "Form Fields"
msgstr "Formuliervelden"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:39
#: ../framework/core/components/extensions/manager/views/extension.php:82
#: ../framework/core/components/extensions/manager/views/extension-page-header.php:31
msgid "Settings"
msgstr "Instellingen"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:42
msgid "Options"
msgstr "Opties"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:53
msgid "Subject Message"
msgstr "Onderwerp Bericht"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:54
msgid "This text will be used as subject message for the email"
msgstr "Deze tekst zal gebruikt worden als onderwerp voor de email"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:55
msgid "New message"
msgstr "Nieuw bericht"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:64
msgid "Submit Button"
msgstr "Verzendknop"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:65
msgid "This text will appear in submit button"
msgstr "Deze tekst verschijnt in de verzendknop"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:66
msgid "Send"
msgstr "Verzenden"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:75
msgid "Success Message"
msgstr "Succes Bericht"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:76
msgid "This text will be displayed when the form will successfully send"
msgstr "Deze tekst wordt getoond wanneer het formulier succesvol is verzonden."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:83
msgid "Failure Message"
msgstr "Mislukt Bericht"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:84
msgid "This text will be displayed when the form will fail to be sent"
msgstr "Deze tekst wordt getoond wanneer het verzenden van het formulier is mislukt."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:94
msgid "Email To"
msgstr "Verzend Naar"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:95
msgid "We recommend you to use an email that you verify often"
msgstr "We raden je aan een emailadres te gebruiken dat je regelmatig controleert."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:96
msgid "The form will be sent to this email address."
msgstr "Het formulier zal verstuurd worden naar dit emailadres."

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:52
msgid "Contact Form"
msgstr "Contactformulier"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:54
msgid "Configure Mailer"
msgstr "Mailer configureren"

#: ../framework/extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:108
msgid "Add a Contact Form"
msgstr "Voeg een Contactformulier toe"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:9
msgid "Note that the type can't be changed later."
msgstr "Opmerking: het type kan later NIET gewijzigd worden."

#: ../framework/extensions/forms/views/backend/submit-box-add.php:11
msgid ""
"You will need to create a new form in order to have a different form type."
msgstr "Je moet een nieuw formulier aanmaken om een ander formuliertype te kunnen gebruiken."

#: ../framework/extensions/forms/views/backend/submit-box-add.php:20
#: ../framework/extensions/forms/views/backend/submit-box-edit.php:16
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:14
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:31
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-error.php:8
msgid "Delete Permanently"
msgstr "Permanent Verwijderen"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:22
#: ../framework/extensions/forms/views/backend/submit-box-edit.php:18
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:16
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:33
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-error.php:10
msgid "Move to Trash"
msgstr "Verplaats naar Prullenbak"

#: ../framework/extensions/forms/views/backend/submit-box-add.php:33
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:35
msgid "Create"
msgstr "Aanmaken"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:24
msgid "Add a Recaptcha field"
msgstr "Voeg Recapthca veld toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:26
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:54
#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:78
msgid "Recaptcha"
msgstr "Recaptcha"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:59
msgid "Set site key"
msgstr "Site key instellen"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:60
msgid "Set secret key"
msgstr "Secret key instellen"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:77
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:76
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:81
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:81
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:78
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:76
msgid "Enter field label (it will be displayed on the web site)"
msgstr "Veld label invoeren (deze wordt getoond op de website)"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:133
msgid "Security Code"
msgstr "Beveiligingscode"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:147
msgid "Could not validate the form"
msgstr "Het formulier kon niet worden gevalideerd."

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:148
msgid "Please fill the recaptcha"
msgstr "Vul a.u.b. de recaptcha in"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:37
msgid "Site key"
msgstr "Site key"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:38
msgid "Your website key. More on how to configure ReCaptcha"
msgstr "De site key voor je website. Meer over hoe ReCaptcha te configureren"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:42
msgid "Secret key"
msgstr "Secret key"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:43
msgid "Your secret key. More on how to configure ReCaptcha"
msgstr "De secret key voor je website. Meer over hoe ReCaptcha te configureren"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:18
msgid "Add a Paragraph Text"
msgstr "Voeg een Alinea toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:20
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:48
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:77
msgid "Paragraph Text"
msgstr "Alinea"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:53
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:52
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:51
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:49
msgid "Toggle mandatory field"
msgstr "Instellen als verplicht veld"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:83
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:88
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:85
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:83
msgid "Mandatory Field"
msgstr "Verplicht Veld"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:84
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:89
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:86
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:84
msgid "Make this field mandatory?"
msgstr "Dit veld verplicht maken?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:98
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:98
msgid "Placeholder"
msgstr "Placeholder"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:99
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:99
msgid "This text will be used as field placeholder"
msgstr "Deze tekst zal worden gebruikt als placeholder voor het veld"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:105
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:107
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:107
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:105
msgid "Default Value"
msgstr "Standaard Waarde"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:106
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:108
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:108
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:106
msgid "This text will be used as field default value"
msgstr "Deze tekst zal gebruikt worden als standaard waarde voor het veld"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:126
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:128
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:126
msgid "Restrictions"
msgstr "Beperkingen"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:127
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:127
msgid "Set characters or words restrictions for this field"
msgstr "Stel teken- of woordbeperkingen in voor dit veld"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:131
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:131
msgid "Characters"
msgstr "Tekens"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:132
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:132
msgid "Words"
msgstr "Woorden"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:140
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:154
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:142
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:156
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:140
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:154
msgid "Min"
msgstr "Minimum"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:141
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:155
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:143
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:157
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:141
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:155
msgid "Minim value"
msgstr "Minimale waarde"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:146
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:160
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:148
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:162
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:146
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:160
msgid "Max"
msgstr "Maximum"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:147
#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:161
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:149
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:163
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:147
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:161
msgid "Maxim value"
msgstr "Maximale waarde"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:178
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:180
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:121
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:137
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:137
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:114
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:121
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:178
msgid "Instructions for Users"
msgstr "Instructies voor Gebruikers"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:179
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:181
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:122
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:138
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:138
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:115
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:122
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:179
msgid "The users will see these instructions in the tooltip near the field"
msgstr "De gebruikers zullen deze instructies kunnen zien in een tooltip nabij het veld"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:331
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:368
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:230
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:244
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:244
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:210
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:217
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:332
msgid "The {label} field is required"
msgstr "Het {label} veld is verplicht"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:336
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:337
#, php-format
msgid "The {label} field must contain minimum %d character"
msgstr "Het {label} veld moet minstens %d teken bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:341
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:342
#, php-format
msgid "The {label} field must contain minimum %d characters"
msgstr "Het {label} veld moet minstens %d tekens bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:346
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:347
#, php-format
msgid "The {label} field must contain maximum %d character"
msgstr "Het {label} veld mag maximaal %d teken bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:351
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:352
#, php-format
msgid "The {label} field must contain maximum %d characters"
msgstr "Het {label} veld mag maximaal %d tekens bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:356
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:357
#, php-format
msgid "The {label} field must contain minimum %d word"
msgstr "Het {label} veld moet minstens %d woord bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:361
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:362
#, php-format
msgid "The {label} field must contain minimum %d words"
msgstr "Het {label} veld moet minstens %d woorden bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:366
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:367
#, php-format
msgid "The {label} field must contain maximum %d word"
msgstr "Het {label} veld mag maximaal %d woord bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:371
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:372
#, php-format
msgid "The {label} field must contain maximum %d words"
msgstr "Het {label} veld mag maximaal %d woorden bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:20
msgid "Add a Number field"
msgstr "Voeg een Numeriek veld toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:50
#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:79
msgid "Number"
msgstr "Nummer"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:129
msgid "Set digits or values restrictions of this field"
msgstr "Stel cijfer-of waardebeperkingen in voor dit veld"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:133
msgid "Digits"
msgstr "Cijfers"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:134
msgid "Value"
msgstr "Waarde"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:363
msgid "The {label} field must be a valid number"
msgstr "Het {label} veld moet een geldig getal bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:373
#, php-format
msgid "The {label} field must have minimum %d digit"
msgstr "het {label} veld moet minstens %d cijfers bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:378
#, php-format
msgid "The {label} field must have minimum %d digits"
msgstr "Het {label} veld moet minstens %d cijfers bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:383
#, php-format
msgid "The {label} field must have maximum %d digit"
msgstr "Het {label} veld mag maximaal %d cijfers bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:388
#, php-format
msgid "The {label} field must have maximum %d digits"
msgstr "Het {label} veld mag maximaal %d cijfers bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:393
#, php-format
msgid "The {label} field minimum value must be %s"
msgstr "De minimale waarde van het {label} veld moet %s zijn"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:398
#, php-format
msgid "The {label} field maximum value must be %s"
msgstr "De maximale waarde van het {label} veld moet %s zijn"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:18
msgid "Add a Dropdown"
msgstr "Voeg een Dropdown toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:79
#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:164
msgid "Dropdown"
msgstr "Dropdown"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:100
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:103
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:103
msgid "Choices"
msgstr "Keuzes"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:101
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:104
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:104
msgid "Add choice"
msgstr "Keuze toevoegen"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:110
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:113
msgid "Randomize"
msgstr "Willekeurig maken"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:111
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:114
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:114
msgid "Do you want choices to be displayed in random order?"
msgstr "Wilt u de keuzes in een willekeurige volgorde weergeven?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:235
#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:249
msgid "{label}: Submitted data contains not existing choice"
msgstr "{label}: Ingezonden data bevat een niet bestaande keuze"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:38
msgid "Edit Title"
msgstr "Titel Bewerken"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:39
msgid "Edit Subtitle"
msgstr "Ondertitel Bewerken"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:57
msgid "The title will be displayed on contact form header"
msgstr "De titel wordt getoond in de header van het contactformulier"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:62
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:7
msgid "Subtitle"
msgstr "Ondertitel"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:63
msgid "The form header subtitle text"
msgstr "De ondertitel tekst voor de formulier header"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:18
msgid "Add a Single Choice field"
msgstr "Voeg een Enkele Keuze veld toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:56
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:56
msgid "{x} More"
msgstr "{x} Meer"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:113
msgid "Randomize?"
msgstr "Willekeurige volgorde?"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:124
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:124
msgid "Field Layout"
msgstr "Veld Layout"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:125
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:125
msgid "Select choice display layout"
msgstr "Selecteer keuzeweergave layout"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:127
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:127
msgid "One column"
msgstr "Eén kolom"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:128
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:128
msgid "Two columns"
msgstr "Twee kolommen"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:129
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:129
msgid "Three columns"
msgstr "Drie kolommen"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:130
#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:130
msgid "Side by side"
msgstr "Naast elkaar"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:18
msgid "Add a Multiple Choices field"
msgstr "Voeg een Meerkeuze veld toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:249
msgid "{label}: Submitted data contains not existing choices"
msgstr "{label}: Ingezonden data bevat niet bestaande keuzes"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:18
msgid "Add an Email field"
msgstr "Voeg een E-mail veld toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:79
msgid "Email"
msgstr "E-mail"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:215
msgid "The {label} field must contain a valid email"
msgstr "Het {label} veld moet een geldig emailadres bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:18
msgid "Add a Website field"
msgstr "Voeg een Website veld toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:22
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:49
#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:79
msgid "Website"
msgstr "Website"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:222
msgid "The {label} field must be a valid website name"
msgstr "Het {label} veld moet een geldige website bevatten"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:18
msgid "Add a Single Line Text"
msgstr "Voeg een Enkele Tekstregel toe"

#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:20
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:47
#: ../framework/extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:77
msgid "Single Line Text"
msgstr "Enkele Tekstregel"

#: ../framework/extensions/breadcrumbs/settings-options.php:17
msgid "Text for Homepage"
msgstr "Tekst voor Homepagina"

#: ../framework/extensions/breadcrumbs/settings-options.php:18
msgid "The homepage anchor will have this text"
msgstr "De homepagina anker zal deze tekst hebben"

#: ../framework/extensions/breadcrumbs/settings-options.php:23
msgid "Text for Blog Page"
msgstr "Tekst voor Blog Pagina"

#: ../framework/extensions/breadcrumbs/settings-options.php:24
msgid ""
"The blog page anchor will have this text. In case homepage will be set as "
"blog page, will be taken the homepage text"
msgstr "De blog pagina anker zal deze tekst hebben. Ingeval de homepagina als blog pagina ingesteld wordt zal de homepagina tekst genomen worden."

#: ../framework/extensions/breadcrumbs/settings-options.php:29
msgid "Text for 404 Page"
msgstr "Tekst voor 404 Pagina"

#: ../framework/extensions/breadcrumbs/settings-options.php:30
msgid "The 404 anchor will have this text"
msgstr "De 404 anker zal deze tekst hebben"

#: ../framework/extensions/breadcrumbs/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:108
msgid "Breadcrumbs"
msgstr "Navigatiepad"

#: ../framework/extensions/breadcrumbs/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:109
msgid ""
"Creates a simplified navigation menu for the pages that can be placed "
"anywhere in the theme. This will make navigating the website much easier."
msgstr "Creëert een vereenvoudigd navigatiemenu voor de pagina's die willekeurig in het thema geplaatst kunnen worden. Dit maakt het navigeren door de website veel gemakkelijker."

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:11
msgid "404 Not found"
msgstr "404 Niet Gevonden"

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:124
msgid "Searching for:"
msgstr "Zoekend naar:"

#: ../framework/extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:248
#: ../framework/includes/option-types/multi-select/class-fw-option-type-multi-select.php:472
msgid "No title"
msgstr "Geen titel"

#: ../framework/extensions/events/class-fw-extension-events.php:89
msgid "Create a event item"
msgstr "Maak evenement item aan"

#: ../framework/extensions/events/class-fw-extension-events.php:186
msgid "Date"
msgstr "Datum"

#: ../framework/extensions/events/class-fw-extension-events.php:204
msgid "Event Options"
msgstr "Evenement Opties"

#: ../framework/extensions/events/class-fw-extension-events.php:310
msgid "Multi Interval Event"
msgstr "Multi Interval Evenement"

#: ../framework/extensions/events/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:181
msgid ""
"This extension adds a fully fledged Events module to your theme. It comes "
"with built in pages that contain a calendar where events can be added."
msgstr "Deze uitbreiding voegt een volledig uitgeruste Evenementen module toe aan je thema. Het komt met ingebouwde pagina's die een kalender bevatten, waaraan evenementen kunnen worden toegevoegd."

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:81
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:189
msgid "Event Categories"
msgstr "Evenement Categorieën"

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:82
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:190
msgid "Select an event category"
msgstr "Selecteer een evenement categorie"

#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:84
#: ../framework/extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:192
msgid "All Events"
msgstr "Alle Evenementen"

#: ../framework/extensions/events/extensions/events-tags/manifest.php:7
msgid "Event-search-tags"
msgstr "Evenement-zoek-tags"

#: ../framework/extensions/events/extensions/events-tags/manifest.php:8
msgid "Connect extension event with shortcodes map & calendar"
msgstr "Verbind evenementen uitbreiding met kaart & kalender shortcodes"

#: ../framework/extensions/events/views/content.php:16
msgid "Google Calendar"
msgstr "Google Kalender"

#: ../framework/extensions/events/views/content.php:17
msgid "Ical Export"
msgstr "lcal Export"

#: ../framework/extensions/events/views/content.php:20
msgid "Start"
msgstr "Start"

#: ../framework/extensions/events/views/content.php:21
msgid "End"
msgstr "Eind"

#: ../framework/extensions/events/views/content.php:25
msgid "Speakers"
msgstr "Sprekers"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:37
msgid "Event Location"
msgstr "Evenement Locatie"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:39
msgid "Where does the event take place?"
msgstr "Waar heeft het evenement plaats?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:43
msgid "All Day Event?"
msgstr "Volledige Dag Evenement?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:44
msgid "Is your event an all day event?"
msgstr "Is je evenement een volledige dag evenement?"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:67
msgid "Start & End of Event"
msgstr "Start é Einde van het evenement"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:68
msgid "Set start and end events datetime"
msgstr "Type start en eind datum é tijd van het evenement"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:97
msgid "Associated User"
msgstr "Geassocieerde Gebruiker"

#: ../framework/extensions/events/includes/option-types/event/class-fw-option-type-event.php:99
msgid "Link this event to a specific user"
msgstr "Verbind dit evenement met een specifieke gebruiker"

#: ../framework/extensions/sidebars/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:132
msgid "Sidebars"
msgstr "Zijbalken"

#: ../framework/extensions/sidebars/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:133
msgid ""
"Brings a new layer of customization freedom to your website by letting you "
"add more than one sidebar to a page, or different sidebars on different "
"pages."
msgstr "Het brengt een nieuwe dimensie van vrijheid om je website aan te passen door het mogelijk te maken om meerdere zijkolommen toe te voegen aan een pagina, of verschillende zijkolommen op verschillende pagina's toe te voegen. "

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:131
msgid "No matches found"
msgstr "Geen overeenkomsten gevonden"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:139
msgid "Do you realy want to change without saving?"
msgstr "Wilt u dit echt veranderen zonder op te slaan?"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:141
msgid "Missing ID. Check that you provided all mandatory data."
msgstr "Geen identiteit bekend. Controleer dat u alle verplichte data heeft versterkt. "

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:142
#: ../framework/extensions/sidebars/views/backend-main-view.php:27
msgid "Created"
msgstr "Gecreëerd"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:143
msgid "(For Grouped Pages)"
msgstr "(Voor gegroepeerde pagina's)"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:144
msgid "(For Specific Pages)"
msgstr "(Voor specifieke pagina's)"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:145
msgid "No sidebar name specified"
msgstr "Geen zijbalk naam ingevoerd"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:146
msgid "Sidebar Name"
msgstr "Naam zijbalk"

#: ../framework/extensions/sidebars/class-fw-extension-sidebars.php:147
msgid "New Sidebar"
msgstr "Nieuwe zijbalk"

#: ../framework/extensions/sidebars/views/frontend-no-widgets.php:5
msgid "Widgets Page"
msgstr "Widgets pagina"

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:5
msgid "For specific"
msgstr "Voor specifieke"

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:23
msgid "Type to search ..."
msgstr "Type om te zoeken ..."

#: ../framework/extensions/sidebars/views/backend-tab-specific.php:30
msgid "Search for a specific page you want to set a sidebar for"
msgstr "Zoek voor een specifieke pagina waar u een zijbalk wilt instellen"

#: ../framework/extensions/sidebars/views/backend-tab-grouped.php:6
msgid "For group"
msgstr "Voor gegroepeerde"

#: ../framework/extensions/sidebars/views/backend-tab-grouped.php:25
msgid "Select group of pages you want to set a sidebar for."
msgstr "Selecteer de groep pagina's waar je een zijbalk voor wilt instellen"

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:18
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:60
msgid "Choose the position for your sidebar(s)"
msgstr "Kies de positie voor de zijbalk(en)"

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:43
#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:40
msgid "Add Sidebar"
msgstr "Voeg zijbalk toe"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:4
msgid "Sidebars for"
msgstr "Zijbalken voor"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Specific Page"
msgstr "Voor specifieke pagina"

#: ../framework/extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Grouped Page"
msgstr "Voor gegroepeerde pagina"

#: ../framework/extensions/sidebars/views/backend-main-view.php:11
#: ../framework/extensions/sidebars/views/backend-main-view.php:15
msgid "Manage Sidebars"
msgstr "Beheer zijbalken"

#: ../framework/extensions/sidebars/views/backend-main-view.php:18
msgid ""
"Use this section to create and/or set different sidebar(s) for different "
"page(s)"
msgstr "Gebruik deze sectie om verschillende zijbalk(en) te creëeren of/en instellen voor verschillende pagina('s). "

#: ../framework/extensions/sidebars/views/backend-main-view.php:24
msgid "For Grouped Pages"
msgstr "Voor gegroepeerde pagina's"

#: ../framework/extensions/sidebars/views/backend-main-view.php:25
msgid "For Specific Pages"
msgstr "Voor specifieke pagina's"

#: ../framework/extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:32
#: ../framework/extensions/sidebars/includes/option-type/sidebar-picker/view.php:121
msgid "Select sidebar you wish to replace."
msgstr "Selecteer de zijbalk die je wenst te vervangen. "

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:241
msgid "No sidebar name specified."
msgstr "Geen naam van de zijbalk gespecificeerd. "

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:270
msgid "Dynamic sidebar doesn't exixt"
msgstr "Dynamische zijbalk bestaat niet"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:277
msgid ""
"The placeholder can't be deleted because it is used in one of sidebars "
"below."
msgstr "De plaatshouder kan niet worden verwijderd omdat het in een van de zijbalken hieronder is gebruikt"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:280
msgid ""
"Please replace it first so that you will not have visual gaps in your "
"layout."
msgstr "Vervang het alstublieft als eerste, zodat u geen visuele gaten heeft in uw lay-out."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:293
msgid "Successfully removed"
msgstr "Succesvol verwijderd"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:543
msgid "Default for all pages"
msgstr "Standaard voor alle pagina's"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:613
msgid " (no title)"
msgstr "(geen titel)"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:780
msgid "Error: Type or sub_type error"
msgstr "Error: Type of sub_type error"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:838
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:869
msgid "Error: Sidebars not set"
msgstr "Error: zijbalken niet ingesteld"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:865
msgid "Error: Position doesn't exists. Please check config file."
msgstr "Error: positie bestaat niet. Controleer alstublieft config bestand."

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:55
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:253
msgid "Page"
msgstr "Pagina"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:56
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:153
msgid "Pages"
msgstr "Pagina's"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:61
msgid "Portfolio Project"
msgstr "Portfolio Project"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:62
msgid "Portfolio Projects"
msgstr "Portfolio Projecten"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:73
msgid "Blog Category"
msgstr "Blog categorie"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:79
msgid "Portfolio Category"
msgstr "Portfolio categorie"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:85
msgid "Home Page"
msgstr "Homepage"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:90
msgid "Search Page"
msgstr "Zoek pagina"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:95
msgid "404 Page"
msgstr "404 Pagina"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:100
msgid "Author Page"
msgstr "Auteur pagina"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:105
msgid "Archive Page"
msgstr "Archief pagina"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:149
#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:308
msgid "All Pages"
msgstr "Alle pagina's"

#: ../framework/extensions/sidebars/includes/class-fw-extension-sidebars-config.php:184
msgid "Others"
msgstr "Anderen"

#: ../framework/extensions/megamenu/manifest.php:7
#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:64
#: ../framework/core/components/extensions/manager/available-extensions.php:60
msgid "Mega Menu"
msgstr "Mega Menu"

#: ../framework/extensions/megamenu/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:61
msgid ""
"The Mega Menu extension adds a user-friendly drop down menu that will let "
"you easily create highly customized menu configurations."
msgstr "De Mega Menu uitbreiding voegt een gebruiksvriendelijk drop down menu toe, die het mogelijk maakt om gemakkelijk een hoog gepersonaliseerd menu te configureren."

#: ../framework/extensions/megamenu/class-fw-extension-megamenu.php:58
msgid "Select Icon"
msgstr "Selecteer icoon"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:44
#, php-format
msgid "%s (Invalid)"
msgstr "%s (Ongeldig)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:48
#, php-format
msgid "%s (Pending)"
msgstr "%s (Afwachtend)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:61
msgid "sub item"
msgstr "Subonderdeel"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:97
msgid "Edit Menu Item"
msgstr "Bewerk menu onderdeel"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:108
msgid "URL"
msgstr "URL"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:117
msgid "Navigation Label"
msgstr "Navigatie label"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:125
msgid "Title Attribute"
msgstr "Titel attribuut"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:134
msgid "Open link in a new window/tab"
msgstr "Open de link in een nieuw venster/tabblad"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:141
msgid "CSS Classes (optional)"
msgstr "CSS Klassen (optioneel)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:149
msgid "Link Relationship (XFN)"
msgstr "Link Relatie (XFN)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:157
msgid "Mega Menu Column Title"
msgstr "Mega Menu Kolom Titel"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:158
msgid "Item Title"
msgstr "Titel van onderdeel"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:166
msgid "Hide"
msgstr "Verbergen"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:172
msgid "This column should start a new row"
msgstr "Deze kolom zal een nieuwe rij starten"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:178
msgid "Description (HTML)"
msgstr "Omschrijving (HTML)"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:181
msgid ""
"The description will be displayed in the menu if the current theme supports "
"it."
msgstr "De beschrijving zal zichtbaar zijn in het menu indien het huidige thema het ondersteunt. "

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:190
msgid "Add Icon"
msgstr "Voeg icoon toe"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:191
msgid "Edit Icon"
msgstr "Bewerk icoon"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:205
msgid "Use as Mega Menu"
msgstr "Gebruik als Mega Menu"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:211
msgid "Move"
msgstr "Verplaats"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:212
msgid "Up one"
msgstr "Eén omhoog"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:213
msgid "Down one"
msgstr "Eén omlaag"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:216
msgid "To the top"
msgstr "Naar het hoogste punt omhoog"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:225
#, php-format
msgid "Original: %s"
msgstr "Origineel: %s"

#: ../framework/extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:239
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:60
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2282
msgid "Cancel"
msgstr "Annuleer"

#: ../framework/extensions/backups/class-fw-extension-backups.php:299
#: ../framework/extensions/backups/class-fw-extension-backups.php:366
msgid "File not specified"
msgstr "Bestand niet gespecificeerd"

#: ../framework/extensions/backups/class-fw-extension-backups.php:399
#: ../framework/extensions/backups/class-fw-extension-backups.php:400
#: ../framework/core/components/extensions/manager/available-extensions.php:156
msgid "Backup"
msgstr "Reservekopie"

#: ../framework/extensions/backups/class-fw-extension-backups.php:554
#: ../framework/extensions/backups/class-fw-extension-backups.php:568
msgid "Access Denied"
msgstr "Toegang geblokkeerd"

#: ../framework/extensions/backups/class-fw-extension-backups.php:561
msgid "Archive not found"
msgstr "Archief niet gevonden"

#: ../framework/extensions/backups/class-fw-extension-backups.php:575
#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:202
msgid "Failed to open file"
msgstr "Bestand openen mislukt"

#: ../framework/extensions/backups/helpers.php:37
msgid "Invalid JSON response"
msgstr "Ongeldige JSON reactie"

#: ../framework/extensions/backups/helpers.php:44
msgid ""
"HTTP Loopback Connections are not enabled on this server. If you need to "
"contact your web host, tell them that when PHP tries to connect back to the "
"site at the URL `{url}` and it gets the error `{error}`. There may be a "
"problem with the server configuration (eg local DNS problems, mod_security, "
"etc) preventing connections from working properly."
msgstr "HTTP Loopback Connections zijn niet ingeschakeld op deze server. Als je contact moet opnemen met je webhost, geef dan aan dat wanneer PHP probeert terug te verbinden met de website via de URL `{url}`, de foutmelding `{error}` ontstaat. Er kan een probleem zijn met de serverconfiguratie (bijv. lokale DNS problemen, mod_security, etc) die beperkt dat verbindingen goed functioneren. "

#: ../framework/extensions/backups/helpers.php:123
#: ../framework/extensions/backups/helpers.php:145
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:363
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:431
#, php-format
msgid "Failed to create dir: %s"
msgstr "%s map aanmaken mislukt"

#: ../framework/extensions/backups/helpers.php:152
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:137
#, php-format
msgid "Failed to copy: %s"
msgstr "%s kopiëren mislukt"

#: ../framework/extensions/backups/manifest.php:7
#: ../framework/core/components/extensions/manager/available-extensions.php:168
msgid "Backup & Demo Content"
msgstr "Backup & Demo Content"

#: ../framework/extensions/backups/manifest.php:9
#: ../framework/core/components/extensions/manager/available-extensions.php:169
msgid ""
"This extension lets you create an automated backup schedule, import demo "
"content or even create a demo content archive for migration purposes."
msgstr "Deze uitbreiding maakt het mogelijk om automatisch backup schema's aan te maken, demo content te importeren en een demo content archief aan te maken voor migratie doeleinden."

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:97
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:98
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:385
msgid "Demo Content Install"
msgstr "Demo Content Installatie"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:282
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:329
#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:362
msgid "Forbidden"
msgstr "Niet Toegestaan"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:342
msgid "Invalid demo"
msgstr "Ongeldige demo"

#: ../framework/extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:349
msgid "A content install is currently running"
msgstr "Er is een content installatie bezig"

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:28
#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:39
#: ../framework/extensions/backups/views/page.php:17
#: ../framework/extensions/backups/views/page.php:28
msgid "Important"
msgstr "belangrijk"

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:30
#: ../framework/extensions/backups/views/page.php:19
#, php-format
msgid "You need to activate %s."
msgstr "Je moet %s activeren."

#: ../framework/extensions/backups/extensions/backups-demo/views/page.php:31
#: ../framework/extensions/backups/views/page.php:20
msgid "zip extension"
msgstr "zip extensie"

#: ../framework/extensions/backups/views/page.php:70
msgid "Archives"
msgstr "Archieven"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:76
msgid "Full Backup"
msgstr "Volledige Backup"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:85
msgid "Content Backup"
msgstr "Content Backup"

#: ../framework/extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:88
msgid ""
"Warning! \n"
"You are about to delete a backup, it will be lost forever. \n"
"Are you sure?"
msgstr "Waarschuwing! \nJe staat op het punt een backup te verwijderen. Deze actie is definitief. \nWeet je het zeker?"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:16
msgid "Interval"
msgstr "Interval"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:20
#: ../framework/core/components/extensions/manager/views/extension.php:180
msgid "Disabled"
msgstr "Disabel"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:25
msgid "Select how often do you want to backup your website."
msgstr "Selecteer hoe dikwijls je je website wil backuppen."

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:32
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:45
#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:58
msgid "Age Limit"
msgstr "Ouderdomslimiet"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:33
msgid "Age limit of backups in months"
msgstr "Ouderdomslimiet van backups in maanden"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:46
msgid "Age limit of backups in weeks"
msgstr "Ouderdomslimiet van backups in weken"

#: ../framework/extensions/backups/includes/module/schedule/settings-options.php:59
msgid "Age limit of backups in days"
msgstr "Ouderdomslimiet van backups in dagen"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:186
msgid "Backup Schedule"
msgstr "Reservekopie schema"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:288
msgid "Once Weekly"
msgstr "Eén keer per week"

#: ../framework/extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:292
msgid "Once a month"
msgstr "Eén keer per maand"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:139
msgid "undefined"
msgstr "niet gedefinieerd"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:279
msgid "Task type not registered"
msgstr "Taak type niet geregistreerd"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:286
msgid "Execution stopped (next step did not started)"
msgstr "Uitvoering gestopt (volgende stap is niet gestart)"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:306
msgid "Timed out"
msgstr "Timed out"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:366
msgid "Invalid execution end time"
msgstr "Ongeldige uitvoeringseinddtijd"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:537
msgid "Invalid execution result"
msgstr "Ongeldig uitvoeringsresultaat"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:639
msgid "Invalid token"
msgstr "Ongeldige token"

#: ../framework/extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:658
msgid "Invalid tasks hash"
msgstr "Ongeldige taak hash"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-restore.php:9
msgid "Image Sizes Restore"
msgstr "Afbeeldingsdimensies Herstellen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:12
msgid "Archive Unzip"
msgstr "Archief Uitpakken"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:39
msgid "Zip file not specified"
msgstr "Zip bestand niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:47
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:50
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:49
#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:114
msgid "Destination dir not specified"
msgstr "Bestemmingsmap niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:56
msgid "Destination dir is not empty"
msgstr "Bestemmingsmap is niet leeg"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:63
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:59
#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:50
msgid "Zip extension missing"
msgstr "Zip extensie niet aanwezig"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:71
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:70
#, php-format
msgid "Cannot open zip (Error code: %s)"
msgstr "Kan zip niet openen (Foutmelding: %s)"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:38
msgid "Database export"
msgstr "Database export"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:71
msgid "Database table disappeared"
msgstr "Database tabel verdwenen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:91
msgid "Cannot create file"
msgstr "Kan bestand niet aanmaken"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:97
msgid "Cannot reopen file"
msgstr "Kan bestand niet heropenen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:141
msgid "Cannot export CREATE TABLE sql"
msgstr "Kan CREATE TABLE sql niet exporteren"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:211
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:267
msgid "Cannot get next database table"
msgstr "Kan volgende database tabel niet bereiken"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:9
msgid "Files Restore"
msgstr "Bestandsherstel"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:32
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:41
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:45
msgid "Source dir not specified"
msgstr "Bronmap niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:39
msgid "Invalid source dir"
msgstr "Ongeldige bronmap"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:46
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:25
msgid "Source dirs not specified"
msgstr "Bronmappen niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:116
msgid "No filesystem access, credentials required"
msgstr "Geen toegang tot het bestandssysteem; logingegevens vereist"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:120
msgid "No filesystem access, invalid credentials"
msgstr "Geen toegang tot het bestandssysteem; ongeldige logingegevens"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:126
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:135
msgid "Filesystem init failed"
msgstr "Bestandssyteem initialisatie mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:192
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:317
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:341
#, php-format
msgid "Cannot convert Filesystem path: %s"
msgstr "Kan bestandssysteem pad niet converteren: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:197
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:322
#, php-format
msgid "Failed to list dir: %s"
msgstr "Map weergeven mislukt: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:227
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:281
#, php-format
msgid "Failed to remove dir: %s"
msgstr "Map verwijderen mislukt: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:234
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:288
#, php-format
msgid "Failed to remove file: %s"
msgstr "Bestand verwijderen mislukt: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:376
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:444
#, php-format
msgid "Failed to copy file: %s"
msgstr "Bestand kopiëren mislukt: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:12
msgid "Archive Zip"
msgstr "Archief Zip"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:97
msgid "Cannot close the zip file"
msgstr "Kan het zip-bestand niet afsluiten"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:103
msgid "Cannot move zip in destination dir"
msgstr "Kan het zip-bestand niet naar bestemmingsdirectorie verplaatsen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:9
msgid "Files Export"
msgstr "Bestandsexport"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:33
msgid "Destination not specified"
msgstr "Bestemming niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:74
#, php-format
msgid "Source dir %s is empty"
msgstr "Bronmap %s is niet leeg"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:116
msgid "Failed to get dir chmod"
msgstr "Map chmod verkrijgen mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:123
msgid "Failed to create destination dir"
msgstr "Doelmap aanmaken mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:214
#, php-format
msgid "Failed to restore dir listing from: %s"
msgstr "Mapweergave herstellen mislukt: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:12
msgid "Database restore"
msgstr "Database herstel"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:63
msgid "Database file not found"
msgstr "Database bestand niet gevonden"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:111
#, php-format
msgid "Cannot drop temporary table: %s"
msgstr "Kan tijdelijke tabel niet verwijderen: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:129
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:285
msgid "Cannot open db file"
msgstr "Kan databasebestand niet openen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:138
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:294
msgid "Cannot move cursor in db file"
msgstr "Kan cursor niet verplaatsen in databasebestand"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:154
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:377
#, php-format
msgid "Failed to decode line %d from db file."
msgstr "Regel %d decoderen uit databasebestand mislukt."

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:186
#, php-format
msgid "Cannot read line %d from db file"
msgstr "Kan regel %d uit databasebestand niet lezen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:195
msgid "Required params not found"
msgstr "Vereiste parameters niet gevonden"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:232
msgid "Cannot do full db restore because backup is missing some tables"
msgstr "Kan geen volledig databaseherstel uitvoeren, omdat de backup enkele tabellen mist"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:396
#, php-format
msgid "Failed to drop tmp table %s"
msgstr "Tijdelijke tabel %s laten vervallen mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:435
#, php-format
msgid "Failed to create tmp table %s"
msgstr "Tijdelijke tabel %s aanmaken mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:447
#, php-format
msgid "Tried to insert data in table that was not imported %s"
msgstr "Poging om data in tabel in te voegen welke niet is geïmporteerd %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:606
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:628
#, php-format
msgid "Failed insert row from line %d"
msgstr "Rij invoegen bij regel %d mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:642
#, php-format
msgid "Invalid json type %s in db file"
msgstr "Ongeldig JSON type %s in databasebestand"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:649
msgid "Cannot read line from db file"
msgstr "Kan regel uit databasebestand niet lezen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:688
msgid "Failed to restore options keeping step"
msgstr "Herstellen van opties stap behouden mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:723
#, php-format
msgid "Failed to keep option: %s"
msgstr "Optie behouden mislukt: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:774
msgid "Tables drop failed"
msgstr "Tabellen laten vervallen mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:787
msgid "Tables rename failed."
msgstr "Tabellen hernoemen mislukt."

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:799
#, php-format
msgid "Invalid sub task %s"
msgstr "Ongeldige subtaak %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:12
msgid "Directory Cleanup"
msgstr "Map Opruiming"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:21
msgid "Dir not specified"
msgstr "Map niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:30
msgid "Cannot remove directory"
msgstr "Kan map niet verwijderen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:37
msgid "Cannot create directory"
msgstr "Kan map niet aanmaken"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:55
#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:70
#, php-format
msgid "Cannot create file: %s"
msgstr "Kan bestand niet aanmaken: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:9
msgid "Image Sizes Remove"
msgstr "Afbeeldingsdimensies Verwijderen"

#: ../framework/extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:23
msgid "Uploads dir not specified"
msgstr "Uploadmap niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:18
msgid "Download"
msgstr "Download"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:119
msgid "Invalid destination dir"
msgstr "Ongeldige bestemmingsmap"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:132
#, php-format
msgid "Invalid type: %s"
msgstr "Ongeldig type: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:141
#, php-format
msgid "Args not specified for type: %s"
msgstr "Argumenten niet gespecificeerd voor type: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:9
msgid "Local Download"
msgstr "Lokale Download"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:24
msgid "Source not specified"
msgstr "Bron niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:29
msgid "Invalid source"
msgstr "Ongeldige bron"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:38
msgid "Invalid source type"
msgstr "Ongeldig brontype"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:59
#, php-format
msgid "Cannot open zip: %s"
msgstr "Kan zip niet openen: %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:70
msgid "Unhandled type"
msgstr "Onverwerkt type"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:13
msgid "Downloading..."
msgstr "Downloading..."

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:16
msgid "Download finished. Doing unzip..."
msgstr "Download gereed. Uitpakken..."

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:19
#, php-format
msgid "Downloading... %s of %s"
msgstr "Downloading... %s van %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:24
#, php-format
msgid "Downloading... %s"
msgstr "Downloading... %s"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:67
msgid "Url not specified"
msgstr "Url niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:72
msgid "Invalid url"
msgstr "Ongeldige url"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:79
msgid "File id not specified"
msgstr "Bestands-id niet gespecificeerd"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:84
msgid "Invalid file id"
msgstr "Ongeldige bestands-id"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:105
#, php-format
msgid "Zip open failed (code %d). Please try again"
msgstr "Zip openen mislukt (code %d). Probeer het opnieuw"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:112
msgid "Zip extract failed"
msgstr "Zip uitpakken mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:119
msgid "Failed to close the zip after extract"
msgstr "Afsluiten van de zip na het uitpakken mislukt"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:152
#, php-format
msgid "Request failed. Error code: %d"
msgstr "Verzoek mislukt. Foutmelding: %d"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:163
msgid "Invalid byte position"
msgstr "Ongeldige byte positie"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:168
msgid "Empty response body"
msgstr "Lege response body"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:188
msgid "File ended without content"
msgstr "Bestand beëindigd zonder inhoud"

#: ../framework/extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:213
msgid "Failed to write data to file"
msgstr "Data naar bestand schrijven mislukt"

#: ../framework/extensions/translation/settings-options.php:11
msgid "Default Language"
msgstr "Standaard Taal"

#: ../framework/extensions/translation/settings-options.php:12
msgid "This is the default language of your website."
msgstr "Dit is de standaard taal voor je website."

#: ../framework/extensions/translation/settings-options.php:19
msgid "Translate to"
msgstr "Vertaal naar"

#: ../framework/extensions/translation/settings-options.php:20
msgid "Choose the languages you want your website translated to."
msgstr "Kies de taal waar je de website naar wilt vertalen."

#: ../framework/extensions/translation/settings-options.php:27
msgid "Convert data"
msgstr "Data converteren"

#: ../framework/extensions/translation/settings-options.php:28
msgid ""
"Set to default language the posts, pages categories or tags that don't have "
"a language set ?"
msgstr "De berichten, pagina's, categorieën of tags die geen taal hebben ingesteld, instellen op de standaard taal?"

#: ../framework/extensions/translation/manifest.php:7
#: ../framework/extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:336
#: ../framework/core/components/extensions/manager/available-extensions.php:264
msgid "Translations"
msgstr "Vertalingen"

#: ../framework/extensions/translation/manifest.php:8
#: ../framework/core/components/extensions/manager/available-extensions.php:265
msgid ""
"This extension lets you translate your website in any language or even add "
"multiple languages for your users to change at their will from the front-"
"end."
msgstr "Deze uitbreiding maakt het mogelijk de website te vertalen in iedere taal en kan zelfs meerdere talen aan bezoekers ter beschikking stellen in de front-end van de website."

#: ../framework/extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:114
msgid "All Languages"
msgstr "Alle Talen"

#: ../framework/extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:332
msgid "The term translation does already exists.ACTION +++ "
msgstr "Vertaling van de term bestaat al.ACTION +++ "

#: ../framework/extensions/translation/extensions/translate-terms/manifest.php:7
msgid "Translate Terms"
msgstr "Termen Vertalen"

#: ../framework/extensions/translation/extensions/translate-terms/manifest.php:8
msgid "This extension translate terms"
msgstr "Deze uitbreiding vertaalt termen"

#: ../framework/extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:341
msgid "Language of this post"
msgstr "Taal van dit bericht"

#: ../framework/extensions/translation/extensions/translate-posts/manifest.php:7
msgid "Translate Posts"
msgstr "Berichten Vertalen"

#: ../framework/extensions/translation/extensions/translate-posts/manifest.php:8
msgid "This extension translate posts"
msgstr "Deze uitbereiding vertaalt berichten"

#: ../framework/extensions/translation/extensions/translate-widgets/manifest.php:7
msgid "Translate Widgets"
msgstr "Widgets Vertalen"

#: ../framework/extensions/translation/extensions/translate-widgets/manifest.php:8
msgid "This extension translate Widgets"
msgstr "Deze uitbreiding vertaalt Widgets"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:10
msgid "Language Switcher"
msgstr "Taalwisselaar"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:11
msgid "A Language Switcher Widget"
msgstr "Een Taalwisselaar Widget"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:42
msgid "New title"
msgstr "Nieuwe titel"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:45
#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:49
msgid "Title:"
msgstr "Titel:"

#: ../framework/extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:14
msgid "A calendar of your site&#8217;s Posts."
msgstr "Een kalender van je website&#8217;s berichten"

#: ../framework/extensions/translation/extensions/translate-menus/manifest.php:7
msgid "Translate Menus"
msgstr "Menu's Vertalen"

#: ../framework/extensions/translation/extensions/translate-menus/manifest.php:8
msgid "This extension translate menus"
msgstr "Deze uitbreiding vertaalt menu's"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:211
msgid "Slider Design"
msgstr "Slider ontwerp"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:213
msgid "Number of Images"
msgstr "Hoeveelheid beelden"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:231
#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:234
#, php-format
msgid "%s updated."
msgstr "%s bijgewerkt."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:232
msgid "Custom field updated."
msgstr "Aangepast veld bijgewerkt."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:233
msgid "Custom field deleted."
msgstr "Aangepastveld verwijderd."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:235
#, php-format
msgid "%s restored to revision from %s"
msgstr "%s hersteld van revisie van %s"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:237
#, php-format
msgid "%s published."
msgstr "%s gepubliceerd."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:238
msgid "Page saved."
msgstr "Pagina opgeslagen."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:239
#, php-format
msgid "%s submitted."
msgstr "%s ingestuurd."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:240
#, php-format
msgid "%s scheduled for: %s."
msgstr "%s gepland voor %s."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:242
#, php-format
msgid "%s draft updated."
msgstr "%s schets bijgewerkt."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:272
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:52
msgid "Publish"
msgstr "Publiceren"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:342
msgid ""
"This slider was created correctly, but the code implementation was delete "
"from source code or blocked from filter.Delete this post or recovery slider "
"implementation"
msgstr "Deze slider is goed gecreëerd, maar de code implementatie was verwijderd uit broncode of geblokkeerd door filter. Verwijder deze post of herstel slider implementatie."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:351
msgid ""
"This slider was created correctly, but the multimedia_types from config.php "
"file was deleted, please set multimedia_types for this slider type."
msgstr "Deze slider is goed gecreëerd, maar de multimedia_types van config.php bestand was verwijderd. Stel alstublieft multimedia_types in voor dit slider type. "

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:361
msgid "Slider Configuration"
msgstr "Slider configuratie"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:375
msgid "Slider Title"
msgstr "Slider Titel"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:377
msgid "Choose a title for your slider only for internal use: Ex: \"Homepage\"."
msgstr "Kies een titel voor je slider voor alleen intern gebruik: vb. \"Homepage\"."

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:433
msgid "Slider Settings"
msgstr "Slider instellingen"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:461
msgid ""
"You don't have slider extensions, please create at least one extension for "
"properly work"
msgstr "Je hebt geen slider modules. Maak ten minste één uitbreiding aan voor passend werk"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:474
msgid "Choose the population method for your slider"
msgstr "Kies de populatie methode voor je slider"

#: ../framework/extensions/media/extensions/slider/class-fw-extension-slider.php:542
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:94
msgid "(no title)"
msgstr "(geen titel)"

#: ../framework/extensions/media/extensions/slider/posts.php:6
#: ../framework/extensions/media/extensions/slider/posts.php:12
#: ../framework/extensions/media/extensions/slider/posts.php:18
#: ../framework/extensions/media/extensions/slider/manifest.php:5
#: ../framework/core/components/extensions/manager/available-extensions.php:12
msgid "Sliders"
msgstr "Sliders"

#: ../framework/extensions/media/extensions/slider/posts.php:7
#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:8
msgid "Slider"
msgstr "Slider"

#: ../framework/extensions/media/extensions/slider/posts.php:9
msgid "Add New Slider"
msgstr "Voeg nieuwe slider toe"

#: ../framework/extensions/media/extensions/slider/posts.php:10
msgid "Edit Slider"
msgstr "Bewerk slider"

#: ../framework/extensions/media/extensions/slider/posts.php:11
msgid "New Slider"
msgstr "Nieuwe slider"

#: ../framework/extensions/media/extensions/slider/posts.php:13
msgid "View Slider"
msgstr "Bekijk slider"

#: ../framework/extensions/media/extensions/slider/posts.php:14
msgid "Search Sliders"
msgstr "Zoek sliders"

#: ../framework/extensions/media/extensions/slider/posts.php:15
msgid "No Sliders found"
msgstr "Geen sliders gevonden"

#: ../framework/extensions/media/extensions/slider/posts.php:16
msgid "No Sliders found in Trash"
msgstr "Geen sliders gevonden in de prullenbak"

#: ../framework/extensions/media/extensions/slider/manifest.php:6
msgid ""
"Adds the Sliders extension to your website. You'll be able to create "
"different built in jQuery sliders for your homepage and all the other "
"website pages."
msgstr "Voegt een Slider module toe aan je website van waaruit je verschillende ingebouwde jQuery sliders kunt aanmaken voor de homepagina en de rest van de pagina's."

#: ../framework/extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:6
msgid "Nivo Slider"
msgstr "Nivo Slider"

#: ../framework/extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:6
msgid "Owl Slider"
msgstr "Owl Slider"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/manifest.php:5
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/manifest.php:6
msgid "Bx-Slider"
msgstr "Bx-Slider"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:4
msgid "Population Method Categories opt 1"
msgstr "Populatie methode categorieën optie 1"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:5
#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:11
msgid "Option description"
msgstr "Optie omschrijving"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:10
msgid "Population Method Categories opt 2"
msgstr "Populatie methode categorieën optie 2"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:5
msgid "Type of Transition"
msgstr "Type transitie"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:6
msgid "Type of transition between slides"
msgstr "Transitietype tussen slides"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:9
msgid "Horizontal"
msgstr "Horizontaal"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:10
msgid "Vertical"
msgstr "Verticaal"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/options.php:11
msgid "Fade"
msgstr "Vervagen"

#: ../framework/extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:9
msgid "Choose a subtitle for your slide."
msgstr "Kies een ondertiteling voor je slide."

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/config.php:9
msgid "Add a Slider"
msgstr "Voeg een slider toe"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:9
msgid "Select Slider"
msgstr "Selecteer slider"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:14
msgid "Set width"
msgstr "Breedte vaststellen"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:19
msgid "Set height"
msgstr "Hoogte vaststellen"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:34
msgid "No Sliders Available"
msgstr "Geen sliders beschikbaar"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:47
msgid "create a new Slider"
msgstr "Maak een nieuwe Slider aan"

#: ../framework/extensions/media/extensions/slider/shortcodes/slider/options.php:49
msgid ""
"No Sliders created yet. Please go to the {br}Sliders page and "
"{add_slider_link}."
msgstr "Er zijn nog geen Sliders aangemaakt. Ga naar de {br}Sliders pagina en {add_slider_link}."

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:5
msgid ""
"Note that the type and population can't be changed later. You'll need to "
"create a new slider to have a different slider type or population method."
msgstr "Het is op te merken dat de type en populatie kan later niet worden veranderd. U zult een nieuwe slider moeten creëeren om een ander slider type of populatie methode te krijgen. "

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:31
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:48
msgid "Schedule"
msgstr "Schema"

#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-raw.php:40
#: ../framework/extensions/media/extensions/slider/views/backend/submit-box-edit.php:57
msgid "Submit for Review"
msgstr "Stuur in voor een beoordeling"

#: ../framework/extensions/media/extensions/population-method/class-fw-extension-population-method.php:36
#, php-format
msgid "Specified population method does not exists: %s"
msgstr "Specifieke populatie methode bestaat niet: %s"

#: ../framework/extensions/media/extensions/population-method/class-fw-extension-population-method.php:55
#, php-format
msgid "Population method %s does not exist"
msgstr "Populatiemethode %s bestaat niet"

#: ../framework/extensions/media/extensions/population-method/manifest.php:3
msgid "Population Methods"
msgstr "Populatie methodes"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:21
msgid "Automatically, fetch images from categories"
msgstr "Automatisch, verkrijg foto's van categorieën"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:29
#, php-format
msgid "%s extension needs configured categories in post types"
msgstr "%s extensie heeft geconfigureerde categorieën nodig in post types"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:34
msgid "Categories Population Method"
msgstr "Categorieën Populatie Methode"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:44
msgid "Choose Category"
msgstr "Kies een categorie"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:53
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:53
msgid "Number of Images in the slider"
msgstr "Hoeveelheid foto's in de slider"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:107
msgid "Select Specific Categories"
msgstr "Selecteer specifieke categorieën"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:4
msgid "Population Method - Categories"
msgstr "Populatie Methode - Categorieën"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:21
msgid "Automatically, fetch images from tags"
msgstr "Automatisch, verkrijg foto's afkomstig van tags"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:29
#, php-format
msgid "%s extension needs configured tags in post types"
msgstr "%s extensie heeft geconfigureerde tags nodig in post type's"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:34
msgid "Tags Population Method"
msgstr "Tags Populatie Methode"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:44
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:44
msgid "Choose Tag"
msgstr "Kies een tag"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:107
msgid "Select Specific tags"
msgstr "Selecteer specifieke tags"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:4
msgid "Population Method - Tags"
msgstr "Populatie methode - Tags"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:21
msgid "Automatically, fetch images from posts"
msgstr "Automatisch, verkrijg foto's afkomstig van posts"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:29
#, php-format
msgid "%s extension needs configured post categories in post types"
msgstr "%s extensie heeft geconfigureerde post categorieën nodig in post type's"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:34
msgid "Posts Population Method"
msgstr "Posts Populatie Methode"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:101
msgid "Select Specific posts"
msgstr "Selecteer specifieke posts"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:4
msgid "Population Method - Posts"
msgstr "Populatie Methode - Posts"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:22
msgid "Manually, I'll upload the images myself"
msgstr "Handmatig, ik zal de foto's zelf uploaden"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:33
msgid "Click to edit / Drag to reorder"
msgstr "Click om te bewerken/Sleep om te verstellen"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:54
msgid "Choose"
msgstr "Kies"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:3
#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:4
msgid "Population Method - Custom"
msgstr "Populatie Methode - Aangepast"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:57
msgid "Add Slide"
msgstr "Voeg een slide toe"

#: ../framework/extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:59
#: ../framework/views/backend-settings-form.php:47
msgid "Save Changes"
msgstr "Wijzigingen Opslaan "

#: ../framework/core/Fw.php:73
msgid "Framework requirements not met:"
msgstr "Framework vereisten niet bereikt:"

#: ../framework/core/class-fw-manifest.php:293
msgid "minimum required version is"
msgstr "minimum vereiste versie is"

#: ../framework/core/class-fw-manifest.php:296
msgid "maximum required version is"
msgstr "maximum vereiste versie is"

#: ../framework/core/class-fw-manifest.php:301
msgid "and"
msgstr "en"

#: ../framework/core/class-fw-manifest.php:308
#, php-format
msgid "Current WordPress version is %s, %s"
msgstr "Huidige Wordpress versie is %s, %s"

#: ../framework/core/class-fw-manifest.php:314
#, php-format
msgid "Current Framework version is %s, %s"
msgstr "Huidige Framework versie is %s, %s"

#: ../framework/core/class-fw-manifest.php:323
#, php-format
msgid "Current version of the %s extension is %s, %s"
msgstr "Huidige versie van de %s extensie is %s, %s"

#: ../framework/core/class-fw-manifest.php:329
#, php-format
msgid "%s extension is required"
msgstr "%s extensie is vereist"

#: ../framework/core/class-fw-manifest.php:334
#, php-format
msgid "%s extension is required (%s)"
msgstr "%s extensie is vereist (%s)"

#: ../framework/core/extends/class-fw-option-type.php:283
#, php-format
msgid "Option type %s has no default value"
msgstr "Optie type %s heeft geen default waarde"

#: ../framework/core/components/backend.php:355
msgid "Done"
msgstr "Gereed"

#: ../framework/core/components/backend.php:356
msgid "Ah, Sorry"
msgstr "Ah, Sorry"

#: ../framework/core/components/backend.php:358
#: ../framework/includes/option-types/color-picker/class-fw-option-type-color-picker.php:39
#: ../framework/includes/option-types/color-picker/class-fw-option-type-color-picker.php:40
#: ../framework/includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:41
#: ../framework/includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:42
msgid "Reset"
msgstr "Herstellen"

#: ../framework/core/components/backend.php:541
#: ../framework/core/components/backend.php:542
#: ../framework/core/components/backend.php:650
msgid "Theme Settings"
msgstr "Thema Instellingen"

#: ../framework/core/components/backend.php:577
msgid "leave a review"
msgstr "laat een beoordeling achter"

#: ../framework/core/components/backend.php:588
msgid ""
"Unyson WordPress Framework is the fastest and easiest way to develop a "
"premium theme. I highly recommend it"
msgstr "Unyson WordPress Framework is de snelste and gemakkelijkste manier om een premium thema te ontwikkelen. I beveel het van harte aan"

#: ../framework/core/components/backend.php:594
msgid ""
"If you like Unyson, {wp_review_link}, share on {facebook_share_link} or "
"{twitter_share_link}."
msgstr "Als Unyson je bevalt, {wp_review_link}, deel op {facebook_share_link} of {twitter_share_link}."

#: ../framework/core/components/backend.php:1303
msgid "You have no permissions to change settings options"
msgstr "U hebt geen toelating om instellingen te wijzigen"

#: ../framework/core/components/backend.php:1316
msgid "The options were successfully reset"
msgstr "De instellingen zijn succesvol gereset"

#: ../framework/core/components/backend.php:1327
msgid "The options were successfully saved"
msgstr "De instellingen zijn succesvol opgeslagen"

#: ../framework/core/components/backend.php:1440
msgid "Unknown collected group"
msgstr "Onbekende verzamelde groep"

#: ../framework/core/components/backend.php:1779
#, php-format
msgid "Undefined option type: %s"
msgstr "Niet gedefinieerd optie type: %s"

#: ../framework/core/components/backend.php:1826
#, php-format
msgid "Undefined container type: %s"
msgstr "Niet gedefinieerd container type: %s"

#: ../framework/core/components/extensions.php:447
#: ../framework/core/components/extensions.php:525
#, php-format
msgid "Extension %s is invalid."
msgstr "Uitdrukking %s is ongeldig."

#: ../framework/core/components/theme.php:206
msgid "Theme requirements not met:"
msgstr "Niet bereikte thema vereisten:"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:351
msgid "Cannot remove the old extensions backup dir"
msgstr "Kan de oude extensie backup dir niet verwijderen"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:357
msgid "Cannot create the extensions backup dir"
msgstr "Kan de extensie backup dir niet aanmaken"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:362
msgid "Cannot backup the extensions"
msgstr "Kan de extensies niet backuppen"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:408
msgid "Cannot clear the extensions directory"
msgstr "Kan de extensie directory niet wissen"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:413
msgid "Cannot recreate the extensions directory"
msgstr "Kan de extensie directory niet opnieuw aanmaken"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:419
msgid "Cannot recover the extensions"
msgstr "Kan de extensies niet herstellen"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:645
#, php-format
msgid "Cannot activate hidden standalone extension %s"
msgstr "Kan verborgen zelfstandige uitbreiding %s niet activeren"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:874
msgid "You are not allowed to install extensions."
msgstr "Je hebt hebt geen toestemming om uitbreidingen te installeren."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:891
msgid "All supported extensions are already installed."
msgstr "Alle ondersteunde uitbreidingen zijn al geïnstalleerd."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:967
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2384
#, php-format
msgid "Cannot remove temporary directory: %s"
msgstr "Kan de tijdelijke directory: %s niet verwijderen"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1037
msgid "You have no permissions to install extensions"
msgstr "Je hebt onvoldoende rechten om uitbreidingen te installeren."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1044
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1517
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1901
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2116
msgid "No extensions provided"
msgstr "Geen uitbreidingen beschikbaar gesteld"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1053
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1527
msgid "WP Filesystem is not initialized"
msgstr "WP Bestandssysteem is nog niet geinitialiseerd"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1079
#, php-format
msgid "Extension \"%s\" is already installed."
msgstr "Extensie \"%s\" is al geïnstalleerd."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1094
#, php-format
msgid "Extension \"%s\" is not available for install."
msgstr "Extensie \"%s\" is niet beschikbaar voor installatie."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1122
#, php-format
msgid "Parent extension \"%s\" not available."
msgstr "Bovenliggende uitbreiding \"%s\" niet beschikbaar."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1156
#, php-format
msgid "Downloading the \"%s\" extension..."
msgstr "Bezig met downloaden van de \"%s\" extensie..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1200
#, php-format
msgid "Installing the \"%s\" extension..."
msgstr "Bezig met installeren van de \"%s\" extensie..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1238
#, php-format
msgid "The %s extension has been successfully installed."
msgstr "De %s uitbreiding is succesvol geïnstalleerd."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1399
msgid "You are not allowed to delete extensions."
msgstr "Je hebt hebt geen toestemming om uitbreidingen te verwijderen."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1510
msgid "You have no permissions to uninstall extensions"
msgstr "Je hebt onvoldoende rechten om uitbreidingen te deïnstalleren."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1578
#, php-format
msgid "Deleting the \"%s\" extension..."
msgstr "Verwijderen van de \"%s\" extensie..."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1590
#, php-format
msgid "Cannot delete the \"%s\" extension."
msgstr "Kan de \"%s\" extensie niet verwijderen."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1602
#, php-format
msgid "The %s extension has been successfully deleted."
msgstr "De %s uitbreiding is succesvol verwijderd."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1703
msgid "Extension not specified."
msgstr "Extensie niet gespecificeerd."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1708
#, php-format
msgid "Extension \"%s\" is not installed."
msgstr "Extensie \"%s\" is niet geïnstalleerd."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1776
#, php-format
msgid "Extension \"%s\" does not exist or is not active."
msgstr "Extensie \"%s\" bestaat niet of is niet actief."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1785
#, php-format
msgid "%s extension does not have settings."
msgstr "%s extensie heeft geen instellingen."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1806
msgid "Extension has no Install Instructions"
msgstr "Extensie heeft geen installatie instructies"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1830
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2047
msgid "Invalid request method."
msgstr "Ongeldige request methode."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1842
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2059
msgid "No extension specified."
msgstr "Geen extensie gespecificeerd."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1894
msgid "You have no permissions to activate extensions"
msgstr "Je hebt onvoldoende rechten om uitbreidingen te activeren."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:1914
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2132
#, php-format
msgid "Extension \"%s\" does not exist."
msgstr "Extensie \"%s\" bestaat niet."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2109
msgid "You have no permissions to deactivate extensions"
msgstr "Je hebt onvoldoende rechten om uitbreidingen te deactiveren."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2297
msgid "You are not allowed to save extensions settings."
msgstr "Je hebt geen toestemming om uitbreidingsinstellingen op te slaan."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2304
msgid "Invalid extension."
msgstr "Ongeldige extensie."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2309
msgid "Extension does not have settings options."
msgstr "Extensie heeft geen instellingen opties."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2341
msgid "Extensions settings successfully saved."
msgstr "Extensie instellingen met succes opgeslagen."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2368
#, php-format
msgid "Extension \"%s\" has no download sources."
msgstr "Extensie \"%s\" heeft geen download bronnen."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2392
#, php-format
msgid "Cannot create temporary directory: %s"
msgstr "Kan de tijdelijke directory %s niet aanmaken"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2403
#, php-format
msgid "\"%s\" extension github source \"user_repo\" parameter is required"
msgstr "\"%s\" extensie github bron \"user_repo\" parameter is vereist"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2454
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (%s)"
msgstr "Toegang verkrijgen tot releases van Github repository \"%s\" mislukt. (%s)"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2477
#, php-format
msgid "\"%s\" extension github repository \"%s\" has no releases."
msgstr "\"%s\" extensie github repository \"%s\" heeft geen releases."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2510
#, php-format
msgid "Cannot download the \"%s\" extension zip. (Response code: %d)"
msgstr "Kan de \"%s\" extensie zip niet downloaden. (Antwoord code: %d)"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2517
#, php-format
msgid "Cannot download the \"%s\" extension zip. %s"
msgstr "Kan de \"%s\" extensie zip niet downloaden. %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2525
#, php-format
msgid "Cannot download the \"%s\" extension zip."
msgstr "Kan de \"%s\" extensie zip niet downloaden."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2538
#, php-format
msgid "Cannot save the \"%s\" extension zip."
msgstr "Kan de \"%s\" extensie zip niet opslaan. "

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2557
#, php-format
msgid "Cannot remove the \"%s\" extension downloaded zip."
msgstr "Kan de gedownloade \"%s\" extension zip niet verwijderen."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2582
#, php-format
msgid "The unzipped \"%s\" extension directory not found."
msgstr "De uitgepakte \"%s\" extensie directory niet gevonden."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2589
#, php-format
msgid "Unknown \"%s\" extension download source \"%s\""
msgstr "Onbekende \"%s\" extensie download bron \"%s\""

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2615
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2634
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2697
#, php-format
msgid "Cannot read directory \"%s\"."
msgstr "Kan de directory \"%s\" niet lezen."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2649
#, php-format
msgid "Cannot delete \"%s\"."
msgstr "Kan \"%s\" niet verwijderen."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2660
#, php-format
msgid "Cannot create the \"%s\" directory."
msgstr "Kan de \"%s\" directory niet aanmaken."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:2678
#, php-format
msgid "Cannot move \"%s\" to \"%s\"."
msgstr "Kan \"%s\" niet verplaatsen naar \"%s\"."

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3103
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3150
#, php-format
msgid "Cannot activate the %s extension because it is not installed. %s"
msgstr "Kan de %s extensie niet activeren, omdat deze niet is geïnstalleerd. %s"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3107
#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3154
msgid "Install"
msgstr "Installeren"

#: ../framework/core/components/extensions/manager/class--fw-extensions-manager.php:3197
msgid "Install theme compatible extensions"
msgstr "Thema compatible extensies installeren"

#: ../framework/core/components/extensions/manager/available-extensions.php:13
msgid ""
"Adds a sliders module to your website from where you'll be able to create "
"different built in jQuery sliders for your homepage and rest of the pages."
msgstr "Voegt een slider module toe aan je website van waaruit je verschillende in jQuery ingebouwde sliders kan aanmaken voor je homepagina en de rest van de pagina's."

#: ../framework/core/components/extensions/manager/available-extensions.php:24
msgid "Media"
msgstr "Media"

#: ../framework/core/components/extensions/manager/available-extensions.php:36
msgid "Population method"
msgstr "Populatie methode"

#: ../framework/core/components/extensions/manager/available-extensions.php:85
msgid ""
"Let's you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr "Laat je gemakkelijk ontelbare pagina's bouwen met behulp van de drag en drop visuele pagina bouwer die komt met heel wat vooraf aangemaakte shortcodes."

#: ../framework/core/components/extensions/manager/available-extensions.php:96
msgid "Shortcodes"
msgstr "Shortcodes"

#: ../framework/core/components/extensions/manager/available-extensions.php:157
msgid ""
"This extension lets you set up daily, weekly or monthly backup schedule. You"
" can choose between a full backup or a data base only backup."
msgstr "Deze uitbreiding biedt de mogelijkheid om een dagelijks, wekelijks of maandelijks backup schema in te stellen. Je kunt daarbij kiezen tussen een volledige backup of alleen een database backup. "

#: ../framework/core/components/extensions/manager/views/extension.php:89
#: ../framework/core/components/extensions/manager/views/extension-page-header.php:25
msgid "Install Instructions"
msgstr "Installatie Instructies"

#: ../framework/core/components/extensions/manager/views/extension.php:102
msgid "Compatible"
msgstr "Compatibel"

#: ../framework/core/components/extensions/manager/views/extension.php:102
msgid "with your current theme"
msgstr "met je huidige thema"

#: ../framework/core/components/extensions/manager/views/extension.php:174
#, php-format
msgid "Parent extension \"%s\" is disabled"
msgstr "Parent extensie \"%s\" is uitgeschakeld"

#: ../framework/core/components/extensions/manager/views/extension.php:199
#, php-format
msgid "You need to update WordPress to %s: %s"
msgstr "Je moet WordPress updaten naar %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:201
msgid "Update WordPress"
msgstr "WordPress bijwerken"

#: ../framework/core/components/extensions/manager/views/extension.php:205
#, php-format
msgid "WordPress needs to be updated to %s"
msgstr "WordPress moet worden bijgewerkt naar %s"

#: ../framework/core/components/extensions/manager/views/extension.php:215
#, php-format
msgid "Maximum supported WordPress version is %s"
msgstr "Maximum ondersteune WordPress versie is %s"

#: ../framework/core/components/extensions/manager/views/extension.php:230
#, php-format
msgid "You need to update %s to %s: %s"
msgstr "Je moet %s updaten naar %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:239
#, php-format
msgid "%s needs to be updated to %s"
msgstr "%s moet worden bijgewerkt naar %s"

#: ../framework/core/components/extensions/manager/views/extension.php:250
#, php-format
msgid "Maximum supported %s version is %s"
msgstr "Maximum ondersteunde %s versie is %s"

#: ../framework/core/components/extensions/manager/views/extension.php:268
#, php-format
msgid "You need to update the %s extension to %s: %s"
msgstr "Je moet de %s extensie updaten naar %s: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:277
#, php-format
msgid "The %s extension needs to be updated to %s"
msgstr "De %s uitbreiding moet worden bijgewerkt naar %s"

#: ../framework/core/components/extensions/manager/views/extension.php:288
#, php-format
msgid "Maximum supported %s extension version is %s"
msgstr "Maximum ondesteunde %s extensie versie is %s"

#: ../framework/core/components/extensions/manager/views/extension.php:303
#, php-format
msgid "The %s extension is disabled"
msgstr "De %s extensie is gedisabled"

#: ../framework/core/components/extensions/manager/views/extension.php:304
#, php-format
msgid "Activate %s"
msgstr "Activeer %s"

#: ../framework/core/components/extensions/manager/views/extension.php:313
#, php-format
msgid "The %s extension is not installed: %s"
msgstr "De %s extensie is niet geïnstalled: %s"

#: ../framework/core/components/extensions/manager/views/extension.php:316
#, php-format
msgid "Install %s"
msgstr "Installeer %s"

#: ../framework/core/components/extensions/manager/views/extension.php:321
#, php-format
msgid "The %s extension is not installed"
msgstr "De %s extensie is niet geïnstalled"

#: ../framework/core/components/extensions/manager/views/extension.php:342
msgid "View Requirements"
msgstr "Bekijk Vereisten"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:40
#, php-format
msgid "%s Settings"
msgstr "%s Instellingen"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:43
#, php-format
msgid "%s Install Instructions"
msgstr "%s Installatie Instructies"

#: ../framework/core/components/extensions/manager/views/extension-page-header.php:46
msgid "Unknown tab:"
msgstr "Onbekende tab:"

#: ../framework/core/components/extensions/manager/views/delete-form.php:42
#: ../framework/core/components/extensions/manager/views/install-form.php:51
msgid "No, Return me to the extension list"
msgstr "Neen, geef me terug de extensielijst"

#: ../framework/core/components/extensions/manager/views/delete-form.php:45
msgid "Click to view entire list of directories which will be deleted"
msgstr "Klik om de volledige lijst van directies die zullen verwijderd worden, te zien"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:17
msgid "Active Extensions"
msgstr "Actieve extensies"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:33
msgid "No extensions activated yet"
msgstr "Nog geen extensies geactiveerd"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:33
msgid "Check the available extensions below"
msgstr "Check de beschikbare extensies hieronder"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:59
msgid "Available Extensions"
msgstr "Beschikbare Extensies"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:184
msgid "Show other extensions"
msgstr "Toon andere extensies"

#: ../framework/core/components/extensions/manager/views/extensions-page.php:185
msgid "Hide other extensions"
msgstr "Verberg andere extensies"

#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:14
#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:14
msgid "Go to extensions page"
msgstr "Ga naar de extensie pagina"

#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:17
#: ../framework/core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:17
msgid "Return to Extensions page"
msgstr "Keer terug naar de extensie pagina"

#: ../framework/views/backend-settings-form.php:48
msgid "Reset Options"
msgstr "Herstel Opties"

#: ../framework/views/backend-settings-form.php:62
msgid "by"
msgstr "door"

#: ../framework/views/backend-settings-form.php:155
msgid ""
"Click OK to reset.\n"
"All settings will be lost and replaced with default settings!"
msgstr "Klik OK om te herstellen.\nAlle instellingen zullen worden verwijderd en vervangen door de standaardinstellingen!"

#: ../framework/views/backend-settings-form.php:202
msgid "Resetting"
msgstr "Bezig met herstellen"

#: ../framework/views/backend-settings-form.php:204
msgid "We are currently resetting your settings."
msgstr "We zijn momenteel je instellingen aan het herstellen."

#: ../framework/views/backend-settings-form.php:206
#: ../framework/views/backend-settings-form.php:212
msgid "This may take a few moments."
msgstr "Dit kan enkele momenten duren."

#: ../framework/views/backend-settings-form.php:208
msgid "Saving"
msgstr "Bezig met opslaan"

#: ../framework/views/backend-settings-form.php:210
msgid "We are currently saving your settings."
msgstr "We zijn momenteel je instellingen aan het opslaan."

#: ../framework/includes/option-types/class-fw-option-type-undefined.php:25
msgid "UNDEFINED OPTION TYPE"
msgstr "NIET GEDEFINIEERD OPTIE TYPE"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:22
msgid "25%"
msgstr "25%"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:23
msgid "50%"
msgstr "50%"

#: ../framework/includes/option-types/radio-text/class-fw-option-type-radio-text.php:24
msgid "100%"
msgstr "100%"

#: ../framework/includes/option-types/map/views/view.php:12
msgid "Specify location"
msgstr "Specificeer locatie"

#: ../framework/includes/option-types/map/views/view.php:27
msgid "Location Venue"
msgstr "Locatie ontmoetingsplaats"

#: ../framework/includes/option-types/map/views/view.php:42
msgid "Address"
msgstr "Adres"

#: ../framework/includes/option-types/map/views/view.php:57
msgid "City"
msgstr "Stad"

#: ../framework/includes/option-types/map/views/view.php:72
msgid "Country"
msgstr "Land"

#: ../framework/includes/option-types/map/views/view.php:87
msgid "State"
msgstr "Staat"

#: ../framework/includes/option-types/map/views/view.php:103
msgid "Zip Code"
msgstr "Postcode"

#: ../framework/includes/option-types/map/views/view.php:138
msgid "Cannot find the location?"
msgstr "Kan de locatie niet vinden?"

#: ../framework/includes/option-types/map/views/view.php:150
msgid "Reset location"
msgstr "Resetten van locatie"

#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:150
msgid "Add Image"
msgstr "Voeg foto toe"

#: ../framework/includes/option-types/upload/class-fw-option-type-upload.php:176
#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:186
msgid "Upload"
msgstr "Uploaden"

#: ../framework/includes/option-types/typography-v2/view.php:39
msgid "Font face"
msgstr "Font face"

#: ../framework/includes/option-types/typography-v2/view.php:48
msgid "Normal"
msgstr "Normaal"

#: ../framework/includes/option-types/typography-v2/view.php:49
msgid "Italic"
msgstr "Cursief"

#: ../framework/includes/option-types/typography-v2/view.php:50
msgid "Oblique"
msgstr "Scheef"

#: ../framework/includes/option-types/typography-v2/view.php:59
#: ../framework/includes/option-types/typography-v2/view.php:118
msgid "Style"
msgstr "Stijl"

#: ../framework/includes/option-types/typography-v2/view.php:85
msgid "Weight"
msgstr "Dikte"

#: ../framework/includes/option-types/typography-v2/view.php:101
msgid "Script"
msgstr "Script"

#: ../framework/includes/option-types/typography-v2/view.php:128
msgid "Size"
msgstr "Grootte"

#: ../framework/includes/option-types/typography-v2/view.php:139
msgid "Line height"
msgstr "Lijnhoogte"

#: ../framework/includes/option-types/typography-v2/view.php:150
msgid "Letter spacing"
msgstr "Spatiëring"

#: ../framework/includes/option-types/typography-v2/view.php:173
msgid "Color"
msgstr "Kleur"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:156
msgid "Unknown Set"
msgstr "Onbekende instelling"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:186
msgid "Web Application Icons"
msgstr "Web Applicatie Iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:187
msgid "Hand Icons"
msgstr "Hand Iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:188
msgid "Transportation Icons"
msgstr "Transport Iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:189
msgid "Gender Icons"
msgstr "Gender Iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:190
msgid "File Type Icons"
msgstr "Bestandstype Iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:193
msgid "Payment Icons"
msgstr "Betaling Iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:195
msgid "Currency Icons"
msgstr "Valuta iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:196
msgid "Text Editor Icons"
msgstr "Text bewerker iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:197
msgid "Directional Icons"
msgstr "Directionele iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:198
msgid "Video Player Icons"
msgstr "Video speler iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:199
msgid "Brand Icons"
msgstr "Merk iconen"

#: ../framework/includes/option-types/icon/class-fw-option-type-icon.php:200
msgid "Medical Icons"
msgstr "Medische iconen"

#: ../framework/includes/option-types/icon/view.php:39
msgid "All Categories"
msgstr "Alle categorieën"

#: ../framework/includes/option-types/datetime-range/view.php:41
#: ../framework/includes/option-types/gradient/view.php:46
msgid "to"
msgstr "naar"

#: ../framework/includes/option-types/multi-picker/class-fw-option-type-multi-picker.php:179
#, php-format
msgid "No 'picker' key set for multi-picker option: %s"
msgstr "Geen \"picker\" toets ingesteld voor multi-picker optie: %s"

#: ../framework/includes/option-types/background-image/view.php:37
msgid "Predefined images"
msgstr "Vooraf gedefinieerde foto's"

#: ../framework/includes/option-types/background-image/view.php:38
msgid "Custom image"
msgstr "Aangepaste foto"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:160
msgid "Add Images"
msgstr "Voeg foto's toe"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:188
msgid "1 File"
msgstr "1 bestand"

#: ../framework/includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:189
#, php-format
msgid "%u Files"
msgstr "%u Bestanden"
