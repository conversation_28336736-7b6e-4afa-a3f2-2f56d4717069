@font-face {
	font-family: 'unyson-font-icon';
	src:url('fonts/icomoon.eot?iganyx');
	src:url('fonts/icomoon.eot?#iefixiganyx') format('embedded-opentype'),
		url('fonts/icomoon.woff?iganyx') format('woff'),
		url('fonts/icomoon.ttf?iganyx') format('truetype'),
		url('fonts/icomoon.svg?iganyx#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

.toplevel_page_fw-extensions > .wp-menu-image:before {
	font-family: 'unyson-font-icon';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;

	content: "\e600";
	font-size: 14px;
	line-height: 20px;
}
