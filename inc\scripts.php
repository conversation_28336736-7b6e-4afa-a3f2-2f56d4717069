<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

use Elementor\Plugin;

class Scripts {

    public $version;
    protected static $instance = null;

    public function __construct() {
        add_action( 'wp_enqueue_scripts', array( $this, 'register_scripts' ), 12 );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ), 15 );
        add_action( 'admin_enqueue_scripts', array( $this, 'admin_scripts' ), 15 ); 
    }

    public static function instance() {
        if ( null == self::$instance ) {
            self::$instance = new self;
        }
        return self::$instance;
    }
 

    public function register_scripts() {

        wp_register_style( 'slick', Helper::get_vendor_css( 'slick' ), array(), AXIL_VERSION );
        wp_register_style( 'slick-theme', Helper::get_vendor_css( 'slick-theme' ), array(), AXIL_VERSION );
        wp_register_style( 'bootstrap', Helper::maybe_vendors_rtl( 'bootstrap.min' ), array(), AXIL_VERSION );
        wp_register_style( 'sal', Helper::get_vendor_css( 'sal' ), array(), AXIL_VERSION );
        wp_register_style( 'simplebar', Helper::get_vendor_css( 'simplebar' ), array(), AXIL_VERSION );
        wp_register_style( 'axil-style', Helper::get_css( 'style' ), array(), AXIL_VERSION );
        wp_register_style( 'axil-woocommerce', Helper::get_css( 'woocommerce' ), array(), AXIL_VERSION );

        wp_register_style( 'axil-style-rtl', Helper::get_rtl_css( 'rtl' ), array(), AXIL_VERSION ); 
    
        wp_register_style( 'etrade-gfonts', $this->etrade_fonts_url(), array(), AXIL_VERSION );
        wp_register_style( 'theme-font-awesome', Helper::get_css( 'font-awesome' ), array(), AXIL_VERSION );

        wp_register_script( 'images-loaded', Helper::get_vendor_js( 'imagesloaded.pkgd.min' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'isotope', Helper::get_vendor_js( 'isotope.pkgd.min' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'slick', Helper::get_vendor_js( 'slick.min' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'modernizr', Helper::get_vendor_js( 'modernizr.min' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'popper-min', Helper::get_vendor_js( 'popper.min' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'bootstrap', Helper::get_vendor_js( 'bootstrap.min' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'tweenmax', Helper::get_vendor_js( 'tweenmax.min' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'gsap', Helper::get_vendor_js( 'gsap' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'axil-copylink', Helper::get_vendor_js( 'commands' ), array( 'jquery' ), AXIL_VERSION, true );

        wp_register_script( 'sal', Helper::get_vendor_js( 'sal' ), array(), AXIL_VERSION );
        wp_register_script( 'jquery-countdown', Helper::get_vendor_js( 'jquery.countdown.min' ), array(), AXIL_VERSION );

        wp_register_script( 'jquery-magnific-popup', Helper::get_vendor_js( 'jquery.magnific-popup.min' ), array(), AXIL_VERSION );
        wp_register_style( 'magnific-popup', Helper::get_vendor_css( 'magnific-popup' ), array(), AXIL_VERSION );
        wp_register_script( 'axil-cookie', Helper::get_vendor_js( 'js.cookie' ), array( 'jquery' ), AXIL_VERSION, true );

        wp_register_script( 'etrade-navigation', Helper::get_js( 'navigation' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'etrade-skip-link-focus-fix', Helper::get_js( 'skip-link-focus-fix' ), array( 'jquery' ), AXIL_VERSION, true );

        if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
            wp_enqueue_script( 'comment-reply' );
        }
        wp_register_script( 'axil-main', Helper::get_js( 'main' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'axil-has-elementor', Helper::get_js( 'has-elementor' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_register_script( 'axil-shop-login', Helper::get_js( 'axil-shop-login' ), array( 'jquery' ), AXIL_VERSION, true );
    }

    public function enqueue_scripts() {
        wp_deregister_style( 'font-awesome' );
        wp_deregister_style( 'elementor-icons-shared-0-css' );
        wp_deregister_style( 'elementor-icons-fa-solid-css' );
        wp_deregister_style( 'elementor-icons-fa-regular-css' );
        wp_deregister_style( 'yith-wcwl-font-awesome' );
        wp_enqueue_style( 'theme-font-awesome' );
        wp_enqueue_style( 'etrade-gfonts' );

        wp_enqueue_style( 'sal' );
        wp_enqueue_style( 'bootstrap' );
        wp_enqueue_style( 'slick' );
        wp_enqueue_style( 'slick-theme' );
        wp_enqueue_style( 'magnific-popup' );
        wp_enqueue_style( 'axil-woocommerce' );
        wp_enqueue_style( 'axil-style' );
        wp_enqueue_style( 'axil-style-rtl' );

        $this->etrade_fonts_url();
        $this->elementor_scripts();
        wp_enqueue_style( 'etrade-style', get_stylesheet_uri() );
        wp_enqueue_style( 'etrade-elementor' );
        wp_enqueue_script( 'popper-min' );
        wp_enqueue_script( 'bootstrap' );
        wp_enqueue_script( 'jquery-countdown' );
        wp_enqueue_script( 'jquery-magnific-popup' );
        wp_enqueue_script( 'slick' );
        wp_enqueue_script( 'sal' );
        wp_enqueue_script( 'jquery-ui-autocomplete' );
        wp_enqueue_script( 'axil-main' );
        wp_enqueue_script( 'axil-has-elementor' );
        wp_enqueue_script( 'axil-shop-login' );

        $this->localized_scripts(); // Localization
    }

    public function elementor_scripts() {
        if ( !did_action( 'elementor/loaded' ) ) {
            return;
        }
        if ( Plugin::$instance->preview->is_preview_mode() ) {

            wp_enqueue_style( 'slick' );
            wp_enqueue_style( 'slick-theme' );
            wp_enqueue_style( 'sal' );
            wp_enqueue_script( 'sal' );
            wp_enqueue_script( 'isotope' );
            wp_enqueue_script( 'images-loaded' );
            wp_enqueue_script( 'slick' );
            wp_enqueue_script( 'jquery-countdown' );

        }
    }

    public function admin_scripts() { 
    
        wp_enqueue_script( 'etrade-logo-uploader', Helper::get_admin_js( 'logo-uploader' ), array( 'jquery' ), AXIL_VERSION, true );
        wp_enqueue_media();
        wp_enqueue_script( 'jquery-ui-tabs' );

    }

    private function etrade_fonts_url() {
        $fonts_url = '';
        $fonts = array();
        $subsets = 'latin,latin-ext';

        /* translators: If there are characters in your language that are not supported by Nunito+Sans Sans, translate this to 'off'. Do not translate into your own language. */
        if ( 'off' !== esc_attr_x( 'on', 'Red Hat Display font: on or off', 'etrade' ) ) {
            $fonts[] = 'DM Sans:ital,wght@0,400;0,500;0,700;1,400';
        }
        if ( $fonts ) {
            $fonts_url = add_query_arg( array(
                'family' => urlencode( implode( '|', $fonts ) ),
                'subset' => urlencode( $subsets ),
            ), 'https://fonts.googleapis.com/css' );
        }

        return esc_url_raw( $fonts_url );
    }

    private function localized_scripts() {
        $axil_options = Helper::axil_get_options();

        $localize_data = array(
            'ajaxurl'      => admin_url( 'admin-ajax.php' ),
            'hasAdminBar'  => is_admin_bar_showing() ? 1 : 0,
            'rtl'          => is_rtl(),

            'day'          => isset( $axil_options['cd_days'] ) ? $axil_options['cd_days'] : esc_html__( 'Days', 'etrade' ),
            'hour'         => isset( $axil_options['cd_hour'] ) ? $axil_options['cd_hour'] : esc_html__( 'Hour', 'etrade' ),
            'minute'       => isset( $axil_options['cd_minute'] ) ? $axil_options['cd_minute'] : esc_html__( 'Minute', 'etrade' ),
            'second'       => isset( $axil_options['cd_second'] ) ? $axil_options['cd_second'] : esc_html__( 'Second', 'etrade' ),

            'under_day'    => isset( $axil_options['under_cd_days'] ) ? $axil_options['under_cd_days'] : esc_html__( 'Days', 'etrade' ),
            'under_hour'   => isset( $axil_options['under_cd_hour'] ) ? $axil_options['under_cd_hour'] : esc_html__( 'Hrs', 'etrade' ),
            'under_minute' => isset( $axil_options['under_cd_minute'] ) ? $axil_options['under_cd_minute'] : esc_html__( 'Min', 'etrade' ),
            'under_second' => isset( $axil_options['under_cd_second'] ) ? $axil_options['under_cd_second'] : esc_html__( 'Sec', 'etrade' ),
             'galleryThumbnailsSlider'       => true,
              'shopYouTubeRelated'           => ( ! defined( 'AXIL_SHOP_YOUTUBE_RELATED' ) ) ? 1 : 0,
            'rtl'          => is_rtl() ? 'yes' : 'no', //@rtl
            'rtlslider'          => is_rtl() ? true : false, //@rtl

        );

        wp_localize_script( 'axil-has-elementor', 'etradeObj', $localize_data );
    }

    private function conditional_scripts() {
        $axil_options = Helper::axil_get_options();
        if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
            wp_enqueue_script( 'comment-reply' );
        }
        if ( is_singular( 'product' ) && $axil_options['product_wc_single_layout'] == '2' ) {
            wp_enqueue_script( 'jquery-sticky-sidebar' );
        }
    }
}
Scripts::instance();