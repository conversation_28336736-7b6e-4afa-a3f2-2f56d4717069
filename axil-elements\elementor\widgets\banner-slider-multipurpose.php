<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Controls_Manager;
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;
use Elementor\Repeater;
use <PERSON>ementor\Widget_Base;

if ( !defined( 'ABSPATH' ) ) {
    exit;
}
// Exit if accessed directly

class axil_banner_slider_multipurpose extends Widget_Base {

    public function get_name() {
        return 'axil-banner-slider-multipurpose';
    }
    public function get_title() {
        return __( 'Banner - Multipurpose', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-banner';
    }
    public function get_categories() {
        return array( ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' );
    }
    public function axil_get_img( $img ) {
        $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
        return $img;
    }

    public function get_product_name( $post_type = 'product' ) {
        $options = array();
        $options = array( '0' => esc_html__( 'None', 'etrade-elements' ) );
        $axil_post = array( 'posts_per_page' => -1, 'post_type' => $post_type );
        $axil_post_terms = get_posts( $axil_post );
        if ( !empty( $axil_post_terms ) && !is_wp_error( $axil_post_terms ) ) {
            foreach ( $axil_post_terms as $term ) {
                $options[$term->ID] = $term->post_title;
            }
            return $options;
        }
    }
    private function axil_get_all_pages() {

        $page_list = get_posts( array(
            'post_type'      => 'page',
            'orderby'        => 'date',
            'order'          => 'DESC',
            'posts_per_page' => -1,
        ) );

        $pages = array();

        if ( !empty( $page_list ) && !is_wp_error( $page_list ) ) {
            foreach ( $page_list as $page ) {
                $pages[$page->ID] = $page->post_title;
            }
        }

        return $pages;
    }

    protected function register_controls() {

        $this->start_controls_section(
            'banner_content_sec',
            array(
                'label' => esc_html__( ' Banner Content', 'etrade-elements' ),

            )
        );
        $this->add_control(
            'list_title',
            array(
                'label'       => esc_html__( 'Slider Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXTAREA,
                'default'     => esc_html__( 'Roco Wireless Headphone', 'etrade-elements' ),
                'label_block' => true,
            )
        );

        $this->add_control(
            'titleimage',
            array(
                'label'   => esc_html__( 'Title Image', 'etrade-elements' ),
                'type'    => Controls_Manager::MEDIA,
                'default' => array(
                    'url' => $this->axil_get_img( 'emoji.png' ),
                ),
                'dynamic' => array(
                    'active' => true,
                ),

            )
        );

        $this->add_group_control(
            Group_Control_Image_Size::get_type(),
            array(
                'name'      => 'title_image_size',
                'default'   => 'full',
                'separator' => 'none',

            )
        );

        $this->add_control(
            'image',
            array(
                'label'     => esc_html__( 'Product Image', 'etrade-elements' ),
                'type'      => Controls_Manager::MEDIA,
                'separator' => 'before',
                'default'   => array(
                    'url' => $this->axil_get_img( 'women_sunglass.png' ),
                ),
                'dynamic'   => array(
                    'active' => true,
                ),

            )
        );

        $this->add_group_control(
            Group_Control_Image_Size::get_type(),
            array(
                'name'      => 'image_size',
                'default'   => 'full',
                'separator' => 'none',

            )
        );

        $this->add_control(
            'btntext',
            array(
                'label'   => __( 'Button Text', 'etrade-elements' ),
                'type'    => Controls_Manager::TEXT,
                'default' => 'View All Products',

            )
        );

        $this->add_control(
            'axil_link_type',
            array(
                'label'       => esc_html__( 'See All Link Type', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT,
                'options'     => array(
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ),
                'default'     => '1',

                'label_block' => true,
            )
        );

        $this->add_control(
            'axil_page_link',
            array(
                'label'       => esc_html__( 'Select See All Page', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT2,
                'label_block' => true,
                'options'     => $this->axil_get_all_pages(),
                'condition'   => array(
                    'axil_link_type' => '2',

                ),
            )
        );

        $this->add_control(
            'url',
            array(
                'label'       => __( 'Detail URL', 'etrade-elements' ),
                'type'        => Controls_Manager::URL,
                'placeholder' => 'https://your-link.com',
                'condition'   => array(
                    'axil_link_type' => '1',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'plus_icon_style_section',
            array(
                'label' => esc_html__( 'Plus Icon', 'etrade-elements' ),

            )
        );

        $this->add_control(
            'plus_icon_display',
            array(
                'type'      => Controls_Manager::SWITCHER,
                'label'     => esc_html__( 'Plus Icon Display', 'etrade-elements' ),
                'label_on'  => esc_html__( 'On', 'etrade-elements' ),
                'label_off' => esc_html__( 'Off', 'etrade-elements' ),
                'default'   => 'yes',
                'separator' => 'before',

            )
        );

        $repeater = new Repeater();
        $repeater->add_responsive_control(
            'plus_icon_active',
            array(
                'type'      => Controls_Manager::SWITCHER,
                'label'     => esc_html__( 'Plus Icon Active', 'etrade-elements' ),
                'label_on'  => esc_html__( 'On', 'etrade-elements' ),
                'label_off' => esc_html__( 'Off', 'etrade-elements' ),
                'default'   => 'no',
                'separator' => 'before',

            )
        );
        $repeater->add_control(
            'product_ids',
            array(
                'label'       => __( 'Select Product will display', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT,
                'options'     => $this->get_product_name(),
                'label_block' => true,
                'multiple'    => true,
                'separator'   => 'before',

            )
        );

        $repeater->add_control(
            'rating_display',
            array(

                'type'      => Controls_Manager::SWITCHER,
                'label'     => esc_html__( 'Rating Display', 'etrade-elements' ),
                'label_on'  => esc_html__( 'On', 'etrade-elements' ),
                'label_off' => esc_html__( 'Off', 'etrade-elements' ),
                'default'   => 'yes',

            )
        );
        $repeater->add_control(
            'sale_price_only',
            array(

                'type'      => Controls_Manager::SWITCHER,
                'label'     => __( 'Display only sale price', 'etrade-elements' ),
                'label_on'  => __( 'On', 'etrade-elements' ),
                'label_off' => __( 'Off', 'etrade-elements' ),
                'default'   => 'yes',

            )
        );
        $repeater->add_control(
            'pos_x_type',
            array(
                'type'    => Controls_Manager::CHOOSE,
                'label'   => esc_html__( 'Horizontal Position', 'etrade-elements' ),

                'options' => array(
                    'left'  => array(
                        'title' => esc_html__( 'Left', 'etrade-elements' ),
                        'icon'  => 'eicon-h-align-left',
                    ),
                    'right' => array(
                        'title' => esc_html__( 'Right', 'etrade-elements' ),
                        'icon'  => 'eicon-h-align-right',
                    ),
                ),
                'default' => 'left',
            )
        );

        $repeater->add_responsive_control(
            'pos_x',
            array(
                'type'       => Controls_Manager::SLIDER,
                'label'      => esc_html__( 'Spacing', 'etrade-elements' ),

                'size_units' => array( 'px', '%' ),
                'range'      => array(
                    'px' => array(
                        'min' => -500,
                        'max' => 500,
                    ),
                    '%'  => array(
                        'min' => -100,
                        'max' => 100,
                    ),
                ),
                'default'    => array(
                    'unit' => '%',
                    'size' => 40,
                ),

                'selectors'  => array(
                    '{{WRAPPER}} {{CURRENT_ITEM}}.banner-product.axil-pos-left'  => 'left: {{SIZE}}{{UNIT}}; right: inherit;',
                    '{{WRAPPER}} {{CURRENT_ITEM}}.banner-product.axil-pos-right' => 'right: {{SIZE}}{{UNIT}}; left: inherit;',
                ),
            )
        );

        $repeater->add_control(
            'pos_y_type',
            array(
                'type'    => Controls_Manager::CHOOSE,
                'label'   => esc_html__( 'Vertical Position', 'etrade-elements' ),

                'options' => array(
                    'top'    => array(
                        'title' => esc_html__( 'Left', 'etrade-elements' ),
                        'icon'  => 'eicon-v-align-top',
                    ),
                    'bottom' => array(
                        'title' => esc_html__( 'Right', 'etrade-elements' ),
                        'icon'  => 'eicon-v-align-bottom',
                    ),
                ),
                'default' => 'top',
            )
        );

        $repeater->add_responsive_control(
            'pos_y',
            array(
                'type'       => Controls_Manager::SLIDER,
                'label'      => esc_html__( 'Spacing', 'etrade-elements' ),
                'size_units' => array( 'px', '%' ),

                'range'      => array(
                    'px' => array(
                        'min' => -500,
                        'max' => 500,
                    ),
                    '%'  => array(
                        'min' => -100,
                        'max' => 100,
                    ),
                ),
                'default'    => array(
                    'unit' => '%',
                    'size' => 30,
                ),
                'selectors'  => array(
                    '{{WRAPPER}} {{CURRENT_ITEM}}.banner-product.axil-pos-top'    => 'top: {{SIZE}}{{UNIT}};',
                    '{{WRAPPER}} {{CURRENT_ITEM}}.banner-product.axil-pos-bottom' => 'bottom: {{SIZE}}{{UNIT}};',
                ),
            )
        );

        $this->add_control(
            'list',
            array(
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $repeater->get_controls(),
                'show_label'  => false,
                'default'     => array(
                    array(
                        'product_ids' => esc_html__( 'Product Title', 'etrade-elements' ),

                    ),

                ),
                'title_field' => '{{{ product_ids }}}',
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'title_style_section',
            array(
                'label' => esc_html__( 'Title', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'title_color',
            array(
                'label'     => esc_html__( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .inner .title'               => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .title' => 'color: {{VALUE}}',
                ),
            )
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'title_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .inner .title , {{WRAPPER}} .main-slider-content .title',
            )
        );

        $this->add_responsive_control(
            'title_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .inner .title'               => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .main-slider-content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'button_style_section',
            array(
                'label' => esc_html__( 'Button', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'button_color',
            array(
                'label'     => esc_html__( 'Text Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .main-slider-content .shop-btn a'   => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .shop-btn a i' => 'color: {{VALUE}}',

                ),
            )
        );
        $this->add_control(
            'button_bg_color',
            array(
                'label'     => esc_html__( 'Background Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .main-slider-content .shop-btn a'        => 'background: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .shop-btn a:before' => 'background: {{VALUE}}',

                ),
            )
        );
        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'button_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .main-slider-content .shop-btn a, {{WRAPPER}} .main-slider-content .shop-btn a i',
            )
        );

        $this->add_responsive_control(
            'button_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .main-slider-content .shop-btn' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );
        $this->end_controls_section();

        $this->start_controls_section(
            'poster_shape10',
            array(
                'label' => esc_html__( 'Background', 'etrade-elements' ),

            )
        );

        $this->add_group_control(
            \Elementor\Group_Control_Background::get_type(),
            array(
                'name'     => 'banner-bg',
                'types'    => array( 'classic', 'gradient' ),
                'selector' => '{{WRAPPER}} .axil-main-slider-area',

            )
        );
        $this->add_control(
            'shape_style_on',
            array(
                'label'     => esc_html__( 'Shape Condition', 'etrade-elements' ),
                'type'      => Controls_Manager::SWITCHER,
                'label_on'  => esc_html__( 'On', 'etrade-elements' ),
                'label_off' => esc_html__( 'Off', 'etrade-elements' ),
                'default'   => 'no',
                'separator' => 'before',

            )
        );

        $this->add_control(
            'svg1_color1',
            array(
                'label'     => esc_html__( 'Svg 1 Color 1', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '#FAF1EE',
                'condition' => array( 'shape_style_on' => array( 'yes' ) ),
                'separator' => 'after',

            )
        );
        $this->add_control(
            'svg1_color2',
            array(
                'label'     => esc_html__( 'Svg 1 Color 2', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '#FEEAE9',
                'condition' => array( 'shape_style_on' => array( 'yes' ) ),
                'separator' => 'before',

            )
        );

        $this->add_control(
            'svg2_color1',
            array(
                'label'     => esc_html__( 'Svg 2 Color 1', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '#FBE9E3',
                'condition' => array( 'shape_style_on' => array( 'yes' ) ),

            )
        );
        $this->add_control(
            'svg2_color2',
            array(
                'label'     => esc_html__( 'Svg 2 Color 2', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '#FFD3C5',
                'condition' => array( 'shape_style_on' => array( 'yes' ) ),

            )
        );

        $this->end_controls_section();
    }

    protected function render() {
        $settings = $this->get_settings();
        $template = 'banner-multipurpose';
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
    }

}