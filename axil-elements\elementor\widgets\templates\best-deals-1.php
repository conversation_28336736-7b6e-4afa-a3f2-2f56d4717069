<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
 
 
use Elementor\Group_Control_Image_Size;
use Elementor\Utils;
use Elementor\Group_Control_Typography;

$attr = ''; 
$btn = ''; 
if ('1' == $settings['link_type']) {
    if ( !empty( $settings['link']['url'] ) ) {
        $attr  = 'href="' . $settings['link']['url'] . '"';
        $attr .= !empty( $settings['link']['is_external'] ) ? ' target="_blank"' : '';
        $attr .= !empty( $settings['link']['nofollow'] ) ? ' rel="nofollow"' : '';
        $title = '<a ' . $attr . '>' . $settings['title'] . '</a>';
    }
    if ( !empty( $settings['link_text'] ) ) {
        $btn = '<a class="axil-btn btn-bg-primary btn-size-md" ' . $attr . '><i class="fal fa-shopping-cart"></i>' . $settings['link_text'] . '</a>';
    }
    }else {
    $attr  = 'href="' . get_permalink($settings['page_link']) . '"';
    $attr .= ' target="_self"';
    $attr .= ' rel="nofollow"';                        
    $btn = '<a class="axil-btn btn-bg-primary btn-size-md" ' . $attr . '><i class="fal fa-shopping-cart"></i>' . $settings['link_text'] . '</a>';
}
$allowed_tags = wp_kses_allowed_html( 'post' );
?> 

<div class="product-collection product-collection-two">
    <div class="collection-content">
        <?php  if ( !empty( $settings['title'] ) ) { ?>    
            <h3 class="title"><?php echo wp_kses( $settings['title'] , $allowed_tags ); ?></h3> 
        <?php } ?>  
        <div class="price-warp">
            <?php  if ( !empty( $settings['subtitle'] ) ) { ?>    
                <span class="price-text"><?php echo wp_kses( $settings['subtitle'] , $allowed_tags ); ?></span>
            <?php } ?>  
            <?php  if ( !empty( $settings['current-price'] ) ) { ?>
                <span class="price"><?php echo wp_kses( $settings['current-price'] , $allowed_tags ); ?></span>
            <?php } ?>  
        </div>   

        <?php  if ( !empty( $settings['link_text'] ) ) { ?>    
            <div class="shop-btn">         
                <?php echo wp_kses( $btn, $allowed_tags ); ?>
            </div>
        <?php } ?>   

        <?php if ( $settings['plus_icon_display'] ):
             $plus_active = $settings['popup_icon_active'] ? 'icon-active' : '';
            ?>  
            <div class="plus-btn <?php echo esc_attr( $plus_active );?> axil-pos-<?php echo esc_attr( $settings['pos_y_type'] );?> axil-pos-<?php echo esc_attr( $settings['pos_x_type'] );?>">
                <a href="#" class="plus-icon"><i class="far fa-plus"></i></a> 
                 <?php   
               
                $posts = array_map( 'trim' , explode( ',', $settings['product_ids'] ) );
                $args   = array(
                    'post_type'      => 'product',
                    'ignore_sticky_posts' => true,
                    'nopaging'       => true,
                    'post__in'       => $posts,
                    'orderby'        => 'post__in',
                    'posts_per_page' => 1,
                ); 
                $query =  new \WP_Query( $args ); ?>

                   <div class="plus-hover-product">  
                    <?php if ( $query->have_posts() ) :  
                        while ( $query->have_posts() ) : $query->the_post(); 
                            $id = get_the_ID();
                            $product = wc_get_product( $id );
                            $price = $product->get_price_html();
                            $title = get_the_title();   

                            if ( $settings['sale_price_only'] ) {
                                $price_html = wc_price( wc_get_price_to_display( $product ) ) . $product->get_price_suffix();
                            }
                            else {
                                $price_html = $product->get_price_html();
                            }   
                            ?>
                             <div class="product-details">
                                <h4 class="title"><a href="<?php the_permalink();?>"><?php echo wp_kses_post( $title ); ?></a></h4>
                                <div class="price"><?php echo wp_kses_post( $price_html ); ?></div>
                               
                               <?php
                                if ( $settings['rating_display'] == "yes" ) {
                                    wc_get_template( 'loop/rating4.php' );
                                }
                            ?>   
                        </div>  
                    <?php endwhile;?>    
                <?php endif;?>   
              <?php wp_reset_postdata();?>   
            </div>
        </div> 
        <?php endif; ?>    
       
    </div>
    <div class="collection-thumbnail">
         <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>
    </div>
</div> 
