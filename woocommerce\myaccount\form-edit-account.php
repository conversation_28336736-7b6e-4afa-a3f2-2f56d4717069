<?php
/**
 * Edit account form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/form-edit-account.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 8.7.0
 */

defined( 'ABSPATH' ) || exit;

do_action( 'woocommerce_before_edit_account_form' );?>

<form class="woocommerce-EditAccountForm edit-account account-details-form" action="" method="post" <?php do_action( 'woocommerce_edit_account_form_tag' );?> >

	<?php do_action( 'woocommerce_edit_account_form_start' );?>
<div class="row">
	<div class="col-lg-6">
	<div class="woocommerce-form-row woocommerce-form-row--first form-row form-row-first form-group">
		<label for="account_first_name"><?php esc_html_e( 'First name', 'etrade' );?>&nbsp;<span class="required">*</span></label>
		<input type="text" class="woocommerce-Input woocommerce-Input--text input-text" name="account_first_name" id="account_first_name" autocomplete="given-name" value="<?php echo esc_attr( $user->first_name ); ?>" />
	</div>
</div>
<div class="col-lg-6">
	<div class="woocommerce-form-row woocommerce-form-row--last form-row form-row-last form-group">
		<label for="account_last_name"><?php esc_html_e( 'Last name', 'etrade' );?>&nbsp;<span class="required">*</span></label>
		<input type="text" class="woocommerce-Input woocommerce-Input--text input-text" name="account_last_name" id="account_last_name" autocomplete="family-name" value="<?php echo esc_attr( $user->last_name ); ?>" />
	</div>
</div>
	<div class="col-lg-12">

	<div class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide form-group">
		<label for="account_display_name"><?php esc_html_e( 'Display name', 'etrade' );?>&nbsp;<span class="required">*</span></label>
		<input type="text" class="woocommerce-Input woocommerce-Input--text input-text" name="account_display_name" id="account_display_name" value="<?php echo esc_attr( $user->display_name ); ?>" /> <span><em><?php esc_html_e( 'This will be how your name will be displayed in the account section and in reviews', 'etrade' );?></em></span>
	</div>
</div>
<div class="col-lg-12">
	<div class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide form-group">
		<label for="account_email"><?php esc_html_e( 'Email address', 'etrade' );?>&nbsp;<span class="required">*</span></label>
		<input type="email" class="woocommerce-Input woocommerce-Input--email input-text" name="account_email" id="account_email" autocomplete="email" value="<?php echo esc_attr( $user->user_email ); ?>" />
	</div>

	<fieldset>
		<legend><?php esc_html_e( 'Password change', 'etrade' );?></legend>

		<div class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide form-group">
			<label for="password_current"><?php esc_html_e( 'Current password (leave blank to leave unchanged)', 'etrade' );?></label>
			<input type="password" class="woocommerce-Input woocommerce-Input--password input-text" name="password_current" id="password_current" autocomplete="off" />
		</div>
		<div class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide form-group">
			<label for="password_1"><?php esc_html_e( 'New password (leave blank to leave unchanged)', 'etrade' );?></label>
			<input type="password" class="woocommerce-Input woocommerce-Input--password input-text" name="password_1" id="password_1" autocomplete="off" />
		</div>
		<div class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide form-group">
			<label for="password_2"><?php esc_html_e( 'Confirm new password', 'etrade' );?></label>
			<input type="password" class="woocommerce-Input woocommerce-Input--password input-text" name="password_2" id="password_2" autocomplete="off" />
		</div>
	</fieldset>
	<div class="clear"></div>

	<?php do_action( 'woocommerce_edit_account_form' );?>

	<div class="form-group mb--0">
		<?php wp_nonce_field( 'save_account_details', 'save-account-details-nonce' );?>
		<button type="submit" class="woocommerce-Button button axil-btn" name="save_account_details" value="<?php esc_attr_e( 'Save changes', 'etrade' );?>"><?php esc_html_e( 'Save changes', 'etrade' );?></button>
		<input type="hidden" name="action" value="save_account_details" />
	</div>
</div>
</div>
	<?php do_action( 'woocommerce_edit_account_form_end' );?>
</form>

<?php do_action( 'woocommerce_after_edit_account_form' );?>
