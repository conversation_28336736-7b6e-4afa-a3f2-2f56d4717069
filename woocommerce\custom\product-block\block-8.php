
<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

$id = $product->get_id();
$attributes_escaped = function_exists( 'wooc_template_loop_attributes' ) ? wooc_template_loop_attributes() : null;
$has_color_pick = "";
$has_color_pick .= ( $attributes_escaped ) ? ' has-color-pick' : '';
$class = WooC_Functions::product_display_hover_wrp_class( $product, $block_data['product_display_hover'] );
?>
 <div class="axil-product product-style-eight <?php echo esc_attr( $has_color_pick ); ?> <?php echo esc_attr( $class ); ?>">
    <div class="thumbnail">
         <?php echo WooC_Functions::get_product_thumbnail_link2( $product, $block_data['thumb_size'], $block_data['product_display_hover'] ); ?>
         <?php woocommerce_show_product_loop_sale_flash();?>
        <div class="product-hover-action">
            <ul class="cart-action">
                <li class="add-to-cart select-option">
                    <?php WooC_Functions::wooc_print_add_to_cart_icon( true, true, false );?>
                </li>
            </ul>
        </div>
    </div>
    <div class="product-content">
        <div class="inner">
             <?php
                    if ( $block_data['display_attributes'] ):
                        if ( $attributes_escaped ):
                            $allowed_tags = wp_kses_allowed_html( 'post' );
                            echo wp_kses( $attributes_escaped, $allowed_tags );

                        endif;
                    endif;
                    ?>
                                <?php if ( WooC_Functions::is_product_archive() ) {
                        do_action( 'woocommerce_before_shop_loop_item_title' );
                    }
                    ?>
                <h5 class="title">
                    <a href="<?php the_permalink();?>"><?php the_title();?>
                        <?php if ( $block_data['display_title_badge_check'] ): ?>
                            <span class="verified-icon"><i class="fas fa-badge-check"></i></span>
                        <?php endif;?>
                    </a>
                </h5>
                <?php if ( WooC_Functions::is_product_archive() ) {
                    do_action( 'woocommerce_after_shop_loop_item_title' );
                }
                ?>  <?php
                if ( $block_data['rating_display'] ) {
                    wc_get_template( 'loop/rating3.php' );
                }
                ?>
            <?php WooC_Functions::wc_get_loop_price( $product, $block_data['sale_price_only'] );?>
        </div>
    </div>
</div>