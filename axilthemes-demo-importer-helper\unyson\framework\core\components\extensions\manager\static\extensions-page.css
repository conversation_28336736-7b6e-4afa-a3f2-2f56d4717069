.fw-extensions-list a {
	text-decoration: none;
}

.fw-extensions-no-active {
	margin: 75px 0;
}

.fw-extensions-no-active .fw-text-muted {
	color: #9d9d9d;
}

.fw-extensions-no-active .fw-extensions-title-icon .dashicons {
	color: #d3d3d3;
	font-size: 46px;
	width: auto;
	height: auto;
	line-height: 35px;
}

.fw-extensions-list .fw-extensions-list-item {
	padding: 0 15px 15px 0;
	vertical-align: top;
	display: inline-block;
	float: none;
	margin: 0 -1px;
}

.fw-extensions-list .fw-extensions-list-item > .inner {
	padding: 20px;
	background-color: #ffffff;
	border: 1px solid #dedede;
	position: relative;
}

.fw-extensions-list .fw-extensions-list-item > .inner > p:last-child {
	margin-bottom: 0;
}

.fw-extensions-list .fw-extensions-list-item .fw-extensions-list-item-thumbnail-wrapper {
	height: 128px;
	width: 128px;
	text-align: center;
	background: url('img/thumbnail-bg.jpg');
	background-size: 128px;
}

.fw-extensions-list .fw-extensions-list-item .fw-extensions-list-item-thumbnail-wrapper img.fw-extensions-list-item-thumbnail {
	display: block;
	height: 100%;
}

.fw-extensions-list .fw-extensions-list-item .fw-extensions-list-item-thumbnail-wrapper span.fw-extensions-list-item-thumbnail {
	display: inline;
	vertical-align: middle;
	color: #fff;
	line-height: 128px;
	font-size: 42px;
}

.fw-extensions-list .fw-extensions-list-item .fw-extensions-list-item-thumbnail-wrapper span.fw-extensions-list-item-thumbnail.dashicons {
	font-size: 48px;
}

.fw-extensions-list .fw-extensions-list-item .fw-extensions-list-item-title {
	margin-top: 0;
}

.fw-extensions-list .fw-extensions-list-item .fw-extensions-list-item-title:last-child {
	margin-bottom: 0;
}


.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table {
	display: table;
	width: 100%;
}

.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row {
	display: table-row;
}

.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell {
	display: table-cell;
	vertical-align: top;
}

/*.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-2,
.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 {
	vertical-align: middle;
}*/

@media (max-width: 782px) {
	.fw-extensions-list .fw-extensions-list-item {
		padding-right: 0;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-text-center {
		text-align: left;
	}

	body.rtl .fw-extensions-list .fw-extensions-list-item .fw-text-center {
		text-align: right;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extensions-list-item-title {
		margin-top: 20px;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table,
	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row,
	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell {
		display: block;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 form,
	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 form .button {
		margin-bottom: 0;
	}
}

@media (min-width: 783px) {
	.fw-extensions-list .fw-extensions-list-item .fw-extensions-list-item-title {
		margin-top: 5px;
	}

	hr.fw-extensions-lists-separator {
		margin: 22px 0 30px;
		margin-right: 15px; /* same as .fw-extensions-list .fw-extensions-list-item padding-right */
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell:first-child,
	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell:last-child {
		width: 10px;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell:not(:first-child):not(:last-child) {
		padding-left: 20px;
	}
	body.rtl .fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell:not(:first-child):not(:last-child) {
		padding-left: 0;
		padding-right: 20px;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell > p:last-child {
		margin-bottom: 0;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 {
		display: block;
		position: absolute;
		top: 20px;
		right: 20px;
		width: auto;
	}
	body.rtl .fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 {
		right: auto;
		left: 20px;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 form {
		display: inline-block;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 form.extension-delete-form {
		padding: 0 0 4px 15px;
		vertical-align: bottom;
	}
	body.rtl .fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 form.extension-delete-form {
		padding: 0 13px 4px 0;
	}

	.fw-extensions-list .fw-extensions-list-item .fw-extension-list-item-table > .fw-extension-list-item-table-row > .fw-extension-list-item-table-cell.cell-3 form.extension-delete-form .btn-icon {
		font-size: 16px;
	}
}


/* disabled style */

.fw-extensions-list .fw-extensions-list-item .fw-extension-disabled {
	display: none;
	background: rgba(255, 255, 255, 0.5) url("img/disabled-bg.png");
	border: 1px solid #ffffff;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.fw-extensions-list .fw-extensions-list-item.disabled .fw-extension-disabled {
	display: block;
}

.fw-extensions-list .fw-extensions-list-item .fw-extension-disabled .fw-extension-disabled-panel {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #ffffff;
	padding: 20px;
	line-height: 28px;
}

@media (max-width: 782px) {
	.fw-extensions-list .fw-extensions-list-item.disabled > .inner {
		min-height: 320px;
	}
}

/* end: disabled style */


/* tip content */

.fw-extension-tip-content {
	padding: 15px;
	max-width: 380px;
}

.fw-extension-tip-content ul.fw-extension-requirements {
	margin: 0;
}

.fw-extension-tip-content ul.fw-extension-requirements li:last-child {
	margin-bottom: 0;
}

/* end: tip content */


/* form ajax loading */

.fw-extensions-list .fw-extensions-list-item .ajax-form-loading {
	display: inline-block;
	margin: -2px 0;
	padding-left: 7px;
}

.fw-extensions-list .fw-extensions-list-item .ajax-form-loading img {
	vertical-align: middle;
	display: inline-block;
	margin-top: -2px;
}

/* end: form ajax loading */


/* anchor */

.fw-extensions-list .fw-extensions-list-item a.fw-ext-anchor {
	position: absolute;
	top: -15px;
}

body.admin-bar .fw-extensions-list .fw-extensions-list-item a.fw-ext-anchor {
	top: -50px;
}

/* end: anchor */