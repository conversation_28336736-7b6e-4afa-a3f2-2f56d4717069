<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

$audio_url      = axil_get_acf_data("axil_upload_audio");
$axil_options   = Helper::axil_get_options();
$thumb_size     = 'axil-blog-grid';
$readmore       = $axil_options['read_more_btn_txt'];
?> 
<div  id="post-<?php the_ID(); ?>" <?php post_class('content-blog'); ?>>
     <div class="inner">  
         <?php if(has_post_thumbnail()){ ?>
            <div class="post-thumbnail thumbnail">
                <a href="<?php the_permalink(); ?>">
                    <?php the_post_thumbnail($thumb_size) ?>
                </a> 
                <?php Helper::axil_post_category_meta(); ?> 
            </div>
        <?php } ?>     
        <div class="content">
              <?php if( $audio_url ): ?>
                <div class="audio-wrp mb--20">
                    <audio class="w-100" controls>
                        <source src="<?php echo esc_url($audio_url['url']); ?>" type="audio/ogg">
                        <source src="<?php echo esc_url($audio_url['url']); ?>" type="audio/mpeg">
                        <?php esc_html_e('Your browser does not support the audio tag.', 'etrade'); ?>
                    </audio>
                </div>
            <?php endif; ?>  
             <h5 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h5>
            <?php if( !empty($readmore)  ){ ?>
                <div class="read-more-btn">
                    <a class="axil-btn right-icon" href="<?php the_permalink(); ?>"><?php echo esc_html($readmore); ?> <i class="fal fa-long-arrow-right"></i></a>
                </div>
              <?php } ?>  
        </div>
    </div>
</div>
