<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base; 
use Elementor\Controls_Manager; 
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;
 
if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Poster_banner extends Widget_Base {

 public function get_name() {
        return 'axil-poster-banner';
    }    
    public function get_title() {
        return esc_html__( 'Poster Banner', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-image-box';
    }
    public function get_categories() {
        return [ ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' ];
    }
  public function axil_get_img($img)
     {
         $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
         return $img;
    }
  
    protected function register_controls() {
              
        $this->start_controls_section(
            'services_layout',
            [
                'label' => esc_html__( 'General Content', 'etrade-elements' ),
            ]
        );
        $this->add_control(
	        'style',
	        [
	            'label' => esc_html__( 'Layout', 'etrade-elements' ),
	            'type' => Controls_Manager::SELECT,
	            'default' => 'style-1',
	            'options' => [
	                'style-1' => esc_html__( 'Style 1', 'etrade-elements' ),
					'style-2' => esc_html__( 'Style 2', 'etrade-elements' ), 
					'style-3' => esc_html__( 'Style 3', 'etrade-elements' ), 
					'style-4' => esc_html__( 'Style 4', 'etrade-elements' ), 
					'style-5' => esc_html__( 'Style 5', 'etrade-elements' ), 
					'style-6' => esc_html__( 'Style 6', 'etrade-elements' ),	 
					'style-7' => esc_html__( 'Style 7', 'etrade-elements' ),			                 
						                 
	            ],
	        ] 
	    );  

		$this->add_control(
		    'title',
		    [
		        'label' => esc_html__( 'Title', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXT,
		        'default' => esc_html__( 'Order Pickup', 'etrade-elements' ),
		        'label_block'   => true,
		        'placeholder' => esc_html__( 'Title', 'etrade-elements' ),
		    ]
		);
		$this->add_control(
		    'subtitle',
		    [
		        'label' => esc_html__( 'Before / Sub Title', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXTAREA,
		        'default' => esc_html__( 'Choose Order Pickup & we’ll have it waiting for you inside the store.', 'etrade-elements' ),
		        
		        'placeholder' => esc_html__( 'Sub title', 'etrade-elements' ),
		    ]
		);
		$this->add_control(
		    'badge',
		    [
		        'label' => esc_html__( 'badge', 'etrade-elements' ),
		        'type' => Controls_Manager::TEXT,
		        'label_block'   => true,
		          'condition'   => array( 'style' => array('style-1','style-2') ),  
		        'default' => esc_html__( 'Always free', 'etrade-elements' ),
		        'placeholder' => esc_html__( 'Badge Here', 'etrade-elements' ),
		    ]
		);
			   

    	$this->add_control(
		    'image',
		    [
		        'label' => esc_html__('Image','etrade-elements'),
		        'type'=>Controls_Manager::MEDIA,			        
		        'default' => [
		           'url' =>  $this->axil_get_img( 'delivery_1.png' ),
		        ],
		        'dynamic' => [
		            'active' => true,
		        ],
		            
		    ]
		);
		$this->add_group_control(
		    Group_Control_Image_Size::get_type(),
		    [
		        'name' => 'image_size',
		        'default'  => 'full',
		        'separator' => 'none',	
		        	         
		    ]
		);		
       

		$this->add_control(
		    'posterurl',
		    [
		        'label'   => esc_html__( 'Detail URL', 'etrade-elements' ),
		        'type'    => Controls_Manager::URL,
		        'placeholder' => 'https://your-link.com',		       
		    ]
		);   
		$this->end_controls_section();


        $this->start_controls_section(
            'sec_style_title',
            [
                'label' => esc_html__( 'Title', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,   
            ]
        );

		$this->add_control(
            'title_color',
            [
                'label' => esc_html__( 'Title Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,    
				'selectors' => array( 
					'{{WRAPPER}} .title' => 'color: {{VALUE}} !important',
					
				),                                  
                
            ]
        );

		$this->add_group_control(
		Group_Control_Typography::get_type(),
			[
			    'name' => 'title_typo',
			    'label' => esc_html__( 'Title', 'etrade-elements' ),  
			    'devices' => [ 'desktop', 'tablet', 'mobile' ],	
			    'selector' => '{{WRAPPER}} .title',
			]
		);

		$this->add_responsive_control(
			'title_typo_margin',
			[
				'label' => esc_html__( 'Title Margin', 'etrade-elements' ),
				'type' => Controls_Manager::DIMENSIONS,
				'size_units' => [ 'px', '%', 'em' ],
				'devices' => [ 'desktop', 'tablet', 'mobile' ],					    
				'selectors' => [
					'{{WRAPPER}} .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
					
				],
			]
		); 	
		
       	$this->end_controls_section();   

        $this->start_controls_section(
            'sec_style_subtitle',
            [
                'label' => esc_html__( 'Subtitle', 'etrade-elements' ),
                'tab' => Controls_Manager::TAB_STYLE,   
            ]
        );

		$this->add_control(
            'subtitle_color',
            [
                'label' => esc_html__( 'Subtitle Color', 'etrade-elements' ),
                'type' => Controls_Manager::COLOR,   
				'selectors' => array(  
					'{{WRAPPER}}  .sub-title' => 'color: {{VALUE}}' , 					
					),                                
             
            ]
        );	

		$this->add_group_control(
		Group_Control_Typography::get_type(),
			[
			    'name' => 'subtitle_typo',
			    'label' => esc_html__( 'Subtitle Typography', 'etrade-elements' ),  
			    'devices' => [ 'desktop', 'tablet', 'mobile' ],	
			    'label'    => esc_html__( 'Subtitle', 'etrade-elements' ),
				'selector' => '{{WRAPPER}} .sub-title',
			]
		);

		$this->add_responsive_control(
			'subtitle_typo_margin',
				[
				    'label' => esc_html__( 'Subtitle Margin', 'etrade-elements' ),
				    'type' => Controls_Manager::DIMENSIONS,
				    'size_units' => [ 'px', '%', 'em' ],
				    'devices' => [ 'desktop', 'tablet', 'mobile' ],					    
				    'selectors' => [
				        '{{WRAPPER}} .sub-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				        
				        
				    ],
				]
			);

		$this->end_controls_section();   

    }

	protected function render() {
		$settings = $this->get_settings();
		$attr = '';
		if ( !empty( $settings['posterurl']['url'] ) ) {
			$attr  = 'href="' . $settings['posterurl']['url'] . '"';
			$attr .= !empty( $settings['posterurl']['is_external'] ) ? ' target="_blank"' : '';
			$attr .= !empty( $settings['posterurl']['nofollow'] ) ? ' rel="nofollow"' : '';
		}
		$wrapper_start = '<div class="es-item">';
		$wrapper_end   = '</div>';

		if ( $settings['posterurl']['url'] ) {
			$wrapper_start = '<a ' . $attr . '>';
			$wrapper_end   = '</a>';
		}
		$allowed_tags = wp_kses_allowed_html( 'post' ); 
		if ( $settings['style'] == 'style-1' ) {	
		?> 
	 	<?php echo wp_kses_post( $wrapper_start);?>
			<div class="delivery-poster pickup">
			    <div class="content">
					<?php if ( $settings['badge'] ): ?>
						<span class="badge"><?php echo esc_html( $settings['badge'] );?></span>
					<?php endif; ?>	
					<?php if ( $settings['subtitle'] ): ?>
						<h4 class="title"><?php echo wp_kses( $settings['title'], $allowed_tags );?></h4>
					<?php endif; ?>	  
					<?php if ( $settings['subtitle'] ): ?>
						<p class="sub-title"><?php echo wp_kses( $settings['subtitle'], $allowed_tags );?></p>
					<?php endif; ?>	  
			    </div>
			    <div class="thumbnail">
			      <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>	
			    </div>
			</div> 
		<?php echo wp_kses_post( $wrapper_end );?>
 
		<?php }elseif(  $settings['style'] == 'style-2' ){ ?>
			
		<?php echo wp_kses_post( $wrapper_start);?>
			<div class="delivery-poster delivery">
			    <div class="content">
					<?php if ( $settings['badge'] ): ?>
						<span class="badge"><?php echo esc_html( $settings['badge'] );?></span>
					<?php endif; ?>	
					<?php if ( $settings['subtitle'] ): ?>
						<h4 class="title"><?php echo wp_kses( $settings['title'], $allowed_tags );?></h4>
					<?php endif; ?>	  
					<?php if ( $settings['subtitle'] ): ?>
						<p class="sub-title"><?php echo wp_kses( $settings['subtitle'], $allowed_tags );?></p>
					<?php endif; ?>	  
			    </div>
			    <div class="thumbnail">
			      <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>	
			    </div>
			</div> 
		<?php echo wp_kses_post( $wrapper_end );?> 

		<?php }elseif(  $settings['style'] == 'style-3' ){ ?>
		<div class="single-poster poster-style-two mb--0">
		   <?php echo wp_kses_post( $wrapper_start);?>
		       <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>	
		        <div class="poster-content">
		            <div class="inner">
		                <h2 class="title h2 heading-color <?php echo wp_kses_post( $settings['style'] );?>"><?php echo wp_kses_post( $settings['title'] );?></h2>
		                <?php if ( $settings['subtitle'] ): ?>
		                	<span class="info-subtitle sub-title primary-color"><?php echo wp_kses_post( $settings['subtitle'] );?></span>
		                <?php endif; ?>	

		            </div>
		        </div>   
		     <?php echo wp_kses_post( $wrapper_end );?>
		</div>			
		<?php }elseif(  $settings['style'] == 'style-4' ){ ?>
			<div class="single-poster poster-style-two mb--0">
			   <?php echo wp_kses_post( $wrapper_start);?>
			       <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>	
			        <div class="poster-content content-bottom-left">
			            <div class="inner">
			                <h2 class="title h3  <?php echo wp_kses_post( $settings['style'] );?>"><?php echo wp_kses_post( $settings['title'] );?></h2>
			                <?php if ( $settings['subtitle'] ): ?>
			                	<span class="info-subtitle sub-title "><?php echo wp_kses_post( $settings['subtitle'] );?></span>
			                <?php endif; ?>	
			            </div>
			        </div>   
			     <?php echo wp_kses_post( $wrapper_end );?>
			</div>			
		<?php }elseif(  $settings['style'] == 'style-5' ){ ?>
			<div class="single-poster mb--0">
			   <?php echo wp_kses_post( $wrapper_start);?>
			       <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>	
			        <div class="poster-content">
			            <div class="inner">
			                <h2 class="title h3  <?php echo wp_kses_post( $settings['style'] );?>"><?php echo wp_kses_post( $settings['title'] );?></h2>
			                <?php if ( $settings['subtitle'] ): ?>
			                	<span class="info-subtitle sub-title "><?php echo wp_kses_post( $settings['subtitle'] );?></span>
			                <?php endif; ?>	
			            </div>
			        </div>   
			     <?php echo wp_kses_post( $wrapper_end );?>
			</div>		
		<?php }elseif(  $settings['style'] == 'style-6' ){ ?>

		<div class="single-poster">
		   <?php echo wp_kses_post( $wrapper_start);?>
		       <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>	
		        <div class="poster-content">
		            <div class="inner">
		                <h3 class="title <?php echo wp_kses_post( $settings['style'] );?>"><?php echo wp_kses_post( $settings['title'] );?></h3>
		                <?php if ( $settings['subtitle'] ): ?>
		                	<span class="sub-title info-subtitle">
		                		<?php echo wp_kses_post( $settings['subtitle'] );?> 
		                		<i class="fal fa-long-arrow-right"></i>
		                	</span> 
		                <?php endif; ?>	

		            </div>
		        </div>   
		     <?php echo wp_kses_post( $wrapper_end );?>
		</div> 
		<?php }elseif(  $settings['style'] == 'style-7' ){ ?>
		<div class="single-poster  <?php echo esc_attr( $settings['style'] );?>">
		   <?php echo wp_kses_post( $wrapper_start);?>
		       <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>	
		        <div class="poster-content content-left">
		            <div class="inner">
						<?php if ( $settings['subtitle'] ): ?>
		                	<span class="sub-title info-subtitle"><?php echo wp_kses_post( $settings['subtitle'] );?></span>
		                <?php endif; ?>	 
		                <h3 class="title"><?php echo wp_kses_post( $settings['title'] );?></h3> 

		            </div>
		        </div>   
		     <?php echo wp_kses_post( $wrapper_end );?>
		</div> 
		<?php }else{ ?>
			<div class="single-poster poster-style-two mb--0">
			   <?php echo wp_kses_post( $wrapper_start);?>
			       <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?>	
			        <div class="poster-content">
			            <div class="inner">
			                <h2 class="title h3  <?php echo wp_kses_post( $settings['style'] );?>"><?php echo wp_kses_post( $settings['title'] );?></h2>
			                <?php if ( $settings['subtitle'] ): ?>
			                	<span class="info-subtitle sub-title "><?php echo wp_kses_post( $settings['subtitle'] );?></span>
			                <?php endif; ?>	
			            </div>
			        </div>   
			     <?php echo wp_kses_post( $wrapper_end );?>
			</div>			
		<?php 	
		}
	}
}

