<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
$thumb_size = array( 158, 155 );
$query = $settings['query'];
$shop_permalink = get_permalink( wc_get_page_id( 'shop' ) );
?>
<?php if ( $query->have_posts() ) :?>		
	<div class="main-slider-style-2">
	<?php  
	while ( $query->have_posts() ) : $query->the_post();?>
		<?php 
		$id = get_the_ID();
		$product = wc_get_product( $id );
		$price = $product->get_price_html();
		$title = get_the_title();	

		if ( $settings['sale_price_only'] ) {
			$price_html = wc_price( wc_get_price_to_display( $product ) ) . $product->get_price_suffix();
		}
		else {
			$price_html = $product->get_price_html();
		}		
		$size 				= 'full';
		$img 				= wp_get_attachment_image( $settings['image']['id'], $size );
		?>
		
		<div class="slider-product-box">
		    <div class="product-thumb">
		        <a href="<?php the_permalink();?>">
		             <?php echo wp_kses_post($img);?>
		        </a>
		    </div>
		    <h6 class="title"><a href="<?php the_permalink();?>"><?php echo wp_kses_post( $title ); ?></a></h6>
		    <span class="price"><?php echo wp_kses_post( $price_html ); ?></span>
		</div>
		
	<?php endwhile;?>	
	</div>
	<?php else:?>
		<?php esc_html_e( 'No products available', 'etrade-elements' ); ?></div>
	<?php endif;?>   
  <?php wp_reset_postdata();?>