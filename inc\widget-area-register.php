<?php
/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */

if (!function_exists('axil_widgets_init')) {
    function axil_widgets_init()
    {

        register_sidebar(array(
            'name' => esc_html__('Sidebar', 'etrade'),
            'id' => 'sidebar-1',
            'description' => esc_html__('Add widgets here.', 'etrade'),
            'before_widget' => '<div class="%1$s axil-single-widget %2$s mt--40">',
            'after_widget' => '</div>',
            'before_title' => '<h6 class="widget-title">',
            'after_title' => '</h6>',
        ));
        register_sidebar(array(
            'name' => esc_html__('Shop Header', 'etrade'),
            'id' => 'widgets-shop-header',
            'before_widget' => '<div id="%1$s" class="widget %2$s">',
            'after_widget' => '</div></div>',
            'before_title' => '<h3 class="d-none axil-widget-title widget-title">',
            'after_title' => '</h3><div class="axil-shop-widget-content">',
        ));

        register_sidebar(array(
            'name' => esc_html__('Shop', 'etrade'),
            'id' => 'widgets-shop',
            'before_widget' => '<div id="%1$s" class="toggle-list product-categories widget %2$s">',
            'after_widget' => '</div></div>',
            'before_title' => '<h6 class="title">',
            'after_title' => '</h6><div class="shop-submenu">',
        ));

        register_sidebar(array(
            'name' => esc_html__('Footer Subscribe', 'etrade'),
            'id' => 'footer-mailchimp',
            'before_widget' => '<div id="%1$s" class="axil-footer-widget %2$s">',
            'after_widget' => '</div>',
            'before_title' => '<h6 class="title">',
            'after_title' => '</h6>',
        ));

        $number_of_widget = 5;
        $axil_widget_titles = array(
            '1' => esc_html__('Footer 1', 'etrade'),
            '2' => esc_html__('Footer 2', 'etrade'),
            '3' => esc_html__('Footer 3', 'etrade'),
            '4' => esc_html__('Footer 4', 'etrade'),
            '5' => esc_html__('Footer 5', 'etrade'),

        );
        for ($i = 1; $i <= $number_of_widget; $i++) {
            register_sidebar(array(
                'name' => $axil_widget_titles[$i],
                'id' => 'footer-' . $i,
                'before_widget' => '<div id="%1$s" class="axil-footer-widget %2$s">',
                'after_widget' => '</div></div>',
                'before_title' => '<h6 class="widget-title">',
                'after_title' => '</h6><div class="inner">',
            ));
        }
    }
}
add_action('widgets_init', 'axil_widgets_init');