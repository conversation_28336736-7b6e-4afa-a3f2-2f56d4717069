msgid ""
msgstr ""
"Project-Id-Version: eTrade\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-10 07:10+0000\n"
"PO-Revision-Date: 2023-10-10 08:19+0000\n"
"Last-Translator: \n"
"Language-Team: বাংলা\n"
"Language: bn_BD\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.6; wp-6.3.1\n"
"X-Domain: etrade"

#: inc/global-functions.php:499
msgid " GET OFFER"
msgstr ""

#: inc/global-functions.php:23
msgid " min read"
msgstr ""

#: inc/global-functions.php:444
msgid " Not a member?"
msgstr ""

#: inc/global-functions.php:445
msgid " Sign Out"
msgstr "সাইন আউট"

#: inc/global-functions.php:437
msgid " Sign Up Now"
msgstr ""

#: inc/helper/meta-trait.php:121
msgid "% Comments"
msgstr ""

#: inc/comment-form.php:41 inc/comment-form.php:110
#, php-format
msgid "%1$s at %2$s"
msgstr ""

#. 1: formatted order total 2: total order items
#: woocommerce/myaccount/my-orders.php:77 woocommerce/myaccount/orders.php:62
#, php-format
msgid "%1$s for %2$s item"
msgid_plural "%1$s for %2$s items"
msgstr[0] ""
msgstr[1] ""

#. 1: reviews count 2: product name
#: woocommerce/single-product-reviews.php:38
#, php-format
msgid "%1$s review for %2$s"
msgid_plural "%1$s reviews for %2$s"
msgstr[0] ""
msgstr[1] ""

#. %s: rating
#: woocommerce/custom/functions.php:1013 woocommerce/custom/functions.php:1029
#, php-format
msgid "%s"
msgstr ""

#: woocommerce/single-product/rating.php:39
#, php-format
msgid "%s customer review"
msgid_plural "%s customer reviews"
msgstr[0] ""
msgstr[1] ""

#. %s: stock amount
#: woocommerce/custom/functions.php:853
#, php-format
msgid "%s in stock"
msgstr ""

#. %s: Quantity.
#: woocommerce/global/quantity-input.php:31
#, php-format
msgid "%s quantity"
msgstr ""

#: woocommerce/custom/functions.php:858
msgid "(can be backordered)"
msgstr ""

#. %s location.
#: woocommerce/cart/cart-totals.php:73
#, php-format
msgid "(estimated for %s)"
msgstr ""

#: woocommerce/loop/rating4.php:38
msgid "+"
msgstr ""

#. used between list items, there is a space after the comma
#: inc/underscore/template-tags.php:62
msgid ", "
msgstr ""

#: inc/global-functions.php:55
msgid "--- Select ---"
msgstr ""

#: inc/options/theme/option-framework.php:732
#: inc/options/theme/option-framework.php:745
#: inc/options/theme/option-framework.php:758
msgid "1 Col"
msgstr ""

#: inc/helper/meta-trait.php:121
msgid "1 Comment"
msgstr ""

#: inc/options/theme/option-framework.php:733
#: inc/options/theme/option-framework.php:746
#: inc/options/theme/option-framework.php:759
msgid "2 Col"
msgstr ""

#: inc/options/theme/option-framework.php:1656
msgid "24 Hour Return Policy"
msgstr ""

#: template-parts/header/header-right/header-search.php:43
msgid "24 Result Found"
msgstr ""

#: inc/options/theme/option-framework.php:1689
msgid "24/7 Live support."
msgstr ""

#: inc/options/theme/option-framework.php:734
#: inc/options/theme/option-framework.php:747
#: inc/options/theme/option-framework.php:760
msgid "3 Col"
msgstr ""

#: inc/options/theme/option-framework.php:735
#: inc/options/theme/option-framework.php:748
#: inc/options/theme/option-framework.php:761
msgid "4 Col"
msgstr ""

#: 404.php:44 inc/options/theme/option-framework.php:2010
msgid "404 Image"
msgstr ""

#: inc/options/theme/option-framework.php:1960
msgid "404 Page"
msgstr ""

#: inc/options/theme/option-framework.php:736
#: inc/options/theme/option-framework.php:749
#: inc/options/theme/option-framework.php:762
msgid "6 Col"
msgstr ""

#: woocommerce/myaccount/form-login.php:110
msgid "A link to set a new password will be sent to your email address."
msgstr ""

#: woocommerce/myaccount/lost-password-confirmation.php:27
msgid ""
"A password reset email has been sent to the email address on file for your "
"account, but may take several minutes to show up in your inbox. Please wait "
"at least 10 minutes before attempting another reset."
msgstr ""

#: inc/global-functions.php:183
msgid "Activate Ad"
msgstr ""

#: template-parts/header/header-right/header-right-account.php:51
msgid "Active"
msgstr ""

#: inc/global-functions.php:191
msgid "Ad Type"
msgstr ""

#: woocommerce/myaccount/my-address.php:65
msgid "Add"
msgstr ""

#: woocommerce/single-product-reviews.php:71
msgid "Add a review"
msgstr ""

#: inc/options/theme/option-framework.php:1968
msgid "Add a text before title."
msgstr ""

#: inc/options/theme/option-framework.php:513
#: inc/options/theme/option-framework.php:530
#: inc/options/theme/option-framework.php:1568
msgid "Add Shortcode Here"
msgstr ""

#: woocommerce/custom/functions.php:214
msgid "Add To Compare"
msgstr ""

#: woocommerce/custom/template-parts/wishlist-icon.php:13
msgid "Add to Wishlist"
msgstr ""

#: inc/widget-area-register.php:15
msgid "Add widgets here."
msgstr ""

#: inc/options/theme/option-framework.php:1985
msgid "Add your custom subtitle."
msgstr ""

#: inc/options/theme/option-framework.php:1976
msgid "Add your Default title."
msgstr ""

#. date added label: 1 date added.
#: woocommerce/wishlist-view.php:471
#, php-format
msgid "Added on: %s"
msgstr ""

#: woocommerce/custom/template-parts/wishlist-icon.php:14
msgid "Added to Wishlist"
msgstr ""

#: woocommerce/checkout/form-shipping.php:57
#: woocommerce/single-product/tabs/additional-information.php:20
msgid "Additional information"
msgstr ""

#: inc/options/theme/option-framework.php:1110
msgid "Additional Information Tab"
msgstr ""

#: inc/tgm-config.php:34
msgid "Advanced Custom Fields Pro"
msgstr ""

#: woocommerce/custom/functions.php:945
msgid "All"
msgstr ""

#: template-parts/header/header-cat-search.php:12
#: template-parts/header/header-right/header-right.php:13
#: template-parts/header/header-right/header-search.php:11
msgid "All Categories"
msgstr ""

#: inc/customizer/color.php:55
msgid "Allows you to customize some example settings for axil."
msgstr ""

#: inc/global-functions.php:422 inc/global-functions.php:430
msgid "Already a member?"
msgstr ""

#: woocommerce/cart/cart.php:158
msgid "Apply"
msgstr ""

#: woocommerce/checkout/form-coupon.php:45 woocommerce/cart/cart.php:157
msgid "Apply coupon"
msgstr ""

#: inc/options/theme/option-framework.php:1224
msgid "Archive"
msgstr ""

#: template-parts/title/breadcrumb.php:181
#: template-parts/title/breadcrumb.php:185
#: template-parts/title/breadcrumb.php:189
#: template-parts/title/breadcrumb.php:196
#: template-parts/title/breadcrumb.php:200
#: template-parts/title/breadcrumb.php:205
msgid "Archives"
msgstr ""

#: woocommerce/wishlist-view.php:210
msgid "Arrange"
msgstr ""

#: author.php:21
msgid "Articles By This Author"
msgstr ""

#: woocommerce/inc/modules/aska-question.php:27
#: woocommerce/inc/modules/aska-question.php:31
msgid "Ask a Question"
msgstr ""

#: inc/options/theme/option-framework.php:1185
#: inc/options/theme/option-framework.php:1193
msgid "Ask Product Question"
msgstr ""

#: inc/options/theme/option-framework.php:1332
#: inc/options/theme/option-framework.php:1406
msgid "Author"
msgstr ""

#: template-parts/title/breadcrumb.php:216
msgid "Author: "
msgstr ""

#: woocommerce/cart/cart.php:103
msgid "Available on backorder"
msgstr ""

#: woocommerce/single-product-reviews.php:96
#: woocommerce/custom/functions.php:526
msgid "Average"
msgstr ""

#: woocommerce/single-product/product-image-popup.php:76
#: woocommerce/single-product/product-image.php:84
#: woocommerce/single-product/product-image.php:119
#: woocommerce/single-product/product-image.php:154
#: woocommerce/single-product/product-image.php:185
#: woocommerce/single-product/product-image.php:218
msgid "Awaiting product image"
msgstr ""

#: inc/tgm-config.php:20
msgid "Axil Elements"
msgstr ""

#. Author of the theme
msgid "Axilthemes"
msgstr ""

#: inc/tgm-config.php:27
msgid "axilthemes-demo-importer-helper"
msgstr ""

#: woocommerce/custom/functions.php:1101
msgid "Back"
msgstr ""

#: inc/options/theme/option-framework.php:2005
msgid "Back to Home"
msgstr ""

#: inc/options/theme/option-framework.php:1577
msgid "Background"
msgstr ""

#: inc/options/theme/option-framework.php:555
#: inc/options/theme/option-framework.php:609
msgid "Banner"
msgstr ""

#: inc/options/theme/option-framework.php:1250
msgid "Banner Image"
msgstr ""

#: inc/options/theme/option-framework.php:245
msgid "Banner Login Image"
msgstr ""

#: inc/options/theme/option-framework.php:261
msgid "Banner Register Image"
msgstr ""

#: woocommerce/single-product-reviews.php:71
#, php-format
msgid "Be the first to review &ldquo;%s&rdquo;"
msgstr ""

#: inc/options/theme/option-framework.php:1967
msgid "Before Title"
msgstr ""

#: woocommerce/checkout/form-billing.php:24
msgid "Billing &amp; Shipping"
msgstr ""

#: woocommerce/myaccount/form-edit-address.php:20
#: woocommerce/myaccount/my-address.php:27
#: woocommerce/myaccount/my-address.php:36
msgid "Billing address"
msgstr "বিলিং ঠিকানা"

#: woocommerce/checkout/form-billing.php:28
msgid "Billing details"
msgstr ""

#. Name of the template
msgid "Blank fix Template"
msgstr ""

#: template-parts/title/blog-title.php:45
#: inc/options/theme/option-framework.php:1215
msgid "Blog"
msgstr ""

#. Name of the template
msgid "Blog Archive Grid"
msgstr ""

#. Name of the template
msgid "Blog Archive List"
msgstr ""

#: inc/options/theme/option-framework.php:1379
msgid "Blog Details Sidebar"
msgstr ""

#: inc/options/theme/option-framework.php:1245
msgid "Blogs"
msgstr ""

#: inc/customizer/color.php:282
msgid "Body background Color"
msgstr ""

#: inc/customizer/color.php:261
msgid "Body Color"
msgstr ""

#: inc/options/theme/option-framework.php:563
msgid "Breadcrumb"
msgstr ""

#: woocommerce/myaccount/orders.php:100
msgid "Browse products"
msgstr ""

#: inc/options/theme/option-framework.php:1992
msgid "Button"
msgstr ""

#: inc/options/theme/option-framework.php:2003
msgid "Button Text"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:24
msgid "Calculate shipping"
msgstr ""

#: template-parts/footer/footer-1.php:96 template-parts/footer/footer-1.php:97
#: template-parts/footer/footer-1.php:98 template-parts/footer/footer-1.php:99
#: template-parts/footer/footer-1.php:100 template-parts/footer/footer-3.php:70
#: template-parts/footer/footer-3.php:72 template-parts/footer/footer-3.php:74
#: template-parts/footer/footer-3.php:76 template-parts/footer/footer-3.php:78
#: template-parts/footer/footer-2.php:104
#: template-parts/footer/footer-2.php:105
#: template-parts/footer/footer-2.php:106
#: template-parts/footer/footer-2.php:107
#: template-parts/footer/footer-2.php:108
msgid "cart"
msgstr ""

#: header.php:33
msgid "Cart review"
msgstr ""

#: woocommerce/cart/cart-totals.php:25
msgid "Cart totals"
msgstr ""

#: template-parts/header/header-2.php:53
#: template-parts/header/header-cat-search.php:38
#: inc/options/theme/option-framework.php:1009
#: inc/options/theme/option-framework.php:1321
msgid "Categories"
msgstr ""

#: woocommerce/single-product/meta.php:30
msgid "Category"
msgid_plural "Categories"
msgstr[0] ""
msgstr[1] ""

#: functions.php:90
msgid "Category Icon Menu"
msgstr ""

#: woocommerce/single-product/meta.php:55
#: woocommerce/single-product/meta.php:92
msgid "Category:"
msgid_plural "Categories:"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/custom/functions.php:236
msgid "Checkout"
msgstr ""

#: woocommerce/inc/admin/admin-product-attributes.php:111
#: woocommerce/inc/admin/admin-product-attributes.php:210
msgid "Choose an image"
msgstr ""

#: inc/options/theme/option-framework.php:648
#: inc/options/theme/option-framework.php:1262
#: inc/options/theme/option-framework.php:1287
msgid "Choose your favorite blog layout"
msgstr ""

#: inc/options/theme/option-framework.php:1380
msgid "Choose your favorite layout"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:76
msgid "City"
msgstr ""

#: woocommerce/custom/single-functions.php:576
msgid "Clear"
msgstr ""

#: woocommerce/checkout/form-coupon.php:31
msgid "Click here to enter your code"
msgstr ""

#: woocommerce/checkout/form-login.php:28
msgid "Click here to login"
msgstr ""

#: functions.php:46 woocommerce/inc/admin/admin-product-attributes.php:85
#: woocommerce/inc/admin/admin-product-attributes.php:172
msgid "Color"
msgstr ""

#: inc/options/theme/option-framework.php:808
msgid "Color Attributes"
msgstr ""

#: construction.php:34
msgid "Coming Soon"
msgstr ""

#: comments.php:80
msgid "Comments are closed."
msgstr ""

#: inc/helper/meta-trait.php:121
msgid "Comments off"
msgstr ""

#. 1: number of comments, 2: post title
#: comments.php:48
#, php-format
msgctxt "comments title"
msgid "%1$s reply on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s replies on &ldquo;%2$s&rdquo;"
msgstr[0] ""
msgstr[1] ""

#. %s: post title
#: comments.php:44
#, php-format
msgctxt "comments title"
msgid "One reply on &ldquo;%s&rdquo;"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:63
msgid "Confirm new password"
msgstr ""

#: inc/tgm-config.php:52
msgid "Contact Form 7"
msgstr ""

#: inc/tgm-config.php:67
msgid "Contact Form 7 Database Addon – CFDB7"
msgstr ""

#: inc/options/theme/option-framework.php:1244
msgid ""
"Controls the Default title of the page which is displayed on the page title "
"are on the blog page."
msgstr ""

#: inc/options/theme/option-framework.php:621
msgid ""
"Controls the Default title of the page which is displayed on the page title "
"are on the shop page."
msgstr ""

#: inc/options/theme/option-framework.php:1128
#: inc/options/theme/option-framework.php:1136
msgid ""
"Controls the Default title of the page which is displayed on the Verifiy are "
"on the Products page."
msgstr ""

#: inc/options/theme/option-framework.php:177
msgid ""
"Controls the font settings of the site title. (Note: Used when Transparent "
"Header is enabled.)"
msgstr ""

#: inc/options/theme/option-framework.php:139
msgid ""
"Controls the top, right, bottom and left padding of the logo. (Note: Used "
"when Transparent Header is enabled.)"
msgstr ""

#: inc/options/theme/option-framework.php:1761
msgid "Copyright Content"
msgstr ""

#: inc/options/theme/option-framework.php:199
#: inc/options/theme/option-framework.php:1911
msgid "Countdown Days label"
msgstr ""

#: inc/options/theme/option-framework.php:205
#: inc/options/theme/option-framework.php:1919
msgid "Countdown Hour label"
msgstr ""

#: inc/options/theme/option-framework.php:211
#: inc/options/theme/option-framework.php:1927
msgid "Countdown Minute label"
msgstr ""

#: inc/options/theme/option-framework.php:217
#: inc/options/theme/option-framework.php:1934
msgid "Countdown Second label"
msgstr ""

#: woocommerce/checkout/form-coupon.php:41
msgid "Coupon code"
msgstr ""

#: woocommerce/cart/cart.php:153
msgid "Coupon:"
msgstr ""

#: woocommerce/checkout/form-billing.php:53
msgid "Create an account?"
msgstr ""

#: inc/options/theme/option-framework.php:1086
msgid "Cross Sell Products"
msgstr ""

#: inc/options/theme/option-framework.php:497
msgid "Currency / Language"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:55
msgid "Current password (leave blank to leave unchanged)"
msgstr ""

#: inc/global-functions.php:194 inc/global-functions.php:229
msgid "Custom Code"
msgstr ""

#: functions.php:149
msgid "Dark"
msgstr ""

#: woocommerce/myaccount/my-orders.php:15
msgid "Date"
msgstr ""

#: inc/options/theme/option-framework.php:302
#: inc/options/theme/option-framework.php:1943
msgid "Date Option"
msgstr ""

#: woocommerce/checkout/order-receipt.php:30
msgid "Date:"
msgstr ""

#: inc/scripts.php:172 inc/scripts.php:177
msgid "Days"
msgstr ""

#: inc/options/theme/option-framework.php:125
msgid "Default Logo"
msgstr ""

#: inc/options/theme/option-framework.php:1135
msgid "Default NFT Button Text"
msgstr ""

#: inc/options/theme/option-framework.php:1127
msgid "Default NFT Verifiy Title"
msgstr ""

#: inc/options/theme/option-framework.php:620
#: inc/options/theme/option-framework.php:1243
msgid "Default Title"
msgstr ""

#: woocommerce/single-product/tabs/description.php:22
msgid "Description"
msgstr ""

#: inc/options/theme/option-framework.php:1094
msgid "Description Tab"
msgstr ""

#: inc/options/theme/option-framework.php:730
msgid "desktop Product Columns"
msgstr ""

#: inc/options/theme/option-framework.php:1236
msgid "Disable"
msgstr ""

#: inc/options/theme/option-framework.php:460
msgid "Disable Categories Menu"
msgstr ""

#: inc/global-functions.php:185 inc/options/theme/option-framework.php:995
#: inc/options/theme/option-framework.php:1003
#: inc/options/theme/option-framework.php:1774
msgid "Disabled"
msgstr ""

#: inc/options/theme/option-framework.php:809
msgid "Display color for product attributes."
msgstr ""

#: inc/options/theme/option-framework.php:848
msgid "Display Compare"
msgstr ""

#: inc/options/theme/option-framework.php:849
msgid "Display Compare for product."
msgstr ""

#: inc/options/theme/option-framework.php:821
msgid "Display Hover image for product."
msgstr ""

#: inc/options/theme/option-framework.php:858
msgid "Display Icon for product Title."
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:41
msgid "Display name"
msgstr ""

#: inc/options/theme/option-framework.php:798
msgid "Display only sale price"
msgstr ""

#: inc/options/theme/option-framework.php:1772
msgid "Display Payment Title"
msgstr ""

#: inc/options/theme/option-framework.php:1506
msgid "Display post Order"
msgstr ""

#: inc/options/theme/option-framework.php:839
msgid "Display Quickview"
msgstr ""

#: inc/options/theme/option-framework.php:840
msgid "Display Quickview for product."
msgstr ""

#: inc/options/theme/option-framework.php:960
msgid "Display Social Sharing"
msgstr ""

#: inc/options/theme/option-framework.php:857
msgid "Display Title Badge Check"
msgstr ""

#: inc/options/theme/option-framework.php:830
msgid "Display Wishlist"
msgstr ""

#: inc/options/theme/option-framework.php:831
msgid "Display Wishlist for product."
msgstr ""

#. If there are characters in your language that are not supported by Nunito+Sans Sans, translate this to 'off'. Do not translate into your own language.
#: inc/register-custom-fonts.php:12
msgctxt "DM Sans Display font: on or off"
msgid "on"
msgstr ""

#. %s: Name of current post. Only visible to screen readers
#: inc/underscore/template-tags.php:99
#, php-format
msgid "Edit <span class=\"screen-reader-text\">%s</span>"
msgstr ""

#: inc/tgm-config.php:47
msgid "Elementor Page Builder"
msgstr ""

#: woocommerce/single-product-reviews.php:79
msgid "Email"
msgstr ""

#: comments.php:102
msgid "Email "
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:47
#: woocommerce/myaccount/form-login.php:97
msgid "Email address"
msgstr ""

#: inc/options/theme/option-framework.php:1235
msgid "Enable"
msgstr ""

#: inc/options/theme/option-framework.php:186
msgid "Enable Back To Top"
msgstr ""

#: inc/options/theme/option-framework.php:505
msgid "Enable Currency"
msgstr ""

#: inc/options/theme/option-framework.php:293
msgid "Enable Date Counter"
msgstr ""

#: inc/options/theme/option-framework.php:522
msgid "Enable Language"
msgstr ""

#: inc/options/theme/option-framework.php:452
#: inc/options/theme/option-framework.php:461
msgid "Enable or disable header Acount Icon."
msgstr ""

#: inc/options/theme/option-framework.php:443
msgid "Enable or disable header cart Icon."
msgstr ""

#: inc/options/theme/option-framework.php:471
msgid "Enable or disable header search form."
msgstr ""

#: inc/options/theme/option-framework.php:435
msgid "Enable or disable header wishlist Icon."
msgstr ""

#: inc/options/theme/option-framework.php:556
#: inc/options/theme/option-framework.php:610
msgid "Enable or disable the banner area."
msgstr ""

#: inc/options/theme/option-framework.php:1233
msgid "Enable or Disable the blog page title."
msgstr ""

#: inc/options/theme/option-framework.php:564
msgid "Enable or disable the breadcrumb area."
msgstr ""

#: inc/options/theme/option-framework.php:506
msgid "Enable or disable the Currency area."
msgstr ""

#: inc/options/theme/option-framework.php:1541
msgid ""
"Enable or disable the Default footer top area. Also Enable or disable page "
"wise "
msgstr ""

#: inc/options/theme/option-framework.php:1599
msgid ""
"Enable or disable the Default Service Policies area. Also Enable or disable "
"page wise"
msgstr ""

#: inc/options/theme/option-framework.php:1718
msgid "Enable or disable the footer area."
msgstr ""

#: inc/options/theme/option-framework.php:1754
msgid "Enable or disable the footer Menu."
msgstr ""

#: inc/options/theme/option-framework.php:1798
msgid "Enable or disable the footer top area."
msgstr ""

#: inc/options/theme/option-framework.php:1993
msgid "Enable or disable the go to home page button."
msgstr ""

#: inc/options/theme/option-framework.php:376
msgid "Enable or disable the header area."
msgstr ""

#: inc/options/theme/option-framework.php:330
msgid "Enable or disable the Header top area."
msgstr ""

#: inc/options/theme/option-framework.php:523
msgid "Enable or disable the Language area."
msgstr ""

#: inc/options/theme/option-framework.php:286
msgid "Enable or disable the Notification area."
msgstr ""

#: inc/options/theme/option-framework.php:294
msgid "Enable or disable the Notification Date Counter."
msgstr ""

#: inc/options/theme/option-framework.php:354
msgid "Enable or disable the Top Right."
msgstr ""

#: inc/options/theme/option-framework.php:285
msgid "Enable Shop Notification"
msgstr ""

#: inc/options/theme/option-framework.php:383
msgid "Enable Sticky Header "
msgstr ""

#: inc/options/theme/option-framework.php:187
msgid ""
"Enable the back to top button that appears in the bottom right corner of the "
"screen."
msgstr ""

#: inc/options/theme/option-framework.php:384
msgid "Enable to activate the sticky header."
msgstr ""

#: inc/global-functions.php:184 inc/options/theme/option-framework.php:994
#: inc/options/theme/option-framework.php:1002
#: inc/options/theme/option-framework.php:1773
msgid "Enabled"
msgstr ""

#: inc/options/theme/option-framework.php:993
msgid "Enabled Select Attribute"
msgstr ""

#: woocommerce/myaccount/form-reset-password.php:26
msgid "Enter a new password below."
msgstr ""

#: woocommerce/cart/cart.php:154
msgid "Enter coupon code"
msgstr ""

#: inc/options/theme/option-framework.php:1813
msgid "Enter social links to show the icons"
msgstr ""

#: inc/options/theme/option-framework.php:159
msgid ""
"Enter your site title here. (Note: Used when Transparent Header is enabled.)"
msgstr ""

#: template-parts/title/breadcrumb.php:231
msgid "Error 404"
msgstr ""

#. Name of the theme
msgid "eTrade"
msgstr ""

#: inc/customizer/color.php:52
msgid "etrade Colors Options"
msgstr ""

#. Description of the theme
msgid ""
"eTrade is a simple, highly customizable, easy-to-use eCommerce Template for "
"WooCommerce. It has been designed to create highly functional online "
"shopping stores like fashion, cosmetics, business, electronics, food, "
"furniture, jewelry, medical, and retail."
msgstr ""

#: inc/options/theme/option-framework.php:622
msgid "Explore All Products"
msgstr ""

#: inc/options/theme/option-framework.php:1606
msgid "Fast & Secure Delivery"
msgstr ""

#: woocommerce/custom/template-parts/shop-top-default.php:25
msgid "FILTER"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:28
msgid "First name"
msgstr ""

#: inc/options/theme/option-framework.php:1805
msgid "Follow us"
msgstr ""

#: inc/customizer/color.php:576 inc/options/theme/option-framework.php:1710
#: inc/options/theme/option-framework.php:1717
msgid "Footer"
msgstr ""

#: inc/widget-area-register.php:50
msgid "Footer 1"
msgstr ""

#: inc/widget-area-register.php:51
msgid "Footer 2"
msgstr ""

#: inc/widget-area-register.php:52
msgid "Footer 3"
msgstr ""

#: inc/widget-area-register.php:53
msgid "Footer 4"
msgstr ""

#: inc/widget-area-register.php:54
msgid "Footer 5"
msgstr ""

#: inc/customizer/color.php:699
msgid "Footer Background Color"
msgstr ""

#: inc/customizer/color.php:680
msgid "Footer Border Color"
msgstr ""

#: inc/options/theme/option-framework.php:1753
msgid "Footer Bottom Menu"
msgstr ""

#: functions.php:93
msgid "Footer Bottom Menu (No depth supported)"
msgstr ""

#: inc/widget-area-register.php:40
msgid "Footer Subscribe"
msgstr ""

#: inc/options/theme/option-framework.php:1532
#: inc/options/theme/option-framework.php:1540
msgid "Footer Top"
msgstr ""

#: inc/options/theme/option-framework.php:1578
msgid "Footer Top Background"
msgstr ""

#. 1: Orders URL 2: Address URL 3: Account URL.
#: woocommerce/myaccount/dashboard.php:45
#, php-format
msgid ""
"From your account dashboard you can view your <a href=\"%1$s\">recent "
"orders</a>, manage your <a href=\"%2$s\">billing address</a>, and <a "
"href=\"%3$s\">edit your password and account details</a>."
msgstr ""

#. 1: Orders URL 2: Addresses URL 3: Account URL.
#: woocommerce/myaccount/dashboard.php:48
#, php-format
msgid ""
"From your account dashboard you can view your <a href=\"%1$s\">recent "
"orders</a>, manage your <a href=\"%2$s\">shipping and billing addresses</a>, "
"and <a href=\"%3$s\">edit your password and account details</a>."
msgstr ""

#: comments.php:99
msgid "Full Name"
msgstr ""

#: inc/customizer/color.php:61 inc/options/theme/option-framework.php:101
msgid "General"
msgstr ""

#: inc/options/theme/option-framework.php:106
msgid "General Setting"
msgstr ""

#: woocommerce/single-product-reviews.php:95
#: woocommerce/custom/functions.php:525
msgid "Good"
msgstr ""

#: inc/options/theme/option-framework.php:1297
msgid "Grid Layout"
msgstr ""

#: woocommerce/myaccount/my-orders.php:65 woocommerce/myaccount/orders.php:50
msgctxt "hash before order number"
msgid "#"
msgstr ""

#: woocommerce/checkout/form-coupon.php:30
msgid "Have a coupon?"
msgstr ""

#: inc/customizer/color.php:368 inc/options/theme/option-framework.php:368
#: inc/options/theme/option-framework.php:375
msgid "Header"
msgstr ""

#: inc/options/theme/option-framework.php:451
msgid "Header Acount Icon"
msgstr ""

#: inc/options/theme/option-framework.php:442
msgid "Header Cart Icon"
msgstr ""

#: inc/options/theme/option-framework.php:396
#: inc/options/theme/option-framework.php:397
#: inc/options/theme/option-framework.php:1728
#: inc/options/theme/option-framework.php:1729
msgid "Header Layout 1"
msgstr ""

#: inc/options/theme/option-framework.php:401
#: inc/options/theme/option-framework.php:402
#: inc/options/theme/option-framework.php:1733
#: inc/options/theme/option-framework.php:1734
msgid "Header Layout 2"
msgstr ""

#: inc/options/theme/option-framework.php:406
#: inc/options/theme/option-framework.php:407
#: inc/options/theme/option-framework.php:1738
#: inc/options/theme/option-framework.php:1739
msgid "Header Layout 3"
msgstr ""

#: inc/options/theme/option-framework.php:411
#: inc/options/theme/option-framework.php:412
msgid "Header Layout 4"
msgstr ""

#: inc/options/theme/option-framework.php:416
#: inc/options/theme/option-framework.php:417
msgid "Header Layout 5"
msgstr ""

#: inc/options/theme/option-framework.php:421
#: inc/options/theme/option-framework.php:422
msgid "Header Layout 6"
msgstr ""

#: inc/options/theme/option-framework.php:470
msgid "Header Search Icon"
msgstr ""

#: inc/options/theme/option-framework.php:322
#: inc/options/theme/option-framework.php:329
msgid "Header Top"
msgstr ""

#: inc/options/theme/option-framework.php:346
msgid "Header Top Center"
msgstr ""

#: inc/options/theme/option-framework.php:339
msgid "Header Top Left"
msgstr ""

#: functions.php:92
msgid "Header Top Menu (No depth supported)"
msgstr ""

#: inc/options/theme/option-framework.php:353
msgid "Header Top right"
msgstr ""

#: inc/customizer/color.php:566
msgid "Header Top: Background Color"
msgstr ""

#: inc/customizer/color.php:526
msgid "Header Top: Link Color"
msgstr ""

#: inc/customizer/color.php:546
msgid "Header Top: Link Hover + Active Color"
msgstr ""

#: inc/options/theme/option-framework.php:434
msgid "Header wishlist Icon"
msgstr ""

#: inc/customizer/color.php:186
msgid "Heading Color"
msgstr ""

#. 1: user display name 2: logout url
#: woocommerce/myaccount/dashboard.php:35
#, php-format
msgid "Hello %1$s (not %1$s? <a href=\"%2$s\">Log out</a>)"
msgstr ""

#: inc/options/theme/option-framework.php:612
#: inc/options/theme/option-framework.php:791
#: inc/options/theme/option-framework.php:800
#: inc/options/theme/option-framework.php:962
#: inc/options/theme/option-framework.php:1011
#: inc/options/theme/option-framework.php:1019
#: inc/options/theme/option-framework.php:1027
#: inc/options/theme/option-framework.php:1036
#: inc/options/theme/option-framework.php:1045
#: inc/options/theme/option-framework.php:1054
#: inc/options/theme/option-framework.php:1080
#: inc/options/theme/option-framework.php:1088
#: inc/options/theme/option-framework.php:1096
#: inc/options/theme/option-framework.php:1104
#: inc/options/theme/option-framework.php:1112
#: inc/options/theme/option-framework.php:1120
#: inc/options/theme/option-framework.php:1146
#: inc/options/theme/option-framework.php:1155
#: inc/options/theme/option-framework.php:1163
#: inc/options/theme/option-framework.php:1171
#: inc/options/theme/option-framework.php:1179
#: inc/options/theme/option-framework.php:1325
#: inc/options/theme/option-framework.php:1336
#: inc/options/theme/option-framework.php:1347
#: inc/options/theme/option-framework.php:1358
#: inc/options/theme/option-framework.php:1410
#: inc/options/theme/option-framework.php:1421
#: inc/options/theme/option-framework.php:1435
#: inc/options/theme/option-framework.php:1544
msgid "Hide"
msgstr ""

#: template-parts/title/breadcrumb.php:23
msgid "Home"
msgstr ""

#: header-login.php:33 header-login.php:47
#: template-parts/header/header-logo/header-logo.php:17
msgid "home"
msgstr ""

#: inc/options/theme/option-framework.php:1469
msgid "Hot News"
msgstr ""

#: inc/scripts.php:173
msgid "Hour"
msgstr ""

#: inc/options/theme/option-framework.php:820
msgid "Hover Image"
msgstr ""

#: inc/scripts.php:178
msgid "Hrs"
msgstr ""

#. URI of the theme
msgid "http://axilthemes.com/themes/etrade/"
msgstr ""

#. Author URI of the theme
msgid "https://themeforest.net/user/axilthemes/portfolio"
msgstr ""

#: functions.php:172
msgid "Huge"
msgstr ""

#: inc/customizer/color.php:493
msgid "Icon Hover Background Color"
msgstr ""

#: functions.php:91
msgid "Icon Menu"
msgstr ""

#: inc/options/theme/option-framework.php:1862
#: inc/options/theme/option-framework.php:1899
msgid "If enable, the frontend shows maintenance / coming soon mode page only."
msgstr ""

#: woocommerce/checkout/form-coupon.php:37
msgid "If you have a coupon code, please apply it below."
msgstr ""

#: woocommerce/checkout/form-login.php:34
msgid ""
"If you have shopped with us before, please enter your details below. If you "
"are a new customer, please proceed to the Billing section."
msgstr ""

#: functions.php:47 inc/global-functions.php:202
#: woocommerce/inc/admin/admin-product-attributes.php:92
#: woocommerce/inc/admin/admin-product-attributes.php:190
msgid "Image"
msgstr ""

#: inc/global-functions.php:193
msgid "Image Link"
msgstr ""

#: inc/options/theme/option-framework.php:1177
msgid "Image Popup zoom"
msgstr ""

#: woocommerce/wishlist-view.php:436 woocommerce/custom/functions.php:821
#: woocommerce/custom/single-functions.php:180
msgid "In Stock"
msgstr ""

#: woocommerce/custom/functions.php:841
msgid "In stock"
msgstr ""

#: inc/options/theme/option-framework.php:1025
msgid "In stock Avaibility"
msgstr ""

#: inc/options/theme/option-framework.php:781
msgid "Infinity Scroll"
msgstr ""

#: inc/options/theme/option-framework.php:1129
msgid "Is this item Authentic?"
msgstr ""

#: inc/options/theme/option-framework.php:1986
msgid ""
"It seems like we dont find what you searched. The page you were looking for "
"doesn't exist, isn't available loading incorrectly."
msgstr ""

#: template-parts/content-none.php:24
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""

#: functions.php:48
msgid "Label"
msgstr ""

#: inc/options/theme/option-framework.php:878
msgid "Label Text"
msgstr ""

#: functions.php:167
msgid "Large"
msgstr ""

#: inc/options/theme/option-framework.php:1511
msgid "Last Modified Posts"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:34
msgid "Last name"
msgstr ""

#. %s: post title
#: inc/underscore/template-tags.php:82
#, php-format
msgid "Leave a Comment <span class=\"screen-reader-text\"> on %s</span>"
msgstr ""

#: comments.php:115
msgid "Leave a Reply"
msgstr ""

#: woocommerce/single-product-reviews.php:72
#, php-format
msgid "Leave a Reply to %s"
msgstr ""

#: inc/options/theme/option-framework.php:651
#: inc/options/theme/option-framework.php:653
#: inc/options/theme/option-framework.php:1265
#: inc/options/theme/option-framework.php:1267
#: inc/options/theme/option-framework.php:1290
#: inc/options/theme/option-framework.php:1383
#: inc/options/theme/option-framework.php:1385
msgid "Left Sidebar"
msgstr ""

#: inc/global-functions.php:209
msgid "Link"
msgstr ""

#: inc/customizer/color.php:639
msgid "Link Color"
msgstr ""

#: inc/customizer/color.php:660
msgid "Link Hover Color"
msgstr ""

#. used between list items, there is a space after the comma
#: inc/underscore/template-tags.php:69
msgctxt "list item separator"
msgid ", "
msgstr ""

#: inc/options/theme/option-framework.php:1292
msgid "List Layout"
msgstr ""

#: inc/loadmore.php:66 inc/options/theme/option-framework.php:780
msgid "Load More"
msgstr ""

#: woocommerce/myaccount/form-login.php:65
msgid "Log in"
msgstr ""

#: inc/comment-form.php:62 inc/comment-form.php:130
msgid "Log in to Reply"
msgstr ""

#: template-parts/header/header-right/header-right-account.php:61
msgid "Login"
msgstr "প্রবেশ করুন"

#: inc/options/theme/option-framework.php:230
msgid "Login / Acount"
msgstr ""

#: inc/options/theme/option-framework.php:238
msgid "Login Title"
msgstr ""

#: inc/options/theme/option-framework.php:138
msgid "Logo Padding"
msgstr ""

#: template-parts/header/header-right/header-right-account.php:68
msgid "Logout"
msgstr "সাইন আউট"

#: inc/options/theme/option-framework.php:1880
msgid ""
"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod "
"tempor incididunt ut labore et dolore magna aliqua."
msgstr ""

#: woocommerce/myaccount/form-login.php:68
msgid "Lost your password?"
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:25
msgid ""
"Lost your password? Please enter your username or email address. You will "
"receive a link to create a new password via email."
msgstr ""

#: inc/tgm-config.php:62
msgid "MailChimp for WordPress"
msgstr ""

#. Name of the template
msgid "Maintenance"
msgstr ""

#: template-parts/header/header-right/header-right-account.php:51
msgid "Menu location"
msgstr ""

#: inc/customizer/color.php:335
msgid "Meta Link Color"
msgstr ""

#: inc/customizer/color.php:355
msgid "Meta Link Hover Color"
msgstr ""

#: inc/customizer/color.php:315
msgid "Meta Text Color"
msgstr ""

#: inc/scripts.php:179
msgid "Min"
msgstr ""

#: inc/scripts.php:174
msgid "Minute"
msgstr ""

#: inc/options/theme/option-framework.php:756
msgid "Mobile Product Columns"
msgstr ""

#: inc/options/theme/option-framework.php:1631
msgid "Money Back Guarantee"
msgstr ""

#: inc/options/theme/option-framework.php:1512
msgid "Most Commented posts"
msgstr ""

#: woocommerce/wishlist-view.php:536
msgid "Move"
msgstr ""

#: woocommerce/wishlist-view.php:567
msgid "Move to another list &rsaquo;"
msgstr ""

#: functions.php:94
#: template-parts/header/header-right/header-right-account.php:71
msgid "My Account"
msgstr "আমার অ্যাকাউন্ট"

#. Name of the template
msgid "My Account Page"
msgstr ""

#: woocommerce/single-product/meta.php:27
#: woocommerce/single-product/meta.php:34
#: woocommerce/single-product/meta.php:49
#: woocommerce/single-product/meta.php:88
msgid "N/A"
msgstr ""

#: woocommerce/single-product-reviews.php:77
msgid "Name"
msgstr ""

#: inc/customizer/color.php:473
msgid "Navigation Background Color"
msgstr ""

#: inc/customizer/color.php:452
msgid "Navigation Icon Hover + Active Color"
msgstr ""

#: inc/customizer/color.php:389
msgid "Navigation Link Color"
msgstr ""

#: inc/customizer/color.php:431
msgid "Navigation Link Hover + Active Color"
msgstr ""

#: inc/customizer/color.php:410
msgid "Navigation Link Icon Color"
msgstr ""

#: woocommerce/myaccount/form-reset-password.php:29
msgid "New password"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:59
msgid "New password (leave blank to leave unchanged)"
msgstr ""

#: inc/options/theme/option-framework.php:1553
msgid "New Product Notifications"
msgstr ""

#: inc/comment-nav.php:18
msgid "Newer Comments"
msgstr ""

#: woocommerce/myaccount/orders.php:94
msgid "Next"
msgstr ""

#: inc/options/theme/option-framework.php:1161
msgid "NFT Categories"
msgstr ""

#: inc/options/theme/option-framework.php:1153
msgid "NFT SKU"
msgstr ""

#: inc/options/theme/option-framework.php:1169
msgid "NFT Tags"
msgstr ""

#: inc/options/theme/option-framework.php:1118
#: inc/options/theme/option-framework.php:1144
msgid "NFTs Enable Verifiy"
msgstr ""

#: inc/options/theme/option-framework.php:190
msgid "No"
msgstr ""

#: template-parts/header/header-right/header-right-account.php:63
#: template-parts/header/header-right/header-right-account.php:70
msgid "No account yet?"
msgstr "এখনও কোন অ্যাকাউন্ট?"

#: inc/helper/meta-trait.php:121
msgid "No Comments"
msgstr ""

#: woocommerce/myaccount/orders.php:101
msgid "No order has been made yet."
msgstr ""

#: woocommerce/wishlist-view.php:641
msgid "No products added to the wishlist"
msgstr ""

#: woocommerce/custom/single-functions.php:561
msgid "No products available"
msgstr ""

#: woocommerce/ajax/cart/mini-cart.php:115
msgid "No products in the cart."
msgstr ""

#: inc/options/theme/option-framework.php:1664
msgid "No question ask."
msgstr ""

#: inc/ajax-search.php:91
msgid "No results"
msgstr ""

#: inc/options/theme/option-framework.php:661
#: inc/options/theme/option-framework.php:663
#: inc/options/theme/option-framework.php:1275
#: inc/options/theme/option-framework.php:1277
#: inc/options/theme/option-framework.php:1393
#: inc/options/theme/option-framework.php:1395
msgid "No Sidebar"
msgstr ""

#: inc/options/theme/option-framework.php:1944
msgid "No validation can be done on this field type"
msgstr ""

#: functions.php:162
msgid "Normal"
msgstr ""

#: inc/global-functions.php:436
msgid "Not a member?"
msgstr ""

#: woocommerce/single-product-reviews.php:97
#: woocommerce/custom/functions.php:527
msgid "Not that bad"
msgstr ""

#: template-parts/content-none.php:13
msgid "Nothing Found"
msgstr ""

#: inc/options/theme/option-framework.php:311
msgid "Notification"
msgstr ""

#: inc/options/theme/option-framework.php:770
msgid "Number of Products Per Page"
msgstr ""

#: inc/options/theme/option-framework.php:779
msgid "Numbered"
msgstr ""

#: inc/options/theme/option-framework.php:1462
msgid "Off"
msgstr ""

#: inc/comment-nav.php:13
msgid "Older Comments"
msgstr ""

#: inc/options/theme/option-framework.php:1461
msgid "On"
msgstr ""

#. %s: stock amount
#: woocommerce/custom/functions.php:848
#, php-format
msgid "Only %s left in stock"
msgstr ""

#: woocommerce/single-product-reviews.php:107
msgid ""
"Only logged in customers who have purchased this product may leave a review."
msgstr ""

#: inc/options/theme/option-framework.php:1969
msgid "Oops! Somthings missing."
msgstr ""

#: inc/global-functions.php:217
msgid "Open Advertisement Tab"
msgstr ""

#: inc/global-functions.php:219
msgid "Open in new tab"
msgstr ""

#: inc/global-functions.php:220
msgid "Open in Same tab"
msgstr ""

#: woocommerce/myaccount/my-orders.php:14
msgid "Order"
msgstr ""

#: woocommerce/checkout/order-receipt.php:26
msgid "Order number:"
msgstr ""

#: inc/options/theme/option-framework.php:1873
msgid "Our new website is on its way"
msgstr ""

#: inc/options/theme/option-framework.php:1561
msgid "Our Newsletter"
msgstr ""

#: woocommerce/custom/functions.php:821
#: woocommerce/custom/single-functions.php:180
msgid "Out of Stock"
msgstr ""

#: woocommerce/wishlist-view.php:436
msgid "Out of stock"
msgstr ""

#: template-parts/title/breadcrumb.php:221
msgid "Page"
msgstr ""

#: inc/options/theme/option-framework.php:1978
msgid "Page not Found"
msgstr ""

#: inc/options/theme/option-framework.php:1872
msgid "Page Title"
msgstr ""

#: inc/options/theme/option-framework.php:570
msgid "Pages Banner Image"
msgstr ""

#: inc/options/theme/option-framework.php:547
msgid "Pages Default Banner"
msgstr ""

#: template-parts/content-page.php:17
#: template-parts/single-post/content-gallery.php:77
#: template-parts/single-post/content.php:61
#: template-parts/single-post/content-audio.php:74
#: template-parts/single-post/content-video.php:69
#: template-parts/single-post/content-quote.php:66
#: template-parts/single-post/content-link.php:65
msgid "Pages:"
msgstr ""

#: inc/options/theme/option-framework.php:777
msgid "Pagination Type"
msgstr ""

#: woocommerce/myaccount/form-login.php:47
#: woocommerce/myaccount/form-login.php:104
msgid "Password"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:52
msgid "Password change"
msgstr ""

#: woocommerce/myaccount/lost-password-confirmation.php:22
msgid "Password reset email has been sent."
msgstr ""

#: inc/options/theme/option-framework.php:1788
msgid "Payment Icons Gallery"
msgstr ""

#: woocommerce/checkout/order-receipt.php:39
msgid "Payment method:"
msgstr ""

#: inc/options/theme/option-framework.php:869
msgid "Percentage"
msgstr ""

#: woocommerce/single-product-reviews.php:94
#: woocommerce/custom/functions.php:524
msgid "Perfect"
msgstr ""

#: searchform.php:16
msgctxt "placeholder"
msgid "Search ..."
msgstr ""

#: woocommerce/checkout/payment.php:34
msgid "Please fill in your details above to see available payment methods."
msgstr ""

#: inc/options/theme/option-framework.php:1047
msgid "Plugin \"YITH WooCommerce compare\" must be enabled to use this feature"
msgstr ""

#: inc/options/theme/option-framework.php:1038
msgid ""
"Plugin \"YITH WooCommerce Wishlist\" must be enabled to use this feature"
msgstr ""

#. %s: post author.
#: inc/underscore/template-tags.php:45
#, php-format
msgctxt "post author"
msgid "by %s"
msgstr ""

#: comments.php:111
msgid "Post Comment"
msgstr ""

#: inc/options/theme/option-framework.php:1306
msgid "Post Content Limit"
msgstr ""

#. %s: post date.
#: inc/underscore/template-tags.php:29
#, php-format
msgctxt "post date"
msgid "Posted on %s"
msgstr ""

#: inc/options/theme/option-framework.php:1492
msgid "Post Query"
msgstr ""

#: inc/options/theme/option-framework.php:1354
msgid "Post View"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:82
msgid "Postcode / ZIP"
msgstr ""

#. 1: list of categories.
#: inc/underscore/template-tags.php:65
#, php-format
msgid "Posted in %1$s"
msgstr ""

#: inc/options/theme/option-framework.php:1497
msgid "Posts by the same Author"
msgstr ""

#: inc/options/theme/option-framework.php:1495
msgid "Posts in the same Categories"
msgstr ""

#: inc/options/theme/option-framework.php:1496
msgid "Posts in the same Tags"
msgstr ""

#: woocommerce/myaccount/orders.php:90
msgid "Previous"
msgstr ""

#: woocommerce/wishlist-view.php:332 woocommerce/cart/cart.php:38
#: woocommerce/cart/cart.php:108
msgid "Price"
msgstr ""

#: functions.php:89 functions.php:129
msgid "Primary"
msgstr ""

#: inc/customizer/color.php:84
msgid "Primary Color"
msgstr ""

#: inc/options/theme/option-framework.php:1681
msgid "Pro Quality Support"
msgstr ""

#: woocommerce/cart/proceed-to-checkout-button.php:26
msgid "Proceed to checkout"
msgstr ""

#: woocommerce/checkout/review-order.php:24 woocommerce/cart/cart.php:70
msgid "Product"
msgstr ""

#: inc/options/theme/option-framework.php:679
msgid "Product Block Style"
msgstr ""

#: inc/options/theme/option-framework.php:682
#: inc/options/theme/option-framework.php:683
#: inc/options/theme/option-framework.php:902
#: inc/options/theme/option-framework.php:903
msgid "Product Layout 1"
msgstr ""

#: inc/options/theme/option-framework.php:687
#: inc/options/theme/option-framework.php:688
#: inc/options/theme/option-framework.php:908
#: inc/options/theme/option-framework.php:909
msgid "Product Layout 2"
msgstr ""

#: inc/options/theme/option-framework.php:692
#: inc/options/theme/option-framework.php:693
#: inc/options/theme/option-framework.php:914
#: inc/options/theme/option-framework.php:915
msgid "Product Layout 3"
msgstr ""

#: inc/options/theme/option-framework.php:697
#: inc/options/theme/option-framework.php:698
#: inc/options/theme/option-framework.php:920
#: inc/options/theme/option-framework.php:921
msgid "Product Layout 4"
msgstr ""

#: inc/options/theme/option-framework.php:702
#: inc/options/theme/option-framework.php:703
#: inc/options/theme/option-framework.php:926
#: inc/options/theme/option-framework.php:927
msgid "Product Layout 5"
msgstr ""

#: inc/options/theme/option-framework.php:707
#: inc/options/theme/option-framework.php:708
#: inc/options/theme/option-framework.php:932
#: inc/options/theme/option-framework.php:933
msgid "Product Layout 6"
msgstr ""

#: inc/options/theme/option-framework.php:712
#: inc/options/theme/option-framework.php:713
#: inc/options/theme/option-framework.php:938
msgid "Product Layout 7"
msgstr ""

#: inc/options/theme/option-framework.php:939
msgid "Product Layout 7 (NFT)"
msgstr ""

#: inc/options/theme/option-framework.php:717
#: inc/options/theme/option-framework.php:718
msgid "Product Layout 8"
msgstr ""

#: woocommerce/wishlist-view.php:106
msgid "Product name"
msgstr ""

#: woocommerce/global/quantity-input.php:47
msgctxt "Product quantity input tooltip"
msgid "Qty"
msgstr ""

#: inc/options/theme/option-framework.php:789
msgid "Product Review Star"
msgstr ""

#: inc/options/theme/option-framework.php:889
msgid "Product Settings"
msgstr ""

#: woocommerce/cart/cart.php:37
msgid "Product Title"
msgstr ""

#: inc/customizer/color.php:207
msgid "Products Title  Color"
msgstr ""

#: inc/customizer/color.php:228
msgid "Products Title Hover Color"
msgstr ""

#: inc/options/theme/option-framework.php:1343
#: inc/options/theme/option-framework.php:1417
msgid "Publish Date"
msgstr ""

#: woocommerce/wishlist-view.php:147 woocommerce/cart/cart.php:39
#: woocommerce/cart/cart.php:114 woocommerce/global/quantity-input.php:31
msgid "Quantity"
msgstr ""

#: inc/options/theme/option-framework.php:1491
msgid "Query Type"
msgstr ""

#: template-parts/header/header-right/header-right-account.php:36
msgid "Quick Link"
msgstr "সরাসরি লিঙ্ক"

#: woocommerce/custom/functions.php:649 woocommerce/custom/functions.php:653
msgid "QuickView"
msgstr ""

#: inc/options/theme/option-framework.php:1510
msgid "Random Posts"
msgstr ""

#: woocommerce/single-product-reviews.php:93
#: woocommerce/custom/functions.php:523
msgid "Rate&hellip;"
msgstr ""

#. 1: rating 2: rating count
#: woocommerce/custom/functions.php:1010 woocommerce/custom/functions.php:1026
#, php-format
msgid "Rated %1$s out of 5 based on %2$s customer rating"
msgid_plural "Rated %1$s out of 5 based on %2$s customer ratings"
msgstr[0] ""
msgstr[1] ""

#. %s: rating
#: woocommerce/custom/functions.php:1051 woocommerce/custom/functions.php:1092
#, php-format
msgid "Rated %s out of 5"
msgstr ""

#: woocommerce/myaccount/form-reset-password.php:33
msgid "Re-enter new password"
msgstr ""

#: inc/options/theme/option-framework.php:1314
msgid "Read More"
msgstr ""

#: inc/options/theme/option-framework.php:1313
msgid "Read More Button Text"
msgstr ""

#: template-parts/content-none.php:18
msgid "Ready to publish your first post? Please create a post."
msgstr ""

#: inc/options/theme/option-framework.php:480
msgid "Recent Items"
msgstr ""

#: woocommerce/myaccount/my-orders.php:38
msgid "Recent orders"
msgstr ""

#: inc/options/theme/option-framework.php:1509
msgid "Recent Posts"
msgstr ""

#: template-parts/header/header-right/header-right.php:53
#: template-parts/header/header-right/header-right-2.php:46
msgid "Recent Product"
msgstr ""

#. If there are characters in your language that are not supported by Nunito+Sans Sans, translate this to 'off'. Do not translate into your own language.
#: inc/scripts.php:151
msgctxt "Red Hat Display font: on or off"
msgid "on"
msgstr ""

#: inc/tgm-config.php:41
msgid "Redux Framework"
msgstr ""

#: woocommerce/myaccount/form-login.php:81
#: woocommerce/myaccount/form-login.php:118
#: woocommerce/myaccount/form-login.php:118
msgid "Register"
msgstr ""

#: template-parts/header/header-right/header-right-account.php:64
msgid "REGISTER HERE."
msgstr "এখানে নিবন্ধন করুন."

#. Name of the template
msgid "Register Page"
msgstr ""

#: inc/options/theme/option-framework.php:254
msgid "Register Page Title"
msgstr ""

#: inc/options/theme/option-framework.php:1475
msgid "Related Post Area Title"
msgstr ""

#: inc/options/theme/option-framework.php:1468
msgid "Related Post Before Title"
msgstr ""

#: inc/options/theme/option-framework.php:1520
msgid "Related Post Title Length"
msgstr ""

#: inc/options/theme/option-framework.php:1476
msgid "Related Posts"
msgstr ""

#: inc/options/theme/option-framework.php:1052
msgid "Related Products"
msgstr ""

#: inc/options/theme/option-framework.php:1062
msgid "Related Products Before Title"
msgstr ""

#: inc/options/theme/option-framework.php:1069
msgid "Related Products Title"
msgstr ""

#: woocommerce/myaccount/form-login.php:56
msgid "Remember me"
msgstr ""

#: woocommerce/wishlist-view.php:599
msgid "Remove"
msgstr ""

#: woocommerce/inc/admin/admin-product-attributes.php:97
#: woocommerce/inc/admin/admin-product-attributes.php:196
msgid "Remove image"
msgstr ""

#: woocommerce/cart/cart.php:61 woocommerce/ajax/cart/mini-cart.php:51
msgid "Remove this item"
msgstr ""

#: woocommerce/wishlist-view.php:254 woocommerce/wishlist-view.php:599
msgid "Remove this product"
msgstr ""

#: inc/comment-form.php:61
msgid "Reply"
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:38
msgid "Reset password"
msgstr ""

#: woocommerce/checkout/cart-errors.php:25
msgid "Return to cart"
msgstr ""

#: woocommerce/ajax/cart/mini-cart.php:116
msgid "Return to shop"
msgstr ""

#: woocommerce/checkout/form-login.php:28
msgid "Returning customer?"
msgstr ""

#: woocommerce/single-product-reviews.php:41 woocommerce/loop/rating4.php:38
msgid "Reviews"
msgstr ""

#: inc/options/theme/option-framework.php:1102
msgid "Reviews Tab"
msgstr ""

#: inc/options/theme/option-framework.php:656
#: inc/options/theme/option-framework.php:658
#: inc/options/theme/option-framework.php:1270
#: inc/options/theme/option-framework.php:1272
#: inc/options/theme/option-framework.php:1295
#: inc/options/theme/option-framework.php:1388
#: inc/options/theme/option-framework.php:1390
msgid "Right Sidebar"
msgstr ""

#: inc/options/theme/option-framework.php:867
msgid "Sale Product Label"
msgstr ""

#: woocommerce/custom/functions.php:483 woocommerce/custom/functions.php:1073
#: woocommerce/loop/sale-flash2.php:27 woocommerce/loop/sale-flash-left.php:27
msgid "Sale!"
msgstr ""

#: woocommerce/myaccount/form-reset-password.php:46
msgid "Save"
msgstr ""

#: woocommerce/myaccount/form-edit-address.php:46
msgid "Save address"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:73
msgid "Save changes"
msgstr ""

#: template-parts/header/header-right/header-search.php:24
#: template-parts/header/header-right/header-search3.php:18
#: template-parts/header/header-right/header-search2.php:11
msgid "Search"
msgstr ""

#: woocommerce/product-searchform.php:25
msgid "Search products ..."
msgstr ""

#: template-parts/title/blog-title.php:39
#: template-parts/title/breadcrumb.php:226
msgid "Search results for: "
msgstr ""

#: inc/ajax-search.php:94
msgid "Search Suggestions"
msgstr ""

#: inc/scripts.php:180
msgid "Sec"
msgstr ""

#: inc/scripts.php:175
msgid "Second"
msgstr ""

#: functions.php:134
msgid "Secondary"
msgstr ""

#: inc/customizer/color.php:130
msgid "Secondary Alt Color"
msgstr ""

#: inc/customizer/color.php:107
msgid "Secondary Color"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:31
msgid "Select a country / region&hellip;"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:56
msgid "Select an option&hellip;"
msgstr ""

#: inc/options/theme/option-framework.php:1286
msgid "Select Blog Layout"
msgstr ""

#: inc/options/theme/option-framework.php:1261
msgid "Select Blog Sidebar"
msgstr ""

#: inc/options/theme/option-framework.php:1725
msgid "Select Footer Layout"
msgstr ""

#: inc/options/theme/option-framework.php:393
msgid "Select Header Layout"
msgstr ""

#: inc/options/theme/option-framework.php:115
msgid "Select Logo Type"
msgstr ""

#: inc/options/theme/option-framework.php:116
msgid ""
"Select logo type, if the image is chosen the existing options of  image "
"below will work, or text option will work. (Note: Used when Transparent "
"Header is enabled.)"
msgstr ""

#: inc/options/theme/option-framework.php:898
msgid "Select Product Layout"
msgstr ""

#: inc/options/theme/option-framework.php:647
msgid "Select Shop Page Sidebar"
msgstr ""

#: inc/options/theme/option-framework.php:671
msgid "Select Shop Siebar"
msgstr ""

#: inc/options/theme/option-framework.php:1591
#: inc/options/theme/option-framework.php:1598
msgid "Service Policies"
msgstr ""

#: inc/options/theme/option-framework.php:1605
msgid "Service Policy 1"
msgstr ""

#: inc/options/theme/option-framework.php:1621
msgid "Service Policy 1 Image"
msgstr ""

#: inc/options/theme/option-framework.php:1630
msgid "Service Policy 2"
msgstr ""

#: inc/options/theme/option-framework.php:1646
msgid "Service Policy 2 Image"
msgstr ""

#: inc/options/theme/option-framework.php:1655
msgid "Service Policy 3"
msgstr ""

#: inc/options/theme/option-framework.php:1671
msgid "Service Policy 3 Image"
msgstr ""

#: inc/options/theme/option-framework.php:1680
msgid "Service Policy 4"
msgstr ""

#: inc/options/theme/option-framework.php:1697
msgid "Service Policy 4 Image"
msgstr ""

#: inc/options/theme/option-framework.php:2004
msgid "Set the custom text of go to home page button."
msgstr ""

#: woocommerce/single-product/meta.php:101
msgid "Share:"
msgstr ""

#: woocommerce/checkout/form-shipping.php:26
msgid "Ship to a different address?"
msgstr ""

#: woocommerce/cart/cart-totals.php:53 woocommerce/cart/cart-totals.php:54
msgid "Shipping"
msgstr ""

#: woocommerce/myaccount/form-edit-address.php:20
#: woocommerce/myaccount/my-address.php:28
msgid "Shipping address"
msgstr "প্রেরণের ঠিকানা"

#: inc/widget-area-register.php:31
msgid "Shop"
msgstr ""

#: inc/helper/layout-trait.php:580
msgid "shop"
msgstr ""

#: template-parts/title/wooc-shop.php:56
#: inc/options/theme/option-framework.php:635
msgid "Shop Banner Image"
msgstr ""

#: inc/options/theme/option-framework.php:628
msgid "Shop Banner Sub Title"
msgstr ""

#: inc/widget-area-register.php:22
msgid "Shop Header"
msgstr ""

#: inc/options/theme/option-framework.php:278
msgid "Shop Notification"
msgstr ""

#: inc/options/theme/option-framework.php:601
msgid "Shop Settings"
msgstr ""

#: inc/options/theme/option-framework.php:611
#: inc/options/theme/option-framework.php:790
#: inc/options/theme/option-framework.php:799
#: inc/options/theme/option-framework.php:961
#: inc/options/theme/option-framework.php:1010
#: inc/options/theme/option-framework.php:1018
#: inc/options/theme/option-framework.php:1026
#: inc/options/theme/option-framework.php:1035
#: inc/options/theme/option-framework.php:1044
#: inc/options/theme/option-framework.php:1053
#: inc/options/theme/option-framework.php:1079
#: inc/options/theme/option-framework.php:1087
#: inc/options/theme/option-framework.php:1095
#: inc/options/theme/option-framework.php:1103
#: inc/options/theme/option-framework.php:1111
#: inc/options/theme/option-framework.php:1119
#: inc/options/theme/option-framework.php:1145
#: inc/options/theme/option-framework.php:1154
#: inc/options/theme/option-framework.php:1162
#: inc/options/theme/option-framework.php:1170
#: inc/options/theme/option-framework.php:1178
#: inc/options/theme/option-framework.php:1324
#: inc/options/theme/option-framework.php:1335
#: inc/options/theme/option-framework.php:1346
#: inc/options/theme/option-framework.php:1357
#: inc/options/theme/option-framework.php:1409
#: inc/options/theme/option-framework.php:1420
#: inc/options/theme/option-framework.php:1434
#: inc/options/theme/option-framework.php:1543
msgid "Show"
msgstr ""

#: inc/options/theme/option-framework.php:1451
msgid "Show Author Info"
msgstr ""

#: inc/options/theme/option-framework.php:1001
msgid "Show excerpt when short description doesn't exist"
msgstr ""

#: woocommerce/inc/admin/admin-product-data.php:51
msgid "Show in product catalog"
msgstr ""

#: inc/options/theme/option-framework.php:1452
msgid "Show or hide the Author Info box of single post."
msgstr ""

#: inc/options/theme/option-framework.php:1322
#: inc/options/theme/option-framework.php:1333
#: inc/options/theme/option-framework.php:1407
msgid "Show or hide the author of blog post."
msgstr ""

#: inc/options/theme/option-framework.php:1355
msgid "Show or hide the post view of blog post."
msgstr ""

#: inc/options/theme/option-framework.php:1344
#: inc/options/theme/option-framework.php:1418
msgid "Show or hide the publish date of blog post."
msgstr ""

#: inc/options/theme/option-framework.php:1444
msgid "Show or hide the social share of single post."
msgstr ""

#: inc/options/theme/option-framework.php:1432
msgid "Show or hide the tags of blog post."
msgstr ""

#: inc/options/theme/option-framework.php:1460
msgid "Show Related post"
msgstr ""

#: inc/options/theme/option-framework.php:1483
msgid "Show Related Post Number"
msgstr ""

#. %d: total results
#: woocommerce/loop/result-count.php:30
msgid "Showing all %d result"
msgid_plural "Showing all %d results"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/loop/result-count.php:27
msgid "Showing the single result"
msgstr ""

#: inc/widget-area-register.php:13 inc/helper/layout-trait.php:579
msgid "Sidebar"
msgstr ""

#: inc/global-functions.php:423 inc/global-functions.php:431
msgid "Sign In"
msgstr ""

#: woocommerce/myaccount/form-login.php:36
msgid "Sign in to"
msgstr ""

#. $1 and $2 opening and closing emphasis tags respectively
#: woocommerce/checkout/payment.php:43
#, php-format
msgid ""
"Since your browser does not support JavaScript, or it is disabled, please "
"ensure you click the %1$sUpdate Totals%2$s button before placing your order. "
"You may be charged more than the amount stated above if you fail to do so."
msgstr ""

#: inc/options/theme/option-framework.php:1371
msgid "Single"
msgstr ""

#: inc/options/theme/option-framework.php:158
msgid "Site Title"
msgstr ""

#: inc/options/theme/option-framework.php:165
msgid "Site Title Font Settings"
msgstr ""

#: woocommerce/single-product/meta.php:49
#: woocommerce/single-product/meta.php:88
msgid "SKU:"
msgstr ""

#: functions.php:157
msgid "Small"
msgstr ""

#: inc/options/theme/option-framework.php:1797
msgid "Social Enable"
msgstr ""

#: inc/options/theme/option-framework.php:1812
msgid "Social Icons"
msgstr ""

#: inc/options/theme/option-framework.php:1443
msgid "Social Link"
msgstr ""

#: inc/options/theme/option-framework.php:969
msgid "Social Sharing Icons"
msgstr ""

#: inc/options/theme/option-framework.php:1804
msgid "Social Title"
msgstr ""

#: template-parts/content-none.php:20
msgid ""
"Sorry, but nothing matched your search terms. Please try again with some "
"different keywords."
msgstr ""

#: woocommerce/checkout/payment.php:34
msgid ""
"Sorry, it seems that there are no available payment methods for your state. "
"Please contact us if you require assistance or wish to make alternate "
"arrangements."
msgstr ""

#: inc/options/theme/option-framework.php:1505
msgid "Sort Order"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:50
#: woocommerce/cart/shipping-calculator.php:55
#: woocommerce/cart/shipping-calculator.php:67
msgid "State / County"
msgstr ""

#: woocommerce/wishlist-view.php:404 woocommerce/myaccount/my-orders.php:16
msgid "Status"
msgstr ""

#: woocommerce/wishlist-view.php:168
msgid "Stock status"
msgstr ""

#: inc/options/theme/option-framework.php:1560
#: inc/options/theme/option-framework.php:1613
#: inc/options/theme/option-framework.php:1638
#: inc/options/theme/option-framework.php:1663
#: inc/options/theme/option-framework.php:1688
#: inc/options/theme/option-framework.php:1879
#: inc/options/theme/option-framework.php:1984
msgid "Sub Title"
msgstr ""

#: woocommerce/single-product-reviews.php:82
msgid "Submit Comment"
msgstr ""

#: woocommerce/checkout/review-order.php:25
#: woocommerce/checkout/review-order.php:57 woocommerce/cart/cart.php:40
#: woocommerce/cart/cart.php:136 woocommerce/cart/cart-totals.php:31
#: woocommerce/cart/cart-totals.php:32
msgid "Subtotal"
msgstr ""

#: inc/global-functions.php:231
msgid "Supports: Shortcode, Adsense, Text, HTML, Scripts"
msgstr ""

#: inc/options/theme/option-framework.php:743
msgid "Tablet Product Columns"
msgstr ""

#: woocommerce/single-product/meta.php:31
msgid "Tag"
msgid_plural "Tags"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/meta.php:59
#: woocommerce/single-product/meta.php:96
msgid "Tag:"
msgid_plural "Tags:"
msgstr[0] ""
msgstr[1] ""

#. 1: list of tags.
#: inc/underscore/template-tags.php:72
#, php-format
msgid "Tagged %1$s"
msgstr ""

#: inc/options/theme/option-framework.php:1017
#: inc/options/theme/option-framework.php:1431
msgid "Tags"
msgstr ""

#: template-parts/single-post/content.php:69
#: template-parts/single-post/content-link.php:73
msgid "Tags:"
msgstr ""

#: inc/options/theme/option-framework.php:1614
msgid "Tell about your service."
msgstr ""

#: functions.php:139
msgid "Tertiary"
msgstr ""

#: inc/customizer/color.php:153
msgid "Tertiary Color"
msgstr ""

#: inc/options/theme/option-framework.php:870
msgid "Text"
msgstr ""

#: inc/customizer/color.php:618
msgid "Text Color"
msgstr ""

#: woocommerce/myaccount/my-address.php:47
msgid "The following addresses will be used on the checkout page by default."
msgstr ""

#: inc/options/theme/option-framework.php:24
#: inc/options/theme/option-framework.php:25
msgid "Theme Options"
msgstr ""

#: woocommerce/single-product-reviews.php:61
msgid "There are no reviews yet."
msgstr ""

#: woocommerce/checkout/cart-errors.php:21
msgid ""
"There are some issues with the items in your cart. Please go back to the "
"cart page and resolve these issues before checking out."
msgstr ""

#: template-parts/header/header-notification-top2.php:20
msgid ""
"This is a demo store for testing purposes &mdash; no orders shall be "
"fulfilled."
msgstr ""

#: inc/options/theme/option-framework.php:1945
msgid "This is the description field, again good for additional info."
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:42
msgid ""
"This will be how your name will be displayed in the account section and in "
"reviews"
msgstr ""

#: inc/options/theme/option-framework.php:1232
#: inc/options/theme/option-framework.php:1552
#: inc/options/theme/option-framework.php:1781
#: inc/options/theme/option-framework.php:1975
msgid "Title"
msgstr ""

#: inc/customizer/color.php:597
msgid "Title Color"
msgstr ""

#: woocommerce/myaccount/my-orders.php:17
#: woocommerce/checkout/review-order.php:104
#: woocommerce/cart/cart-totals.php:99 woocommerce/cart/cart-totals.php:100
msgid "Total"
msgstr ""

#: woocommerce/checkout/order-receipt.php:34
msgid "Total:"
msgstr ""

#: inc/options/theme/option-framework.php:1852
#: inc/options/theme/option-framework.php:1854
msgid "Under Construction"
msgstr ""

#: inc/options/theme/option-framework.php:1861
#: inc/options/theme/option-framework.php:1898
msgid "Under Construction / Coming Soon Mode"
msgstr ""

#: inc/options/theme/option-framework.php:1887
msgid "Under Construction Image"
msgstr ""

#: woocommerce/wishlist-view.php:126
msgid "Unit price"
msgstr ""

#: inc/options/theme/option-framework.php:1078
msgid "Up Sell Products"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:86
msgid "Update"
msgstr ""

#: woocommerce/cart/cart.php:167
msgid "Update cart"
msgstr ""

#: woocommerce/checkout/payment.php:45
msgid "Update totals"
msgstr ""

#: inc/options/theme/option-framework.php:126
msgid ""
"Upload the main logo of your site. ( Recommended size: Width 267px and "
"Height: 70px )"
msgstr ""

#: woocommerce/inc/admin/admin-product-attributes.php:96
#: woocommerce/inc/admin/admin-product-attributes.php:195
msgid "Upload/Add image"
msgstr ""

#: woocommerce/inc/admin/admin-product-attributes.php:112
#: woocommerce/inc/admin/admin-product-attributes.php:211
msgid "Use image"
msgstr ""

#: woocommerce/myaccount/form-login.php:90
msgid "Username"
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:28
msgid "Username or email"
msgstr ""

#: woocommerce/myaccount/form-login.php:43
msgid "Username or email address"
msgstr ""

#: woocommerce/single-product/review-meta.php:30
msgid "Verified Owner"
msgstr ""

#: inc/options/theme/option-framework.php:1137
msgid "Verifiy"
msgstr ""

#: woocommerce/custom/functions.php:528
msgid "Very Poor"
msgstr ""

#: woocommerce/single-product-reviews.php:98
msgid "Very poor"
msgstr ""

#: inc/ajax-search.php:86
#: template-parts/header/header-right/header-right.php:54
#: template-parts/header/header-right/header-right-2.php:47
#: template-parts/header/header-right/header-search.php:44
msgid "View All"
msgstr ""

#: woocommerce/custom/functions.php:229
msgid "View cart"
msgstr ""

#: woocommerce/custom/single-functions.php:70
msgid "Watch Video"
msgstr ""

#: comments.php:104
msgid "Website"
msgstr ""

#: template-parts/header/header-cat-search.php:51
msgid "What are you looking for...."
msgstr ""

#: template-parts/header/header-right/header-right-2.php:37
#: template-parts/header/header-right/header-search3.php:12
msgid "What are you looking for?"
msgstr ""

#: functions.php:144
msgid "White"
msgstr ""

#: template-parts/header/header-right/header-right-wishlist.php:21
msgid "Wish List"
msgstr ""

#: woocommerce/custom/template-parts/wishlist-icon.php:33
#: woocommerce/custom/template-parts/wishlist-icon.php:37
msgid "WishList"
msgstr ""

#: inc/options/theme/option-framework.php:1043
msgid "Wishlist compare"
msgstr ""

#: inc/options/theme/option-framework.php:1034
msgid "Wishlist Icon"
msgstr ""

#. 1: first result 2: last result 3: total results
#: woocommerce/loop/result-count.php:35
#, php-format
msgctxt "with first and last result"
msgid "Showing the single result"
msgid_plural ""
"Showing %1$d&ndash;<span class=\"wc-last-result-count\">%2$d</span> of %3$d "
"results"
msgstr[0] ""
msgstr[1] ""

#: inc/options/theme/option-framework.php:1639
msgid "Within 10 days."
msgstr ""

#: inc/options/theme/option-framework.php:591
msgid "WooCommerce"
msgstr ""

#: inc/options/theme/option-framework.php:943
#: inc/options/theme/option-framework.php:944
msgid "Woocommerce Default Layout"
msgstr ""

#: template-parts/header/header-right/header-right.php:44
#: template-parts/header/header-right/header-search.php:35
msgid "Write Something...."
msgstr ""

#: comments.php:113
msgid "Write your comment here… "
msgstr ""

#: inc/options/theme/option-framework.php:189
msgid "Yes"
msgstr ""

#: woocommerce/myaccount/my-address.php:71
msgid "You have not set up this type of address yet."
msgstr ""

#: woocommerce/single-product/up-sells.php:23
msgid "You may also like&hellip;"
msgstr ""

#: woocommerce/cart/cross-sells.php:26
msgid "You may be interested in&hellip;"
msgstr ""

#: woocommerce/single-product-reviews.php:88
#, php-format
msgid "You must be <a href=\"%s\">logged in</a> to post a review."
msgstr ""

#: woocommerce/checkout/form-checkout.php:34
msgid "You must be logged in to checkout."
msgstr ""

#: template-parts/post/content-audio.php:34
#: template-parts/post/content-grid-audio.php:31
#: template-parts/single-post/content-audio.php:66
msgid "Your browser does not support the audio tag."
msgstr ""

#: woocommerce/custom/functions.php:532
msgid "Your Comment"
msgstr ""

#: inc/comment-form.php:32 inc/comment-form.php:143
msgid "Your comment is awaiting moderation."
msgstr ""

#: woocommerce/checkout/form-checkout.php:58
msgid "Your order"
msgstr ""

#: woocommerce/custom/functions.php:521
msgid "Your Rating"
msgstr ""

#: woocommerce/single-product-reviews.php:92
msgid "Your rating"
msgstr ""

#: woocommerce/single-product-reviews.php:102
msgid "Your review"
msgstr ""

#: woocommerce/single-product/review-meta.php:33
msgid "Your review is awaiting approval"
msgstr ""
