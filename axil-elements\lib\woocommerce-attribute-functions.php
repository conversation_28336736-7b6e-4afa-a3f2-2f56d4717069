<?php

/*
 * WooCommerce - Attribute functions
=============================================================== */

global $wooc_globals;

/*
 * Product attribute: Get properties
 *
 * Note: Code from "get_tax_attribute()" function in the "../variation-swatches-for-woocommerce.php" file of the "Variation Swatches for WooCommerce" plugin
 */
function wooc_woocommerce_get_taxonomy_attribute( $taxonomy ) {
    global $wpdb, $wooc_globals;

    // Returned cached data if available
    if ( isset( $wooc_globals['pa_cache'][$taxonomy] ) ) {
        return $wooc_globals['pa_cache'][$taxonomy];
    }

    $attr = substr( $taxonomy, 3 );
    $attr = $wpdb->get_row( "SELECT * FROM " . $wpdb->prefix . "woocommerce_attribute_taxonomies WHERE attribute_name = '$attr'" );

    // Save data to avoid multiple database calls
    $wooc_globals['pa_cache'][$taxonomy] = $attr;

    return $attr;
}

/*
 *  Widget: Filter Products by Attribute - Include custom elements
 */

function variation_radio_buttons( $html, $args ) {
    $args = wp_parse_args( apply_filters( 'woocommerce_dropdown_variation_attribute_options_args', $args ), array(
        'options'          => false,
        'attribute'        => false,
        'product'          => false,
        'selected'         => false,
        'name'             => '',
        'id'               => '',
        'class'            => '',
        'meta_data'        => array(),
        'show_option_none' => esc_html__( 'Choose an option', 'etrade' ),
    ) );
    global $wooc_globals;
    $attr = wooc_woocommerce_get_taxonomy_attribute( $args['attribute'] );
    $variation_type = ( $attr ) ? $attr->attribute_type : null;

    if ( false === $args['selected'] && $args['attribute'] && $args['product'] instanceof WC_Product ) {
        $selected_key = 'attribute_' . sanitize_title( $args['attribute'] );
        $args['selected'] = isset( $_REQUEST[$selected_key] ) ? wc_clean( wp_unslash( $_REQUEST[$selected_key] ) ) : $args['product']->get_variation_default_attribute( $args['attribute'] );
    }
    $options = $args['options'];
    $product = $args['product'];
    $attribute = $args['attribute'];
    $name = $args['name'] ? $args['name'] : 'attribute_' . sanitize_title( $attribute );
    $id = $args['id'] ? $args['id'] : sanitize_title( $attribute );
    $class = $args['class'];
    $show_option_none = (bool) $args['show_option_none'];
    $show_option_none_text = $args['show_option_none'] ? $args['show_option_none'] : __( 'Choose an option', 'etrade' );

    if ( empty( $options ) && !empty( $product ) && !empty( $attribute ) ) {
        $attributes = $product->get_variation_attributes();
        $options = $attributes[$attribute];
    }

    $radios = '<div class="variation-radios color-variant-wrapper">';

    if ( !empty( $options ) ) {

        if ( $product && taxonomy_exists( $attribute ) ) {

            $terms = wc_get_product_terms( $product->get_id(), $attribute, array(
                'fields' => 'all',
            ) );

            switch ( $variation_type ) {

            // Control type: Color swatch
            case 'color':
                // Save data in global variable to avoid getting the "wooc_pa_colors" option multiple times

                if ( !isset( $wooc_globals['pa_colors'] ) ) {
                    $wooc_globals['pa_colors'] = get_option( 'wooc_pa_colors' );
                }

                $radios .= '<ul class="color-variant mt--0">';
                foreach ( $terms as $term ) {
                    if ( in_array( $term->slug, $options, true ) ) {

                        $color = ( isset( $wooc_globals['pa_colors'][$term->term_id] ) ) ? $wooc_globals['pa_colors'][$term->term_id] : '#ccc';

                        $radios .= '<li class="color-list">

                        <input type="radio" name="' . esc_attr( $name ) . '" value="' . esc_attr( $term->slug ) . '"  id="' . esc_attr( $term->slug ) . '" '
                        . checked( sanitize_title( $args['selected'] ), $term->slug, false ) . '>
                            <label  for="' . esc_attr( $term->slug ) . '">
                                <span><span class="color"  style="background:' . esc_attr( $color ) . ';"></span></span>
                            </label>
                        </li>';
                    }

                }
                $radios .= '</ul>';
                break;
            // Control type: Image swatch
            case 'image':

                $radios .= '<ul class="range-variant image-variant mt--0">';
                foreach ( $terms as $term ) {

                    if ( in_array( $term->slug, $options, true ) ) {

                        $image_id = absint( get_term_meta( $term->term_id, 'wooc_pa_image_thumbnail_id', true ) );
                        $image_url = ( $image_id ) ? wp_get_attachment_url( $image_id ) : '';

                        $radios .= '<li class="v-color-list"><input type="radio" name="' . esc_attr( $name ) . '" value="' . esc_attr( $term->slug ) . '"  id="' . esc_attr( $term->slug ) . '" '
                        . checked( sanitize_title( $args['selected'] ), $term->slug, false ) . '>

                        <label  for="' . esc_attr( $term->slug ) . '"><img src="' . esc_url( $image_url ) . '" class="wooc-pa-image-thumbnail"></label></li>';
                    }
                }
                $radios .= '</ul>';

                break;
            // Control type: Label
            default:
                $radios .= '<ul class="range-variant mt--0">';
                foreach ( $terms as $term ) {
                    if ( in_array( $term->slug, $options, true ) ) {

                        $radios .= '<li class="v-color-label"><input type="radio" name="' . esc_attr( $name ) . '" value="' . esc_attr( $term->slug ) . '"  id="' . esc_attr( $term->slug ) . '" '
                        . checked( sanitize_title( $args['selected'] ), $term->slug, false ) . '><label  style=""  for="' . esc_attr( $term->slug ) . '">' . esc_html( apply_filters( 'woocommerce_variation_option_name', $term->name ) ) . '</label></li>';
                    }
                }
                $radios .= '</ul>';
            }

        } else {
            $radios .= '<ul class="range-variant mt--0">';
            foreach ( $options as $option ) {
                $checked = sanitize_title( $args['selected'] ) === $args['selected'] ? checked( $args['selected'], sanitize_title( $option ), false ) : checked( $args['selected'], $option, false );
                $radios .= '<li class="v-color-others"><input type="radio" name="' . esc_attr( $name ) . '" value="' . esc_attr( $option ) . '" id="' . sanitize_title( $option ) . '" ' . $checked . '><label for="' . sanitize_title( $option ) . '">' . esc_html( apply_filters( 'woocommerce_variation_option_name', $option ) ) . '</label></li>';

            }
            $radios .= '</ul>';
        }
    }

    $radios .= '</div>';

    return $html . $radios;
}
add_filter( 'woocommerce_dropdown_variation_attribute_options_html', 'variation_radio_buttons', 20, 2 );

function wooc_woocommerce_layered_nav_count( $term_html, $term, $link, $count ) {
    global $wooc_globals;

    // Get attribute type
    $attr = wooc_woocommerce_get_taxonomy_attribute( $term->taxonomy );
    $attr_type = ( $attr ) ? $attr->attribute_type : '';

    $custom_html = null;

    // Type: Color
    if ( 'color' == $attr_type || 'pa_' . $wooc_globals['pa_color_slug'] == $term->taxonomy ) {
        // Save data in global variable to avoid getting the "wooc_pa_colors" option multiple times
        if ( !isset( $wooc_globals['pa_colors'] ) ) {
            $wooc_globals['pa_colors'] = get_option( 'wooc_pa_colors' );
        }

        $id = $term->term_id;

        $color = ( isset( $wooc_globals['pa_colors'][$id] ) ) ? $wooc_globals['pa_colors'][$id] : '#c0c0c0';
        $custom_html = '<i style="background:' . esc_attr( $color ) . ';" class="wooc-pa-color wooc-pa-color-' . esc_attr( strtolower( $term->slug ) ) . '"></i>';
    }
    // Type: Image
    else if ( 'image' == $attr_type ) {
        $image_id = absint( get_term_meta( $term->term_id, 'wooc_pa_image_thumbnail_id', true ) );

        if ( $image_id ) {
            $image_url = wp_get_attachment_url( $image_id );
            $custom_html = '<div class="wooc-pa-image-thumbnail-wrap">
            <img src="' . esc_url( $image_url ) . '" class="wooc-pa-image-thumbnail" alt="' . esc_html( $term->name ) . '"></div>';
        }
    }

    if ( $custom_html ) {
        // Code from "layered_nav_list()" function in "../plugins/woocommerce/includes/widgets/class-wc-widget-layered-nav.php" file
        if ( $count > 0 ) {
            $term_html = '<a rel="nofollow" href="' . $link . '">' . $custom_html . esc_html( $term->name ) . '</a>';
        } else {
            $term_html = '<span>' . $custom_html . esc_html( $term->name ) . '</span>';
        }
    }

    return $term_html;
}
add_filter( 'woocommerce_layered_nav_term_html', 'wooc_woocommerce_layered_nav_count', 1, 4 );

/*
 *  Product variations: Get variation image src
 */
function wooc_woocommerce_get_variation_image_src( $available_variations, $attribute_name ) {
    foreach ( $available_variations as $variation ) {
        if ( isset( $variation['attributes']['attribute_' . $attribute_name] ) ) {

            return $variation['image']['thumb_src'];
        }
    }

    return null;
}

function wooc_template_loop_attributes( $product = null ) {
    global $wooc_globals;

    if ( !$product ) {
    }
    global $product;

    if ( !$product->is_type( 'variable' ) ) {
        return;
    }

    $axil_options = Helper::axil_get_options();
    $product_id = $product->get_id();
    $enabled_globaly = $axil_options['product_display_attributes'];
    $enabled_globaly_attribute_types = apply_filters( 'wooc_product_display_attribute_types', array( 'color' => '1', 'image' => '1', 'size' => '1' ) ); // Excluding "size" by default
    $enabled_attributes = get_post_meta( $product_id, 'wooc_attribute_catalog_visibility', true );

    if ( $enabled_globaly || $enabled_attributes ) {
        $available_variations = $product->get_available_variations();
        $attributes = $product->get_variation_attributes();
        $html = '';

        if ( !empty( $available_variations ) ) {
            $product_url = get_permalink( $product_id );

            $html .= '<div class="variations_form wooc-archive-variation-wrapper">';

            $html .= '<div class="color-variant-wrapper">';

            foreach ( $attributes as $attribute_name => $options ) {

                $attr = wooc_woocommerce_get_taxonomy_attribute( $attribute_name );
                $attr_type = ( $attr ) ? $attr->attribute_type : null;

                if ( !$attr_type ) {
                    continue;
                }

                // Only display custom attributes
                $is_custom_attribute = ( $enabled_globaly ) ? isset( $enabled_globaly_attribute_types[$attr_type] ) : isset( $enabled_attributes[$attribute_name] );

                if ( empty( $options ) && !empty( $product ) && !empty( $attribute_name ) ) {
                    $attributes = $product->get_variation_attributes();
                    $options = $attributes[$attribute_name];
                }

                if ( $is_custom_attribute && !empty( $options ) ) {
                    $terms = wc_get_product_terms( $product_id, $attribute_name, array( 'fields' => 'all' ) );

                    switch ( $attr_type ) {
                    // Type: Color swatch
                    case 'color':

                        // Save data in global variable to avoid getting the "wooc_pa_colors" option multiple times
                        if ( !isset( $wooc_globals['pa_colors'] ) ) {
                            $wooc_globals['pa_colors'] = get_option( 'wooc_pa_colors' );
                        }

                        $html .= '<ul class="color-variant wooc-color">';

                            foreach ( $terms as $key => $term ) {

                                if ( in_array( $term->slug, $options, true ) ) {

                                    $url = $product_url . '?attribute_' . $attribute_name . '=' . $term->slug;
                                    $color = ( isset( $wooc_globals['pa_colors'][$term->term_id] ) ) ? $wooc_globals['pa_colors'][$term->term_id] : '#ccc';
                                    $img_url = ( isset( $available_variations[$key]['attributes']['attribute_' . $attribute_name] ) ) ? $available_variations[$key]['image']['thumb_src'] : '';

                                    $html .= '<li"><a class="wcvaswatchinput" data-o-src="' . esc_url( $img_url ) . '" href="' . esc_url( $url ) . '">';
                                    $html .= '<span"><span style="background:' . esc_attr( $color ) . ';" class="color wooc-pa-color wooc-pa-color-' . esc_attr( strtolower( $term->slug ) ) . '"></span></span>';
                                    $html .= '</a></li>';
                                }
                            } 
                         $html .= '</ul>';
                        break; 
                    
                    default:
 
                    }

                   
                }
            }

            $html .= '</div>';
            $html .= '</div>';

            return $html;
        }
    }

    return null;
}

$template_include_action = apply_filters( 'wooc_template_loop_attributes_above_thumbnail', 'woocommerce_before_shop_loop_item' );
add_action( $template_include_action, 'wooc_template_loop_attributes', 5 );