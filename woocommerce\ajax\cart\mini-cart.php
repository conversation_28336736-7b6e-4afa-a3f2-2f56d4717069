<?php
/**
 * Mini-cart
 *
 * Contains the markup for the mini-cart, used by the cart widget.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/mini-cart.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.7.0
 */

defined( 'ABSPATH' ) || exit;

do_action( 'woocommerce_before_mini_cart' );
$allowed_tags = wp_kses_allowed_html( 'post' );
?>

<?php if ( !WC()->cart->is_empty() ): ?>
 <div class="cart-body">
	<ul class="cart-item-list woocommerce-mini-cart cart_list product_list_widget">
		<?php
do_action( 'woocommerce_before_mini_cart_contents' );
foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
    $_product = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
    $product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );

    if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_widget_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
        $product_name = apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key );
        $thumbnail = apply_filters( 'woocommerce_cart_item_thumbnail', $_product->get_image( array( 100, 110 ) ), $cart_item, $cart_item_key );
        $product_price = apply_filters( 'woocommerce_cart_item_price', WC()->cart->get_product_price( $_product ), $cart_item, $cart_item_key );
        $product_permalink = apply_filters( 'woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '', $cart_item, $cart_item_key );
        ?>
				<li class="cart-item woocommerce-mini-cart-item <?php echo esc_attr( apply_filters( 'woocommerce_mini_cart_item_class', 'mini_cart_item', $cart_item, $cart_item_key ) ); ?>">
					<div class="thumb-wrapper item-img">
						<?php echo wp_kses( $thumbnail, $allowed_tags ); ?>
							<div class="remove-wrapper">
								<?php
								echo apply_filters(  // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
										'woocommerce_cart_item_remove_link',
										sprintf(
											'<a href="%s" class="close-btn remove remove_from_cart_button" aria-label="%s" data-product_id="%s" data-cart_item_key="%s" data-product_sku="%s"><i class="fas fa-times"></i></a>',
											esc_url( wc_get_cart_remove_url( $cart_item_key ) ),
											esc_attr__( 'Remove this item', 'etrade' ),
											esc_attr( $product_id ),
											esc_attr( $cart_item_key ),
											esc_attr( $_product->get_sku() )
										),
										$cart_item_key
									);
									?>
						</div>
					</div>
				<div class="item-content">
					<h3 class="item-title">
						<?php echo wp_kses( $product_name, $allowed_tags ); ?>
					</h3>
				<div class="pro-qty item-quantity">
					<?php echo wc_get_formatted_cart_item_data( $cart_item ); ?>
							<?php
								if ( $_product->is_sold_individually() ) {
											$product_quantity = sprintf( '<input type="hidden" name="cart[%s][qty]" value="1" />', $cart_item_key );
										} else {
											$input_args = array(
												'input_name'  => "cart[{$cart_item_key}][qty]",
												'max_value'   => $_product->backorders_allowed() ? '' : $_product->get_stock_quantity(),
												'input_value' => $cart_item['quantity'],
												'min_value'   => '0',
											);

											$product_quantity = woocommerce_quantity_input( $input_args, $_product, false );
										}
										//echo apply_filters('woocommerce_cart_item_quantity', $product_quantity, $cart_item_key, $cart_item);
										?>
										</div>
										<div class="item-price">
											<?php echo wc_get_formatted_cart_item_data( $cart_item ); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped   ?>
											<?php echo apply_filters( 'woocommerce_widget_cart_item_quantity', '<span class="quantity">' . sprintf( '%s <i class="fas fa-times"></i> %s', $cart_item['quantity'], $product_price ) . '</span>', $cart_item, $cart_item_key ); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped   ?>
										</div>
									</div>
								</li>
								<?php
							}
						}
						do_action( 'woocommerce_mini_cart_contents' );
						?>
							</ul>
						</div>
						<div class="cart-footer">
							<div class="woocommerce-mini-cart__total total">
									<h3 class="cart-subtotal">
										<?php
						/**
						 * Hook: woocommerce_widget_shopping_cart_total.
						 *
						 * @hooked woocommerce_widget_shopping_cart_subtotal - 10
						 */
						do_action( 'woocommerce_widget_shopping_cart_total' );
						?>
		 			</h3>
				</div>
				<?php do_action( 'woocommerce_widget_shopping_cart_before_buttons' );?>
				<p class="woocommerce-mini-cart__buttons buttons"> <div class="group-btn"><?php do_action( 'woocommerce_widget_shopping_cart_buttons' );?>	</div></p>
				<?php do_action( 'woocommerce_widget_shopping_cart_after_buttons' );?>
			</div>
			<?php else: ?>
			<div class="return-to-shop text-center pt--80">
				<h5 class="woocommerce-mini-cart__empty-message"><?php esc_html_e( 'No products in the cart.', 'etrade' );?></h5>
				<a href="<?php echo wc_get_page_permalink( 'shop' ); ?>" class="axil-btn checkout-btn"><?php esc_html_e( 'Return to shop', 'etrade' );?></a>
				</div>
<?php endif;?>
<?php do_action( 'woocommerce_after_mini_cart' );?>

