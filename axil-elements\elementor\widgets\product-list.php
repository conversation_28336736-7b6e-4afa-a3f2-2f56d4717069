<?php

/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class Axil_Product_list extends Widget_Base
{

	public function get_name()
	{
		return 'axil-product-list';
	}
	public function get_title()
	{
		return __('Product List', 'etrade-elements');
	}
	public function get_icon()
	{
		return 'eicon-products-archive';
	}
	public function get_categories()
	{
		return [ETRADE_ELEMENTS_THEME_PREFIX . '-widgets'];
	}

	private function wooc_cat_dropdown_1()
	{
		$terms = get_terms(array('taxonomy' => 'product_cat'));
		$category_dropdown = array('0' => __('All Categories', 'etrade-elements'));

		foreach ($terms as $category) {
			$category_dropdown[$category->term_id] = $category->name;
		}

		return $category_dropdown;
	}
	private function wooc_cat_dropdown_2()
	{
		$terms = get_terms(array('taxonomy' => 'product_cat', 'orderby' => 'count', 'hide_empty' => false));
		$category_dropdown = array();
		foreach ($terms as $category) {
			$category_dropdown[$category->term_id] = $category->name;
		}

		return $category_dropdown;
	}
	public function get_product_name($post_type = 'product')
	{
		$options = array();
		$options = ['0' => esc_html__('None', 'etrade-elements')];
		$axil_post = array('posts_per_page' => -1, 'post_type' => $post_type);
		$axil_post_terms = get_posts($axil_post);
		if (!empty($axil_post_terms) && !is_wp_error($axil_post_terms)) {
			foreach ($axil_post_terms as $term) {
				$options[$term->ID] = $term->post_title;
			}
			return $options;
		}
	}

	private function wooc_build_query($settings)
	{

		if (!$settings['custom_id']) {

			$p_ids = array();
			if (!empty($settings['posts_not_in'])) {
				foreach ($settings['posts_not_in'] as $p_idsn) {
					$p_ids[] = $p_idsn;
				}
			}

			// Post type
			$number = $settings['number'];
			$args = array(
				'post_type'      => 'product',
				'posts_per_page' => $number ? $number : 6,
				'ignore_sticky_posts' => true,
				'post_status'         => 'publish',
				'suppress_filters'    => false,
				'post__not_in'   	  => $p_ids
			);

			$args['tax_query'] = array();

			// Category
			if (!empty($settings['cat'])) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_cat',
					'field'    => 'term_id',
					'terms'    => $settings['cat'],
				);
			}

			// Featured only
			if ($settings['featured_only']) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_visibility',
					'field'    => 'slug',
					'terms'    => 'featured',
				);
			}

			// Out-of-stock hide
			if ($settings['out_stock_hide']) {
				$args['tax_query'][] = array(
					'taxonomy' => 'product_visibility',
					'field'    => 'slug',
					'terms'    => 'outofstock',
					'operator' => 'NOT IN',
				);
			}

			// Order
			$args['orderby'] = $settings['orderby'];
			switch ($settings['orderby']) {

				case 'title':
				case 'menu_order':
					$args['order']    = 'ASC';
					break;

				case 'bestseller':
					$args['orderby']  = 'meta_value_num';
					$args['meta_key'] = 'total_sales';
					break;

				case 'rating':
					$args['orderby']  = 'meta_value_num';
					$args['meta_key'] = '_wc_average_rating';
					break;

				case 'price_l':
					$args['orderby']  = 'meta_value_num';
					$args['order']    = 'ASC';
					$args['meta_key'] = '_price';
					break;

				case 'price_h':
					$args['orderby']  = 'meta_value_num';
					$args['meta_key'] = '_price';
					break;
			}
		} else {

			$posts = $settings['product_ids'];


			$args = array(
				'post_type'      => 'product',
				'ignore_sticky_posts' => true,
				'nopaging'       => true,
				'post__in'       => $posts,
				'orderby'        => 'post__in',
			);
		}

		return new \WP_Query($args);
	}

	protected function register_controls()
	{

		$args = array(
			'post_type'           => 'product',
			'posts_per_page'      => -1,
			'post_status'         => 'publish',
			'suppress_filters'    => false,
			'ignore_sticky_posts' => true,
		);
		$products = get_posts($args);
		$products_dropdown = array();

		foreach ($products as $product) {
			$products_dropdown[$product->ID] = $product->post_title;
		}

		$this->start_controls_section(
			'sec_filter',
			[
				'label' => __('Product Filtering', 'etrade-elements'),

			]
		);

		$this->add_control(
			'style',
			[
				'label' => __('Style', 'etrade-elements'),
				'type' => Controls_Manager::SELECT,
				'default' => '1',
				'options' => [
					'1'   => __('Style One', 'etrade-elements'),
					'2'   => __('Style Two', 'etrade-elements'),
					'3'   => __('Style Three', 'etrade-elements'),


				],
			]
		);

		$this->add_control(
			'custom_id',
			[

				'type' => Controls_Manager::SWITCHER,
				'label'       => __('Custom Product', 'etrade-elements'),
				'label_on'    => __('On', 'etrade-elements'),
				'label_off'   => __('Off', 'etrade-elements'),
				'default'     => '',

			]
		);

		$this->add_control(
			'product_ids',
			[
				'label' => __('Product Seperated by commas', 'etrade-elements'),
				'type' => Controls_Manager::SELECT2,
				'options'       => $this->get_product_name(),
				'label_block'   => true,
				'multiple'      => true,
				'separator'     => 'before',
				'condition'   => array('custom_id' => 'yes'),
			]
		);

		$this->add_control(
			'number',
			[
				'label'   => __('Number of items', 'etrade-elements'),
				'type'    => Controls_Manager::NUMBER,
				'default'     => 6,
				'condition'   => array('custom_id' => ''),

			]

		);

		$this->add_control(
			'cat',
			[
				'label' => __('Categories', 'etrade-elements'),
				'type' => Controls_Manager::SELECT2,
				'options'     => $this->wooc_cat_dropdown_2(),
				'label_block'   => true,
				'default'     => '0',
				'separator'     => 'before',
				'multiple'  => true,
				'condition'   => array('custom_id' => ''),
			]
		);

		$this->add_control(
			'posts_not_in',
			[
				'label' => __('Select The Posts that will not display', 'etrade-elements'),
				'type' => Controls_Manager::SELECT2,
				'options'       => $this->get_product_name(),
				'label_block'   => true,
				'multiple'      => true,
				'separator'     => 'before',
				'condition'   => array('custom_id' => ''),
			]
		);

		$this->add_control(
			'orderby',
			[
				'label' => __('Order', 'etrade-elements'),
				'type' => Controls_Manager::SELECT2,
				'options'     => array(
					'date'        => __('Date (Recents comes first)', 'etrade-elements'),
					'title'       => __('Title', 'etrade-elements'),
					'bestseller'  => __('Bestseller', 'etrade-elements'),
					'rating'      => __('Rating(High-Low)', 'etrade-elements'),
					'price_l'     => __('Price(Low-High)', 'etrade-elements'),
					'price_h'     => __('Price(High-Low)', 'etrade-elements'),
					'rand'        => __('Random(Changes on every page load)', 'etrade-elements'),
					'menu_order'  => __('Custom Order (Available via Order field inside Page Attributes box)', 'etrade-elements'),
				),
				'label_block'   => true,
				'default'     => 'date',
				'separator'     => 'before',
				'condition'   => array('custom_id' => ''),
			]
		);

		$this->add_control(
			'out_stock_hide',
			[

				'type' => Controls_Manager::SWITCHER,
				'label'       => __('Hide Out-of-stock Products', 'etrade-elements'),
				'label_on'    => __('On', 'etrade-elements'),
				'label_off'   => __('Off', 'etrade-elements'),
				'default'     => '',
				'condition'   => array('custom_id' => ''),

			]
		);

		$this->add_control(
			'featured_only',
			[

				'type' => Controls_Manager::SWITCHER,
				'label'       => __('Display only Featured Products', 'etrade-elements'),
				'label_on'    => __('On', 'etrade-elements'),
				'label_off'   => __('Off', 'etrade-elements'),
				'default'     => '',
				'condition'   => array('custom_id' => ''),

			]
		);
		$this->add_control(
			'price_display',
			[

				'type' => Controls_Manager::SWITCHER,
				'label'       => __('Display price ', 'etrade-elements'),
				'label_on'    => __('On', 'etrade-elements'),
				'label_off'   => __('Off', 'etrade-elements'),
				'default'     => 'yes',

			]
		);
		$this->add_control(
			'sale_price_only',
			[

				'type' => Controls_Manager::SWITCHER,
				'label'       => __('Display only sale price', 'etrade-elements'),
				'label_on'    => __('On', 'etrade-elements'),
				'label_off'   => __('Off', 'etrade-elements'),
				'default'     => 'yes',

			]
		);

		$this->add_control(
			'rating_display',
			[

				'type' => Controls_Manager::SWITCHER,
				'label'       => __('Rating Display', 'etrade-elements'),
				'label_on'    => __('On', 'etrade-elements'),
				'label_off'   => __('Off', 'etrade-elements'),
				'default'     => 'yes',
				'condition'   => array('style' => '1'),

			]
		);
		$this->end_controls_section();
		$this->start_controls_section(
			'etrade_responsive',
			[
				'label' => __('Responsive Columns', 'etrade-elements'),

			]
		);
		$this->add_control(
			'col_xl',
			[
				'label' => __('Desktops: > 1199px', 'etrade-elements'),
				'type' => Controls_Manager::SELECT,
				'options' => [
					'1'  => esc_html__('1 Col', 'etrade-elements'),
					'2'  => esc_html__('2 Col', 'etrade-elements'),
					'3'  => esc_html__('3 Col', 'etrade-elements'),
					'4'  => esc_html__('4 Col', 'etrade-elements'),
					'5'  => esc_html__('5 Col', 'etrade-elements'),
					'6'  => esc_html__('6 Col', 'etrade-elements'),
				],
				'default' => '3',
			]
		);
		$this->add_control(
			'col_lg',
			[
				'label' => __('Desktops: > 991px', 'etrade-elements'),
				'type' => Controls_Manager::SELECT,
				'options' => [
					'1'  => esc_html__('1 Col', 'etrade-elements'),
					'2'  => esc_html__('2 Col', 'etrade-elements'),
					'3'  => esc_html__('3 Col', 'etrade-elements'),
					'4'  => esc_html__('4 Col', 'etrade-elements'),
					'5'  => esc_html__('5 Col', 'etrade-elements'),
					'6'  => esc_html__('6 Col', 'etrade-elements'),
				],
				'default' => '2',
			]
		);
		$this->add_control(
			'col_md',
			[
				'label' => __('Tablets: > 767px', 'etrade-elements'),
				'type' => Controls_Manager::SELECT,
				'options' => [
					'1'  => esc_html__('1 Col', 'etrade-elements'),
					'2'  => esc_html__('2 Col', 'etrade-elements'),
					'3'  => esc_html__('3 Col', 'etrade-elements'),
					'4'  => esc_html__('4 Col', 'etrade-elements'),
					'5'  => esc_html__('5 Col', 'etrade-elements'),
					'6'  => esc_html__('6 Col', 'etrade-elements'),
				],
				'default' => '2',
			]
		);

		$this->add_control(
			'col_sm',
			[
				'label' => __('Phones: >575px', 'etrade-elements'),
				'type' => Controls_Manager::SELECT,
				'options' => [
					'1'  => esc_html__('1 Col', 'etrade-elements'),
					'2'  => esc_html__('2 Col', 'etrade-elements'),
					'3'  => esc_html__('3 Col', 'etrade-elements'),
					'4'  => esc_html__('4 Col', 'etrade-elements'),
					'5'  => esc_html__('5 Col', 'etrade-elements'),
					'6'  => esc_html__('6 Col', 'etrade-elements'),
				],
				'default' => '1',
			]
		);
		$this->add_control(
			'col_mobile',
			[
				'label' => __('Small Phones: <576px', 'etrade-elements'),
				'type' => Controls_Manager::SELECT,
				'options' => [
					'1'  => esc_html__('1 Col', 'etrade-elements'),
					'2'  => esc_html__('2 Col', 'etrade-elements'),
					'3'  => esc_html__('3 Col', 'etrade-elements'),
					'4'  => esc_html__('4 Col', 'etrade-elements'),
					'5'  => esc_html__('5 Col', 'etrade-elements'),
					'6'  => esc_html__('6 Col', 'etrade-elements'),
				],
				'default' => '1',
			]
		);
		$this->end_controls_section();
	}

	protected function render()
	{
		$settings 	= $this->get_settings();
		$template 	= 'product-list';
		$template   = 'product-list-' . str_replace("style", "", $settings['style']);
		$settings['query'] = $this->wooc_build_query($settings);
		return wooc_Elements_Helper::wooc_element_template($template, $settings);
	}
}
