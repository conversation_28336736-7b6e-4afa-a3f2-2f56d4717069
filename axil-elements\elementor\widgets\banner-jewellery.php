<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;

use Elementor\Controls_Manager;
use Elementor\Group_Control_Background;
use Elementor\Group_Control_Typography;
use Elementor\Widget_Base;

if ( !defined( 'ABSPATH' ) ) {
    exit;
}
// Exit if accessed directly

class axil_banner_jewellery extends Widget_Base {

    public function get_name() {
        return 'axil-banner-slider-jewellery';
    }
    public function get_title() {
        return __( 'Banner - Jewellery', 'etrade-elements' );
    }
    public function get_icon() {
        return ' eicon-banner';
    }
    public function get_categories() {
        return array( ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' );
    }
    public function axil_get_img( $img ) {
        $img = ETRADE_ELEMENTS_BASE_URL . '/assets/images/' . $img;
        return $img;
    }
    private function axil_get_all_pages() {

        $page_list = get_posts( array(
            'post_type'      => 'page',
            'orderby'        => 'date',
            'order'          => 'DESC',
            'posts_per_page' => -1,
        ) );

        $pages = array();

        if ( !empty( $page_list ) && !is_wp_error( $page_list ) ) {
            foreach ( $page_list as $page ) {
                $pages[$page->ID] = $page->post_title;
            }
        }

        return $pages;
    }

    protected function register_controls() {

        $this->start_controls_section(
            'banner_content_sec',
            array(
                'label' => esc_html__( ' Banner Content', 'etrade-elements' ),

            )
        );
        $this->add_group_control(
            Group_Control_Background::get_type(),
            array(
                'name'     => 'banner_background',
                'label'    => esc_html__( 'Background Image', 'etrade-elements' ),
                'types'    => array( 'classic', 'gradient' ),
                'selector' => '{{WRAPPER}} .axil-main-slider-area.main-slider-style-7',
            )
        );
        $this->add_control(
            'sub_title',
            array(
                'label'       => __( 'Before Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXT,
                'placeholder' => __( 'Type your Title here.', 'etrade-elements' ),
                'default'     => esc_html__( 'Hot Deal In Diamond', 'etrade-elements' ),

            )
        );
        $this->add_control(
            'beforetitlestyle',
            array(
                'label'   => esc_html__( 'Before Color', 'etrade-elements' ),
                'type'    => Controls_Manager::SELECT,
                'default' => 'primary',
                'options' => array(
                    'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                    'secondary' => esc_html__( 'Secondary', 'etrade-elements' ),
                    'primary2'  => esc_html__( 'Primary 2', 'etrade-elements' ),

                ),
            )
        );
        $this->add_control(
            'icon',
            array(
                'label'     => __( 'Icons', 'etrade-elements' ),
                'type'      => Controls_Manager::ICONS,
                'separator' => 'after',
                'default'   => array(
                    'value'   => 'fas fa-fire',
                    'library' => 'solid',
                ),

            )
        );

        $this->add_control(
            'banner_title',
            array(
                'label'       => esc_html__( 'Slider Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXTAREA,
                'default'     => esc_html__( 'Exclusive Design Collection', 'etrade-elements' ),
                'label_block' => true,
            )
        );
        $this->add_control(
            'banner_description',
            array(
                'label'       => esc_html__( 'Description', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXTAREA,
                'default'     => esc_html__( 'Casual line with short design in 100% suede Diamond', 'etrade-elements' ),
                'label_block' => true,
            )
        );

        $this->add_control(
            'btntext',
            array(
                'label'     => __( 'Button Text', 'etrade-elements' ),
                'type'      => Controls_Manager::TEXT,
                'default'   => 'Shop Now',
                'separator' => 'before',

            )
        );

        $this->add_control(
            'axil_link_type',
            array(
                'label'       => esc_html__( 'See All Link Type', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT,
                'options'     => array(
                    '1' => 'Custom Link',
                    '2' => 'Internal Page',
                ),
                'default'     => '1',

                'label_block' => true,
            )
        );

        $this->add_control(
            'axil_page_link',
            array(
                'label'       => esc_html__( 'Select See All Page', 'etrade-elements' ),
                'type'        => Controls_Manager::SELECT2,
                'label_block' => true,
                'options'     => $this->axil_get_all_pages(),
                'condition'   => array(
                    'axil_link_type' => '2',

                ),
            )
        );

        $this->add_control(
            'url',
            array(
                'label'       => __( 'Detail URL', 'etrade-elements' ),
                'type'        => Controls_Manager::URL,
                'placeholder' => 'https://your-link.com',
                'condition'   => array(
                    'axil_link_type' => '1',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'subtitle_style_section',
            array(
                'label' => esc_html__( 'Title Before', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'subtitle_color',
            array(
                'label'     => esc_html__( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .main-slider-content .subtitle'   => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .subtitle i' => 'background-color: {{VALUE}}',
                ),
            )
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'subtitle_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .main-slider-content .subtitle',
            )
        );

        $this->add_responsive_control(
            'subtitle_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .main-slider-content .subtitle' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'title_style_section',
            array(
                'label' => esc_html__( 'Title', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'title_color',
            array(
                'label'     => esc_html__( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .inner .title'               => 'color: {{VALUE}}',
                    '{{WRAPPER}} .main-slider-content .title' => 'color: {{VALUE}}',
                ),
            )
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'title_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .inner .title , {{WRAPPER}} .main-slider-content .title',
            )
        );
        $this->add_responsive_control(
            'title_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .inner .title'               => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .main-slider-content .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );
        $this->end_controls_section();
        $this->start_controls_section(
            'description_style_section',
            array(
                'label' => esc_html__( 'Description', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'description_color',
            array(
                'label'     => esc_html__( 'Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} .banner-description' => 'color: {{VALUE}}',

                ),
            )
        );
        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'description_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),

                'selector' => '{{WRAPPER}} .banner-description',
            )
        );
        $this->add_responsive_control(
            'description_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .banner-description' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'btn_style_section',
            array(
                'label' => esc_html__( 'Button', 'etrade-elements' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_control(
            'btn_color',
            array(
                'label'     => esc_html__( 'Text Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} a.axil-btn.btn-bg-secondary, button.axil-btn.btn-bg-secondary'     => 'color: {{VALUE}}',
                    '{{WRAPPER}} a.axil-btn.btn-bg-secondary i, button.axil-btn.btn-bg-secondary i' => 'color: {{VALUE}}',

                ),
            )
        );
        $this->add_control(
            'btn_bg_color',
            array(
                'label'     => esc_html__( 'Text Color', 'etrade-elements' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',

                'selectors' => array(
                    '{{WRAPPER}} a.axil-btn.btn-bg-secondary:before, button.axil-btn.btn-bg-secondary:before' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} a.axil-btn.btn-bg-secondary, button.axil-btn.btn-bg-secondary'               => 'background-color: {{VALUE}}',

                ),
            )
        );
        $this->add_group_control(
            Group_Control_Typography::get_type(),
            array(
                'name'     => 'btn_font_size',
                'label'    => esc_html__( 'Typography', 'etrade-elements' ),
                'selector' => '{{WRAPPER}} a.axil-btn, button.axil-btn',
            )
        );
        $this->add_responsive_control(
            'btn_margin',
            array(
                'label'      => esc_html__( 'Margin', 'etrade-elements' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => array( 'px', '%', 'em' ),

                'selectors'  => array(
                    '{{WRAPPER}} .shop-btn' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',

                ),
            )
        );

        $this->end_controls_section();

    }

    protected function render() {
        $settings = $this->get_settings();
        $template = 'banner-jewellery'; 
        return wooc_Elements_Helper::wooc_element_template( $template, $settings );
    }

}