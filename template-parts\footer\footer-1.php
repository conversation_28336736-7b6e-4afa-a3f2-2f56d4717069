<?php
/**
 * Template part for displaying footer layout one
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package etrade
 */

// Get Value
$axil_options = Helper::axil_get_options();
$axil_footer_bottom_menu_args = Helper::axil_footer_bottom_menu_args();
$lineclass = ( is_active_sidebar( 'footer-1' ) || is_active_sidebar( 'footer-2' ) || is_active_sidebar( 'footer-3' ) || is_active_sidebar( 'footer-4' ) ) ? "footer-menu-active" : "";
$allowed_tags = wp_kses_allowed_html( 'post' );
?>

<div class="axil-footer-area footer-style-1 bg-color-white">
    <?php if ( is_active_sidebar( 'footer-1' ) || is_active_sidebar( 'footer-2' ) || is_active_sidebar( 'footer-3' ) || is_active_sidebar( 'footer-4' ) ) {?>
        <!-- Start Footer Top Area -->
            <div class="footer-top separator-top">
                <div class="container">
                <div class="row">
                    <?php if ( is_active_sidebar( 'footer-1' ) ) {?>
                        <!-- Start Single Widget -->
                        <div class="col-md-3 col-sm-12">
                            <div class="axil-footer-widget axil-border-right">
                                <?php dynamic_sidebar( 'footer-1' );?>
                            </div>
                        </div><!-- End Single Widget -->
                    <?php }?>

                    <?php if ( is_active_sidebar( 'footer-2' ) ) {?>
                        <!-- Start Single Widget -->
                        <div class="col-md-3 col-sm-12">
                            <div class="axil-footer-widget">
                                <?php dynamic_sidebar( 'footer-2' );?>
                            </div>
                        </div><!-- End Single Widget -->
                    <?php }?>

                    <?php if ( is_active_sidebar( 'footer-3' ) ) {?>
                        <!-- Start Single Widget -->
                        <div class="col-md-3 col-sm-12">
                            <div class="axil-footer-widget">
                                <?php dynamic_sidebar( 'footer-3' );?>
                            </div>
                        </div><!-- End Single Widget -->
                    <?php }?>

                    <?php if ( is_active_sidebar( 'footer-4' ) ) {?>
                        <!-- Start Single Widget -->
                        <div class="col-md-3 col-sm-12">
                            <div class="axil-footer-widget widget-last">
                                <?php dynamic_sidebar( 'footer-4' );?>
                            </div>
                        </div><!-- End Single Widget -->
                    <?php }?>
                </div>
            </div>
        </div>
        <!-- End Footer Top Area -->
    <?php }?>
 <?php if ( !empty( $axil_options['axil_copyright_contact'] ) || !empty( $axil_options['axil_footer_footerbottom'] ) || !empty( $axil_options['payment_icons'] ) ) {?>
    <!-- Start Copyright Area  -->
    <div class="copyright-area copyright-default separator-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-xl-7 col-lg-12">
                    <div class="copyright-left d-flex flex-wrap justify-content-xl-start justify-content-center">
                        <?php if ( !empty( $axil_options['axil_footer_footerbottom'] ) ) {?>
                            <?php if ( has_nav_menu( 'footerbottom' ) ) {?>
                                <?php wp_nav_menu( $axil_footer_bottom_menu_args );?>
                            <?php }?>
                        <?php }?>
                        <?php if ( !empty( $axil_options['axil_copyright_contact'] ) ) {?>
                             <ul class="quick-link quick-copyright">
                                <li><?php echo wp_kses( $axil_options['axil_copyright_contact'], $allowed_tags ); ?></li>
                           </ul>
                        <?php }?>
                    </div>
                </div>
                <?php if ( !empty( $axil_options['payment_icons'] ) ) {?>
                    <div class="col-xl-5 col-lg-12">
                        <div class="copyright-right d-flex flex-wrap justify-content-xl-end justify-content-center align-items-center">
                            <?php if ( !empty( $axil_options['Display_Payment_title'] ) ): ?>
                                    <span class="card-text"><?php echo esc_attr( $axil_options['Display_Payment_title'] ); ?></span>
                            <?php endif;?>
                            <?php if ( $axil_options['payment_icons'] ): ?>
                                <ul class="payment-icons-bottom quick-link">
                                    <?php if ( $axil_options['payment_img'] ): ?>
                                        <?php $axil_cards = explode( ',', $axil_options['payment_img'] );?>
                                        <?php foreach ( $axil_cards as $axil_card ): ?>
                                            <li><?php echo wp_get_attachment_image( $axil_card ); ?></li>
                                        <?php endforeach;?>
                                        <?php else: ?>
                                            <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>" src="<?php echo esc_url( Helper::get_img( 'cart-1.svg' ) ); ?>"></li>
                                            <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>" src="<?php echo esc_url( Helper::get_img( 'cart-2.svg' ) ); ?>"></li>
                                            <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>" src="<?php echo esc_url( Helper::get_img( 'cart-3.svg' ) ); ?>"></li>
                                            <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>" src="<?php echo esc_url( Helper::get_img( 'cart-4.svg' ) ); ?>"></li>
                                            <li><img alt="<?php esc_attr_e( 'cart', 'etrade' );?>" src="<?php echo esc_url( Helper::get_img( 'cart-5.svg' ) ); ?>"></li>
                                        <?php endif;?>
                                    </ul>
                                <?php endif;?>
                        </div>
                    </div>
                <?php }?>
            </div>
        </div>
    </div>
    <!-- End Copyright Area  -->
<?php }?>
</div>
<!-- End Footer Area  -->


