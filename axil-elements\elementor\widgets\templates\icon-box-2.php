<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */
use Elementor\Icons_Manager;
use Elementor\Group_Control_Image_Size;
 ?>

 <div class="service-box service-style-2"> 
     <?php if ( $settings['icontype'] == 'image' ): ?>
        <div class="img-box icon">
            <?php echo Group_Control_Image_Size::get_attachment_image_html( $settings, 'image_size', 'image' );?> 
        </div>
    <?php else: ?>
        <div class="item-icon icon">
            <?php Icons_Manager::render_icon( $settings['icon'] ); ?>
        </div> 
    <?php endif; ?>  
    <div class="content">
        <?php if ( $settings['title'] ): ?>
            <h6 class="title"><?php echo wp_kses_post( $settings['title'] );?></h6>
        <?php endif; ?>  
        <?php if ( $settings['subtitle'] ): ?>
            <p class="item-subtitle"><?php echo wp_kses_post( $settings['subtitle'] );?></p>
        <?php endif; ?>  
    </div>
</div>
 