<?php
/**
 * @package etrade
 */

if( !class_exists('etrade_Download_App') ){
    class etrade_Download_App extends WP_Widget{
    	/**
         * Register widget with WordPress.
         */
        function __construct(){

            $widget_options = array(
                'description'                   => esc_html__('etrade: Download App', 'etrade-elements'),
                'customize_selective_refresh'   => true,
            );

            parent:: __construct('etrade_Download_App', esc_html__( 'etrade: Download App', 'etrade-elements'), $widget_options );

        }
        /**
         * Front-end display of widget.
         *
         * @see WP_Widget::widget()
         *
         * @param array $args     Widget arguments.
         * @param array $instance Saved values from database.
         */
        public function widget( $args, $instance ){
        	echo wp_kses_post( $args['before_widget'] );
        	if ( ! empty( $instance['title'] ) ) {
        		echo wp_kses_post( $args['before_title'] ) . apply_filters( 'widget_title', esc_html( $instance['title'] ) ) . wp_kses_post( $args['after_title'] );
        	}
        	$app        = isset( $instance['app'] ) ? $instance['app'] : '';
            $app2       = isset( $instance['app2'] ) ? $instance['app2'] : '';
            $qr_code    = isset( $instance['qr_code'] ) ? $instance['qr_code'] : '';
        	$content    = isset( $instance['content'] ) ? $instance['content'] : '';
            $app_link2    = isset( $instance['app_link2'] ) ? $instance['app_link2'] : '';
            $app_link    = isset( $instance['app_link'] ) ? $instance['app_link'] : '';
              
        	?>
  
            <div class="inner">
                 <?php if ( !empty($content) ): ?>
                    <div><?php echo wpautop( $content ); ?></div>
                 <?php endif ?> 

                <div class="download-btn-group"> 
                    <?php if ( !empty($qr_code) ) { ?>
                        <div class="qr-code">
                           <img src="<?php echo esc_url( $qr_code ) ; ?>" alt="<?php echo esc_attr('Play Store'); ?>">
                        </div>
                    <?php } ?>  
                        <div class="app-link">
                              <?php if ( !empty($app) ) { ?>
                                <a href="<?php echo esc_url( $app_link ); ?>" class="ax-foo-app">
                                    <img src="<?php echo esc_url( $app ) ; ?>" alt="<?php echo esc_attr('app'); ?>">
                                </a>
                             <?php } ?> 
                             <?php if ( !empty($app2) ) { ?>
                                <a href="<?php echo esc_url( $app_link ); ?>" class="ax-foo-app">
                                    <img src="<?php echo esc_url( $app2) ; ?>" alt="<?php echo esc_attr('Play Store'); ?>">
                                </a>
                             <?php } ?> 
                        </div>  
                    </div>
                </div>
                 
        	<?php
        	echo wp_kses_post( $args['after_widget'] );
        }
        /**
         * Sanitize widget form values as they are saved.
         *
         * @see WP_Widget::update()
         *
         * @param array $new_instance Values just sent to be saved.
         * @param array $old_instance Previously saved values from database.
         *
         * @return array Updated safe values to be saved.
         */
        public function update( $new_instance, $old_instance ){
        	$instance              = array();
        	$instance['title']     = ( ! empty( $new_instance['title'] ) ) ? sanitize_text_field( $new_instance['title'] ) : '';
        	$instance['content']   = ( ! empty( $new_instance['content'] ) ) ? strip_tags ( $new_instance['content'] ) : '';
        	$instance['app'] 	   = ( ! empty( $new_instance['app'] ) ) ? strip_tags ( $new_instance['app'] ) : '';
            $instance['app_link2'] = ( ! empty( $new_instance['app_link2'] ) ) ? strip_tags ( $new_instance['app_link2'] ) : '';
            $instance['app_link']  = ( ! empty( $new_instance['app_link'] ) ) ? strip_tags ( $new_instance['app_link'] ) : '';
            $instance['app2']      = ( ! empty( $new_instance['app2'] ) ) ? strip_tags ( $new_instance['app2'] ) : '';
            $instance['qr_code']   = ( ! empty( $new_instance['qr_code'] ) ) ? strip_tags ( $new_instance['qr_code'] ) : '';
        	 
            if ( current_user_can( 'unfiltered_html' ) ) {
			        $instance['content'] = $new_instance['content'];
			} else {
			        $instance['content'] = wp_kses_post( $new_instance['content'] );
			}
        	return $instance;
        }

        /**
         * Back-end widget form.
         *
         * @see WP_Widget::form()
         *
         * @param array $instance Previously saved values from database.
         */
        public function form($instance){ 
        	$title         = !empty( $instance['title'] ) ? $instance['title'] : '';
        	$app           = !empty( $instance['app'] ) ? $instance['app'] : '';
            $app_link      = !empty( $instance['app_link'] ) ? $instance['app_link'] : '';
            $app2          = !empty( $instance['app2'] ) ? $instance['app2'] : '';
            $app_link2     = !empty( $instance['app_link2'] ) ? $instance['app_link2'] : '';
            $qr_code       = !empty( $instance['qr_code'] ) ? $instance['qr_code'] : '';
        	$content       = !empty( $instance['content'] ) ? $instance['content'] : ''; 
        	 
        	?>
			<p>
				<label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php echo esc_html__('Title:' ,'etrade-elements') ?></label>
				<input id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" class="widefat" value="<?php echo esc_textarea( $title ); ?>">
			</p>

			<div class="image_box_wrap" style="margin:20px 0 15px 0; width: 100%;">
                <label for="<?php echo esc_attr($this->get_field_id('app')); ?>"><?php echo esc_html__('App Image:' ,'etrade-elements') ?></label>
				<button class="button button-primary author_info_image">
					<?php esc_html_e('Upload app', 'etrade-elements'); ?>
				</button>
				<div class="image_box widefat">
					<img src="<?php if( !empty($app)){echo esc_html($app);} ?>" style="margin:15px 0 0 0;padding:0;max-width: 100%;display:inline-block; height: auto;" alt="<?php echo esc_attr(''); ?>" />
				</div>
				<input type="text" class="widefat image_link" name="<?php echo esc_attr($this->get_field_name('app')); ?>" id="<?php echo esc_attr($this->get_field_id('app')); ?>" value="<?php echo esc_attr($app); ?>" style="margin:15px 0 0 0;">
			</div>
            <p>
                <label for="<?php echo esc_attr($this->get_field_id('app_link')); ?>"><?php echo esc_html__('App link:' ,'etrade-elements') ?></label>
                <input id="<?php echo esc_attr($this->get_field_id('app_link')); ?>" name="<?php echo esc_attr($this->get_field_name('app_link')); ?>" type="text" class="widefat" value="<?php echo esc_textarea( $app_link ); ?>">
            </p>
  
            <div class="image_box_wrap" style="margin:20px 0 15px 0; width: 100%;">
                <label for="<?php echo esc_attr($this->get_field_id('app2')); ?>">
                    <?php echo esc_html__('App Image2:' ,'etrade-elements') ?>
                </label>
                <button class="button button-primary author_info_image">
                    <?php esc_html_e('Upload app', 'etrade-elements'); ?>
                </button>
                <div class="image_box widefat">
                    <img src="<?php if( !empty($app2)){echo esc_html($app2);} ?>" style="margin:15px 0 0 0;padding:0;max-width: 100%;display:inline-block; height: auto;" alt="<?php echo esc_attr(''); ?>" />
                </div>
                <input type="text" class="widefat image_link" name="<?php echo esc_attr($this->get_field_name('app2')); ?>" id="<?php echo esc_attr($this->get_field_id('app2')); ?>" value="<?php echo esc_attr($app2); ?>" style="margin:15px 0 0 0;">
            </div>    
 
            <p>
                <label for="<?php echo esc_attr($this->get_field_id('app_link2')); ?>"><?php echo esc_html__('App link:' ,'etrade-elements') ?></label>
                <input id="<?php echo esc_attr($this->get_field_id('app_link2')); ?>" name="<?php echo esc_attr($this->get_field_name('app_link2')); ?>" type="text" class="widefat" value="<?php echo esc_textarea( $app_link2 ); ?>">
            </p>


            <div class="image_box_wrap" style="margin:20px 0 15px 0; width: 100%;">
                <label for="<?php echo esc_attr($this->get_field_id('app2')); ?>">
                    <?php echo esc_html__('Qr Code:' ,'etrade-elements') ?>
                </label>
                <button class="button button-primary author_info_image">
                    <?php esc_html_e('Upload app', 'etrade-elements'); ?>
                </button>
                <div class="image_box widefat">
                    <img src="<?php if( !empty($qr_code)){echo esc_html($qr_code);} ?>" style="margin:15px 0 0 0;padding:0;max-width: 100%;display:inline-block; height: auto;" alt="<?php echo esc_attr(''); ?>" />
                </div>
                <input type="text" class="widefat image_link" name="<?php echo esc_attr($this->get_field_name('qr_code')); ?>" id="<?php echo esc_attr($this->get_field_id('qr_code')); ?>" value="<?php echo esc_attr($qr_code); ?>" style="margin:15px 0 0 0;">
            </div> 

			<p>
				<label for="<?php echo esc_attr($this->get_field_id('content')); ?>"><?php echo esc_html__('Content:' ,'etrade-elements') ?></label>
				<textarea  id="<?php echo esc_attr($this->get_field_id('content')); ?>" name="<?php echo esc_attr($this->get_field_name('content')); ?>" rows="7" class="widefat" ><?php echo esc_textarea( $content ); ?></textarea>
			</p>
			 
        	<?php
        }
	}
}
function etrade_Download_App(){
    register_widget('etrade_Download_App');
}
add_action('widgets_init','etrade_Download_App');