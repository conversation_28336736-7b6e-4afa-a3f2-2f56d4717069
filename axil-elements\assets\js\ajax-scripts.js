
// Mini Cart
(function($){
	$(document).ready(function(){
		
		var context = {
		container: ".wooc-mini-cart-container",
		close: ".close",
		overlay: ".wooc-overlay-cart"
		};
		
		$(`${context.container} ${context.close}`).on('click', function(e){
			axil_close_cart();
		});

		$(context.overlay).on('click', axil_close_cart);

		function axil_close_cart(){
			$(context.container).removeClass('open')
			
			$('.close.filter-drawer').trigger('click');

			$(context.overlay).animate({
				opacity: 0
			}, 500, 'swing', function(){
				$(context.overlay).hide();
			});
		}

		function axil_open_side_cart(){
			$(context.container).addClass('open')

			$(context.overlay).show('fast', function(){

			$(context.overlay).animate({
				opacity: 0.5,
			}, 500, 'swing');

			});

			axil_get_side_cart_content();
		}

		function axil_get_side_cart_content(){
			// Requesting for data ajaxurl
			$.ajax({
				url: eTradeAjaxObj.ajaxurl,
				data: {
					action: 'cart_template_load',
					template: 'cart/mini',
					part: 'cart',
				},
				type: "POST",
				success: function(data) {
					$("#etrade-content-cart-template").html(data);
				},

				error: function(MLHttpRequest, textStatus, errorThrown){
					console.log(errorThrown);
				}
			});
		}

		// Open on click
		$('.cart-icon-area').ready(function(){
			$('.cart-icon-area').click(function(e){
				e.preventDefault();
				axil_open_side_cart();
			});
		});

		// Open on product added trigger
		$(document).on('added_to_cart', function(){
			$('#yith-quick-view-close').trigger("click");
			axil_open_side_cart();
		});

		// Removed From cart
		$(document).on('removed_from_cart', axil_get_side_cart_content);

		$('.mobile-filter-button').on('click', function(){
			$('.woocommerce-top-bar-widget-wrapper').addClass('show-drawer');
			
			$('.wooc-overlay-cart').css({'display': 'block'}).animate({
				opacity: 0.5
			}, 500, 'swing');

		});

		$('.close.filter-drawer').on('click', function(){

			$('.woocommerce-top-bar-widget-wrapper').removeClass('show-drawer');
			$('.wooc-overlay-cart').animate({
				opacity: 0
			}, 500, 'swing', function(){$('.wooc-overlay-cart').css({'display': 'none'})});
		});

		var recent_product = $('.recent-product');
		$(document).on('input', '#prod-search', function () {
			if (this.value.length <= 0) {
				console.log(recent_product);
				$('.search-results-body').html(recent_product);
			} else if(this.value.length >= 3) {
				var keyword = $('#prod-search').val();
				$.ajax({
					url: eTradeAjaxObj.ajaxurl,
					type: 'POST',
					data: {
						'action': 'prod_search',
						'keyword': keyword,
					},
					context: this,
					beforeSend: function () {
						// $('.search-results-body').empty();
						$('.search_input_loader').show();
						//$('#prod-search').attr('disabled', 'disabled');
					},
					success: function (data) {
						$('.search_input_loader').hide();
						$('.search-results-body').html(data);
					},
					// complete: function () {
					// 	$('.search_input_loader').hide();
					// 	// $('#prod-search').removeAttr('disabled').focus();
					// }
				});
			}
		})

		var category_val = "";
		$(document).on('focus', '#product_search', function () {
			category_val = $('#search_category_filter').val();
		});

		$(document).on('change', '#search_category_filter', function () {
			$('#product_search').val('');
			$('.search_suggetion_field').empty();
		});

		$(document).on('input', '#product_search', function () {
			var keyword = $('#product_search').val();
			if (keyword.length < 3) {
				$('.search_suggetion_field').empty();
			} else {
				$.ajax({
					url: eTradeAjaxObj.ajaxurl,
					type: 'POST',
					data: {
						'action': 'product_search',
						'category_val': category_val,
						'keyword': keyword,
					},
					context: this,
					beforeSend: function () {
						$('.search_input_loader').show();
					},
					success: function (data) {
						$('.search_input_loader').hide();
						$('.search_suggetion_field').html(data);
					},
				});
			}
		})
	});

})(jQuery);

jQuery(document).on('submit', '.shop_table.cart form', function() {			
        	updateMiniCartQuantity();
        	return false;
    	});

function updateMiniCartQuantity() {
	    var cartForm = jQuery('.shop_table.cart form');
	    jQuery('<input />').attr('type', 'hidden')
	        .attr('name', 'update_cart')
	        .attr('value', 'Update Cart')
	        .appendTo(cartForm);

	    var formData = cartForm.serialize();
	    jQuery.ajax({
	        type: cartForm.attr('method'),
	        url: cartForm.attr('action'),
	        data: formData,
	        dataType: 'html',
	        success: function(response) {
				console.log(response);

	            let wc_cart_fragment_url = (wc_cart_fragments_params.wc_ajax_url).replace("%%endpoint%%", "get_refreshed_fragments");
	            jQuery.ajax({
	                type: 'post',
	                url: wc_cart_fragment_url,
	                success: function(response) {
	                    console.log(response);
	                    var mini_cart_wrapper = jQuery('.widget_shopping_cart_content');
	                    var parent = mini_cart_wrapper.parent();
	                    mini_cart_wrapper.remove();
	                    parent.append(response.fragments['div.widget_shopping_cart_content']);
	                },
	                complete: function() {
	                    cartForm = jQuery('.shop_table.cart form');
	                }
	            });
	        }
	    });
	}

