<?php
if ( !axil_is_woocommerce_activated() ) return;

/**
 * ------------------------------------------------------------------------------------------------
 * Size Guide button
 * ------------------------------------------------------------------------------------------------
 */
if (! function_exists('axil_the_aska_question')) {
    function axil_the_aska_question( $product_id )
    {
         $axil_options  = Helper::axil_get_options();  
         $aska_question = $axil_options['single_aska_question'];
        
        if( empty($aska_question) ) return;
        
        wp_enqueue_script('jquery-magnific-popup');
        wp_enqueue_style('magnific-popup');

        $product    = wc_get_product( $product_id );
        $image_id   = $product->get_image_id();
        $image      = wp_get_attachment_image( $image_id, 'woocommerce_thumbnail' );
        ?>
        <li class="item model-aska-question">
            <a href="#model-content-aska-question" class="popup-button-open">
                <i class="fad fa-question"></i>
                <span><?php esc_html_e('Ask a Question', 'etrade'); ?></span>
            </a>
            <div id="model-content-aska-question" class="model-popup-content popup-aska-question zoom-anim-dialog mfp-hide">
                <div class="content">
                    <h3 class="model-headling-popup"><?php esc_html_e('Ask a Question', 'etrade'); ?></h3>
                    <div class="model-product media">
                        <div class="image media-left">
                            <?php echo trim($image); ?>  
                        </div>
                        <div class="product-info media-body">
                            <h4 class="name"><?php echo trim($product->get_name()); ?></h4>
                            <span class="price"><?php echo trim($product->get_price_html()); ?></span>
                        </div>
                    </div>
                    <div class="model-wrap">
                        <?php echo do_shortcode($aska_question); ?>
                    </div>
                </div>
            </div>
        </li>
        <?php
    }
}