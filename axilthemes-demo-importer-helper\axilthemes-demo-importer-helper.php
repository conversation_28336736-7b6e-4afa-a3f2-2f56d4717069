<?php
/*
Plugin Name: axilthemes-demo-importer-helper
Plugin URI: axilthemes.com/demo/template/etrade/axil-demo-importer-helper
Description: Uninstall this plugin after you've finished importing demo contents
Version: 1.0
Author: axilthemes
Author URI: http://axilthemes.com
*/

if ( ! defined( 'ABSPATH' ) ) exit;
	if ( ! defined( 'AXILTHEME' ) ) {
		define( 'AXILTHEME',  					 ( WP_DEBUG ) ? time() : '1.0' );
		define( 'AXILTHEME_PREVIEW', 			'https://dev.axilthemes.com/themes/etrade/demo/preview/');	
		define( 'AXILTHEME_PREVIEW_LINK', 		'https://dev.axilthemes.com/themes/etrade/');	
		define( 'AXILTHEME_DEMO_DATA_URL', 		'https://dev.axilthemes.com/themes/etrade/demo/');	
		define( 'AXILTHEME_PREFIX',      		'axilthemesdemo' );
		define( 'AXILTHEME_DEMO_HELPER_URL', 			plugin_dir_url( __FILE__ ) );
		define( 'AXILTHEME_DEMO_HELPER_ASSETS', 		trailingslashit( AXILTHEME_DEMO_HELPER_URL . 'assets' ) );			
	}

if ( is_admin() && !defined( 'FW' ) ) {
	require_once dirname(__FILE__) . '/unyson/framework/bootstrap.php';
	add_filter( 'fw_framework_directory_uri', 'axilthemes_fw_framework_directory_uri' );
	add_action( 'admin_menu',                 'axilthemes_remove_unyson_menus', 12 );
	add_action( 'network_admin_menu',         'axilthemes_remove_unyson_menus', 12 );
	add_action( 'after_setup_theme',          'axilthemes_remove_unyson_footer_version', 12 );
	add_action( 'admin_enqueue_scripts',      'axilthemes_fw_admin_styles', 20 );
	// skip image import
	//add_filter( 'fw:ext:backups:add-restore-task:image-sizes-restore', '__return_false' );
	add_action( 'plugins_loaded', 			  'axilthemes_unyson_demo_importer', 20 );	
	add_action( 'after_plugin_row_' . plugin_basename( __FILE__ ), 'show_demo_plugin_notification', 10, 3 );	
}

add_action( 'plugins_loaded', 	'axilthemes_elementor_textdomain', 16 );

function axilthemes_elementor_textdomain() {
		load_plugin_textdomain( 'axilthemesdemo', false, dirname( plugin_basename( __FILE__ ) ) . '/languages' ); 
	}

function axilthemes_fw_framework_directory_uri() {
	return plugin_dir_url( __FILE__ ) . 'unyson/framework';
}
 
function axilthemes_remove_unyson_menus() {
	remove_menu_page( 'fw-extensions' );
	 
	remove_submenu_page( 'tools.php', 'fw-backups' );
}
function axilthemes_remove_unyson_footer_version() {
	$axilthemes_obj = fw();
	remove_filter( 'update_footer', array( $axilthemes_obj->backend, '_filter_footer_version'), 11 );
}
function axilthemes_fw_admin_styles(){ 
	wp_enqueue_style('axilthemes-demo-helper',   AXILTHEME_DEMO_HELPER_ASSETS . '/demo.css');	
}
function axilthemes_unyson_demo_importer() {
	require_once 'unyson-demo-importer.php';
}

 
function show_demo_plugin_notification( $plugin_file, $plugin_data, $status ) {
	?>
    <tr class="plugin-update-tr">
        <td colspan="4">
            <div class="notice inline notice-warning notice-alt">
                <p>
                    <strong>
						<?php esc_html_e( "Please, deactivate and delete the plugin after demo content install.", 'axilthemesdemo' ); ?>
                    </strong>
                </p>
        </td>
    </tr>
	<?php
}

/**
 * Remove admin notices
 */
add_action( 'in_admin_header', 'remove_all_notices', 1000 );
function remove_all_notices() {
	$screen = get_current_screen();
	if ( isset( $screen->base ) && 'tools_page_fw-backups-demo-content' == $screen->base ) {
		remove_all_actions( 'admin_notices' );
		remove_all_actions( 'all_admin_notices' );
	}
}