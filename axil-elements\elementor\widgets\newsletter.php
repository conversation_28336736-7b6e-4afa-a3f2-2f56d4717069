<?php
/**
 * <AUTHOR>
 * @since   1.0
 * @version 1.0
 */

namespace axiltheme\etrade_elements;
use Elementor\Controls_Manager;
use Elementor\Icons_Manager;
use Elementor\Widget_Base;

if ( !defined( 'ABSPATH' ) ) {
    exit;
}
// Exit if accessed directly

class axil_newsletter extends Widget_Base {

    public function get_name() {
        return 'wooc-newsletter';
    }
    public function get_title() {
        return esc_html__( 'Newsletter Banner', 'etrade-elements' );
    }
    public function get_icon() {
        return 'eicon-banner';
    }
    public function get_categories() {
        return array( ETRADE_ELEMENTS_THEME_PREFIX . '-widgets' );
    }

    public function get_axil_contact_form() {
        if ( !class_exists( 'WPCF7' ) ) {
            return;
        }
        $axil_cfa = array();
        $axil_cf_args = array( 'posts_per_page' => -1, 'post_type' => 'wpcf7_contact_form' );
        $axil_forms = get_posts( $axil_cf_args );
        $axil_cfa = array( '0' => esc_html__( 'Select Form', 'etrade-elements' ) );
        if ( $axil_forms ) {
            foreach ( $axil_forms as $axil_form ) {
                $axil_cfa[$axil_form->ID] = $axil_form->post_title;
            }
        } else {
            $axil_cfa[esc_html__( 'No contact form found', 'etrade-elements' )] = 0;
        }
        return $axil_cfa;
    }

    protected function register_controls() {
        $this->start_controls_section(
            'info-layout',
            array(
                'label' => esc_html__( 'General', 'etrade-elements' ),
            )
        );
        $this->add_control(
            'beforetitlestyle',
            array(
                'label'   => esc_html__( 'Before Color', 'etrade-elements' ),
                'type'    => Controls_Manager::SELECT,
                'default' => 'primary',
                'options' => array(
                    'primary'   => esc_html__( 'Primary', 'etrade-elements' ),
                    'secondary' => esc_html__( 'Secondary', 'etrade-elements' ),
                    'primary2'  => esc_html__( 'Primary 2', 'etrade-elements' ),

                ),
            )
        );
        $this->add_control(
            'icon',
            array(
                'label'     => __( 'Icons', 'etrade-elements' ),
                'type'      => Controls_Manager::ICONS,
                'separator' => 'after',
                'default'   => array(
                    'value'   => 'fas fa-envelope-open',
                    'library' => 'solid',
                ),

            )
        );
        $this->add_control(
            'subtitle',
            array(
                'label'       => esc_html__( 'Before Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXTAREA,
                'default'     => esc_html__( 'Newsletter', 'etrade-elements' ),
                'placeholder' => esc_html__( 'Sub title', 'etrade-elements' ),
            )
        );

        $this->add_control(
            'title',
            array(
                'label'       => esc_html__( 'Title', 'etrade-elements' ),
                'type'        => Controls_Manager::TEXTAREA,
                'default'     => esc_html__( 'Get weekly update', 'etrade-elements' ),
                'placeholder' => esc_html__( 'Title', 'etrade-elements' ),
            )
        );
        $this->add_group_control(
            \Elementor\Group_Control_Background::get_type(),
            array(
                'name'     => 'background',
                'label'    => esc_html__( 'Background', 'etrade-elements' ),
                'types'    => array( 'classic', 'gradient', 'video' ),
                'selector' => '{{WRAPPER}} .etrade-newsletter-wrapper.bg_image ',
            )
        );

        $this->add_control(
            'select_contact_form',
            array(
                'label'     => esc_html__( 'Select Form', 'etrade-elements' ),
                'type'      => Controls_Manager::SELECT,
                'default'   => '0',
                'options'   => $this->get_axil_contact_form(),
                'separator' => 'before',
            )
        );

        $this->end_controls_section();

    }

    protected function render() {
        $settings = $this->get_settings();
        $allowed_tags = wp_kses_allowed_html( 'post' );
        ?>

		<div class="etrade-newsletter-wrapper bg_image bg_image--5">
		    <div class="newsletter-content">
		         <?php if ( $settings['subtitle'] ): ?>
	                <span class="title-highlighter highlighter-<?php echo esc_attr( $settings['beforetitlestyle'] ); ?>"><?php Icons_Manager::render_icon( $settings['icon'] );?><?php echo wp_kses( $settings['subtitle'], $allowed_tags ); ?></span>
	            <?php endif;?>
		        <h2 class="title mb--40 mb_sm--30"><?php echo esc_attr( $settings['title'] ); ?></h2>
		           <?php if ( !empty( $settings['select_contact_form'] ) ) {
            echo do_shortcode( '[contact-form-7  id="' . $settings['select_contact_form'] . '"]' );
        } else {
            echo '<div class="alert alert-info"><p>' . __( 'Please Select contact form.', 'etrade-elements' ) . '</p></div>';
        }
        ?>
		    </div>
		</div>
		<?php
}
}

?>
