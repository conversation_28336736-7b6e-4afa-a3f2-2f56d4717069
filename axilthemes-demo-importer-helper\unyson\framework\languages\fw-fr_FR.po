msgid ""
msgstr ""
"Project-Id-Version: Unyson\n"
"POT-Creation-Date: 2016-10-29 11:11+0200\n"
"PO-Revision-Date: 2016-10-30 02:11+0200\n"
"Language-Team: ThemeFuse <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.11\n"
"X-Poedit-KeywordsList: _:1;gettext:1;dgettext:2;ngettext:1,2;dngettext:2,3;"
"__:1;_e:1;_c:1;_n:1,2;_n_noop:1,2;_nc:1,2;__ngettext:1,2;__ngettext_noop:1,2;"
"_x:1,2c;_ex:1,2c;_nx:1,2,4c;_nx_noop:1,2,3c;_n_js:1,2;_nx_js:1,2,3c;"
"esc_attr__:1;esc_html__:1;esc_attr_e:1;esc_html_e:1;esc_attr_x:1,2c;"
"esc_html_x:1,2c;comments_number_link:2,3;t:1;st:1;trans:1;transChoice:1,2\n"
"X-Poedit-Basepath: .\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Last-Translator: \n"
"Language: fr_FR\n"
"X-Poedit-SearchPath-0: ..\n"

#: ../manifest.php:5
msgid "Unyson"
msgstr "Unyson"

#: ../helpers/class-fw-wp-filesystem.php:43
msgid "Cannot connect to Filesystem directly"
msgstr "Impossible de se connecter directement au système de fichiers"

#: ../helpers/class-fw-wp-filesystem.php:271
#, php-format
msgid "Cannot create directory \"%s\". It must be inside \"%s\""
msgstr ""
"Vous ne pouvez pas créer le répertoire \"%s\". Il doit être à l'intérieur de "
"\"%s\""

#: ../helpers/class-fw-wp-filesystem.php:273
msgid "\" or \""
msgstr "\" or \""

#: ../helpers/class-fw-flash-messages.php:95
#, php-format
msgid "Invalid flash message type: %s"
msgstr "Type de message flash invalide : %s"

#: ../helpers/class-fw-wp-list-table.php:185
msgid "No items found."
msgstr "Aucun élément trouvé."

#: ../helpers/class-fw-wp-list-table.php:309
msgid "Bulk Actions"
msgstr "Actions en masse"

#: ../helpers/class-fw-wp-list-table.php:319
msgid "Apply"
msgstr "Appliquer"

#: ../helpers/class-fw-wp-list-table.php:403
msgid "All dates"
msgstr "Toutes les dates"

#: ../helpers/class-fw-wp-list-table.php:416
#, php-format
msgid "%1$s %2$d"
msgstr "%1$s %2$d"

#: ../helpers/class-fw-wp-list-table.php:432
msgid "List View"
msgstr "Liste des vues"

#: ../helpers/class-fw-wp-list-table.php:433
msgid "Excerpt View"
msgstr "Résumer des vues"

#: ../helpers/class-fw-wp-list-table.php:459
#, php-format
msgid "%s pending"
msgstr "%s en cours"

#: ../helpers/class-fw-wp-list-table.php:714
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:33
msgid "Select All"
msgstr "Tout sélectionner"

#: ../helpers/general.php:1150
msgid "year"
msgstr "année"

#: ../helpers/general.php:1151
msgid "years"
msgstr "années"

#: ../helpers/general.php:1153
msgid "month"
msgstr "mois"

#: ../helpers/general.php:1154
msgid "months"
msgstr "mois"

#: ../helpers/general.php:1156
msgid "week"
msgstr "semaine"

#: ../helpers/general.php:1157
msgid "weeks"
msgstr "semaines"

#: ../helpers/general.php:1159
msgid "day"
msgstr "jour"

#: ../helpers/general.php:1160
msgid "days"
msgstr "jours"

#: ../helpers/general.php:1162
msgid "hour"
msgstr "heure"

#: ../helpers/general.php:1163
msgid "hours"
msgstr "heures"

#: ../helpers/general.php:1165
msgid "minute"
msgstr "minute"

#: ../helpers/general.php:1166
msgid "minutes"
msgstr "minutes"

#: ../helpers/general.php:1168
msgid "second"
msgstr "seconde"

#: ../helpers/general.php:1169
msgid "seconds"
msgstr "secondes"

#: ../helpers/general.php:1558
msgid "Maximum stack depth exceeded"
msgstr "Le nombre maximal a été dépassé"

#: ../helpers/general.php:1561
msgid "Underflow or the modes mismatch"
msgstr "Soupassement ou non-concordance des modes"

#: ../helpers/general.php:1564
msgid "Unexpected control character found"
msgstr "Caractère de contrôle inattendue trouvé"

#: ../helpers/general.php:1567
msgid "Syntax error, malformed JSON"
msgstr "Erreur de syntaxe, JSON malformé"

#: ../helpers/general.php:1570
msgid "Malformed UTF-8 characters, possibly incorrectly encoded"
msgstr "Caractères UTF-8 malformés, peut-être est-il mal codé"

#: ../helpers/general.php:1573
#: ../extensions/backups/class-fw-extension-backups.php:550
msgid "Unknown error"
msgstr "Erreur inconnu:"

#: ../helpers/class-fw-form.php:80
#, php-format
msgid "Form with id \"%s\" was already defined"
msgstr "Formulaire avec id \"%s\" a été déjà défini"

#: ../helpers/class-fw-form.php:168
msgid "Nonce verification failed"
msgstr "La vérification a échoué"

#: ../helpers/class-fw-form.php:331
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/views/form.php:13
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:66
msgid "Submit"
msgstr "Soumettre"

#: ../extensions/update/class-fw-extension-update.php:285
#: ../extensions/update/class-fw-extension-update.php:602
msgid "Cannot delete: "
msgstr "Vous ne pouvez pas supprimer :"

#: ../extensions/update/class-fw-extension-update.php:292
msgid "Cannot create: "
msgstr "Vous ne pouvez pas créer :"

#: ../extensions/update/class-fw-extension-update.php:370
msgid "Cannot remove old temporary directory: "
msgstr "Impossible de supprimer le répertoire temporaire :"

#: ../extensions/update/class-fw-extension-update.php:376
#: ../extensions/update/class-fw-extension-update.php:572
msgid "Cannot create directory: "
msgstr "Vous ne pouvez pas créer le répertoire :"

#: ../extensions/update/class-fw-extension-update.php:381
#, php-format
msgid "Downloading the %s..."
msgstr "Téléchargement du %s ..."

#: ../extensions/update/class-fw-extension-update.php:386
#, php-format
msgid "Cannot download the %s."
msgstr "Vous ne pouvez pas télécharger %s."

#: ../extensions/update/class-fw-extension-update.php:396
#, php-format
msgid "Installing the %s..."
msgstr "Installation de %s ..."

#: ../extensions/update/class-fw-extension-update.php:402
#: ../extensions/update/class-fw-extension-update.php:431
#: ../extensions/update/class-fw-extension-update.php:531
#: ../extensions/update/class-fw-extension-update.php:552
#: ../extensions/update/class-fw-extension-update.php:582
msgid "Cannot access directory: "
msgstr "Impossible d'accéder au répertoire :"

#: ../extensions/update/class-fw-extension-update.php:421
msgid "Cannot remove: "
msgstr "Impossible de supprimer le répertoire :"

#: ../extensions/update/class-fw-extension-update.php:456
#: ../extensions/update/class-fw-extension-update.php:522
#: ../extensions/update/class-fw-extension-update.php:628
#: ../extensions/update/class-fw-extension-update.php:652
#, php-format
msgid "Cannot move \"%s\" to \"%s\""
msgstr "Impossible de déplacer \"%s\" à \"%s\""

#: ../extensions/update/class-fw-extension-update.php:472
#, php-format
msgid "Cannot merge \"%s\" with \"%s\""
msgstr "Impossible de dupliquer \"%s\" avec \"%s\""

#: ../extensions/update/class-fw-extension-update.php:485
#, php-format
msgid "The %s has been successfully updated."
msgstr "Le %s a été mis à jour avec succès."

#: ../extensions/update/class-fw-extension-update.php:492
#, php-format
msgid "Cannot remove temporary directory \"%s\"."
msgstr "Impossible de supprimer le répertoire temporaire \"%s\"."

#: ../extensions/update/class-fw-extension-update.php:672
#: ../extensions/update/class-fw-extension-update.php:740
#: ../extensions/update/class-fw-extension-update.php:808
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:930
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1427
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1837
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2054
msgid "Invalid nonce."
msgstr "Non valide."

#: ../extensions/update/class-fw-extension-update.php:683
msgid "Framework Update"
msgstr "Mise à jour du Framework"

#: ../extensions/update/class-fw-extension-update.php:699
msgid "Failed to get framework latest version."
msgstr "Impossible d'obtenir la dernière version de la structure."

#: ../extensions/update/class-fw-extension-update.php:716
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:360
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:374
#: ../extensions/update/views/updates-list.php:10
#: ../core/class-fw-manifest.php:353
msgid "Framework"
msgstr "Framework"

#: ../extensions/update/class-fw-extension-update.php:751
msgid "Theme Update"
msgstr "Mise à jour du thème"

#: ../extensions/update/class-fw-extension-update.php:767
msgid "Failed to get theme latest version."
msgstr "Impossible d'obtenir le dernière version du thème."

#: ../extensions/update/class-fw-extension-update.php:784
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:393
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:407
msgid "Theme"
msgstr "Thème"

#: ../extensions/update/class-fw-extension-update.php:817
msgid "Please check the extensions you want to update."
msgstr ""
"S'il vous plaît vérifier les extensions que vous souhaitez mettre à jour."

#: ../extensions/update/class-fw-extension-update.php:841
msgid "Extensions Update"
msgstr "Mise à jour des extensions"

#: ../extensions/update/class-fw-extension-update.php:879
msgid "No extensions updates found."
msgstr "Aucune extension trouvée."

#: ../extensions/update/class-fw-extension-update.php:886
#, php-format
msgid "Extension \"%s\" does not exist or is disabled."
msgstr "Extension \"%s\" n'existe pas ou est désactivé."

#: ../extensions/update/class-fw-extension-update.php:893
#, php-format
msgid "No update found for the \"%s\" extension."
msgstr "Aucune mise à jour trouvée pour l'extension \"%s\"."

#: ../extensions/update/class-fw-extension-update.php:915
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:426
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:440
#, php-format
msgid "%s extension"
msgstr "%s extension"

#: ../extensions/update/manifest.php:5
#: ../extensions/builder/includes/option-types/builder/includes/fullscreen.php:82
msgid "Update"
msgstr "Mettre à jour"

#: ../extensions/update/manifest.php:6
msgid "Keep you framework, extensions and theme up to date."
msgstr "Gardez vous cadres, extensions et thèmes à jour."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:91
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:232
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2439
msgid "Github error:"
msgstr "Erreur Github"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:100
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:241
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2446
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (Response code: %d)"
msgstr ""
"Impossible d'accéder au répertoire de référence GitHub \"%s\". (Code de "
"réponse  :%d)"

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:108
#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:249
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2462
#, php-format
msgid "Failed to access Github repository \"%s\" releases."
msgstr "Impossible d'accéder au répertoire de référence GitHub \"%s\". "

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:122
#, php-format
msgid "No releases found in repository \"%s\"."
msgstr "Aucun parution trouvés dans le dépôt \"% s\"."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:143
#, php-format
msgid ""
"%s manifest has invalid \"github_update\" parameter. Please use \"user/repo"
"\" format."
msgstr ""
"Le manifeste %s de paramètre non valide a \"github_uptade\". S'il vous plaît "
"utiliser \"user/repo\" format."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:171
#, php-format
msgid "Failed to fetch %s latest version from github \"%s\"."
msgstr "Impossible de récupérer la dernière version de %s de github \"%s\"."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:264
#, php-format
msgid "%s github repository \"%s\" does not have the \"%s\" release."
msgstr "%s du répertoire GitHub \"%s\" n'a pas la référence \"%s\"."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:284
#, php-format
msgid "Cannot download %s zip."
msgstr "Vous ne pouvez pas télécharger le zip : %s ."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:297
#, php-format
msgid "Cannot save %s zip."
msgstr "Vous ne pouvez pas sauvegarder le zip : %s ."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:316
#, php-format
msgid "Cannot remove %s zip."
msgstr "Vous ne pouvez pas supprimer le zip : %s ."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:325
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2566
msgid "Cannot access the unzipped directory files."
msgstr "Vous ne pouvez pas accéder aux fichiers du répertoire décompressés."

#: ../extensions/update/extensions/github-update/class-fw-extension-github-update.php:341
#, php-format
msgid "The unzipped %s directory not found."
msgstr "Le répertoire décompressé  du %s  n'est pas trouvé."

#: ../extensions/update/views/updates-list.php:12
#, php-format
msgid "You have the latest version of %s."
msgstr "Vous avez la dernière version de %s."

#: ../extensions/update/views/updates-list.php:25
msgid "Update Framework"
msgstr "Mettre à jour le Framework"

#: ../extensions/update/views/updates-list.php:37
msgid "Your theme is up to date."
msgstr "Votre thème est à jour."

#: ../extensions/update/views/updates-list.php:50
msgid "Update Theme"
msgstr "Mise à jour du Thème"

#: ../extensions/update/views/updates-list.php:60
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:844
#, php-format
msgid "%s Extensions"
msgstr "%s Extensions"

#: ../extensions/update/views/updates-list.php:62
#, php-format
msgid "You have the latest version of %s Extensions."
msgstr "Vous avez la dernière version de l'extensions : %s."

#: ../extensions/update/views/updates-list.php:80
#: ../extensions/update/views/updates-list.php:95
#: ../extensions/update/views/updates-list.php:100
msgid "Update Extensions"
msgstr "Mettre à jour les extensions"

#: ../extensions/update/views/updates-list.php:99
msgid "New extensions updates available."
msgstr "De nouvelles mises à jours de l'extensions sont disponibles."

#: ../extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:16
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:14
#: ../extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:16
msgid "Go to updates page"
msgstr "Aller à la page des mises à jour"

#: ../extensions/update/includes/classes/class--fw-ext-update-theme-upgrader-skin.php:19
#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-upgrader-skin.php:17
#: ../extensions/update/includes/classes/class--fw-ext-update-framework-upgrader-skin.php:19
msgid "Return to Updates page"
msgstr "Retour à la page des mises à jour"

#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:84
#, php-format
msgid "You have version %s installed. Update to %s."
msgstr "Vous avez la version %s est installé. Mise à jour de %s."

#: ../extensions/update/includes/classes/class--fw-ext-update-extensions-list-table.php:126
msgid "No Extensions for update."
msgstr "Aucune mise à jour des extensions"

#: ../extensions/portfolio/manifest.php:7
#: ../extensions/portfolio/class-fw-extension-portfolio.php:260
#: ../core/components/extensions/manager/available-extensions.php:72
msgid "Portfolio"
msgstr "Portfolio"

#: ../extensions/portfolio/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:73
msgid ""
"This extension will add a fully fledged portfolio module that will let you "
"display your projects using the built in portfolio pages."
msgstr ""
"Cette extension ajoutera un module de portfolio à part entière qui vous "
"permettra d'afficher vos projets en utilisant le haut dans les pages du "
"portfolio."

#: ../extensions/portfolio/class-fw-extension-portfolio.php:115
#: ../extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Project"
msgstr "Projet"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:116
msgid "Projects"
msgstr "projets"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:123
#: ../extensions/learning/class-fw-extension-learning.php:63
#: ../extensions/learning/class-fw-extension-learning.php:127
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:57
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:58
#: ../extensions/events/class-fw-extension-events.php:76
#: ../extensions/media/extensions/slider/posts.php:8
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:32
msgid "Add New"
msgstr "Ajouter un projet"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:124
#: ../extensions/portfolio/class-fw-extension-portfolio.php:198
#: ../extensions/learning/class-fw-extension-learning.php:64
#: ../extensions/learning/class-fw-extension-learning.php:128
#: ../extensions/events/class-fw-extension-events.php:77
#: ../extensions/events/class-fw-extension-events.php:131
#, php-format
msgid "Add New %s"
msgstr "Ajouter un nouveau %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:125
#: ../extensions/learning/class-fw-extension-learning.php:129
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:89
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:76
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:88
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:86
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:68
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:34
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:50
#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:110
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:45
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:37
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:73
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:81
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:57
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:50
#: ../extensions/events/class-fw-extension-events.php:78
#: ../includes/option-types/popup/class-fw-option-type-popup.php:158
#: ../includes/option-types/upload/class-fw-option-type-upload.php:151
#: ../includes/option-types/upload/class-fw-option-type-upload.php:177
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:161
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:187
msgid "Edit"
msgstr "Editer"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:126
#: ../extensions/portfolio/class-fw-extension-portfolio.php:196
#: ../extensions/learning/class-fw-extension-learning.php:65
#: ../extensions/learning/class-fw-extension-learning.php:66
#: ../extensions/learning/class-fw-extension-learning.php:130
#: ../extensions/learning/class-fw-extension-learning.php:192
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:59
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:60
#: ../extensions/events/class-fw-extension-events.php:79
#: ../extensions/events/class-fw-extension-events.php:129
#, php-format
msgid "Edit %s"
msgstr "Editer %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:127
#: ../extensions/learning/class-fw-extension-learning.php:67
#: ../extensions/learning/class-fw-extension-learning.php:131
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:61
#: ../extensions/events/class-fw-extension-events.php:80
#, php-format
msgid "New %s"
msgstr "Nouveau %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:128
#: ../extensions/portfolio/class-fw-extension-portfolio.php:193
#: ../extensions/learning/class-fw-extension-learning.php:68
#: ../extensions/learning/class-fw-extension-learning.php:132
#: ../extensions/learning/class-fw-extension-learning.php:189
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:62
#: ../extensions/events/class-fw-extension-events.php:81
#: ../extensions/events/class-fw-extension-events.php:126
#, php-format
msgid "All %s"
msgstr "Tout les %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:129
#: ../extensions/portfolio/class-fw-extension-portfolio.php:130
#: ../extensions/learning/class-fw-extension-learning.php:69
#: ../extensions/learning/class-fw-extension-learning.php:70
#: ../extensions/learning/class-fw-extension-learning.php:133
#: ../extensions/learning/class-fw-extension-learning.php:134
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:63
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:64
#: ../extensions/events/class-fw-extension-events.php:82
#: ../extensions/events/class-fw-extension-events.php:83
#, php-format
msgid "View %s"
msgstr "Voir %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:131
#: ../extensions/portfolio/class-fw-extension-portfolio.php:192
#: ../extensions/learning/class-fw-extension-learning.php:71
#: ../extensions/learning/class-fw-extension-learning.php:135
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:65
#: ../extensions/events/class-fw-extension-events.php:84
#: ../extensions/events/class-fw-extension-events.php:125
#, php-format
msgid "Search %s"
msgstr "Rechercher %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:132
#: ../extensions/learning/class-fw-extension-learning.php:72
#: ../extensions/learning/class-fw-extension-learning.php:136
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:66
#: ../extensions/events/class-fw-extension-events.php:85
#, php-format
msgid "No %s Found"
msgstr "Aucun %s trouvé"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:133
#: ../extensions/learning/class-fw-extension-learning.php:73
#: ../extensions/learning/class-fw-extension-learning.php:137
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:67
#: ../extensions/events/class-fw-extension-events.php:86
#, php-format
msgid "No %s Found In Trash"
msgstr "Aucun %s trouvé dans la poubelle"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:136
msgid "Create a portfolio item"
msgstr "Créer un projet"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:183
#: ../extensions/events/class-fw-extension-events.php:116
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:268
msgid "Category"
msgstr "Catégorie"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:184
#: ../extensions/events/class-fw-extension-events.php:117
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:168
msgid "Categories"
msgstr "Categories"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:194
#: ../extensions/learning/class-fw-extension-learning.php:190
#: ../extensions/events/class-fw-extension-events.php:127
#, php-format
msgid "Parent %s"
msgstr "Parent %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:195
#: ../extensions/learning/class-fw-extension-learning.php:191
#: ../extensions/events/class-fw-extension-events.php:128
#, php-format
msgid "Parent %s:"
msgstr "Parent %s :"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:197
#: ../extensions/learning/class-fw-extension-learning.php:193
#: ../extensions/events/class-fw-extension-events.php:130
#: ../core/components/extensions/manager/views/extension.php:234
#: ../core/components/extensions/manager/views/extension.php:272
#, php-format
msgid "Update %s"
msgstr "Mise à jour %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:199
#: ../extensions/learning/class-fw-extension-learning.php:195
#: ../extensions/events/class-fw-extension-events.php:132
#, php-format
msgid "New %s Name"
msgstr "Nouveau nom de %s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:200
#: ../extensions/learning/class-fw-extension-learning.php:196
#: ../extensions/events/class-fw-extension-events.php:133
#, php-format
msgid "%s"
msgstr "%s"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:232
msgid "Gallery"
msgstr "Galerie"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:240
msgid "Set project gallery"
msgstr "Régler la galerie du projet"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:241
msgid "Edit project gallery"
msgstr "Editer la galerie du projet"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:271
msgid "Project Cover Image"
msgstr "Image de couverture du projet"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:287
msgid "Edit this item"
msgstr "Editer cet objet"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:348
#: ../extensions/portfolio/class-fw-extension-portfolio.php:361
#: ../extensions/learning/class-fw-extension-learning.php:320
#: ../extensions/learning/class-fw-extension-learning.php:333
msgid "View all categories"
msgstr "Voir toutes les categories"

#: ../extensions/portfolio/class-fw-extension-portfolio.php:381
msgid "Cover Image"
msgstr "Image de couverture"

#: ../extensions/seo/settings-options.php:17
#: ../extensions/social/settings-options.php:11
msgid "General"
msgstr "Général"

#: ../extensions/seo/settings-options.php:21
#: ../extensions/social/settings-options.php:15
msgid "General Settings"
msgstr "Réglages généraux"

#: ../extensions/seo/class-fw-extension-seo.php:89
msgid "Site name"
msgstr "Nom du site"

#: ../extensions/seo/class-fw-extension-seo.php:95
msgid "Site description"
msgstr "Description du site"

#: ../extensions/seo/class-fw-extension-seo.php:101
msgid "Current time"
msgstr "Heure actuelle"

#: ../extensions/seo/class-fw-extension-seo.php:107
msgid "Current date"
msgstr "Date actuelle"

#: ../extensions/seo/class-fw-extension-seo.php:113
msgid "Current month"
msgstr "Mois en cours"

#: ../extensions/seo/class-fw-extension-seo.php:119
msgid "Current year"
msgstr "Année en cours"

#: ../extensions/seo/class-fw-extension-seo.php:125
msgid "Date of the post/page"
msgstr "Date de l'article/page"

#: ../extensions/seo/class-fw-extension-seo.php:131
msgid "Title of the post/page/term"
msgstr "Titre de l'article/page/terme"

#: ../extensions/seo/class-fw-extension-seo.php:137
msgid "Excerpt of the current post, of auto-generate if it is not set"
msgstr "Extrait du poste actuel, de l'auto-généré si elle n'est pas réglé"

#: ../extensions/seo/class-fw-extension-seo.php:143
msgid "Excerpt of the current post, without auto-generation"
msgstr "Extrait du poste actuel, sans auto-génération"

#: ../extensions/seo/class-fw-extension-seo.php:149
msgid "Post tags, separated by coma"
msgstr "Etiquettes, séparés par des virgules"

#: ../extensions/seo/class-fw-extension-seo.php:155
msgid "Post categories, separated by coma"
msgstr "Catégories des articles, séparés par des virgules"

#: ../extensions/seo/class-fw-extension-seo.php:161
msgid "Category/tag/term description"
msgstr "Catégorie/étiquette/description du terme"

#: ../extensions/seo/class-fw-extension-seo.php:167
msgid "Term title"
msgstr "Terme du titre"

#: ../extensions/seo/class-fw-extension-seo.php:173
msgid "Post modified time"
msgstr "Heure du message modifié"

#: ../extensions/seo/class-fw-extension-seo.php:179
msgid "Post/page id"
msgstr "Article/page id"

#: ../extensions/seo/class-fw-extension-seo.php:185
msgid "Post/page author \"nicename\""
msgstr "Article/page de l'auteur \"surnom\""

#: ../extensions/seo/class-fw-extension-seo.php:191
msgid "Post/page author id"
msgstr "Article/page de l'identifiant de l'auteur"

#: ../extensions/seo/class-fw-extension-seo.php:197
msgid "Search phrase in search page"
msgstr "Recherche la phrase dans la page de recherche"

#: ../extensions/seo/class-fw-extension-seo.php:203
#: ../extensions/seo/class-fw-extension-seo.php:209
msgid "Page number"
msgstr "Numéro de page"

#: ../extensions/seo/class-fw-extension-seo.php:215
msgid "Attachment caption"
msgstr "Légende de la pièce jointe"

#: ../extensions/seo/class-fw-extension-seo.php:435
#: ../extensions/seo/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:120
msgid "SEO"
msgstr "Référencement - SEO"

#: ../extensions/seo/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:121
msgid ""
"This extension will enable you to have a fully optimized WordPress website "
"by adding optimized meta titles, keywords and descriptions."
msgstr ""
"Cette extension vous permettra d'avoir un site WordPress entièrement "
"optimisé en ajoutant des méta titres, mots clés et descriptions."

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:22
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:310
msgid "Titles & Meta"
msgstr "Titres & Meta"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:26
#: ../extensions/breadcrumbs/settings-options.php:6
#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:9
msgid "Homepage"
msgstr "Page d'accueil"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:30
msgid "Homepage Title"
msgstr "Titre de la page d'accueil"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:31
msgid "Set homepage title format"
msgstr "Définir le format de titre de la page d'accueil"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:36
msgid "Homepage Description"
msgstr "Description de la page d'accueil"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:37
msgid "Set homepage description"
msgstr "Définir la description page d'accueil"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:44
msgid "Homepage Meta Keywords"
msgstr "Meta keywords de la page d'accueil"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:45
msgid "Set homepage meta keywords"
msgstr "Définir les meta keywords de la page d'accueil"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:54
msgid "Custom Posts"
msgstr "Messages personnalisés"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:59
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:111
#: ../extensions/learning/includes/class-fw-widget-learning.php:120
#: ../extensions/shortcodes/shortcodes/testimonials/options.php:7
#: ../extensions/shortcodes/shortcodes/accordion/options.php:15
#: ../extensions/shortcodes/shortcodes/icon/options.php:12
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:8
#: ../extensions/shortcodes/shortcodes/tabs/options.php:15
#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:56
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:480
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:62
msgid "Title"
msgstr "Titre"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:60
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:112
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:287
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:315
msgid "Set title format"
msgstr "Configurer le format du titre"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:63
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:74
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:87
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:115
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:126
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:139
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:247
msgid "Here are some tags examples:"
msgstr "voici quelques exemples de tags:"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:70
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:122
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:320
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:66
msgid "Description"
msgstr "Description"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:71
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:123
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:293
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:321
msgid "Set description format"
msgstr "Configurer le format de la description"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:83
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:135
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:328
msgid "Meta Keywords"
msgstr "Meta Keywords"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:84
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:136
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:329
msgid "Set meta keywords"
msgstr "Configurer les meta keywords"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:96
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:148
msgid "Meta Robots"
msgstr "Meta Robots"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:97
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:149
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:188
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:227
msgid "noindex, follow"
msgstr "noindex, follow"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:105
msgid "Taxonomies"
msgstr "Taxonomies"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:158
msgid "Other"
msgstr "Autres"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:165
msgid "Author Page Title"
msgstr "Titre de la page d'auteur"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:166
msgid "Set author page title format"
msgstr "Configurer le format du titre de la page d'auteur"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:171
msgid "Author Page Description"
msgstr "Description de l'auteur"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:172
msgid "Set author page description"
msgstr "Page de description de l'auteur"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:179
msgid "Author Meta Keywords"
msgstr "Meta de l'auteur"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:180
msgid "Set author page meta keywords"
msgstr "Configurer les meta keywords de la page auteur"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:187
#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:226
msgid "Metarobots"
msgstr "Metarobots"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:193
msgid "Disable Author Archives"
msgstr "Désactiver les archives de l'auteur"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:194
msgid "Disable Author archives SEO settings"
msgstr "Désactiver les paramètres SEO des archives d'auteur"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:204
msgid "Date Achieves Title"
msgstr "Date de titre atteint"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:205
msgid "Set date achieves title format"
msgstr "Définir la date atteint le format de titre"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:210
msgid "Date Achieves Description"
msgstr "Date de description atteint"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:211
msgid "Set date achieves description"
msgstr "Définir la date atteint la description"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:218
msgid "Date achieves Meta Keywords"
msgstr "Date de réalise des Meta Keywords"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:219
msgid "Set date achieves meta keywords"
msgstr "Définir la date atteint les meta keywords"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:232
msgid "Disable Date Archives"
msgstr "Date d'archives désactiver"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:233
msgid "Disable date archives SEO settings"
msgstr "Désactiver les paramètres de SEO des archives des dates"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:243
msgid "Search Page Title"
msgstr "Titre de la page Recherche"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:244
msgid "Set search page title format"
msgstr "Configurer le format du titre de la page 404 "

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:259
msgid "404 Page Title"
msgstr "Titre de la page 404"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:260
msgid "Set 404 page title format"
msgstr "Configurer le format du titre de la page 404 "

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:262
#: ../extensions/breadcrumbs/settings-options.php:8
msgid "404 Not Found"
msgstr "404 Aucun élément trouvé."

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:286
msgid "SEO Title"
msgstr "Titre SEO"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:292
msgid "SEO Description"
msgstr "SEO Description"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:314
msgid "Page Title"
msgstr "Titre de la page"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:356
msgid "Use Meta Keywords"
msgstr "Utilisez Meta Keywords"

#: ../extensions/seo/extensions/seo-titles-metas/includes/fw-title-meta-options.php:357
msgid "Allow the use of meta keywords in posts and taxonomies"
msgstr ""
"Autoriser l'utilisation des meta keywords dans les postes et les taxonomies"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:33
msgid "Google Webmasters"
msgstr "Google Webmasters"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:34
msgid "Insert Google Webmasters verification code"
msgstr "Insérez le code de vérification - Google Webmasters"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:41
msgid "Bing Webmasters"
msgstr "Bing Webmasters"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:42
msgid "Insert Bing Webmasters verification code"
msgstr "Insérez le code de vérification - Bing"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:80
msgid "Webmasters"
msgstr "Webmasters"

#: ../extensions/seo/extensions/seo-webmasters/class-fw-extension-seo-webmasters.php:89
#, php-format
msgid "Webmaster %s already exists"
msgstr "Webmasters %s existe déjà"

#: ../extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:634
msgid "Google"
msgstr "Google"

#: ../extensions/seo/extensions/seo-sitemap/class-fw-extension-seo-sitemap.php:638
msgid "Bing"
msgstr "Bing"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:19
msgid "Check if you want to exclude this page"
msgstr "Vérifiez si vous voulez exclure cette page"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:40
msgid "Check if you want to exclude this category"
msgstr "Vérifiez si vous voulez exclure cette catégorie"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:57
msgid "Sitemap"
msgstr "Plan du site"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:68
msgid "View Sitemap"
msgstr "Voir le plan du site"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:69
msgid "Press button to view sitemap file"
msgstr "Appuyez sur le bouton pour voir le fichier du plan du site"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:71
msgid "XML Sitemap"
msgstr "XML - Plan du site"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:81
msgid "Search Engines"
msgstr "Moteurs de recherche"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:83
msgid "After adding content the extension will automatically ping to:"
msgstr ""
"Après l'ajout de contenu de l'extension sera automatiquement un ping à:"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:93
msgid "Exclude Pages"
msgstr "Exclure les pages"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:95
msgid "Please check the pages you do not want to include in sitemap"
msgstr ""
"S'il vous plaît, vérifier les pages que vous ne voulez pas inclure dans "
"sitemap"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:106
msgid "Exclude Categories"
msgstr "Exclure les catégories"

#: ../extensions/seo/extensions/seo-sitemap/includes/fw-sitemap-options.php:108
msgid "Please check the categories you do not want to include in sitemap"
msgstr ""
"S'il vous plaît vérifier les catégories que vous ne voulez pas inclure dans "
"sitemap"

#: ../extensions/mailer/manifest.php:5
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:187
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:188
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:103
#: ../core/components/extensions/manager/available-extensions.php:240
msgid "Mailer"
msgstr "Mailer"

#: ../extensions/mailer/manifest.php:6
#: ../core/components/extensions/manager/available-extensions.php:241
msgid ""
"This extension will let you set some global email options and it is used by "
"other extensions (like Forms) to send emails."
msgstr ""
"Cette extension vous permet de définir des options de messagerie "
"électronique et il est utilisé par d'autres extensions (comme Forms) pour "
"envoyer des emails."

#: ../extensions/mailer/class-fw-extension-mailer.php:48
msgid "Invalid send method"
msgstr "Méthode d'envoi non valide"

#: ../extensions/mailer/class-fw-extension-mailer.php:81
msgid "The message has been successfully sent!"
msgstr "Le message a été envoyé avec succès !"

#: ../extensions/mailer/includes/class-mailer-sender.php:27
msgid "Invalid email configuration"
msgstr "Configuration mail non valide"

#: ../extensions/mailer/includes/class-mailer-sender.php:145
#: ../extensions/mailer/includes/class-mailer-sender.php:161
msgid "Email sent"
msgstr "Email envoyé"

#: ../extensions/mailer/includes/class-mailer-sender.php:146
msgid "Could not send via smtp"
msgstr "Impossible d'envoyer via smtp"

#: ../extensions/mailer/includes/class-mailer-sender.php:162
msgid "Could not send via wp_mail"
msgstr "Impossible d'envoyer via wp_mail"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:25
msgid "Server Address"
msgstr "Adresse du serveur"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:26
msgid "Enter your email server"
msgstr "Entrer l'adresse de messagerie de votre serveur"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:31
msgid "Username"
msgstr "Prénom"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:32
msgid "Enter your username"
msgstr "Entrez votre nom d'utilisateur"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:37
msgid "Password"
msgstr "Mot de passe"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:38
msgid "Enter your password"
msgstr "Entrer votre mot de passe"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:43
msgid "Secure Connection"
msgstr "Connexion sécurisée"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:54
msgid "Custom Port"
msgstr "Port personnalisée"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:55
msgid "Optional - SMTP port number to use."
msgstr "Facultatif - numéro de port SMTP à utiliser."

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:56
msgid "Leave blank for default (SMTP - 25, SMTPS - 465)"
msgstr "Laissez vide par défaut (SMTP - 25, SMTPS - 465)"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:82
msgid "Username cannot be empty"
msgstr "Nom d'utilisateur ne peut pas être vide"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:89
msgid "Password cannot be empty"
msgstr "Mot de passe ne peut pas être vide"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:96
msgid "Invalid host"
msgstr "Hôte non valide"

#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-smtp.php:176
#: ../extensions/mailer/includes/send-methods/class-fw-ext-mailer-send-method-wpmail.php:56
msgid "Could not send the email"
msgstr "Impossible d'envoyer l'e-mail"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:76
msgid "Send Method"
msgstr "Méthode d'envoi"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:77
msgid "Select the send form method"
msgstr "Sélectionnez la méthode du formulaire d'envoi"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:94
msgid "From Name"
msgstr "Du nom"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:95
msgid "The name you'll see in the From filed in your email client."
msgstr "Le nom que vous verrez dans le champ dans votre client de messagerie."

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:102
msgid "From Address"
msgstr "De l'adresse"

#: ../extensions/mailer/includes/option-type-mailer/class-fw-option-type-mailer.php:103
msgid "The form will look like was sent from this email address."
msgstr ""
"La formulaire ressemblera a celui envoyé à partir de cette adresse e-mail."

#: ../extensions/learning/class-fw-extension-learning.php:56
msgid "Lesson"
msgstr "Leçon"

#: ../extensions/learning/class-fw-extension-learning.php:57
#: ../extensions/learning/views/content-course.php:14
msgid "Lessons"
msgstr "Leçons"

#: ../extensions/learning/class-fw-extension-learning.php:78
msgid "Create a lesson"
msgstr "Créer une leçon"

#: ../extensions/learning/class-fw-extension-learning.php:120
#: ../extensions/learning/hooks.php:53
msgid "Course"
msgstr "Cours"

#: ../extensions/learning/class-fw-extension-learning.php:121
#: ../extensions/learning/includes/class-fw-widget-learning.php:108
msgid "Courses"
msgstr "Cours"

#: ../extensions/learning/class-fw-extension-learning.php:142
msgid "Create a course"
msgstr "Créer un cours"

#: ../extensions/learning/class-fw-extension-learning.php:181
#: ../extensions/learning/class-fw-extension-learning.php:222
msgid "Course Category"
msgstr "Catégorie de cours"

#: ../extensions/learning/class-fw-extension-learning.php:182
#: ../extensions/learning/class-fw-extension-learning.php:223
msgid "Course Categories"
msgstr "Catégories de cours"

#: ../extensions/learning/class-fw-extension-learning.php:188
msgid "Search categories"
msgstr "Rechercher les catégories"

#: ../extensions/learning/class-fw-extension-learning.php:194
msgid "Add New category"
msgstr "Ajouter une nouvelle catégorie"

#: ../extensions/learning/class-fw-extension-learning.php:285
msgid "View all courses"
msgstr "Voir tous les cours"

#: ../extensions/learning/class-fw-extension-learning.php:511
msgid "No courses available"
msgstr "Aucun cours disponible"

#: ../extensions/learning/class-fw-extension-learning.php:513
msgid "Without Course"
msgstr "Sans cours"

#: ../extensions/learning/class-fw-extension-learning.php:520
msgid "Select Course"
msgstr "Sélectionner le cours"

#: ../extensions/learning/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:216
msgid "Learning"
msgstr "Apprentissage"

#: ../extensions/learning/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:217
msgid ""
"This extension adds a Learning module to your theme. Using this extension "
"you can add courses, lessons and tests for your users to take."
msgstr ""
"Cette extension ajoute un module d'apprentissage à votre thème. En utilisant "
"cette extension, vous pouvez ajouter des cours, des leçons et des tests pour "
"vos utilisateurs à prendre."

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:118
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:145
msgid "Quiz"
msgstr "Quiz"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:259
msgid "Quiz Elements"
msgstr "Eléments de quiz"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:272
msgid "Quiz settings"
msgstr "Réglages du quiz"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:276
msgid "Quiz Passmark Points"
msgstr "Points d'évaluation du quiz"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:278
msgid "The points number at which the test will be passed."
msgstr "Le nombre de points où le test sera passé."

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:286
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:296
msgid "Lesson Quiz"
msgstr "Leçon du Quiz"

#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:535
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:544
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:552
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:559
#: ../extensions/learning/extensions/learning-quiz/class-fw-extension-learning-quiz.php:568
msgid "Invalid Quiz"
msgstr "Quizz invalide"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:21
#, php-format
msgid "You require %d points in oder to pass the test"
msgstr "Vous avez besoin de %d points afin de passer le test"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:27
msgid "Sorry, you did not pass the test"
msgstr "Désolé, vous ne passez pas le test"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:29
msgid "Congratulation, you passed the test"
msgstr "Félicitation, vous a passé le test"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:43
#, php-format
msgid "You answered correctly %s questions from %s"
msgstr "Vous avez répondu correctement à %s questions sur %s"

#: ../extensions/learning/extensions/learning-quiz/views/content.php:69
#: ../extensions/learning/views/content-lesson.php:18
msgid "Back to"
msgstr "Retour à"

#: ../extensions/learning/extensions/learning-quiz/views/start-quiz.php:11
msgid "Start Quiz"
msgstr "Démarrer le Quiz"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:20
msgid "Correct answers"
msgstr "Bonnes réponses"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:21
msgid "Add correct answer variants"
msgstr "Ajouter des variantes de réponses correctes"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:24
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:23
msgid "Set Correct Answer"
msgstr "Définir une réponse correcte"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:34
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:31
msgid "Wrong answers"
msgstr "Les mauvaises réponses"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:35
msgid "Add wrong answer variants"
msgstr "Ajouter des variantes de mauvaises réponses"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:38
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:35
msgid "Set Wrong Answer"
msgstr "Définir la mauvaise réponse"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:42
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:55
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:53
msgid "Creates a"
msgstr "Crée un"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:59
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:91
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:82
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:180
msgid "Multiple Choice"
msgstr "Choix multiple"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:57
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
msgid "item"
msgstr "objet"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:87
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:73
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:86
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:84
#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:76
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:75
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:80
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:80
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:48
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:75
msgid "Label"
msgstr "Étiquette"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:88
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:75
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:87
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:85
msgid "Add/Edit Question"
msgstr "Ajouter/modifier la question"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:90
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:77
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:90
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:87
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:58
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:51
msgid "Delete"
msgstr "Supprimer"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:92
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:90
msgid "More"
msgstr "Plus"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:93
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:91
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:57
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:57
msgid "Close"
msgstr "Fermer"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:94
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:78
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:91
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:56
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:55
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:52
msgid "Edit Label"
msgstr "Modifier le libellé"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:96
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:80
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:93
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:93
msgid "The question label is empty"
msgstr "L'étiquette de question est vide"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:97
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:81
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:94
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:94
msgid "Invalid mark point number"
msgstr "Point de repère invalide nombre"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/multiple-choice/class-fw-option-type-quiz-builder-item-multiple-choice.php:98
msgid "There needs to be at least one correct answer"
msgstr "Il doit y avoir au moins une bonne réponse"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:18
msgid "Correct answer"
msgstr "Bonne réponse"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:19
msgid "The question answer will be true or false"
msgstr "La question réponse sera vrai ou faux"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:22
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:20
msgid "True"
msgstr "Vrai"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:26
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/views/view.php:24
msgid "False"
msgstr "Faux"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:43
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:45
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/true-false/class-fw-option-type-quiz-builder-item-true-false.php:74
msgid "True/False"
msgstr "Vrai/Faux"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:19
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:21
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:97
msgid "Text before gap"
msgstr "Texte avant fossé"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:28
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:30
msgid "Gap"
msgstr "Écart"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:37
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:39
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:98
msgid "Text after gap"
msgstr "Texte après écart"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:58
msgid "Gap Fill"
msgstr "Ecart de remplissage"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:89
msgid "Gap _____ Fill"
msgstr "Ecart de remplissage"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/gap-fill/class-fw-option-type-quiz-builder-item-gap-fill.php:96
#, php-format
msgid "At least one of the fields ( %s or %s ) has to ve filled with text"
msgstr "Au moins l'un des champs (%s ou %s) doit être rempli avec du texte"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:19
msgid "Correct Answer"
msgstr "Bonne réponse"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:20
msgid "Write the correct answer text"
msgstr "Écrivez le texte de la réponse correcte"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:32
msgid "Add wrong answers variants"
msgstr "Ajouter les mauvaises réponses variantes"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:54
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:56
#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:23
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:82
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:180
msgid "Single Choice"
msgstr "Choix unique"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:95
msgid "Correct answer cannot be empty"
msgstr "La réponse correcte ne peut pas être vide"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/items/single-choice/class-fw-option-type-quiz-builder-item-single-choice.php:96
msgid "There are not any wrong answers set"
msgstr "Il n'y a pas de mauvaises réponses prévues"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:51
msgid "Question"
msgstr "Question"

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:53
msgid "Type the question..."
msgstr "Tapez la question ..."

#: ../extensions/learning/extensions/learning-quiz/includes/option-types/quiz-builder/extends/class-fw-option-type-quiz-builder-item.php:58
msgid "Points"
msgstr "Points"

#: ../extensions/learning/includes/class-fw-widget-learning.php:18
msgid "Get list of courses"
msgstr "Obtenez la liste des cours"

#: ../extensions/learning/includes/class-fw-widget-learning.php:20
msgid "Lesson Courses"
msgstr "Leçon des cours"

#: ../extensions/learning/includes/class-fw-widget-learning.php:126
msgid "Number of courses"
msgstr "Nombre de cours"

#: ../extensions/analytics/settings-options.php:11
msgid "Google Analytics"
msgstr "Google Analytics"

#: ../extensions/analytics/settings-options.php:12
msgid "Enter your Google Analytics code (Ex: UA-XXXXX-X)"
msgstr "Entrez votre code d'identification Google Analytics (Ex : UA-XXXXX-X)"

#: ../extensions/analytics/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:192
msgid "Analytics"
msgstr "Analyse des visites"

#: ../extensions/analytics/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:193
msgid ""
"Enables the possibility to add the Google Analytics tracking code that will "
"let you get all the analytics about visitors, page views and more."
msgstr ""
"Permet la possibilité d'ajouter le code Google Analytics qui vous permettra "
"d'obtenir toutes les analyses sur les visiteurs, pages vues et plus de suivi."

#: ../extensions/blog/class-fw-extension-blog.php:36
#: ../extensions/blog/class-fw-extension-blog.php:37
#: ../extensions/breadcrumbs/settings-options.php:7
#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:10
msgid "Blog"
msgstr "Blog"

#: ../extensions/blog/class-fw-extension-blog.php:38
msgid "Add blog post"
msgstr "Ajouter un article du blog"

#: ../extensions/blog/class-fw-extension-blog.php:39
msgid "Add new blog post"
msgstr "Ajouter un nouvel article du blog"

#: ../extensions/blog/class-fw-extension-blog.php:40
msgid "All blog posts"
msgstr "Tous les articles de blog"

#: ../extensions/blog/class-fw-extension-blog.php:41
msgid "Edit blog post"
msgstr "Modifier l'article du blog"

#: ../extensions/blog/class-fw-extension-blog.php:42
#: ../extensions/blog/class-fw-extension-blog.php:43
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:49
msgid "Blog Post"
msgstr "Article"

#: ../extensions/blog/class-fw-extension-blog.php:44
msgid "New blog post"
msgstr "Nouve"

#: ../extensions/blog/class-fw-extension-blog.php:45
msgid "No blog posts found"
msgstr "Aucun message du blog trouvé"

#: ../extensions/blog/class-fw-extension-blog.php:46
msgid "No blog posts found in trash"
msgstr "Aucun message du blog trouvé dans la poubelle"

#: ../extensions/blog/class-fw-extension-blog.php:47
msgid "Search blog posts"
msgstr "Rechercher les articles du blog"

#: ../extensions/blog/class-fw-extension-blog.php:48
msgid "View blog post"
msgstr "Voir l'article du blog"

#: ../extensions/blog/class-fw-extension-blog.php:67
#: ../extensions/blog/class-fw-extension-blog.php:87
#: ../extensions/blog/manifest.php:7 ../extensions/blog/manifest.php:8
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:50
msgid "Blog Posts"
msgstr "Article"

#: ../extensions/blog/class-fw-extension-blog.php:76
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:74
msgid "Blog Categories"
msgstr "Catégories du blog"

#: ../extensions/styling/class-fw-extension-styling.php:60
#: ../extensions/styling/class-fw-extension-styling.php:61
#: ../extensions/styling/class-fw-extension-styling.php:78
#: ../extensions/styling/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:48
msgid "Styling"
msgstr "Style"

#: ../extensions/styling/class-fw-extension-styling.php:104
#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:278
#: ../core/components/backend.php:357
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2277
msgid "Save"
msgstr "Sauvegarder"

#: ../extensions/styling/class-fw-extension-styling.php:118
msgid "You have no permission to change Styling options"
msgstr "Vous n'êtes pas autorisé à modifier les options de style"

#: ../extensions/styling/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:49
msgid ""
"This extension lets you control the website visual style. Starting from "
"predefined styles to changing specific fonts and colors across the website."
msgstr ""
"Cette extension vous permet de contrôler le style visuel d'un site web. A "
"partir de styles prédéfinis pour modifier les polices et les couleurs "
"spécifiques à travers le site."

#: ../extensions/styling/extensions/switch-style-panel/manifest.php:7
msgid "Switch Style Panel"
msgstr "Panneau de style de commutateur"

#: ../extensions/styling/extensions/switch-style-panel/manifest.php:8
msgid ""
"Show on the front-end a panel that allows the user to make the switch "
"between predefined styles."
msgstr ""
"Voir sur le front-end un panneau qui permet à l'utilisateur de faire la "
"transition entre les styles prédéfinis."

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:9
msgid "Frontend Style Switcher"
msgstr "Avant changeur de style d'extrémité"

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:10
msgid "Enable frontend style switcher"
msgstr "Activer avant changeur de style d'extrémité"

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:13
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:274
#: ../extensions/shortcodes/shortcodes/map/options.php:45
#: ../extensions/shortcodes/shortcodes/button/options.php:24
#: ../extensions/shortcodes/shortcodes/media-image/options.php:42
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:34
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:48
#: ../includes/option-types/simple.php:454
#: ../includes/option-types/switch/class-fw-option-type-switch.php:151
msgid "Yes"
msgstr "Oui"

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:17
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:278
#: ../extensions/shortcodes/shortcodes/map/options.php:49
#: ../extensions/shortcodes/shortcodes/button/options.php:28
#: ../extensions/shortcodes/shortcodes/media-image/options.php:46
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:38
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:52
#: ../includes/option-types/switch/class-fw-option-type-switch.php:147
msgid "No"
msgstr "Non"

#: ../extensions/styling/extensions/switch-style-panel/options/settings.php:24
msgid "The text that will be displayed at the top of the panel."
msgstr "Le texte qui sera affiché en haut du panneau."

#: ../extensions/styling/includes/option-types/style/views/settings.php:119
msgid "Background"
msgstr "Contexte"

#: ../extensions/styling/includes/option-types/style/views/predefined.php:26
msgid "Predefined Styles"
msgstr "Styles prédéfinis"

#: ../extensions/styling/includes/option-types/style/views/preview.php:41
msgid "This is a simplified preview, not changes are reflected."
msgstr "Ceci est un aperçu simplifié, il n'y pas de changements se reflètent."

#: ../extensions/feedback/class-fw-extension-feedback.php:64
#: ../core/components/extensions/manager/available-extensions.php:144
msgid "Feedback"
msgstr "Commentaire"

#: ../extensions/feedback/class-fw-extension-feedback.php:111
msgid "Reviews"
msgstr "Avis"

#: ../extensions/feedback/settings-options.php:10
#: ../extensions/shortcodes/extensions/page-builder/settings-options.php:11
msgid "Activate for"
msgstr "Activer pour"

#: ../extensions/feedback/settings-options.php:16
msgid "Select the options you want the Feedback extension to be activated for"
msgstr "Select the options you want the Feedback extension to be activated for"

#: ../extensions/feedback/manifest.php:7
msgid "FeedBack"
msgstr "Livre d'or"

#: ../extensions/feedback/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:145
msgid ""
"Adds the possibility to leave feedback (comments, reviews and rating) about "
"your products, articles, etc. This replaces the default comments system."
msgstr ""
"Ajoute la possibilité de laisser des commentaires (commentaires, avis et "
"notes) sur vos produits, articles, etc. Cela remplace le système de "
"commentaires par défaut."

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:84
msgid "Rating:"
msgstr "Évaluation :"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:195
msgid "Feedback Stars"
msgstr "Etoiles de retour"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:221
#: ../extensions/feedback/extensions/feedback-stars/views/rate.php:12
msgid "Rating"
msgstr "Évaluation"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "ERROR"
msgstr "ERREUR"

#: ../extensions/feedback/extensions/feedback-stars/class-fw-extension-feedback-stars.php:264
msgid "please rate the post."
msgstr "s'il vous plaît noter l'article."

#: ../extensions/feedback/extensions/feedback-stars/manifest.php:7
msgid "FeedBack Stars"
msgstr "Etoiles des retours"

#: ../extensions/feedback/extensions/feedback-stars/manifest.php:8
msgid "Allows visitors to appreciate a post using star rating"
msgstr "Permet aux visiteurs d'apprécier un poste en utilisant étoiles"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
msgid "Pingback:"
msgstr "Pingback :"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:22
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
msgid "(Edit)"
msgstr "(Editer)"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:37
msgid "Post author"
msgstr "Auteur de l'article"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:57
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:129
#, php-format
msgid "%1$s at %2$s"
msgstr "%1$s a %2$s"

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review.php:63
#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:53
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:56
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:122
msgid "Your comment is awaiting moderation."
msgstr "Votre commentaire est en attente de modération."

#: ../extensions/feedback/extensions/feedback-stars/views/listing-review-html5.php:24
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:40
#: ../extensions/feedback/extensions/feedback-stars/includes/extends/class-fw-feedback-stars-walker.php:119
msgid "says"
msgstr "dit"

#: ../extensions/feedback/extensions/feedback-stars/views/view-rates.php:24
#, php-format
msgid "Based on %s Votes"
msgstr "Sur la base de %s Votes"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:6
msgid "Rating System"
msgstr "Système d'évaluation"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:9
msgid "Enter the number of stars you want in the rating system"
msgstr "Entrez le nombre d'étoiles que vous voulez dans le système de notation"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:11
msgid "5 stars"
msgstr "5 étoiles"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:12
msgid "7 stars"
msgstr "7 étoiles"

#: ../extensions/feedback/extensions/feedback-stars/options/settings.php:13
msgid "10 stars"
msgstr "10 étoiles"

#: ../extensions/feedback/views/reviews.php:32
#: ../extensions/feedback/views/reviews.php:53
msgid "Comment navigation"
msgstr "Commenter la navigation"

#: ../extensions/feedback/views/reviews.php:35
#: ../extensions/feedback/views/reviews.php:56
msgid "&larr; Older Comments"
msgstr "&larr; Anciens commentaires"

#: ../extensions/feedback/views/reviews.php:36
#: ../extensions/feedback/views/reviews.php:57
msgid "Newer Comments &rarr;"
msgstr "Commentaires &rarr;"

#: ../extensions/feedback/views/reviews.php:62
msgid "Comments are closed."
msgstr "Les commentaires sont fermés."

#: ../extensions/shortcodes/extensions/page-builder/settings-options.php:18
msgid ""
"Select the posts you want the Page Builder extension to be activated for"
msgstr ""
"Sélectionnez les messages de l'extension Page Builder que vous souhaitez "
"activer pour "

#: ../extensions/shortcodes/extensions/page-builder/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:84
msgid "Page Builder"
msgstr "Mise en page"

#: ../extensions/shortcodes/extensions/page-builder/manifest.php:9
msgid ""
"Lets you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr ""
"Vous permet de créer facilement d'innombrables pages avec l'aide de la "
"glisser-déposer la page constructeur visuel qui vient avec beaucoup de "
"shortcodes déjà créés."

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:51
msgid ""
"There must not be more than one page Editor integrated with the wp post "
"editor per page"
msgstr ""
"l ne doit pas avoir plus d'un éditeur de pages intégré avec l'éditeur wp "
"post par page"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:96
msgid "Visual Page Builder"
msgstr "Visuel de Page Builder"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:97
msgid "Default Editor"
msgstr "Éditeur par défaut"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/class-fw-option-type-page-builder.php:126
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:117
#: ../extensions/shortcodes/shortcodes/section/config.php:5
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:22
msgid "Layout Elements"
msgstr "Éléments de mise en page"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:99
#, php-format
msgid "No Page Builder tab specified for shortcode: %s"
msgstr "Aucun onglet Page Bulter n'est spécifié pour le shortcode : %s"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:111
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:75
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:83
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:57
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:238
#: ../core/components/extensions/manager/views/extension.php:141
#: ../core/components/extensions/manager/views/extension.php:345
msgid "Remove"
msgstr "Supprimer"

#: ../extensions/shortcodes/extensions/page-builder/includes/page-builder/includes/item-types/simple/class-page-builder-simple-item.php:112
#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:74
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:82
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:56
msgid "Duplicate"
msgstr "Dupliquer"

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:8
#: ../extensions/shortcodes/shortcodes/testimonials/options.php:12
msgid "Testimonials"
msgstr "Témoignages"

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:9
msgid "Add some Testimonials"
msgstr "Ajouter quelques témoignages"

#: ../extensions/shortcodes/shortcodes/testimonials/config.php:10
#: ../extensions/shortcodes/shortcodes/accordion/config.php:10
#: ../extensions/shortcodes/shortcodes/table/config.php:10
#: ../extensions/shortcodes/shortcodes/map/config.php:10
#: ../extensions/shortcodes/shortcodes/special-heading/config.php:8
#: ../extensions/shortcodes/shortcodes/team-member/config.php:10
#: ../extensions/shortcodes/shortcodes/icon/config.php:8
#: ../extensions/shortcodes/shortcodes/icon-box/config.php:8
#: ../extensions/shortcodes/shortcodes/button/config.php:10
#: ../extensions/shortcodes/shortcodes/calendar/config.php:10
#: ../extensions/shortcodes/shortcodes/notification/config.php:10
#: ../extensions/shortcodes/shortcodes/widget-area/config.php:10
#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:10
#: ../extensions/shortcodes/shortcodes/text-block/config.php:10
#: ../extensions/shortcodes/shortcodes/divider/config.php:8
#: ../extensions/shortcodes/shortcodes/tabs/config.php:10
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:10
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:106
msgid "Content Elements"
msgstr "Éléments de contenu"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:8
msgid "Option Testimonials Title"
msgstr "Option du titre du témoignage"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:13
msgid "Add/Edit Testimonial"
msgstr "Ajouter/Éditer témoignage"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:14
msgid "Here you can add, remove and edit your Testimonials."
msgstr "Ici, vous pouvez ajouter, supprimer et modifier vos témoignages."

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:19
msgid "Quote"
msgstr "Citation"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:20
msgid "Enter the testimonial here"
msgstr "Entrez le témoignage ici"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:25
#: ../extensions/shortcodes/shortcodes/media-image/config.php:6
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:102
msgid "Image"
msgstr "Image"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:26
#: ../extensions/shortcodes/shortcodes/team-member/options.php:8
#: ../extensions/shortcodes/shortcodes/media-image/options.php:9
msgid ""
"Either upload a new, or choose an existing image from your media library"
msgstr ""
"Soit télécharger une nouvelle, ou choisissez une image existante à partir de "
"votre bibliothèque multimédia"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:30
msgid "Name"
msgstr "Nom"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:31
msgid "Enter the Name of the Person to quote"
msgstr "Entrez le nom de la personne à citer"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:35
msgid "Position"
msgstr "Position"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:36
msgid "Can be used for a job description"
msgstr "Peut être utilisé pour une description de poste"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:40
msgid "Website Name"
msgstr "Nom du site"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:41
msgid "Linktext for the above Link"
msgstr "Le texte du lien pour le lien ci-dessus"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:45
msgid "Website Link"
msgstr "Lien du site internet"

#: ../extensions/shortcodes/shortcodes/testimonials/options.php:46
msgid "Link to the Persons website"
msgstr "Lien vers le site personnel"

#: ../extensions/shortcodes/shortcodes/accordion/config.php:8
msgid "Accordion"
msgstr "Accordéon"

#: ../extensions/shortcodes/shortcodes/accordion/config.php:9
msgid "Add an Accordion"
msgstr "Ajouter un accordéon"

#: ../extensions/shortcodes/shortcodes/accordion/options.php:8
#: ../extensions/shortcodes/shortcodes/tabs/config.php:8
#: ../extensions/shortcodes/shortcodes/tabs/options.php:8
msgid "Tabs"
msgstr "Onglet"

#: ../extensions/shortcodes/shortcodes/accordion/options.php:9
msgid "Add/Edit Tabs"
msgstr "Ajouter/Editer l'onglet"

#: ../extensions/shortcodes/shortcodes/accordion/options.php:10
#: ../extensions/shortcodes/shortcodes/tabs/options.php:10
msgid "Create your tabs"
msgstr "Créer votre onglet"

#: ../extensions/shortcodes/shortcodes/accordion/options.php:19
#: ../extensions/shortcodes/shortcodes/icon-box/options.php:24
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:13
#: ../extensions/shortcodes/shortcodes/text-block/options.php:10
#: ../extensions/shortcodes/shortcodes/tabs/options.php:19
msgid "Content"
msgstr "Contenu"

#: ../extensions/shortcodes/shortcodes/table/config.php:8
msgid "Table"
msgstr "Table"

#: ../extensions/shortcodes/shortcodes/table/config.php:9
msgid "Add a Table"
msgstr "Ajouter une table"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:64
msgid "table-builder option type must be inside the table shortcode"
msgstr ""
"table constructeur type d'option doit être à l'intérieur de la table "
"shortcode"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:171
msgid "Table Styling"
msgstr "Style de la table"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:172
msgid "Choose the table styling options"
msgstr "Choisissez les options de style de la table"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:174
msgid "Use the table as a pricing table"
msgstr "Utilisez la table comme une table de prix"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:175
msgid "Use the table to display tabular data"
msgstr "Utilisez le tableau pour afficher les données tabulaires"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:197
msgid "Default row"
msgstr "Colonne par défault"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:198
msgid "Heading row"
msgstr "Ligne de l'en-tête"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:199
msgid "Pricing row"
msgstr "Ligne des prix"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:200
msgid "Button row"
msgstr "Ligne de bouton"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:201
msgid "Row switch"
msgstr "Ligne de bouton"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:211
msgid "Default column"
msgstr "Colonne par default"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:212
msgid "Description column"
msgstr "Description de la colonne"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:213
msgid "Highlight column"
msgstr "Colonne souligner"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:214
msgid "Center text column"
msgstr "Colonne de texte centrer"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:251
msgid "per month"
msgstr "par mois"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:262
#: ../extensions/shortcodes/shortcodes/button/config.php:8
msgid "Button"
msgstr "Bouton"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/class-fw-option-type-table.php:263
#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-worksheet-template.php:39
#: ../extensions/sidebars/class-fw-extension-sidebars.php:148
#: ../includes/option-types/addable-option/class-fw-option-type-addable-option.php:20
#: ../includes/option-types/addable-popup/class-fw-option-type-addable-popup.php:153
#: ../includes/option-types/addable-box/class-fw-option-type-addable-box.php:186
msgid "Add"
msgstr "Ajouter"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/cell-head-template.php:27
msgid "Add Column"
msgstr "Ajouter une colonne"

#: ../extensions/shortcodes/shortcodes/table/includes/fw-option-type-table/views/view.php:161
msgid "Add Row"
msgstr "Ajouter une rangée"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:24
#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:25
msgid "Custom"
msgstr "Personnalisée"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:27
msgid "Locations"
msgstr "Locations"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:28
msgid "Add/Edit Location"
msgstr "Ajouter/Modifier l'emplacement"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:31
msgid "Note: Please set location"
msgstr "Remarque : S'il vous plaît, définissez l'emplacement"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:35
#: ../extensions/events/class-fw-extension-events.php:187
msgid "Location"
msgstr "Location"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:39
msgid "Location Title"
msgstr "Titre de l'emplacement"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:40
msgid "Set location title"
msgstr "Configurer le titre de la l'emplacement"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:44
msgid "Location Description"
msgstr "Description de la l'emplacement"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:45
msgid "Set location description"
msgstr "Configurer la description de l'emplacement"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:49
msgid "Location Url"
msgstr "Localisation Url"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:50
msgid "Set page url (Ex: http://example.com)"
msgstr "Page url (Daedans: http://example.com)"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:53
msgid "Location Image"
msgstr "Image de l'emplacement"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:54
msgid "Add location image"
msgstr "Ajouter une image de l'emplacement"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:113
msgid "No location provider specified for map shortcode"
msgstr "Aucun fournisseur d'emplacement spécifié pour la carte shortcode"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:115
msgid "Map Placeholder"
msgstr "Espace réservé de la Carte"

#: ../extensions/shortcodes/shortcodes/map/class-fw-shortcode-map.php:122
#, php-format
msgid "Unknown location provider \"%s\" specified for map shortcode"
msgstr "Emplacement inconnu \"%s\" spécifiée pour le shortcode de la carte "

#: ../extensions/shortcodes/shortcodes/map/config.php:8
msgid "Map"
msgstr "Carte"

#: ../extensions/shortcodes/shortcodes/map/config.php:9
msgid "Add a Map"
msgstr "Ajouter une carte"

#: ../extensions/shortcodes/shortcodes/map/options.php:13
#: ../extensions/shortcodes/shortcodes/calendar/options.php:13
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:214
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:473
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:11
msgid "Population Method"
msgstr "Méthode de la population"

#: ../extensions/shortcodes/shortcodes/map/options.php:14
msgid "Select map population method (Ex: events, custom)"
msgstr "Sélectionner la carte (Ex : événement, personnalisé)"

#: ../extensions/shortcodes/shortcodes/map/options.php:24
msgid "Map Type"
msgstr "Type de carte"

#: ../extensions/shortcodes/shortcodes/map/options.php:25
msgid "Select map type"
msgstr "Sélectionnez le type de carte"

#: ../extensions/shortcodes/shortcodes/map/options.php:27
msgid "Roadmap"
msgstr "Feuille de route"

#: ../extensions/shortcodes/shortcodes/map/options.php:28
msgid "Terrain"
msgstr "Terrain"

#: ../extensions/shortcodes/shortcodes/map/options.php:29
msgid "Satellite"
msgstr "Satellite"

#: ../extensions/shortcodes/shortcodes/map/options.php:30
msgid "Hybrid"
msgstr "Hybride"

#: ../extensions/shortcodes/shortcodes/map/options.php:34
msgid "Map Height"
msgstr "Hauteur de la carte"

#: ../extensions/shortcodes/shortcodes/map/options.php:35
msgid "Set map height (Ex: 300)"
msgstr "Configurer la largeur de la carte (Ex : 300)"

#: ../extensions/shortcodes/shortcodes/map/options.php:41
msgid "Disable zoom on scroll"
msgstr "Désactiver zoom défilement"

#: ../extensions/shortcodes/shortcodes/map/options.php:42
msgid "Prevent the map from zooming when scrolling until clicking on the map"
msgstr ""
"Empêcher la carte de zoomer lors du défilement jusqu'au clic sur la carte"

#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:76
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:65
msgid "Column"
msgstr "Colonne"

#: ../extensions/shortcodes/shortcodes/column/includes/page-builder-column-item/class-page-builder-column-item.php:120
#, php-format
msgid "Add a %s column"
msgstr "Ajouter une %s colonne"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:12
msgid "Columns"
msgstr "Colonnes"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:47
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:47
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:51
msgid "No Templates Saved"
msgstr "Aucun templates sauvegardé"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:50
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:50
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:54
msgid "Load Template"
msgstr "Charger le Template"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:91
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:91
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:94
msgid "Template Name"
msgstr "Nom du modèle"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:92
msgid "Save Column"
msgstr "Enregistrer la colonne"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:93
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:93
msgid "Save as Template"
msgstr "Enregistrer en tant que modèle"

#: ../extensions/shortcodes/shortcodes/column/includes/template-component/class-fw-ext-builder-templates-component-column.php:174
#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:174
#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:206
msgid "No Title"
msgstr "Sans titre"

#: ../extensions/shortcodes/shortcodes/special-heading/config.php:6
msgid "Special Heading"
msgstr "En-tête spéciale"

#: ../extensions/shortcodes/shortcodes/special-heading/config.php:7
msgid "Add a Special Heading"
msgstr "Ajouter une en-tête spécial"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:8
msgid "Heading Title"
msgstr "Titre de l'en-tête"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:9
msgid "Write the heading title content"
msgstr "Ecrire le contenu en-tête de titre"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:13
msgid "Heading Subtitle"
msgstr "Sous-titre de l'en-tête"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:14
msgid "Write the heading subtitle content"
msgstr "Ecrire le contenu des sous-titres rubrique"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:18
msgid "Heading Size"
msgstr "Taille de l'en-tête"

#: ../extensions/shortcodes/shortcodes/special-heading/options.php:30
msgid "Centered"
msgstr "Centré"

#: ../extensions/shortcodes/shortcodes/team-member/config.php:8
msgid "Team Member"
msgstr "Membre de l'équipe"

#: ../extensions/shortcodes/shortcodes/team-member/config.php:9
msgid "Add a Team Member"
msgstr "Ajouter un membre de l'équipe"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:7
msgid "Team Member Image"
msgstr "Image du membre de l'équipe"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:12
msgid "Team Member Name"
msgstr "Nom de membre de l'équipe"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:13
msgid "Name of the person"
msgstr "Nom de la personne"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:18
msgid "Team Member Job Title"
msgstr "Titre du poste du membre de l'équipe"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:19
msgid "Job title of the person."
msgstr "Titre du poste de la personne."

#: ../extensions/shortcodes/shortcodes/team-member/options.php:24
msgid "Team Member Description"
msgstr "Description du membre de l'équipe"

#: ../extensions/shortcodes/shortcodes/team-member/options.php:25
msgid "Enter a few words that describe the person"
msgstr "Saisissez quelques mots qui décrivent la personne"

#: ../extensions/shortcodes/shortcodes/icon/config.php:6
#: ../extensions/shortcodes/shortcodes/icon/options.php:8
#: ../extensions/megamenu/class-fw-extension-megamenu.php:115
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:188
msgid "Icon"
msgstr "Icône"

#: ../extensions/shortcodes/shortcodes/icon/config.php:7
msgid "Add an Icon"
msgstr "Ajouter une icône"

#: ../extensions/shortcodes/shortcodes/icon/options.php:13
msgid "Icon title"
msgstr "Icône de titre"

#: ../extensions/shortcodes/shortcodes/icon-box/config.php:6
msgid "Icon Box"
msgstr "Icône de boîte"

#: ../extensions/shortcodes/shortcodes/icon-box/config.php:7
msgid "Add an Icon Box"
msgstr "Ajouter une icône de boîtes"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:8
msgid "Box Style"
msgstr "Style de la boite"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:10
msgid "Icon above title"
msgstr "Icône au dessus du titre"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:11
msgid "Icon in line with title"
msgstr "Icône en ligne avec le titre"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:16
msgid "Choose an Icon"
msgstr "Choisir une icône"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:20
msgid "Title of the Box"
msgstr "Titre de la boite"

#: ../extensions/shortcodes/shortcodes/icon-box/options.php:25
msgid "Enter the desired content"
msgstr "Entrez le contenu désiré"

#: ../extensions/shortcodes/shortcodes/button/config.php:9
msgid "Add a Button"
msgstr "Ajouter un bouton"

#: ../extensions/shortcodes/shortcodes/button/options.php:7
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:17
msgid "Button Label"
msgstr "Bouton Étiquette"

#: ../extensions/shortcodes/shortcodes/button/options.php:8
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:18
msgid "This is the text that appears on your button"
msgstr "Ceci est le texte qui apparaît sur votre bouton"

#: ../extensions/shortcodes/shortcodes/button/options.php:13
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:23
msgid "Button Link"
msgstr "Lien du bouton"

#: ../extensions/shortcodes/shortcodes/button/options.php:14
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:24
msgid "Where should your button link to"
msgstr "Où devrait être votre lien de bouton pour"

#: ../extensions/shortcodes/shortcodes/button/options.php:20
#: ../extensions/shortcodes/shortcodes/media-image/options.php:38
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:30
msgid "Open Link in New Window"
msgstr "Ouvrir le lien dans une nouvelle fenêtre"

#: ../extensions/shortcodes/shortcodes/button/options.php:21
#: ../extensions/shortcodes/shortcodes/media-image/options.php:39
#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:31
msgid "Select here if you want to open the linked page in a new window"
msgstr ""
"Sélectionnez ici si vous voulez ouvrir la page liée dans une nouvelle fenêtre"

#: ../extensions/shortcodes/shortcodes/button/options.php:32
msgid "Button Color"
msgstr "Bouton Couleur"

#: ../extensions/shortcodes/shortcodes/button/options.php:33
msgid "Choose a color for your button"
msgstr "Choisissez une couleur pour votre bouton"

#: ../extensions/shortcodes/shortcodes/button/options.php:36
msgid "Default"
msgstr "Default"

#: ../extensions/shortcodes/shortcodes/button/options.php:37
msgid "Black"
msgstr "Noir"

#: ../extensions/shortcodes/shortcodes/button/options.php:38
msgid "Blue"
msgstr "Bleu"

#: ../extensions/shortcodes/shortcodes/button/options.php:39
msgid "Green"
msgstr "Vert"

#: ../extensions/shortcodes/shortcodes/button/options.php:40
msgid "Red"
msgstr "Rouge"

#: ../extensions/shortcodes/shortcodes/media-video/config.php:6
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:108
msgid "Video"
msgstr "Vidéo"

#: ../extensions/shortcodes/shortcodes/media-video/config.php:7
msgid "Add a Video"
msgstr "Ajouter une vidéo"

#: ../extensions/shortcodes/shortcodes/media-video/config.php:8
#: ../extensions/shortcodes/shortcodes/media-image/config.php:8
#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:10
msgid "Media Elements"
msgstr "Éléments du média"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:8
msgid "Insert Video URL"
msgstr "Insérer l'URL de la vidéo"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:9
#: ../extensions/shortcodes/shortcodes/section/options.php:24
msgid "Insert Video URL to embed this video"
msgstr "Insérer l'URL de la vidéo pour intégrer cette vidéo"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:13
msgid "Video Width"
msgstr "Largeur de la vidéo"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:14
msgid "Enter a value for the width"
msgstr "Entrez une valeur pour la largeur"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:19
msgid "Video Height"
msgstr "Hauteur de la vidéo"

#: ../extensions/shortcodes/shortcodes/media-video/options.php:20
msgid "Enter a value for the height"
msgstr "Entrez une valeur pour la hauteur"

#: ../extensions/shortcodes/shortcodes/calendar/config.php:8
#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:16
msgid "Calendar"
msgstr "Calendrier"

#: ../extensions/shortcodes/shortcodes/calendar/config.php:9
msgid "Add a Calendar"
msgstr "Ajouter un calendrier"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:14
msgid "Select calendar population method (Ex: events, custom)"
msgstr ""
"Sélectionnez le calendrier des méthode de la population (Ex: événements, "
"personnalisé)"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:23
msgid "Calendar Type"
msgstr "Type de calendrier"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:24
msgid "Select calendar type"
msgstr "Sélectionnez le type de calendrier"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:27
#: ../extensions/backups/includes/module/schedule/settings-options.php:23
msgid "Daily"
msgstr "Tous les jours"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:28
#: ../extensions/backups/includes/module/schedule/settings-options.php:22
msgid "Weekly"
msgstr "Hebdomadaire"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:29
#: ../extensions/backups/includes/module/schedule/settings-options.php:21
msgid "Monthly"
msgstr "Mensuel"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:33
msgid "Start Week On"
msgstr "Début Semaine Sur"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:34
msgid "Select first day of week"
msgstr "Sélectionner le premier jour de la semaine"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:37
#: ../extensions/shortcodes/shortcodes/calendar/static.php:110
msgid "Monday"
msgstr "Lundi"

#: ../extensions/shortcodes/shortcodes/calendar/options.php:38
#: ../extensions/shortcodes/shortcodes/calendar/static.php:109
msgid "Sunday"
msgstr "Dimanche"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:29
#: ../extensions/shortcodes/shortcodes/calendar/static.php:56
#: ../extensions/shortcodes/shortcodes/calendar/static.php:82
#: ../extensions/events/class-fw-extension-events.php:69
#: ../extensions/events/class-fw-extension-events.php:74
#: ../extensions/events/manifest.php:7
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:77
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:185
#: ../core/components/extensions/manager/available-extensions.php:180
msgid "Events"
msgstr "Événements"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:30
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:59
msgid "Add/Edit Date & Time"
msgstr "Ajouter/Modifier Date et heure"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:33
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:63
msgid "Note: Please set start & end event datetime"
msgstr ""
"Remarque: S'il vous plaît régler l'heure de début et de fin date de "
"l'événement"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:37
msgid "Event Title"
msgstr "Titre de l'événement"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:38
msgid "Enter the event title"
msgstr "Entrez le titre de l'événement"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:42
msgid "Event URL"
msgstr "URL de l'événement"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:43
msgid "Enter the event URL (Ex: http://your-domain.com/event)"
msgstr "Entrez l'événement URL (Ex: http://your-domain.com/event)"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:47
#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:58
msgid "Date & Time"
msgstr "Date et heure"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:48
msgid "Enter the event date & time"
msgstr "Entrez la date et l'heure de l'événement"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:129
msgid "No events provider specified for calendar shortcode"
msgstr "Aucun fournisseur d'événements spécifié pour le calendrier shortcode"

#: ../extensions/shortcodes/shortcodes/calendar/class-fw-shortcode-calendar.php:138
#, php-format
msgid "Unknown events provider \"%s\" specified for calendar shortcode"
msgstr ""
"Fournisseur d'événements inconnus \"%s\" spécifié pour le calendrier "
"shortcode"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:55
#: ../extensions/events/class-fw-extension-events.php:68
#: ../extensions/events/class-fw-extension-events.php:75
msgid "Event"
msgstr "Un événement"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:57
#: ../extensions/shortcodes/shortcodes/calendar/views/view.php:23
msgid "Today"
msgstr "Aujourd'hui"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:66
#, php-format
msgid "Calendar: View %s not found"
msgstr "Calendrier: Vue %s non trouvé"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:67
#, php-format
msgid ""
"Calendar: Wrong date format %s. Should be either \"now\" or \"yyyy-mm-dd\""
msgstr ""
"Calendrier: Mauvais format de date %s. Devrait être «maintenant» ou «aaaa-mm-"
"dd\""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:69
msgid "Calendar: Event URL is not set"
msgstr "Calendrier : URL de l'événement n'est pas défini"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:70
#, php-format
msgid ""
"Calendar: Wrong navigation direction %s. Can be only \"next\" or \"prev\" or "
"\"today\""
msgstr ""
"Calendrier: Mauvaise direction de navigation %s. Peut-être que \"suivant\" "
"ou \"précédent\" ou \"aujourd'hui\""

#: ../extensions/shortcodes/shortcodes/calendar/static.php:72
msgid ""
"Calendar: Time split parameter should divide 60 without decimals. Something "
"like 10, 15, 30"
msgstr ""
"Calendrier: Temps paramètre split devrait diviser 60 sans décimales. Quelque "
"chose comme 10, 15, 30"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:74
msgid "No events in this day."
msgstr "Aucun événement à ce jour."

#: ../extensions/shortcodes/shortcodes/calendar/static.php:77
#, php-format
msgid "week %s of %s"
msgstr "Semaine %s à %s"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:79
msgid "Week "
msgstr "Semaine"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:80
msgid "All day"
msgstr "Tous les jours"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:81
msgid "Time"
msgstr "Temps"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:83
msgid "Ends before timeline"
msgstr "Termine avant chronologie"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:84
msgid "Starts after timeline"
msgstr "Commence après chronologie"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:85
msgid "January"
msgstr "Janvier"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:86
msgid "February"
msgstr "Février"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:87
msgid "March"
msgstr "Mars"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:88
msgid "April"
msgstr "Avril"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:89
#: ../extensions/shortcodes/shortcodes/calendar/static.php:101
msgid "May"
msgstr "Mai"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:90
msgid "June"
msgstr "Juin"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:91
msgid "July"
msgstr "Juillet"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:92
msgid "August"
msgstr "Août"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:93
msgid "September"
msgstr "Septembre"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:94
msgid "October"
msgstr "Octobre"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:95
msgid "November"
msgstr "Novembre"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:96
msgid "December"
msgstr "Décembre"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:97
msgid "Jan"
msgstr "Jan"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:98
msgid "Feb"
msgstr "Fev"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:99
msgid "Mar"
msgstr "Mar"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:100
msgid "Apr"
msgstr "Avr"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:102
msgid "Jun"
msgstr "Jui"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:103
msgid "Jul"
msgstr "Jui"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:104
msgid "Aug"
msgstr "Aoû"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:105
msgid "Sep"
msgstr "Sep"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:106
msgid "Oct"
msgstr "Oct"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:107
msgid "Nov"
msgstr "Nov"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:108
msgid "Dec"
msgstr "Dec"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:111
msgid "Tuesday"
msgstr "Mardi"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:112
msgid "Wednesday"
msgstr "Mercredi"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:113
msgid "Thursday"
msgstr "Jeudi"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:114
msgid "Friday"
msgstr "Vendredi"

#: ../extensions/shortcodes/shortcodes/calendar/static.php:115
msgid "Saturday"
msgstr "Samedi"

#: ../extensions/shortcodes/shortcodes/media-image/config.php:7
msgid "Add an Image"
msgstr "Ajouter une image"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:8
msgid "Choose Image"
msgstr "Choisissez l'image"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:16
msgid "Width"
msgstr "Largeur"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:17
msgid "Set image width"
msgstr "Configurer la largeur de l'image"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:22
#: ../extensions/shortcodes/shortcodes/divider/options.php:24
msgid "Height"
msgstr "Hauteur"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:23
msgid "Set image height"
msgstr "Configurer la hauteur de l'image"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:33
msgid "Image Link"
msgstr "Lien de l'image"

#: ../extensions/shortcodes/shortcodes/media-image/options.php:34
msgid "Where should your image link to?"
msgstr "Où devrait votre lien d'image?"

#: ../extensions/shortcodes/shortcodes/notification/config.php:8
msgid "Notification"
msgstr "Notification"

#: ../extensions/shortcodes/shortcodes/notification/config.php:9
msgid "Add a Notification Box"
msgstr "Ajouter une boîte de notification"

#: ../extensions/shortcodes/shortcodes/notification/options.php:7
msgid "Message"
msgstr "Message"

#: ../extensions/shortcodes/shortcodes/notification/options.php:8
msgid "Notification message"
msgstr "Message de notification"

#: ../extensions/shortcodes/shortcodes/notification/options.php:10
msgid "Message!"
msgstr "Message !"

#: ../extensions/shortcodes/shortcodes/notification/options.php:13
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:444
msgid "Type"
msgstr "Type"

#: ../extensions/shortcodes/shortcodes/notification/options.php:14
msgid "Notification type"
msgstr "Type de notification"

#: ../extensions/shortcodes/shortcodes/notification/options.php:17
msgid "Congratulations"
msgstr "Félicitations à vous"

#: ../extensions/shortcodes/shortcodes/notification/options.php:18
msgid "Information"
msgstr "Information"

#: ../extensions/shortcodes/shortcodes/notification/options.php:19
msgid "Alert"
msgstr "Alerte"

#: ../extensions/shortcodes/shortcodes/notification/options.php:20
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:456
msgid "Error"
msgstr "Erreur"

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:9
msgid "Congratulations!"
msgstr "Félicitations à vous !"

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:13
msgid "Information!"
msgstr "Information !"

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:17
msgid "Alert!"
msgstr "Alerte !"

#: ../extensions/shortcodes/shortcodes/notification/views/view.php:21
msgid "Error!"
msgstr "Erreur !"

#: ../extensions/shortcodes/shortcodes/widget-area/config.php:8
msgid "Widget Area"
msgstr "Zone Widget"

#: ../extensions/shortcodes/shortcodes/widget-area/config.php:9
msgid "Add a Widget Area"
msgstr "Ajouter une zone Widget"

#: ../extensions/shortcodes/shortcodes/widget-area/options.php:5
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:5
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:7
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:40
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:91
msgid "Sidebar"
msgstr "Sidebar"

#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:8
msgid "Call To Action"
msgstr "Appel à l'action"

#: ../extensions/shortcodes/shortcodes/call-to-action/config.php:9
msgid "Add a Call to Action"
msgstr "Ajouter un appel à l'action"

#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:9
msgid "This can be left blank"
msgstr "Cela peut être laissée en blanc"

#: ../extensions/shortcodes/shortcodes/call-to-action/options.php:14
msgid "Enter some content for this Info Box"
msgstr "Entrez une partie du contenu de cette boîte d'information"

#: ../extensions/shortcodes/shortcodes/text-block/config.php:8
msgid "Text Block"
msgstr "Bloc de texte"

#: ../extensions/shortcodes/shortcodes/text-block/config.php:9
msgid "Add a Text Block"
msgstr "Ajouter un bloc de texte"

#: ../extensions/shortcodes/shortcodes/text-block/options.php:11
msgid "Enter some content for this texblock"
msgstr "Entrez une partie du contenu de ce bloc de texte"

#: ../extensions/shortcodes/shortcodes/divider/config.php:6
msgid "Divider"
msgstr "Séparateur"

#: ../extensions/shortcodes/shortcodes/divider/config.php:7
msgid "Add a Divider"
msgstr "Ajouter un diviseur"

#: ../extensions/shortcodes/shortcodes/divider/options.php:13
msgid "Ruler Type"
msgstr "Type de règle"

#: ../extensions/shortcodes/shortcodes/divider/options.php:14
msgid "Here you can set the styling and size of the HR element"
msgstr "Ici vous pouvez définir le style et la taille de l'élément HR"

#: ../extensions/shortcodes/shortcodes/divider/options.php:16
msgid "Line"
msgstr "Ligne"

#: ../extensions/shortcodes/shortcodes/divider/options.php:17
msgid "Whitespace"
msgstr "Espace blanc"

#: ../extensions/shortcodes/shortcodes/divider/options.php:25
msgid ""
"How much whitespace do you need? Enter a pixel value. Positive value will "
"increase the whitespace, negative value will reduce it. eg: '50', '-25', "
"'200'"
msgstr ""
"Combien d'espace blanc avez-vous besoin? Entrez une valeur de pixel. Valeur "
"positive va augmenter l'espace blanc, valeur négative réduire. par exemple: "
"'50', '-25', '200'"

#: ../extensions/shortcodes/shortcodes/section/config.php:6
#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:23
msgid "Section"
msgstr "Section"

#: ../extensions/shortcodes/shortcodes/section/config.php:7
msgid "Add a Section"
msgstr "Ajouter une section "

#: ../extensions/shortcodes/shortcodes/section/options.php:7
msgid "Full Width"
msgstr "Pleine largeur"

#: ../extensions/shortcodes/shortcodes/section/options.php:11
msgid "Background Color"
msgstr "Couleur de fond"

#: ../extensions/shortcodes/shortcodes/section/options.php:12
msgid "Please select the background color"
msgstr "S'il vous plaît choisir la couleur d'arrière-plan"

#: ../extensions/shortcodes/shortcodes/section/options.php:16
msgid "Background Image"
msgstr "Image de fond"

#: ../extensions/shortcodes/shortcodes/section/options.php:17
msgid "Please select the background image"
msgstr "S'il vous plaît sélectionner l'image d'arrière-plan"

#: ../extensions/shortcodes/shortcodes/section/options.php:23
msgid "Background Video"
msgstr "Arrière-plan vidéo"

#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:12
msgid "Sections"
msgstr "Sections"

#: ../extensions/shortcodes/shortcodes/section/includes/template-component/class-fw-ext-builder-templates-component-section.php:92
msgid "Save Section"
msgstr "Sauvegarder la section"

#: ../extensions/shortcodes/shortcodes/section/includes/page-builder-section-item/class-page-builder-section-item.php:24
msgid "Creates a section"
msgstr "Crée une section"

#: ../extensions/shortcodes/shortcodes/tabs/config.php:9
msgid "Add some Tabs"
msgstr "Ajouter des onglets"

#: ../extensions/shortcodes/shortcodes/tabs/options.php:9
msgid "Add/Edit Tab"
msgstr "Ajouter/Modifier l'ongler"

#: ../extensions/shortcodes/includes/class-fw-shortcode.php:194
#, php-format
msgid "No default view (views/view.php) found for shortcode: %s"
msgstr "Aucune vue par défaut (view/view.php) trouvé pour shortcodes : %s"

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:203
#, php-format
msgid "Shortcode \"%s\" from %s was already defined at %s"
msgstr "Shortcode \"%s\" de %s est déjà défini à %s"

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:264
#, php-format
msgid "Class file found for shortcode %s but no class %s found"
msgstr ""
"Fichier de classe trouvée pour shortcodes %s mais pas de classe %s trouvé"

#: ../extensions/shortcodes/includes/class-fw-shortcodes-loader.php:269
#, php-format
msgid "The class %s must extend from FW_Shortcode"
msgstr "La classe %s doit étendre à partir de FW_Shortcode"

#: ../extensions/builder/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:204
msgid "Builder"
msgstr "Builder"

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:306
msgid "Full Screen"
msgstr "Plein écran"

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:307
msgid "Exit Full Screen"
msgstr "Quitter le mode plein écran"

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:334
msgid "Undo"
msgstr "Annuler"

#: ../extensions/builder/includes/option-types/builder/extends/class-fw-option-type-builder.php:335
msgid "Redo"
msgstr "Prêt"

#: ../extensions/builder/includes/option-types/builder/includes/fullscreen.php:81
msgid "Preview Changes"
msgstr "Aperçu des modifications"

#: ../extensions/builder/includes/option-types/builder/includes/templates/class-fw-ext-builder-templates.php:119
msgid "Templates"
msgstr "Templates"

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:12
msgid "Full Templates"
msgstr "Templates complets"

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:61
msgid "Save Full Template"
msgstr "Enregistrer le modèle complet"

#: ../extensions/builder/includes/option-types/builder/includes/templates/components/full/class-fw-ext-builder-templates-component-full.php:95
msgid "Save Builder Template"
msgstr "Enregistrer le modèle du Builder"

#: ../extensions/social/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:252
msgid "Social"
msgstr "Réseaux sociaux"

#: ../extensions/social/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:253
msgid ""
"Use this extension to configure all your social related APIs. Other "
"extensions will use the Social extension to connect to your social accounts."
msgstr ""
"Utilisez cette extension pour configurer tous vos API sociales connexes. "
"D'autres extensions vont utiliser l'extension sociale de se connecter à vos "
"comptes sociaux."

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:62
#: ../core/components/backend.php:584
msgid "Facebook"
msgstr "Facebook"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:66
#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:33
msgid "API Settings"
msgstr "API configuration"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:72
msgid "App ID/API Key:"
msgstr "App ID/API Key:"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:73
msgid "Enter Facebook App ID / API Key."
msgstr "Entrez Facebook App ID Key / API."

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:78
msgid "App Secret:"
msgstr "App Secret:"

#: ../extensions/social/extensions/social-facebook/class-fw-extension-social-facebook.php:79
msgid "Enter Facebook App Secret."
msgstr "Entrer Facebook App Secret."

#: ../extensions/social/extensions/social-facebook/manifest.php:7
#: ../extensions/social/extensions/social-facebook/manifest.php:8
msgid "Social Facebook"
msgstr "Social Facebook"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:29
#: ../core/components/backend.php:592
msgid "Twitter"
msgstr "Twitter"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:39
msgid "Consumer Key"
msgstr "Consumer Key"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:40
msgid "Enter Twitter Consumer Key."
msgstr "Entrez la clé du client Twitter."

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:45
msgid "Consumer Secret"
msgstr "Consumer Secret"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:46
msgid "Enter Twitter App Secret."
msgstr "Entrer Twitter app Secret."

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:51
msgid "Access Token"
msgstr "Jeton d'accès"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:52
msgid "Enter Twitter Access Token."
msgstr "Entrez jeton d'accès Twitter."

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:57
msgid "Access Token Secret"
msgstr "Jeton d'accès secret"

#: ../extensions/social/extensions/social-twitter/class-fw-extension-social-twitter.php:58
msgid "Enter Twitter Access Token Secret."
msgstr "Entrez le jeton Twitter de l'accès secret."

#: ../extensions/social/extensions/social-twitter/manifest.php:7
#: ../extensions/social/extensions/social-twitter/manifest.php:8
msgid "Social Twitter"
msgstr "Social Twitter"

#: ../extensions/forms/class-fw-extension-forms.php:112
#: ../extensions/forms/class-fw-extension-forms.php:123
#: ../extensions/forms/class-fw-extension-forms.php:131
#: ../extensions/forms/class-fw-extension-forms.php:142
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:102
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:114
#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:124
msgid "Unable to process the form"
msgstr "Impossible de traiter le formulaire"

#: ../extensions/forms/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:228
msgid "Forms"
msgstr "Formulaire"

#: ../extensions/forms/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:229
msgid ""
"This extension adds the possibility to create a contact form. Use the drag & "
"drop form builder to create any contact form you'll ever want or need."
msgstr ""
"Cette extension ajoute la possibilité de créer un formulaire de contact. "
"Utilisez le drag & drop du constructeur de formulaire pour créer toute forme "
"de formulaire de contact dont vous aurez envie ou besoin."

#: ../extensions/forms/extensions/contact-forms/manifest.php:5
msgid "Contact Forms"
msgstr "Formulaires de contact"

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:134
msgid "Invalid destination email (please contact the site administrator)"
msgstr ""
"Destination de l'e-mail invalide (s'il vous plaît contacter l'administrateur "
"du site)"

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:158
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:77
msgid "Message sent!"
msgstr "Message envoyé !"

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:164
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:85
msgid "Oops something went wrong."
msgstr "Oops quelque chose s'est mal passé."

#: ../extensions/forms/extensions/contact-forms/class-fw-extension-contact-forms.php:190
msgid "Please configure the {mailer_link} extension."
msgstr "S'il vous plaît, configurer l'extension {mailer_link}."

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:8
#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:107
msgid "Contact form"
msgstr "Formulaire de contact"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/config.php:9
msgid "Build contact forms"
msgstr "Construire des formulaires de contact"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:15
msgid "Form Fields"
msgstr "Champs de formulaires"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:39
#: ../core/components/extensions/manager/views/extension.php:82
#: ../core/components/extensions/manager/views/extension-page-header.php:31
msgid "Settings"
msgstr "Paramètres"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:42
msgid "Options"
msgstr "Options"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:53
msgid "Subject Message"
msgstr "Sujet du message"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:54
msgid "This text will be used as subject message for the email"
msgstr "Ce texte sera utilisé comme objet du message pour l'e-mail"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:55
msgid "New message"
msgstr "Nouveau message"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:64
msgid "Submit Button"
msgstr "Bouton envoyer"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:65
msgid "This text will appear in submit button"
msgstr "Ce texte apparaîtra dans le bouton envoyer"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:66
msgid "Send"
msgstr "Envoyer"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:75
msgid "Success Message"
msgstr "Message de réussite"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:76
msgid "This text will be displayed when the form will successfully send"
msgstr "Ce texte sera affiché lorsque le formulaire sera envoyer avec succès"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:83
msgid "Failure Message"
msgstr "Message d'échec"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:84
msgid "This text will be displayed when the form will fail to be sent"
msgstr "Ce texte sera affiché lorsque le formulaire ne pourra pas être envoyé"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:94
msgid "Email To"
msgstr "Envoyer à"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:95
msgid "We recommend you to use an email that you verify often"
msgstr "Nous vous recommandons d'utiliser un e-mail que vous vérifiez souvent"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/options.php:96
msgid "The form will be sent to this email address."
msgstr "Le formulaire sera envoyé à cette adresse e-mail."

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:52
msgid "Contact Form"
msgstr "Formulaire de contact"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:54
msgid "Configure Mailer"
msgstr "Configurer Mailer"

#: ../extensions/forms/extensions/contact-forms/shortcodes/contact-form/includes/item/class-page-builder-contact-form-item.php:108
msgid "Add a Contact Form"
msgstr "Ajouter un formulaire de contact"

#: ../extensions/forms/views/backend/submit-box-add.php:9
msgid "Note that the type can't be changed later."
msgstr "Notez que le type ne peut pas être modifié ultérieurement."

#: ../extensions/forms/views/backend/submit-box-add.php:11
msgid ""
"You will need to create a new form in order to have a different form type."
msgstr ""
"Vous aurez besoin de créer une nouveau formulaire afin d'avoir un autre type "
"de formulaire."

#: ../extensions/forms/views/backend/submit-box-add.php:20
#: ../extensions/forms/views/backend/submit-box-edit.php:16
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:14
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:31
#: ../extensions/media/extensions/slider/views/backend/submit-box-error.php:8
msgid "Delete Permanently"
msgstr "Supprimer définitivement"

#: ../extensions/forms/views/backend/submit-box-add.php:22
#: ../extensions/forms/views/backend/submit-box-edit.php:18
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:16
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:33
#: ../extensions/media/extensions/slider/views/backend/submit-box-error.php:10
msgid "Move to Trash"
msgstr "Mettre à la corbeille"

#: ../extensions/forms/views/backend/submit-box-add.php:33
#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:35
msgid "Create"
msgstr "Créer"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:24
msgid "Add a Recaptcha field"
msgstr "Ajouter un champ Recaptcha"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:26
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:54
#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:78
msgid "Recaptcha"
msgstr "Recaptcha"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:59
msgid "Set site key"
msgstr "Configurer la clé du site"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:60
msgid "Set secret key"
msgstr "Configurer la clé secrète"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:77
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:76
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:81
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:81
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:78
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:76
msgid "Enter field label (it will be displayed on the web site)"
msgstr "Entrez le champ de l'étiquette (il sera affiché sur le site web)"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:133
msgid "Security Code"
msgstr "Code de sécurité"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:147
msgid "Could not validate the form"
msgstr "Impossible de valider le formulaire"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/class-fw-option-type-form-builder-item-recaptcha.php:148
msgid "Please fill the recaptcha"
msgstr "S'il vous plaît, remplissez le recaptcha"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:37
msgid "Site key"
msgstr "Clé du site"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:38
msgid "Your website key. More on how to configure ReCaptcha"
msgstr ""
"Votre clé de site Web. Plus de détails sur la façon de configurer ReCaptcha"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:42
msgid "Secret key"
msgstr "Clef secrète"

#: ../extensions/forms/includes/option-types/form-builder/items/recaptcha/includes/option-type-recaptcha/class-fw-option-type-recaptcha.php:43
msgid "Your secret key. More on how to configure ReCaptcha"
msgstr ""
"Votre clé secrète. Plus de détails sur la façon de configurer ReCaptcha"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:18
msgid "Add a Paragraph Text"
msgstr "Ajouter un paragraphe texte"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:20
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:48
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:77
msgid "Paragraph Text"
msgstr "Paragraphe du texte"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:53
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:52
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:51
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:49
msgid "Toggle mandatory field"
msgstr "Basculer le champ obligatoire"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:83
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:88
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:85
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:83
msgid "Mandatory Field"
msgstr "Champ obligatoire"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:84
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:89
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:86
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:84
msgid "Make this field mandatory?"
msgstr "Faire de ce champ une obligation?"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:98
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:98
msgid "Placeholder"
msgstr "Espace réservé"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:99
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:99
msgid "This text will be used as field placeholder"
msgstr "Ce texte sera utilisé comme champ espace réservé"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:105
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:107
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:107
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:105
msgid "Default Value"
msgstr "Valeur par défaut"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:106
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:108
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:108
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:106
msgid "This text will be used as field default value"
msgstr "Ce texte sera utilisé comme valeur par défaut du champ"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:126
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:128
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:126
msgid "Restrictions"
msgstr "Restrictions"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:127
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:127
msgid "Set characters or words restrictions for this field"
msgstr ""
"Configurer les caractères ou mots pour définir les restrictions de ce champ"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:131
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:131
msgid "Characters"
msgstr "Caractères"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:132
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:132
msgid "Words"
msgstr "Mots"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:140
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:154
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:142
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:156
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:140
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:154
msgid "Min"
msgstr "Min"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:141
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:155
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:143
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:157
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:141
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:155
msgid "Minim value"
msgstr "Valeur minimum"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:146
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:160
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:148
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:162
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:146
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:160
msgid "Max"
msgstr "Max"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:147
#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:161
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:149
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:163
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:147
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:161
msgid "Maxim value"
msgstr "Valeur maximum"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:178
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:180
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:121
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:137
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:137
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:114
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:121
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:178
msgid "Instructions for Users"
msgstr "Instructions pour les utilisateurs"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:179
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:181
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:122
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:138
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:138
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:115
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:122
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:179
msgid "The users will see these instructions in the tooltip near the field"
msgstr ""
"Les utilisateurs verront ces instructions dans l'infobulle près du champ"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:331
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:368
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:230
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:244
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:244
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:210
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:217
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:332
msgid "The {label} field is required"
msgstr "Le champ {label} est obligatoire"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:336
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:337
#, php-format
msgid "The {label} field must contain minimum %d character"
msgstr "Le champ {label} doit contenir au minimum %d caractères"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:341
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:342
#, php-format
msgid "The {label} field must contain minimum %d characters"
msgstr "Le champ {label} doit contenir au minimum %d caractères"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:346
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:347
#, php-format
msgid "The {label} field must contain maximum %d character"
msgstr "Le champ {label} doit contenir au maximum %d caractère"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:351
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:352
#, php-format
msgid "The {label} field must contain maximum %d characters"
msgstr "Le champ {label} doit contenir au maximum %d caractères"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:356
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:357
#, php-format
msgid "The {label} field must contain minimum %d word"
msgstr "Le champ {label} doit contenir au minimum %d mot"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:361
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:362
#, php-format
msgid "The {label} field must contain minimum %d words"
msgstr "Le champ {label} doit contenir au minimum %d mots"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:366
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:367
#, php-format
msgid "The {label} field must contain maximum %d word"
msgstr "The {label} field must contain maximum %d mot"

#: ../extensions/forms/includes/option-types/form-builder/items/textarea/class-fw-option-type-form-builder-item-textarea.php:371
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:372
#, php-format
msgid "The {label} field must contain maximum %d words"
msgstr "The {label} field must contain maximum %d mots"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:20
msgid "Add a Number field"
msgstr "Ajouter un champ numérique"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:50
#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:79
msgid "Number"
msgstr "Nombre"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:129
msgid "Set digits or values restrictions of this field"
msgstr "Définir des chiffres ou des valeurs restrictions de ce champ"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:133
msgid "Digits"
msgstr "Chiffre"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:134
msgid "Value"
msgstr "Valeur"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:363
msgid "The {label} field must be a valid number"
msgstr "Le champ {label} doit être un nombre valide"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:373
#, php-format
msgid "The {label} field must have minimum %d digit"
msgstr "Le champ {label} doit avoir un minimum %d chiffre"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:378
#, php-format
msgid "The {label} field must have minimum %d digits"
msgstr "Le champ {label} doit avoir un minimum %d chiffres"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:383
#, php-format
msgid "The {label} field must have maximum %d digit"
msgstr "Le champ {label} doit avoir un maximum %d chiffre"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:388
#, php-format
msgid "The {label} field must have maximum %d digits"
msgstr "Le champ {label} doit avoir un maximum %d chiffres"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:393
#, php-format
msgid "The {label} field minimum value must be %s"
msgstr "Le valeur minimale du champ {label} doit être %s"

#: ../extensions/forms/includes/option-types/form-builder/items/number/class-fw-option-type-form-builder-item-number.php:398
#, php-format
msgid "The {label} field maximum value must be %s"
msgstr "Le valeur maximale du champ {label} doit être %s"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:18
msgid "Add a Dropdown"
msgstr "Ajouter un menu déroulant"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:79
#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:164
msgid "Dropdown"
msgstr "Menu déroulant"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:100
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:103
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:103
msgid "Choices"
msgstr "Choix"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:101
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:104
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:104
msgid "Add choice"
msgstr "Ajouter le choix"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:110
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:113
msgid "Randomize"
msgstr "Aléatoire"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:111
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:114
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:114
msgid "Do you want choices to be displayed in random order?"
msgstr "Voulez-vous des choix à afficher dans un ordre aléatoire?"

#: ../extensions/forms/includes/option-types/form-builder/items/select/class-fw-option-type-form-builder-item-select.php:235
#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:249
msgid "{label}: Submitted data contains not existing choice"
msgstr "{Label}: Données soumises contient des choix non existant"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:38
msgid "Edit Title"
msgstr "Editer le titre"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:39
msgid "Edit Subtitle"
msgstr "Editer le sous-titre"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:57
msgid "The title will be displayed on contact form header"
msgstr "Le titre sera affiché sur le formulaire de contact d'en-tête"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:62
#: ../extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:7
msgid "Subtitle"
msgstr "Sous-titre"

#: ../extensions/forms/includes/option-types/form-builder/items/form-header-title/class-fw-option-type-form-builder-item-form-header-title.php:63
msgid "The form header subtitle text"
msgstr "Le texte sous forme de sous-titres en-tête"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:18
msgid "Add a Single Choice field"
msgstr "Ajouter un champ à choix unique"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:56
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:56
msgid "{x} More"
msgstr "{x} Plus"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:113
msgid "Randomize?"
msgstr "Aléatoire ?"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:124
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:124
msgid "Field Layout"
msgstr "Mise en page du champ"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:125
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:125
msgid "Select choice display layout"
msgstr "Sélectionnez le choix d'affichage mise en page"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:127
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:127
msgid "One column"
msgstr "Une colonne"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:128
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:128
msgid "Two columns"
msgstr "Deux colonnes"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:129
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:129
msgid "Three columns"
msgstr "Trois colonnes"

#: ../extensions/forms/includes/option-types/form-builder/items/radio/class-fw-option-type-form-builder-item-radio.php:130
#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:130
msgid "Side by side"
msgstr "Cote à cote"

#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:18
msgid "Add a Multiple Choices field"
msgstr "Ajouter un champ de choix multiple"

#: ../extensions/forms/includes/option-types/form-builder/items/checkboxes/class-fw-option-type-form-builder-item-checkboxes.php:249
msgid "{label}: Submitted data contains not existing choices"
msgstr "{Label} : Les données soumises contiennent des choix non existants"

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:18
msgid "Add an Email field"
msgstr "Ajouter un champ d'Email"

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:79
msgid "Email"
msgstr "Email"

#: ../extensions/forms/includes/option-types/form-builder/items/email/class-fw-option-type-form-builder-item-email.php:215
msgid "The {label} field must contain a valid email"
msgstr "Le champ {label} doit contenir une adresse email valide"

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:18
msgid "Add a Website field"
msgstr "Ajouter des imagesAdd a Website field"

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:22
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:49
#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:79
msgid "Website"
msgstr "Website"

#: ../extensions/forms/includes/option-types/form-builder/items/website/class-fw-option-type-form-builder-item-website.php:222
msgid "The {label} field must be a valid website name"
msgstr "Le champ {label} doit être un nom de site Web valide"

#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:18
msgid "Add a Single Line Text"
msgstr "Ajouter une simple ligne de texte"

#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:20
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:47
#: ../extensions/forms/includes/option-types/form-builder/items/text/class-fw-option-type-form-builder-item-text.php:77
msgid "Single Line Text"
msgstr "Ligne texte"

#: ../extensions/breadcrumbs/settings-options.php:17
msgid "Text for Homepage"
msgstr "Texte pour la page d'accueil"

#: ../extensions/breadcrumbs/settings-options.php:18
msgid "The homepage anchor will have this text"
msgstr "L'ancre de page d'accueil aura ce texte"

#: ../extensions/breadcrumbs/settings-options.php:23
msgid "Text for Blog Page"
msgstr "Texte pour la page blog"

#: ../extensions/breadcrumbs/settings-options.php:24
msgid ""
"The blog page anchor will have this text. In case homepage will be set as "
"blog page, will be taken the homepage text"
msgstr ""
"L'ancre blog page aura ce texte. Dans le cas où la page d'accueil sera "
"définie comme page de blog, seront prises le texte de de la page d'accueil"

#: ../extensions/breadcrumbs/settings-options.php:29
msgid "Text for 404 Page"
msgstr "Texte pour la page 404"

#: ../extensions/breadcrumbs/settings-options.php:30
msgid "The 404 anchor will have this text"
msgstr "L'ancre 404 aura ce texte"

#: ../extensions/breadcrumbs/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:108
msgid "Breadcrumbs"
msgstr "Fil d'Ariane"

#: ../extensions/breadcrumbs/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:109
msgid ""
"Creates a simplified navigation menu for the pages that can be placed "
"anywhere in the theme. This will make navigating the website much easier."
msgstr ""
"Crée un menu de navigation simplifiée pour les pages qui peuvent être placés "
"partout dans le thème. Cela rendra la navigation sur le site beaucoup plus "
"facile."

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:11
msgid "404 Not found"
msgstr "404 non trouvé"

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:124
msgid "Searching for:"
msgstr "À la recherche de :"

#: ../extensions/breadcrumbs/includes/class-breadcrumbs-builder.php:248
#: ../includes/option-types/multi-select/class-fw-option-type-multi-select.php:472
msgid "No title"
msgstr "Sans titre"

#: ../extensions/events/class-fw-extension-events.php:89
msgid "Create a event item"
msgstr "Créer un élément de l'événement"

#: ../extensions/events/class-fw-extension-events.php:186
msgid "Date"
msgstr "Date"

#: ../extensions/events/class-fw-extension-events.php:204
msgid "Event Options"
msgstr "Options de l'évènement"

#: ../extensions/events/class-fw-extension-events.php:310
msgid "Multi Interval Event"
msgstr "Intervalle multiple d'événement"

#: ../extensions/events/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:181
msgid ""
"This extension adds a fully fledged Events module to your theme. It comes "
"with built in pages that contain a calendar where events can be added."
msgstr ""
"Cette extension ajoute un module d'événements à part entière à votre thème. "
"Il est livré avec construit dans les pages qui contiennent un calendrier où "
"les événements peuvent être ajoutés."

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:81
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:189
msgid "Event Categories"
msgstr "Catégories d'événements"

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:82
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:190
msgid "Select an event category"
msgstr "Sélectionnez une catégorie d'événement"

#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:84
#: ../extensions/events/extensions/events-tags/class-fw-extension-events-tags.php:192
msgid "All Events"
msgstr "Tout les événements"

#: ../extensions/events/extensions/events-tags/manifest.php:7
msgid "Event-search-tags"
msgstr "Evénement-recherche-tags"

#: ../extensions/events/extensions/events-tags/manifest.php:8
msgid "Connect extension event with shortcodes map & calendar"
msgstr "Connectez événement d'extension avec shortcodes map & calendrier"

#: ../extensions/events/views/content.php:16
msgid "Google Calendar"
msgstr "Calendrier Google"

#: ../extensions/events/views/content.php:17
msgid "Ical Export"
msgstr "Exportation Ical"

#: ../extensions/events/views/content.php:20
msgid "Start"
msgstr "Début"

#: ../extensions/events/views/content.php:21
msgid "End"
msgstr "Fin"

#: ../extensions/events/views/content.php:25
msgid "Speakers"
msgstr "Haut-parleurs"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:37
msgid "Event Location"
msgstr "Lieu de l'événement"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:39
msgid "Where does the event take place?"
msgstr "Où est-ce que l'événement a lieu?"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:43
msgid "All Day Event?"
msgstr "Événement d'une journée?"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:44
msgid "Is your event an all day event?"
msgstr "Est-ce votre événement est un événement d'une journée?"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:67
msgid "Start & End of Event"
msgstr "Début & fin de l'événement"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:68
msgid "Set start and end events datetime"
msgstr "Définir le début et la fin des événements"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:97
msgid "Associated User"
msgstr "Utilisateur associé"

#: ../extensions/events/includes/option-types/event/class-fw-option-type-event.php:99
msgid "Link this event to a specific user"
msgstr "Associer cet événement à un utilisateur spécifique"

#: ../extensions/sidebars/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:132
msgid "Sidebars"
msgstr "Sidebars"

#: ../extensions/sidebars/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:133
msgid ""
"Brings a new layer of customization freedom to your website by letting you "
"add more than one sidebar to a page, or different sidebars on different "
"pages."
msgstr ""
"Apporte une nouvelle couche de la liberté de personnalisation à votre site "
"Web en vous permettant d'ajouter plus d'une barre latérale à une page, ou "
"différents encadrés sur des pages différentes."

#: ../extensions/sidebars/class-fw-extension-sidebars.php:131
msgid "No matches found"
msgstr "Aucun résultat"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:139
msgid "Do you realy want to change without saving?"
msgstr "Voulez-vous vraiment changer sans enregistrer?"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:141
msgid "Missing ID. Check that you provided all mandatory data."
msgstr ""
"ID Manquant. Vérifiez que vous avez fourni toutes les données obligatoires."

#: ../extensions/sidebars/class-fw-extension-sidebars.php:142
#: ../extensions/sidebars/views/backend-main-view.php:27
msgid "Created"
msgstr "Créé"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:143
msgid "(For Grouped Pages)"
msgstr "(Pour Groupé Pages)"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:144
msgid "(For Specific Pages)"
msgstr "(Pour des pages spécifiques)"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:145
msgid "No sidebar name specified"
msgstr "Aucun nom de Sidebar spécifiée"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:146
msgid "Sidebar Name"
msgstr "Nom de la barre latérale"

#: ../extensions/sidebars/class-fw-extension-sidebars.php:147
msgid "New Sidebar"
msgstr "Nouvelle Sidebar"

#: ../extensions/sidebars/views/frontend-no-widgets.php:5
msgid "Widgets Page"
msgstr "Page des Widgets"

#: ../extensions/sidebars/views/backend-tab-specific.php:5
msgid "For specific"
msgstr "Pour spécifique"

#: ../extensions/sidebars/views/backend-tab-specific.php:23
msgid "Type to search ..."
msgstr "Tapez à la recherche ..."

#: ../extensions/sidebars/views/backend-tab-specific.php:30
msgid "Search for a specific page you want to set a sidebar for"
msgstr ""
"Rechercher une page spécifique que vous souhaitez définir pour une sidebar"

#: ../extensions/sidebars/views/backend-tab-grouped.php:6
msgid "For group"
msgstr "Pour le groupe"

#: ../extensions/sidebars/views/backend-tab-grouped.php:25
msgid "Select group of pages you want to set a sidebar for."
msgstr ""
"Sélectionner le groupe de pages que vous souhaitez définir pour une sidebar."

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:18
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:60
msgid "Choose the position for your sidebar(s)"
msgstr "Choisissez la position de votre Sidebar(s)"

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-insert.php:43
#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:40
msgid "Add Sidebar"
msgstr "Ajouter une Sidebar"

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:4
msgid "Sidebars for"
msgstr "Sidebards pour"

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Specific Page"
msgstr "Pour la page spécifique"

#: ../extensions/sidebars/views/backend-tab-created-sidebars.php:16
msgid "For Grouped Page"
msgstr "Pour la page groupée"

#: ../extensions/sidebars/views/backend-main-view.php:11
#: ../extensions/sidebars/views/backend-main-view.php:15
msgid "Manage Sidebars"
msgstr "Gérer la Sidebars"

#: ../extensions/sidebars/views/backend-main-view.php:18
msgid ""
"Use this section to create and/or set different sidebar(s) for different "
"page(s)"
msgstr ""
"Utilisez cette section pour créer et/ou définir des sidebar(s) pour chaque "
"page(s) différente"

#: ../extensions/sidebars/views/backend-main-view.php:24
msgid "For Grouped Pages"
msgstr "Pour les pages groupées"

#: ../extensions/sidebars/views/backend-main-view.php:25
msgid "For Specific Pages"
msgstr "Pour pages spécifiques"

#: ../extensions/sidebars/views/backend-sidebars-positions-mode-replace.php:32
#: ../extensions/sidebars/includes/option-type/sidebar-picker/view.php:121
msgid "Select sidebar you wish to replace."
msgstr "Sélectionnez la barre latérale que vous souhaitez remplacer."

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:241
msgid "No sidebar name specified."
msgstr "Aucun nom de barre latérale spécifiée."

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:270
msgid "Dynamic sidebar doesn't exixt"
msgstr "N'existe pas encadré dynamique"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:277
msgid ""
"The placeholder can't be deleted because it is used in one of sidebars below."
msgstr ""
"L'espace réservé ne peut pas être supprimé car il est utilisé dans l'une des "
"barres latérales ci-dessous."

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:280
msgid ""
"Please replace it first so that you will not have visual gaps in your layout."
msgstr ""
"S'il vous plaît remplacer en premier afin que vous ne serez pas avoir des "
"lacunes visuelles dans votre mise en page."

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:293
msgid "Successfully removed"
msgstr "Suppression réussi"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:543
msgid "Default for all pages"
msgstr "Par défaut pour toutes les pages"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:613
msgid " (no title)"
msgstr "(sans titre)"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:780
msgid "Error: Type or sub_type error"
msgstr "Erreur : Type ou erreur de sous-type"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:838
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:869
msgid "Error: Sidebars not set"
msgstr "Erreur: Sidebars non réglées"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-backend.php:865
msgid "Error: Position doesn't exists. Please check config file."
msgstr ""
"Erreur: Position n'existe pas. S'il vous plaît vérifier le fichier de "
"configuration."

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:55
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:253
msgid "Page"
msgstr "Page"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:56
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:153
msgid "Pages"
msgstr "Pages"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:61
msgid "Portfolio Project"
msgstr "Portfolio de projet"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:62
msgid "Portfolio Projects"
msgstr "Portfolio des projets"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:73
msgid "Blog Category"
msgstr "Blog Catégorie"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:79
msgid "Portfolio Category"
msgstr "Catégorie"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:85
msgid "Home Page"
msgstr "Page d'accueil"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:90
msgid "Search Page"
msgstr "Page de recherche"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:95
msgid "404 Page"
msgstr "Page 404"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:100
msgid "Author Page"
msgstr "Page d'auteur"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:105
msgid "Archive Page"
msgstr "Page d'archive"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:149
#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:308
msgid "All Pages"
msgstr "Toutes les pages"

#: ../extensions/sidebars/includes/class-fw-extension-sidebars-config.php:184
msgid "Others"
msgstr "Autres"

#: ../extensions/megamenu/manifest.php:7
#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:64
#: ../core/components/extensions/manager/available-extensions.php:60
msgid "Mega Menu"
msgstr "Mega Menu"

#: ../extensions/megamenu/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:61
msgid ""
"The Mega Menu extension adds a user-friendly drop down menu that will let "
"you easily create highly customized menu configurations."
msgstr ""
"L'extension Menu Mega ajoute une goutte conviviale menu déroulant qui vous "
"permettra de créer facilement des configurations de menu très personnalisées."

#: ../extensions/megamenu/class-fw-extension-megamenu.php:58
msgid "Select Icon"
msgstr "Sélectionnez l'icône"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:44
#, php-format
msgid "%s (Invalid)"
msgstr "%s (non valide)"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:48
#, php-format
msgid "%s (Pending)"
msgstr "%s (dépenses)"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:61
msgid "sub item"
msgstr "Sous élément"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:97
msgid "Edit Menu Item"
msgstr "Modifier élément de menu"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:108
msgid "URL"
msgstr "URL"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:117
msgid "Navigation Label"
msgstr "Étiquette Navigation"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:125
msgid "Title Attribute"
msgstr "Titre Attribut"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:134
msgid "Open link in a new window/tab"
msgstr "Ouvrir le lien dans une nouvelle fenêtre/onglet"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:141
msgid "CSS Classes (optional)"
msgstr "Classes CSS (en option)"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:149
msgid "Link Relationship (XFN)"
msgstr "Lien Relation (XFN)"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:157
msgid "Mega Menu Column Title"
msgstr "Titre de la colonne du Mega Menu"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:158
msgid "Item Title"
msgstr "Titre de l'article"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:166
msgid "Hide"
msgstr "Cacher"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:172
msgid "This column should start a new row"
msgstr "Cette colonne doit commencer une nouvelle ligne"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:178
msgid "Description (HTML)"
msgstr "Description (HTML)"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:181
msgid ""
"The description will be displayed in the menu if the current theme supports "
"it."
msgstr ""
"La description sera affichée dans le menu si le thème actuel prend en charge."

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:190
msgid "Add Icon"
msgstr "Ajouter Icône"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:191
msgid "Edit Icon"
msgstr "Modifier Icône"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:205
msgid "Use as Mega Menu"
msgstr "Utiliser comme Mega Menu"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:211
msgid "Move"
msgstr "Déplacer"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:212
msgid "Up one"
msgstr "Remonter"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:213
msgid "Down one"
msgstr "Descendre"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:216
msgid "To the top"
msgstr "Jusqu'au sommet"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:225
#, php-format
msgid "Original: %s"
msgstr "Original: %s"

#: ../extensions/megamenu/includes/class-fw-ext-mega-menu-admin-walker.php:239
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:60
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2282
msgid "Cancel"
msgstr "Annuler"

#: ../extensions/backups/class-fw-extension-backups.php:299
#: ../extensions/backups/class-fw-extension-backups.php:366
msgid "File not specified"
msgstr "Fichier non spécifié"

#: ../extensions/backups/class-fw-extension-backups.php:399
#: ../extensions/backups/class-fw-extension-backups.php:400
#: ../core/components/extensions/manager/available-extensions.php:156
msgid "Backup"
msgstr "Sauvegarde"

#: ../extensions/backups/class-fw-extension-backups.php:554
#: ../extensions/backups/class-fw-extension-backups.php:568
msgid "Access Denied"
msgstr "Accès refusé"

#: ../extensions/backups/class-fw-extension-backups.php:561
msgid "Archive not found"
msgstr "Archive introuvable"

#: ../extensions/backups/class-fw-extension-backups.php:575
#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:202
msgid "Failed to open file"
msgstr "Échec de l'ouverture du fichier"

#: ../extensions/backups/helpers.php:37
msgid "Invalid JSON response"
msgstr "Réponse JSON non valide"

#: ../extensions/backups/helpers.php:44
msgid ""
"HTTP Loopback Connections are not enabled on this server. If you need to "
"contact your web host, tell them that when PHP tries to connect back to the "
"site at the URL `{url}` and it gets the error `{error}`. There may be a "
"problem with the server configuration (eg local DNS problems, mod_security, "
"etc) preventing connections from working properly."
msgstr ""
"Connexions de bouclage HTTP ne sont pas activés sur ce serveur. Si vous "
"devez contacter votre hébergeur, dites-leur que lorsque PHP tente de se "
"connecter vers le site à l'URL `{url}` et il obtient l'erreur `{erreur}`. Il "
"peut y avoir un problème avec la configuration du serveur (par exemple, les "
"problèmes de DNS locaux, mod_security, etc.) empêchant les connexions de "
"fonctionner correctement."

#: ../extensions/backups/helpers.php:123 ../extensions/backups/helpers.php:145
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:363
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:431
#, php-format
msgid "Failed to create dir: %s"
msgstr "Impossible de créer le répertoire : %s"

#: ../extensions/backups/helpers.php:152
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:137
#, php-format
msgid "Failed to copy: %s"
msgstr "Échec de la copie: %s"

#: ../extensions/backups/manifest.php:7
#: ../core/components/extensions/manager/available-extensions.php:168
msgid "Backup & Demo Content"
msgstr "Sauvegarde & Démo"

#: ../extensions/backups/manifest.php:9
#: ../core/components/extensions/manager/available-extensions.php:169
msgid ""
"This extension lets you create an automated backup schedule, import demo "
"content or even create a demo content archive for migration purposes."
msgstr ""
"Cette extension vous permet de créer un calendrier, le contenu de "
"démonstration d'importation sauvegarde automatisée ou même créer une archive "
"de contenu de démonstration à des fins de migration."

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:97
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:98
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:385
msgid "Demo Content Install"
msgstr "Installation de la démo de contenu"

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:282
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:329
#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:362
msgid "Forbidden"
msgstr "Interdit"

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:342
msgid "Invalid demo"
msgstr "Démo non valide"

#: ../extensions/backups/extensions/backups-demo/class-fw-extension-backups-demo.php:349
msgid "A content install is currently running"
msgstr "Un contenu d'installation est en cours d'exécution"

#: ../extensions/backups/extensions/backups-demo/views/page.php:28
#: ../extensions/backups/extensions/backups-demo/views/page.php:39
#: ../extensions/backups/views/page.php:17
#: ../extensions/backups/views/page.php:28
msgid "Important"
msgstr "Important"

#: ../extensions/backups/extensions/backups-demo/views/page.php:30
#: ../extensions/backups/views/page.php:19
#, php-format
msgid "You need to activate %s."
msgstr "Vous devez activer %s."

#: ../extensions/backups/extensions/backups-demo/views/page.php:31
#: ../extensions/backups/views/page.php:20
msgid "zip extension"
msgstr "extension zip"

#: ../extensions/backups/views/page.php:70
msgid "Archives"
msgstr "Archives"

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../extensions/backups/includes/module/schedule/settings-options.php:76
msgid "Full Backup"
msgstr "Sauvegarde complète"

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:69
#: ../extensions/backups/includes/module/schedule/settings-options.php:85
msgid "Content Backup"
msgstr "sauvegarde du contenu"

#: ../extensions/backups/includes/list-table/class--fw-ext-backups-list-table.php:88
msgid ""
"Warning! \n"
"You are about to delete a backup, it will be lost forever. \n"
"Are you sure?"
msgstr ""
"Attention!\n"
"Vous êtes sur le point de supprimer une sauvegarde, il sera perdu à jamais.\n"
"Êtes-vous sûr?"

#: ../extensions/backups/includes/module/schedule/settings-options.php:16
msgid "Interval"
msgstr "Intervalle"

#: ../extensions/backups/includes/module/schedule/settings-options.php:20
#: ../core/components/extensions/manager/views/extension.php:180
msgid "Disabled"
msgstr "Désactivé"

#: ../extensions/backups/includes/module/schedule/settings-options.php:25
msgid "Select how often do you want to backup your website."
msgstr ""
"Sélectionnez la fréquence que vous souhaitez sauvegarder votre site Web."

#: ../extensions/backups/includes/module/schedule/settings-options.php:32
#: ../extensions/backups/includes/module/schedule/settings-options.php:45
#: ../extensions/backups/includes/module/schedule/settings-options.php:58
msgid "Age Limit"
msgstr "limite d'âge"

#: ../extensions/backups/includes/module/schedule/settings-options.php:33
msgid "Age limit of backups in months"
msgstr "Limite d'âge des sauvegardes en mois"

#: ../extensions/backups/includes/module/schedule/settings-options.php:46
msgid "Age limit of backups in weeks"
msgstr "Limite d'âge des sauvegardes en semaines"

#: ../extensions/backups/includes/module/schedule/settings-options.php:59
msgid "Age limit of backups in days"
msgstr "Limite d'âge des sauvegardes en jours"

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:186
msgid "Backup Schedule"
msgstr "Planification de la sauvegarde"

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:288
msgid "Once Weekly"
msgstr "Une fois par semaine"

#: ../extensions/backups/includes/module/schedule/class--fw-ext-backups-module-schedule.php:292
msgid "Once a month"
msgstr "Une fois par mois"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:139
msgid "undefined"
msgstr "indéfini"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:279
msgid "Task type not registered"
msgstr "Type de tâche non enregistré"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:286
msgid "Execution stopped (next step did not started)"
msgstr "Exécution arrêtée (l'étape suivante n'a pas commencé)"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:306
msgid "Timed out"
msgstr "Temps passé"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:366
msgid "Invalid execution end time"
msgstr "Exécution invalide de l'heure de fin"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:537
msgid "Invalid execution result"
msgstr "Exécution invalide du résultat"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:639
msgid "Invalid token"
msgstr "Jeton invalide"

#: ../extensions/backups/includes/module/tasks/class--fw-ext-backups-module-tasks.php:658
msgid "Invalid tasks hash"
msgstr "tâches non valides hachage"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-restore.php:9
msgid "Image Sizes Restore"
msgstr "Tailles d'image restaurer"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:12
msgid "Archive Unzip"
msgstr "Archive Décompressez"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:39
msgid "Zip file not specified"
msgstr "Fichier Zip non spécifié"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:47
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:50
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:49
#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:114
msgid "Destination dir not specified"
msgstr "Destination \"dir\" non spécifié"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:56
msgid "Destination dir is not empty"
msgstr "Destination dir n'est pas vide"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:63
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:59
#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:50
msgid "Zip extension missing"
msgstr "Extension zip manquant"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-unzip.php:71
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:70
#, php-format
msgid "Cannot open zip (Error code: %s)"
msgstr "Vous ne pouvez pas ouvrir le zip (codes d'erreur : %s)"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:38
msgid "Database export"
msgstr "Exportation de la base de données"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:71
msgid "Database table disappeared"
msgstr "La base de donnés a disparu"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:91
msgid "Cannot create file"
msgstr "Impossible de créer le fichier"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:97
msgid "Cannot reopen file"
msgstr "Vous ne pouvez pas rouvrir le dossier"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:141
msgid "Cannot export CREATE TABLE sql"
msgstr "Vous ne pouvez pas exporter CREATE TABLE sql"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:211
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-export.php:267
msgid "Cannot get next database table"
msgstr "Impossible d'obtenir la prochaine table de base de données"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:9
msgid "Files Restore"
msgstr "Restaurer les fichiers"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:32
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:41
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:45
msgid "Source dir not specified"
msgstr "Destination dir n'est pas spécifié"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:39
msgid "Invalid source dir"
msgstr "Source dir invalide"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:46
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:25
msgid "Source dirs not specified"
msgstr "Source dirs non spécifié"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:116
msgid "No filesystem access, credentials required"
msgstr ""
"Au accès du système de fichiers, les informations d'identification sont "
"nécessaires"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:120
msgid "No filesystem access, invalid credentials"
msgstr ""
"Au accès du système de fichiers, les informations d'identification ne sont "
"pas valides"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:126
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:135
msgid "Filesystem init failed"
msgstr "File System Échec de l'initialisation"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:192
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:317
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:341
#, php-format
msgid "Cannot convert Filesystem path: %s"
msgstr "Vous ne pouvez pas convertir les chemins du système de fichiers : %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:197
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:322
#, php-format
msgid "Failed to list dir: %s"
msgstr "Échec de la liste du répertoire :% s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:227
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:281
#, php-format
msgid "Failed to remove dir: %s"
msgstr "Impossible de supprimer le répertoire \"%s\"."

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:234
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:288
#, php-format
msgid "Failed to remove file: %s"
msgstr "Impossible de supprimer les fichiers : %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:376
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-restore.php:444
#, php-format
msgid "Failed to copy file: %s"
msgstr "Impossible de copier les fichiers : %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:12
msgid "Archive Zip"
msgstr "Zip Archive"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:97
msgid "Cannot close the zip file"
msgstr "Impossible de fermer le fichier zip"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-zip.php:103
msgid "Cannot move zip in destination dir"
msgstr "Vous ne pouvez pas déplacer le zip dans la destination du répertoire"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:9
msgid "Files Export"
msgstr "Fichiers d'exportation"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:33
msgid "Destination not specified"
msgstr "Destination non spécifiée"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:74
#, php-format
msgid "Source dir %s is empty"
msgstr "Répertoire source %s est vide"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:116
msgid "Failed to get dir chmod"
msgstr "Impossible d'obtenir dir chmod"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:123
msgid "Failed to create destination dir"
msgstr "Impossible de créer la destination du répertoire"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-files-export.php:214
#, php-format
msgid "Failed to restore dir listing from: %s"
msgstr "Impossible de restaurer fiche dir de: %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:12
msgid "Database restore"
msgstr "Base de données de restauration"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:63
msgid "Database file not found"
msgstr "Fichier de base de données non trouvée"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:111
#, php-format
msgid "Cannot drop temporary table: %s"
msgstr "Impossible de supprimer la table temporaire : %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:129
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:285
msgid "Cannot open db file"
msgstr "Impossible d'ouvrir le fichier de la base de donnée"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:138
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:294
msgid "Cannot move cursor in db file"
msgstr ""
"Vous ne pouvez pas déplacer le curseur dans le fichier de la base de donnée"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:154
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:377
#, php-format
msgid "Failed to decode line %d from db file."
msgstr "Impossible de décoder la ligne %d du fichier de la base de donnée."

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:186
#, php-format
msgid "Cannot read line %d from db file"
msgstr "Vous ne pouvez pas lire la lignes %d du fichier de la base de donnée"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:195
msgid "Required params not found"
msgstr "Paramètre obligatoires est introuvables"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:232
msgid "Cannot do full db restore because backup is missing some tables"
msgstr ""
"Vous ne pouvez pas faire plein db restauration car la sauvegarde est "
"manquant quelques tables"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:396
#, php-format
msgid "Failed to drop tmp table %s"
msgstr "Échec de glisser la table de temp %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:435
#, php-format
msgid "Failed to create tmp table %s"
msgstr "Impossible de créer la table de temp %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:447
#, php-format
msgid "Tried to insert data in table that was not imported %s"
msgstr ""
"Essayé d'insérer des données dans le tableau qui n'a pas été importés %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:606
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:628
#, php-format
msgid "Failed insert row from line %d"
msgstr "Échec ligne d'insertion de la ligne %d"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:642
#, php-format
msgid "Invalid json type %s in db file"
msgstr "Type JSON invalide %s dans le fichier de la base de donnée"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:649
msgid "Cannot read line from db file"
msgstr "Vous ne pouvez pas lire ligne à partir du fichier de la base de donnés"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:688
msgid "Failed to restore options keeping step"
msgstr "Impossible de restaurer l'étape des options de conservation"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:723
#, php-format
msgid "Failed to keep option: %s"
msgstr "Impossible de garder l'option: %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:774
msgid "Tables drop failed"
msgstr "Tables goutte échoué"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:787
msgid "Tables rename failed."
msgstr "Tables renommez échoué."

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-db-restore.php:799
#, php-format
msgid "Invalid sub task %s"
msgstr "Sous-tâches non valides %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:12
msgid "Directory Cleanup"
msgstr "Annuaire Nettoyage"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:21
msgid "Dir not specified"
msgstr "Dir non spécifié"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:30
msgid "Cannot remove directory"
msgstr "Vous ne pouvez pas supprimer le répertoire"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:37
msgid "Cannot create directory"
msgstr "Vous ne pouvez pas créer le répertoire"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:55
#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-dir-clean.php:70
#, php-format
msgid "Cannot create file: %s"
msgstr "Vous ne pouvez pas créer le fichier: %s"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:9
msgid "Image Sizes Remove"
msgstr "Tailles des images supprimer"

#: ../extensions/backups/includes/module/tasks/type/class-fw-ext-backups-task-type-image-sizes-remove.php:23
msgid "Uploads dir not specified"
msgstr "Envois dir non précisés"

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:18
msgid "Download"
msgstr "Télécharger"

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:119
msgid "Invalid destination dir"
msgstr "Invalid destination dir"

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:132
#, php-format
msgid "Invalid type: %s"
msgstr "Type non valide: %s"

#: ../extensions/backups/includes/module/tasks/type/download/class-fw-ext-backups-task-type-download.php:141
#, php-format
msgid "Args not specified for type: %s"
msgstr "Arguments non spécifié pour le type : %s"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:9
msgid "Local Download"
msgstr "Télécharger local"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:24
msgid "Source not specified"
msgstr "Source non spécifiée"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:29
msgid "Invalid source"
msgstr "Source non valide"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:38
msgid "Invalid source type"
msgstr "Type de source non valide"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:59
#, php-format
msgid "Cannot open zip: %s"
msgstr "Vous ne pouvez pas ouvrir le zip: %s"

#: ../extensions/backups/includes/module/tasks/type/download/type/class-fw-ext-backups-task-type-download-local.php:70
msgid "Unhandled type"
msgstr "Type non géré"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:13
msgid "Downloading..."
msgstr "Téléchargement en cours..."

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:16
msgid "Download finished. Doing unzip..."
msgstr "Téléchargement terminé. Décompression..."

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:19
#, php-format
msgid "Downloading... %s of %s"
msgstr "Téléchargement ... %s de %s"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:24
#, php-format
msgid "Downloading... %s"
msgstr "Téléchargement ... %s"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:67
msgid "Url not specified"
msgstr "Url non spécifié"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:72
msgid "Invalid url"
msgstr "URL invalide"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:79
msgid "File id not specified"
msgstr "Id de fichier non spécifié"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:84
msgid "Invalid file id"
msgstr "Id de fichier non valide"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:105
#, php-format
msgid "Zip open failed (code %d). Please try again"
msgstr "L'extrait de Zip a échoué (code %d). Veuillez réessayer"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:112
msgid "Zip extract failed"
msgstr "L'extrait de Zip a échoué"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:119
msgid "Failed to close the zip after extract"
msgstr "Impossible de fermer le zip après l'extrait"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:152
#, php-format
msgid "Request failed. Error code: %d"
msgstr "Demande échoué. Code d'erreur : %d"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:163
msgid "Invalid byte position"
msgstr "Position de l'octet non valide"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:168
msgid "Empty response body"
msgstr "Corps de la réponse vide"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:188
msgid "File ended without content"
msgstr "Fichier terminée sans contenu"

#: ../extensions/backups/includes/module/tasks/type/download/type/piecemeal/class-fw-ext-backups-task-type-download-piecemeal.php:213
msgid "Failed to write data to file"
msgstr "Impossible d'écrire des données à déposer"

#: ../extensions/translation/settings-options.php:11
msgid "Default Language"
msgstr "Langage par défaut"

#: ../extensions/translation/settings-options.php:12
msgid "This is the default language of your website."
msgstr "Ceci est la langue par défaut de votre site web."

#: ../extensions/translation/settings-options.php:19
msgid "Translate to"
msgstr "Traduire en"

#: ../extensions/translation/settings-options.php:20
msgid "Choose the languages you want your website translated to."
msgstr "Choisissez les langues que vous voulez que votre site traduit à."

#: ../extensions/translation/settings-options.php:27
msgid "Convert data"
msgstr "Convertir les données"

#: ../extensions/translation/settings-options.php:28
msgid ""
"Set to default language the posts, pages categories or tags that don't have "
"a language set ?"
msgstr ""
"Réglez les messages, pages catégories ou tags qui ne disposent pas d'un jeu "
"de langue par défaut langue?"

#: ../extensions/translation/manifest.php:7
#: ../extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:336
#: ../core/components/extensions/manager/available-extensions.php:264
msgid "Translations"
msgstr "Traductions"

#: ../extensions/translation/manifest.php:8
#: ../core/components/extensions/manager/available-extensions.php:265
msgid ""
"This extension lets you translate your website in any language or even add "
"multiple languages for your users to change at their will from the front-end."
msgstr ""
"Cette extension vous permet de traduire votre site Web dans toutes les "
"langues ou même ajouter plusieurs langues pour vos utilisateurs de modifier "
"à leur gré du front-end."

#: ../extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:114
msgid "All Languages"
msgstr "Toutes les categoriesToutes les langues"

#: ../extensions/translation/extensions/translate-terms/class-fw-extension-translate-terms.php:332
msgid "The term translation does already exists.ACTION +++ "
msgstr "Le terme traduit existe déjà. ACTION +++ "

#: ../extensions/translation/extensions/translate-terms/manifest.php:7
msgid "Translate Terms"
msgstr "Traduire les Conditions"

#: ../extensions/translation/extensions/translate-terms/manifest.php:8
msgid "This extension translate terms"
msgstr "Cette extension traduire les termes"

#: ../extensions/translation/extensions/translate-posts/class-fw-extension-translate-posts.php:341
msgid "Language of this post"
msgstr "Langue de cet article"

#: ../extensions/translation/extensions/translate-posts/manifest.php:7
msgid "Translate Posts"
msgstr "Traduire l'article"

#: ../extensions/translation/extensions/translate-posts/manifest.php:8
msgid "This extension translate posts"
msgstr "Cette extension traduis les articles"

#: ../extensions/translation/extensions/translate-widgets/manifest.php:7
msgid "Translate Widgets"
msgstr "Traduire les Widgets"

#: ../extensions/translation/extensions/translate-widgets/manifest.php:8
msgid "This extension translate Widgets"
msgstr "Cette extension traduit les Widgets"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:10
msgid "Language Switcher"
msgstr "Language des Switcher"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:11
msgid "A Language Switcher Widget"
msgstr "Un widget de sélecteur de langue"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:42
msgid "New title"
msgstr "Nouveau titre"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-language-switcher.php:45
#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:49
msgid "Title:"
msgstr "Titre :"

#: ../extensions/translation/extensions/translate-widgets/includes/class-fw-widget-calendar.php:14
msgid "A calendar of your site&#8217;s Posts."
msgstr "Un calendrier des messages de votre site&#8217;s."

#: ../extensions/translation/extensions/translate-menus/manifest.php:7
msgid "Translate Menus"
msgstr "Traduire les menus"

#: ../extensions/translation/extensions/translate-menus/manifest.php:8
msgid "This extension translate menus"
msgstr "Cette extension traduit les menus"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:211
msgid "Slider Design"
msgstr "Slider Design"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:213
msgid "Number of Images"
msgstr "Nombre d'images"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:231
#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:234
#, php-format
msgid "%s updated."
msgstr "%s mis à jour."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:232
msgid "Custom field updated."
msgstr "Champ personnalisé mis à jour."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:233
msgid "Custom field deleted."
msgstr "Champ personnalisé supprimer."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:235
#, php-format
msgid "%s restored to revision from %s"
msgstr "%s de restauration à la révision de %s"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:237
#, php-format
msgid "%s published."
msgstr "%s publiés."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:238
msgid "Page saved."
msgstr "Page sauvegardé."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:239
#, php-format
msgid "%s submitted."
msgstr "%s soumis."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:240
#, php-format
msgid "%s scheduled for: %s."
msgstr "%s prévu pour : %s"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:242
#, php-format
msgid "%s draft updated."
msgstr "%s brouillon mise à jour"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:272
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:52
msgid "Publish"
msgstr "Publier"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:342
msgid ""
"This slider was created correctly, but the code implementation was delete "
"from source code or blocked from filter.Delete this post or recovery slider "
"implementation"
msgstr ""
"Ce Slider a été correctement créé, mais le code exécuté a supprimé le code "
"source ou la bloqué par l'action d'un filtre. Supprimer ce poste ou exécuté "
"une récupération."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:351
msgid ""
"This slider was created correctly, but the multimedia_types from config.php "
"file was deleted, please set multimedia_types for this slider type."
msgstr ""
"Ce Slider a été créé correctement, mais les multimédias_types à partir du "
"fichier config.php a été supprimé, il faut définir les multimédias_ types "
"pour ce type de Slider."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:361
msgid "Slider Configuration"
msgstr "Configuration du Slider"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:375
msgid "Slider Title"
msgstr "Titre du Slider"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:377
msgid "Choose a title for your slider only for internal use: Ex: \"Homepage\"."
msgstr ""
"Choisissez un titre pour votre slider uniquement pour un usage interne: Ex: "
"\"Page d'accueil\"."

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:433
msgid "Slider Settings"
msgstr "Paramètres Slider"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:461
msgid ""
"You don't have slider extensions, please create at least one extension for "
"properly work"
msgstr ""
"Vous ne disposez pas d'extensions pour le slider, s'il vous plaît créer au "
"moins une extension pour fonctionner correctement"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:474
msgid "Choose the population method for your slider"
msgstr "Choisissez la méthode de la population pour votre slider"

#: ../extensions/media/extensions/slider/class-fw-extension-slider.php:542
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:94
msgid "(no title)"
msgstr "(pas de titre)"

#: ../extensions/media/extensions/slider/posts.php:6
#: ../extensions/media/extensions/slider/posts.php:12
#: ../extensions/media/extensions/slider/posts.php:18
#: ../extensions/media/extensions/slider/manifest.php:5
#: ../core/components/extensions/manager/available-extensions.php:12
msgid "Sliders"
msgstr "Sliders"

#: ../extensions/media/extensions/slider/posts.php:7
#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:8
msgid "Slider"
msgstr "Slider"

#: ../extensions/media/extensions/slider/posts.php:9
msgid "Add New Slider"
msgstr "Ajouter un nouveau Slider"

#: ../extensions/media/extensions/slider/posts.php:10
msgid "Edit Slider"
msgstr "Editer le Slider"

#: ../extensions/media/extensions/slider/posts.php:11
msgid "New Slider"
msgstr "Nouveau Slider"

#: ../extensions/media/extensions/slider/posts.php:13
msgid "View Slider"
msgstr "Voir le Slider"

#: ../extensions/media/extensions/slider/posts.php:14
msgid "Search Sliders"
msgstr "Recherche Sliders"

#: ../extensions/media/extensions/slider/posts.php:15
msgid "No Sliders found"
msgstr "Aucun Slider trouvé."

#: ../extensions/media/extensions/slider/posts.php:16
msgid "No Sliders found in Trash"
msgstr "Aucun Slider trouvé dans la poubelle"

#: ../extensions/media/extensions/slider/manifest.php:6
msgid ""
"Adds the Sliders extension to your website. You'll be able to create "
"different built in jQuery sliders for your homepage and all the other "
"website pages."
msgstr ""
"Ajoute l'extension Sliders à votre site Web. Vous serez en mesure de créer "
"différents Slider construit pour votre page d'accueil et toutes les autres "
"pages du site."

#: ../extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/nivo-slider/manifest.php:6
msgid "Nivo Slider"
msgstr "Nivo Slider"

#: ../extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/owl-carousel/manifest.php:6
msgid "Owl Slider"
msgstr "Owl Slider"

#: ../extensions/media/extensions/slider/extensions/bx-slider/manifest.php:5
#: ../extensions/media/extensions/slider/extensions/bx-slider/manifest.php:6
msgid "Bx-Slider"
msgstr "Bx-Slider"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:4
msgid "Population Method Categories opt 1"
msgstr "Population Méthode Catégories opt 1"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:5
#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:11
msgid "Option description"
msgstr "Description de l'option"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/categories.php:10
msgid "Population Method Categories opt 2"
msgstr "Population Méthode Catégories opt 2"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:5
msgid "Type of Transition"
msgstr "Type de transition"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:6
msgid "Type of transition between slides"
msgstr "Type de transition entre les diapositives"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:9
msgid "Horizontal"
msgstr "Horizontal"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:10
msgid "Vertical"
msgstr "Verticale"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/options.php:11
msgid "Fade"
msgstr "Fade"

#: ../extensions/media/extensions/slider/extensions/bx-slider/options/custom.php:9
msgid "Choose a subtitle for your slide."
msgstr "Choisissez un sous-titre pour votre diapositive."

#: ../extensions/media/extensions/slider/shortcodes/slider/config.php:9
msgid "Add a Slider"
msgstr "Ajouter un Slider"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:9
msgid "Select Slider"
msgstr "Sélectionner le Slider"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:14
msgid "Set width"
msgstr "Réglez la hauteur"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:19
msgid "Set height"
msgstr "Réglez la hauteur"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:34
msgid "No Sliders Available"
msgstr "Aucun Sliders disponible"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:47
msgid "create a new Slider"
msgstr "Créer un nouveau Slider"

#: ../extensions/media/extensions/slider/shortcodes/slider/options.php:49
msgid ""
"No Sliders created yet. Please go to the {br}Sliders page and "
"{add_slider_link}."
msgstr ""
"Aucun Sliders encore créé. S'il vous plaît aller à la {br} Sliders la page "
"et {add_slider_link}."

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:5
msgid ""
"Note that the type and population can't be changed later. You'll need to "
"create a new slider to have a different slider type or population method."
msgstr ""
"Notez que le type et la population ne peuvent pas être modifiées "
"ultérieurement. Vous aurez besoin de créer un nouveau Slider pour avoir un "
"type de Slider ou d'une méthode différente de la population."

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:31
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:48
msgid "Schedule"
msgstr "Programme"

#: ../extensions/media/extensions/slider/views/backend/submit-box-raw.php:40
#: ../extensions/media/extensions/slider/views/backend/submit-box-edit.php:57
msgid "Submit for Review"
msgstr "Soumettre pour correction"

#: ../extensions/media/extensions/population-method/class-fw-extension-population-method.php:36
#, php-format
msgid "Specified population method does not exists: %s"
msgstr "Méthode de la population spécifique n'existe pas : %s"

#: ../extensions/media/extensions/population-method/class-fw-extension-population-method.php:55
#, php-format
msgid "Population method %s does not exist"
msgstr "Méthode de la population %s n'existe pas"

#: ../extensions/media/extensions/population-method/manifest.php:3
msgid "Population Methods"
msgstr "Méthodes de population"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:21
msgid "Automatically, fetch images from categories"
msgstr "Automatiquement, chercher des images des catégories"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:29
#, php-format
msgid "%s extension needs configured categories in post types"
msgstr ""
"L'extension %s a besoin de configurer les catégories dans le types d'article"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:34
msgid "Categories Population Method"
msgstr "Catégories de la méthode population"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:44
msgid "Choose Category"
msgstr "Choisir une catégorie"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:53
#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:53
msgid "Number of Images in the slider"
msgstr "Nombre d'images dans le curseur"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/class-fw-extension-population-method-categories.php:107
msgid "Select Specific Categories"
msgstr "Sélectionnez les catégories spécifiques"

#: ../extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-categories/manifest.php:4
msgid "Population Method - Categories"
msgstr "Population Méthode - Catégories"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:21
msgid "Automatically, fetch images from tags"
msgstr "Automatiquement, chercher des images à partir des étiquettes"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:29
#, php-format
msgid "%s extension needs configured tags in post types"
msgstr ""
"Les besoins d'extension de %s configurés balises dans les types de poste"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:34
msgid "Tags Population Method"
msgstr "Mots-clefs Méthode Population"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:44
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:44
msgid "Choose Tag"
msgstr "Choisir le tag"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/class-fw-extension-population-method-tags.php:107
msgid "Select Specific tags"
msgstr "Sélectionnez les balises spécifiques"

#: ../extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-tags/manifest.php:4
msgid "Population Method - Tags"
msgstr "Population Méthode - Tags"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:21
msgid "Automatically, fetch images from posts"
msgstr "Automatiquement, rapporter les images des articles"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:29
#, php-format
msgid "%s extension needs configured post categories in post types"
msgstr ""
"Les besoins d'extension de %s configurées catégories de poste dans les types "
"de poste"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:34
msgid "Posts Population Method"
msgstr "Publie Méthode Population"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/class-fw-extension-population-method-posts.php:101
msgid "Select Specific posts"
msgstr "Sélectionnez les messages spécifiques"

#: ../extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-posts/manifest.php:4
msgid "Population Method - Posts"
msgstr "Méthode de la population - Articles"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:22
msgid "Manually, I'll upload the images myself"
msgstr "Manuellement, je vais télécharger les images moi-même"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:33
msgid "Click to edit / Drag to reorder"
msgstr "Cliquez pour modifier / Glisser pour réorganiser"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/class-fw-extension-population-method-custom.php:54
msgid "Choose"
msgstr "Choisir"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:3
#: ../extensions/media/extensions/population-method/extensions/population-method-custom/manifest.php:4
msgid "Population Method - Custom"
msgstr "Méthode de la population - Personnalisé"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:57
msgid "Add Slide"
msgstr "Ajouter un Slide"

#: ../extensions/media/extensions/population-method/extensions/population-method-custom/includes/slides/views/slides.php:59
#: ../views/backend-settings-form.php:47
msgid "Save Changes"
msgstr "Sauvegarder les modifications"

#: ../core/Fw.php:73
msgid "Framework requirements not met:"
msgstr "Les exigences du Framework ne sont pas satisfaites:"

#: ../core/class-fw-manifest.php:293
msgid "minimum required version is"
msgstr "version minimale requise est"

#: ../core/class-fw-manifest.php:296
msgid "maximum required version is"
msgstr "version maximale requise est"

#: ../core/class-fw-manifest.php:301
msgid "and"
msgstr "et"

#: ../core/class-fw-manifest.php:308
#, php-format
msgid "Current WordPress version is %s, %s"
msgstr "La version actuelle de WordPress est %s, %s"

#: ../core/class-fw-manifest.php:314
#, php-format
msgid "Current Framework version is %s, %s"
msgstr "La version actuelle du Framework est %s, %s"

#: ../core/class-fw-manifest.php:323
#, php-format
msgid "Current version of the %s extension is %s, %s"
msgstr "La version actuelle de l'extension du %s est %s, %s"

#: ../core/class-fw-manifest.php:329
#, php-format
msgid "%s extension is required"
msgstr "L'extension %s est nécessaire"

#: ../core/class-fw-manifest.php:334
#, php-format
msgid "%s extension is required (%s)"
msgstr "L'extension %s est nécessaire (%s)"

#: ../core/extends/class-fw-option-type.php:283
#, php-format
msgid "Option type %s has no default value"
msgstr "Type d'option %s n'a pas de valeur par défaut"

#: ../core/components/backend.php:355
msgid "Done"
msgstr "Terminé"

#: ../core/components/backend.php:356
msgid "Ah, Sorry"
msgstr "Ah désolé"

#: ../core/components/backend.php:358
#: ../includes/option-types/color-picker/class-fw-option-type-color-picker.php:39
#: ../includes/option-types/color-picker/class-fw-option-type-color-picker.php:40
#: ../includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:41
#: ../includes/option-types/rgba-color-picker/class-fw-option-type-rgba-color-picker.php:42
msgid "Reset"
msgstr "Réinitialiser"

#: ../core/components/backend.php:541 ../core/components/backend.php:542
#: ../core/components/backend.php:650
msgid "Theme Settings"
msgstr "Réglage des thèmes"

#: ../core/components/backend.php:577
msgid "leave a review"
msgstr "laisser un avis"

#: ../core/components/backend.php:588
msgid ""
"Unyson WordPress Framework is the fastest and easiest way to develop a "
"premium theme. I highly recommend it"
msgstr ""
"Unyson est le moyen le plus rapide et le plus facile de développer un thème "
"premium. Je le recommande fortement"

#: ../core/components/backend.php:594
msgid ""
"If you like Unyson, {wp_review_link}, share on {facebook_share_link} or "
"{twitter_share_link}."
msgstr ""
"Si vous aimez Unyson, {wp_review_link}, partager sur {facebook_share_link} "
"ou {twitter_share_link}."

#: ../core/components/backend.php:1303
msgid "You have no permissions to change settings options"
msgstr "Vous n'avez pas autorisé à modifier les options de paramètres"

#: ../core/components/backend.php:1316
msgid "The options were successfully reset"
msgstr "Les options ont été réinitialisés avec succès"

#: ../core/components/backend.php:1327
msgid "The options were successfully saved"
msgstr "Les options ont été enregistrées avec succès"

#: ../core/components/backend.php:1440
msgid "Unknown collected group"
msgstr "Groupe incconu rassemblé"

#: ../core/components/backend.php:1779
#, php-format
msgid "Undefined option type: %s"
msgstr "Type d'option indéfini : %s"

#: ../core/components/backend.php:1826
#, php-format
msgid "Undefined container type: %s"
msgstr "Type de conteneur indéfini : %s"

#: ../core/components/extensions.php:447 ../core/components/extensions.php:525
#, php-format
msgid "Extension %s is invalid."
msgstr "Type de message flash invalide : %s."

#: ../core/components/theme.php:206
msgid "Theme requirements not met:"
msgstr "Les exigences thématiques ne sont pas satisfaites :"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:351
msgid "Cannot remove the old extensions backup dir"
msgstr ""
"Impossible de supprimer l'ancien répertoire de sauvegarde des extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:357
msgid "Cannot create the extensions backup dir"
msgstr "Impossible de créer le répertoire extensions de sauvegarde"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:362
msgid "Cannot backup the extensions"
msgstr "Impossible de sauvegarder les extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:408
msgid "Cannot clear the extensions directory"
msgstr "Vous ne pouvez pas effacer le répertoire d'extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:413
msgid "Cannot recreate the extensions directory"
msgstr "Vous ne pouvez pas recréer le répertoire d'extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:419
msgid "Cannot recover the extensions"
msgstr "Vous ne pouvez pas récupérer les extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:645
#, php-format
msgid "Cannot activate hidden standalone extension %s"
msgstr "Vous ne pouvez pas activer l'extension autonome cachée % s"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:874
msgid "You are not allowed to install extensions."
msgstr "Vous n'êtes pas autorisé à installer des extensions."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:891
msgid "All supported extensions are already installed."
msgstr "Toutes les extensions prises en charge sont déjà installés."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:967
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2384
#, php-format
msgid "Cannot remove temporary directory: %s"
msgstr "Impossible de supprimer le répertoire temporaire: %s"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1037
msgid "You have no permissions to install extensions"
msgstr "Vous n'avez pas d'autorisations pour installer des extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1044
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1517
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1901
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2116
msgid "No extensions provided"
msgstr "Aucune extensions fournies"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1053
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1527
msgid "WP Filesystem is not initialized"
msgstr "WP Filesystem n'est pas initialisé"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1079
#, php-format
msgid "Extension \"%s\" is already installed."
msgstr "L'extension \"%s\" est déjà installé."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1094
#, php-format
msgid "Extension \"%s\" is not available for install."
msgstr "L'extension \"%s\" n'est pas disponible pour l'installation."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1122
#, php-format
msgid "Parent extension \"%s\" not available."
msgstr "L'extension \"%s\" n'est pas disponible."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1156
#, php-format
msgid "Downloading the \"%s\" extension..."
msgstr "Téléchargement de l'extension \"%s\"..."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1200
#, php-format
msgid "Installing the \"%s\" extension..."
msgstr "Installation de l'extension \"%s\" ..."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1238
#, php-format
msgid "The %s extension has been successfully installed."
msgstr "L'extension %s a été installée avec succès."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1399
msgid "You are not allowed to delete extensions."
msgstr "Vous n'êtes pas autorisé à supprimer des extensions."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1510
msgid "You have no permissions to uninstall extensions"
msgstr "Vous n'avez pas les autorisations pour désinstaller les extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1578
#, php-format
msgid "Deleting the \"%s\" extension..."
msgstr "Suppression de l'extension \"%s\" ..."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1590
#, php-format
msgid "Cannot delete the \"%s\" extension."
msgstr "Vous ne pouvez pas supprimer l'extension \"%s\"."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1602
#, php-format
msgid "The %s extension has been successfully deleted."
msgstr "L'extension %s a été supprimé avec succès."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1703
msgid "Extension not specified."
msgstr "L'extension n'est pas spécifié."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1708
#, php-format
msgid "Extension \"%s\" is not installed."
msgstr "L'extension \"%s\" n'est pas installé."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1776
#, php-format
msgid "Extension \"%s\" does not exist or is not active."
msgstr "L'extension \"%s\" n'existe pas ou n'est pas active."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1785
#, php-format
msgid "%s extension does not have settings."
msgstr "L'extension %s n'a pas de paramètres."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1806
msgid "Extension has no Install Instructions"
msgstr "Instructions d'installation"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1830
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2047
msgid "Invalid request method."
msgstr "Méthode de requête non valide."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1842
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2059
msgid "No extension specified."
msgstr "Aucune extension spécifiée."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1894
msgid "You have no permissions to activate extensions"
msgstr "Vous avez pas d'autorisations pour activer les extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:1914
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2132
#, php-format
msgid "Extension \"%s\" does not exist."
msgstr "L'extension \"%s\" n'existe pas."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2109
msgid "You have no permissions to deactivate extensions"
msgstr "Vous n'avez pas d'autorisations pour désactiver les extensions"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2297
msgid "You are not allowed to save extensions settings."
msgstr "Vous n'êtes pas autorisé à enregistrer les paramètres des extensions."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2304
msgid "Invalid extension."
msgstr "Extension invalide."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2309
msgid "Extension does not have settings options."
msgstr "L'extension n'a pas d'options de paramètres."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2341
msgid "Extensions settings successfully saved."
msgstr "Paramètres d'extensions enregistrées avec succès."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2368
#, php-format
msgid "Extension \"%s\" has no download sources."
msgstr "L'extension \"%s\" n'a pas de sources de téléchargement."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2392
#, php-format
msgid "Cannot create temporary directory: %s"
msgstr "Vous ne pouvez pas créer un répertoire temporaire: %s"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2403
#, php-format
msgid "\"%s\" extension github source \"user_repo\" parameter is required"
msgstr ""
"L'extension \"%s\"de source github \"utilisateur repo\" le paramètre est "
"nécessaire"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2454
#, php-format
msgid "Failed to access Github repository \"%s\" releases. (%s)"
msgstr "Impossible d'accéder au répertoire Github \"%s\". (%s)"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2477
#, php-format
msgid "\"%s\" extension github repository \"%s\" has no releases."
msgstr "L'extension %s avec pour répertoire github \"%s\" n'a pas de parution."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2510
#, php-format
msgid "Cannot download the \"%s\" extension zip. (Response code: %d)"
msgstr ""
"Vous ne pouvez pas télécharger l'extension zippé \"%s\". (Code de réponse : "
"%d)"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2517
#, php-format
msgid "Cannot download the \"%s\" extension zip. %s"
msgstr "Vous ne pouvez pas télécharger l'extension zippé \"%s\". %s"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2525
#, php-format
msgid "Cannot download the \"%s\" extension zip."
msgstr "Vous ne pouvez pas télécharger l'extension zippé \"%s\"."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2538
#, php-format
msgid "Cannot save the \"%s\" extension zip."
msgstr "Vous ne pouvez pas sauvegardé l'extension zippé \"%s\"."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2557
#, php-format
msgid "Cannot remove the \"%s\" extension downloaded zip."
msgstr "Vous ne pouvez pas supprimer l'extension zippé télécharger \"%s\"."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2582
#, php-format
msgid "The unzipped \"%s\" extension directory not found."
msgstr "Le repertoire d'extension décompressé \"%s\" est introuvable."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2589
#, php-format
msgid "Unknown \"%s\" extension download source \"%s\""
msgstr "Source de téléchargement de l'extension \"%s\" inconnue \"%s\""

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2615
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2634
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2697
#, php-format
msgid "Cannot read directory \"%s\"."
msgstr "Impossible de lire le répertoire \"%s\"."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2649
#, php-format
msgid "Cannot delete \"%s\"."
msgstr "Impossible de supprimer \"%s\"."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2660
#, php-format
msgid "Cannot create the \"%s\" directory."
msgstr "Impossible de créer le répertoire \"%s\"."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:2678
#, php-format
msgid "Cannot move \"%s\" to \"%s\"."
msgstr "Impossible de déplacer \"%s\" à \"%s\"."

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3103
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3150
#, php-format
msgid "Cannot activate the %s extension because it is not installed. %s"
msgstr ""
"Vous ne pouvez pas activer l'extension %s parce qu'il n'est pas installé. %s"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3107
#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3154
msgid "Install"
msgstr "Installation"

#: ../core/components/extensions/manager/class--fw-extensions-manager.php:3197
msgid "Install theme compatible extensions"
msgstr "Installer des extensions compatibles avec ce thème"

#: ../core/components/extensions/manager/available-extensions.php:13
msgid ""
"Adds a sliders module to your website from where you'll be able to create "
"different built in jQuery sliders for your homepage and rest of the pages."
msgstr ""
"Ajoute un module de sliders à votre site Web d'où vous serez en mesure de "
"créer différents sliders construits en jQuery pour votre page d'accueil et "
"le reste des pages."

#: ../core/components/extensions/manager/available-extensions.php:24
msgid "Media"
msgstr "Média"

#: ../core/components/extensions/manager/available-extensions.php:36
msgid "Population method"
msgstr "Méthode de la population"

#: ../core/components/extensions/manager/available-extensions.php:85
msgid ""
"Let's you easily build countless pages with the help of the drag and drop "
"visual page builder that comes with a lot of already created shortcodes."
msgstr ""
"Vous allez pouvoir construire facilement d'innombrables pages à l'aide du "
"constructeur visuel de page par glisser-déposer qui est livré avec un lot de "
"shortcodes prêts à l'emploi."

#: ../core/components/extensions/manager/available-extensions.php:96
msgid "Shortcodes"
msgstr "Shortcodes"

#: ../core/components/extensions/manager/available-extensions.php:157
msgid ""
"This extension lets you set up daily, weekly or monthly backup schedule. You "
"can choose between a full backup or a data base only backup."
msgstr ""
"Cette extension vous permet de configurer tous les jours, le calendrier de "
"sauvegarde hebdomadaire ou mensuelle. Vous pouvez choisir entre une "
"sauvegarde complète ou une base de données uniquement de sauvegarde."

#: ../core/components/extensions/manager/views/extension.php:89
#: ../core/components/extensions/manager/views/extension-page-header.php:25
msgid "Install Instructions"
msgstr "Instructions d'installation"

#: ../core/components/extensions/manager/views/extension.php:102
msgid "Compatible"
msgstr "Compatible"

#: ../core/components/extensions/manager/views/extension.php:102
msgid "with your current theme"
msgstr "avec votre thème actuel"

#: ../core/components/extensions/manager/views/extension.php:174
#, php-format
msgid "Parent extension \"%s\" is disabled"
msgstr "L'extension parent \"%s\" est désactivé"

#: ../core/components/extensions/manager/views/extension.php:199
#, php-format
msgid "You need to update WordPress to %s: %s"
msgstr "Vous devez mettre à jour Wordpress à %s : %s"

#: ../core/components/extensions/manager/views/extension.php:201
msgid "Update WordPress"
msgstr "Mettre à jour WordPress"

#: ../core/components/extensions/manager/views/extension.php:205
#, php-format
msgid "WordPress needs to be updated to %s"
msgstr "WordPress a besoin d'être mis à jour pour %s"

#: ../core/components/extensions/manager/views/extension.php:215
#, php-format
msgid "Maximum supported WordPress version is %s"
msgstr "La version maximum de Wordpress supporté est %s"

#: ../core/components/extensions/manager/views/extension.php:230
#, php-format
msgid "You need to update %s to %s: %s"
msgstr "Vous devez mettre à jour %s à %s : %s"

#: ../core/components/extensions/manager/views/extension.php:239
#, php-format
msgid "%s needs to be updated to %s"
msgstr "%s doit être mise à jours pour %s"

#: ../core/components/extensions/manager/views/extension.php:250
#, php-format
msgid "Maximum supported %s version is %s"
msgstr "La version maximum de %s supporté est %s"

#: ../core/components/extensions/manager/views/extension.php:268
#, php-format
msgid "You need to update the %s extension to %s: %s"
msgstr "Vous devez mettre à jour l'extension %s à %s : %s"

#: ../core/components/extensions/manager/views/extension.php:277
#, php-format
msgid "The %s extension needs to be updated to %s"
msgstr "L'extension %s doit être mise à jours pour %s"

#: ../core/components/extensions/manager/views/extension.php:288
#, php-format
msgid "Maximum supported %s extension version is %s"
msgstr "La version maximum de l'extension %s supporté est %s"

#: ../core/components/extensions/manager/views/extension.php:303
#, php-format
msgid "The %s extension is disabled"
msgstr "Cette extension est désactivée : %s"

#: ../core/components/extensions/manager/views/extension.php:304
#, php-format
msgid "Activate %s"
msgstr "Activer %s"

#: ../core/components/extensions/manager/views/extension.php:313
#, php-format
msgid "The %s extension is not installed: %s"
msgstr "L'extension %s n'est pas installé : %s"

#: ../core/components/extensions/manager/views/extension.php:316
#, php-format
msgid "Install %s"
msgstr "Installer %s"

#: ../core/components/extensions/manager/views/extension.php:321
#, php-format
msgid "The %s extension is not installed"
msgstr "L'extension %s n'est pas installé"

#: ../core/components/extensions/manager/views/extension.php:342
msgid "View Requirements"
msgstr "Voir les exigences"

#: ../core/components/extensions/manager/views/extension-page-header.php:40
#, php-format
msgid "%s Settings"
msgstr "%s Réglages généraux"

#: ../core/components/extensions/manager/views/extension-page-header.php:43
#, php-format
msgid "%s Install Instructions"
msgstr "%s Instructions d'installation"

#: ../core/components/extensions/manager/views/extension-page-header.php:46
msgid "Unknown tab:"
msgstr "Onglet inconnu:"

#: ../core/components/extensions/manager/views/delete-form.php:42
#: ../core/components/extensions/manager/views/install-form.php:51
msgid "No, Return me to the extension list"
msgstr "Non, retournez à la liste d'extension"

#: ../core/components/extensions/manager/views/delete-form.php:45
msgid "Click to view entire list of directories which will be deleted"
msgstr ""
"Cliquez pour voir la liste complète des répertoires qui seront supprimés"

#: ../core/components/extensions/manager/views/extensions-page.php:17
msgid "Active Extensions"
msgstr "Extensions actives"

#: ../core/components/extensions/manager/views/extensions-page.php:33
msgid "No extensions activated yet"
msgstr "Aucune extension n'est encore activée"

#: ../core/components/extensions/manager/views/extensions-page.php:33
msgid "Check the available extensions below"
msgstr "Vérifiez les extensions disponibles ci-dessous"

#: ../core/components/extensions/manager/views/extensions-page.php:59
msgid "Available Extensions"
msgstr "Extensions disponibles"

#: ../core/components/extensions/manager/views/extensions-page.php:184
msgid "Show other extensions"
msgstr "Afficher les autres extensions"

#: ../core/components/extensions/manager/views/extensions-page.php:185
msgid "Hide other extensions"
msgstr "Afficher les autres extensions"

#: ../core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:14
#: ../core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:14
msgid "Go to extensions page"
msgstr "Aller à la page des extensions"

#: ../core/components/extensions/manager/includes/class--fw-extensions-install-upgrader-skin.php:17
#: ../core/components/extensions/manager/includes/class--fw-extensions-delete-upgrader-skin.php:17
msgid "Return to Extensions page"
msgstr "Retour à la page des extensions"

#: ../views/backend-settings-form.php:48
msgid "Reset Options"
msgstr "Réinitialiser les options"

#: ../views/backend-settings-form.php:62
msgid "by"
msgstr "par"

#: ../views/backend-settings-form.php:155
msgid ""
"Click OK to reset.\n"
"All settings will be lost and replaced with default settings!"
msgstr ""
"Cliquez sur OK pour réinitialiser.\n"
"Tous les réglages seront perdus et remplacés par les paramètres par défaut!"

#: ../views/backend-settings-form.php:202
msgid "Resetting"
msgstr "Réinitialisation"

#: ../views/backend-settings-form.php:204
msgid "We are currently resetting your settings."
msgstr "Nous sommes en train de réinitialiser vos paramètres."

#: ../views/backend-settings-form.php:206
#: ../views/backend-settings-form.php:212
msgid "This may take a few moments."
msgstr "Cela peut prendre un certain temps."

#: ../views/backend-settings-form.php:208
msgid "Saving"
msgstr "Sauvegarde"

#: ../views/backend-settings-form.php:210
msgid "We are currently saving your settings."
msgstr "Nous sommes en train d'enregistrer vos paramètres."

#: ../includes/option-types/class-fw-option-type-undefined.php:25
msgid "UNDEFINED OPTION TYPE"
msgstr "TYPE D'OPTION INDEFINI"

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:22
msgid "25%"
msgstr "25%"

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:23
msgid "50%"
msgstr "50%"

#: ../includes/option-types/radio-text/class-fw-option-type-radio-text.php:24
msgid "100%"
msgstr "100%"

#: ../includes/option-types/map/views/view.php:12
msgid "Specify location"
msgstr "Indiquez l'emplacement"

#: ../includes/option-types/map/views/view.php:27
msgid "Location Venue"
msgstr "Localisation du lieu"

#: ../includes/option-types/map/views/view.php:42
msgid "Address"
msgstr "Adresse"

#: ../includes/option-types/map/views/view.php:57
msgid "City"
msgstr "Ville"

#: ../includes/option-types/map/views/view.php:72
msgid "Country"
msgstr "Pays"

#: ../includes/option-types/map/views/view.php:87
msgid "State"
msgstr "État"

#: ../includes/option-types/map/views/view.php:103
msgid "Zip Code"
msgstr "Code postal"

#: ../includes/option-types/map/views/view.php:138
msgid "Cannot find the location?"
msgstr "Vous ne trouvez pas l'emplacement?"

#: ../includes/option-types/map/views/view.php:150
msgid "Reset location"
msgstr "Réinitialiser emplacement"

#: ../includes/option-types/upload/class-fw-option-type-upload.php:150
msgid "Add Image"
msgstr "Ajouter des images"

#: ../includes/option-types/upload/class-fw-option-type-upload.php:176
#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:186
msgid "Upload"
msgstr "Télécharger"

#: ../includes/option-types/typography-v2/view.php:39
msgid "Font face"
msgstr "Type de police"

#: ../includes/option-types/typography-v2/view.php:48
msgid "Normal"
msgstr "Normal"

#: ../includes/option-types/typography-v2/view.php:49
msgid "Italic"
msgstr "Italique"

#: ../includes/option-types/typography-v2/view.php:50
msgid "Oblique"
msgstr "Oblique"

#: ../includes/option-types/typography-v2/view.php:59
#: ../includes/option-types/typography-v2/view.php:118
msgid "Style"
msgstr "Style"

#: ../includes/option-types/typography-v2/view.php:85
msgid "Weight"
msgstr "Épaisseur"

#: ../includes/option-types/typography-v2/view.php:101
msgid "Script"
msgstr "Script"

#: ../includes/option-types/typography-v2/view.php:128
msgid "Size"
msgstr "Taille"

#: ../includes/option-types/typography-v2/view.php:139
msgid "Line height"
msgstr "Hauteur de la ligne"

#: ../includes/option-types/typography-v2/view.php:150
msgid "Letter spacing"
msgstr "Espacement des lettres"

#: ../includes/option-types/typography-v2/view.php:173
msgid "Color"
msgstr "Couleur"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:156
msgid "Unknown Set"
msgstr "Ensemble inconnu"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:186
msgid "Web Application Icons"
msgstr "Icônes d'application Web"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:187
msgid "Hand Icons"
msgstr "Icônes de main"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:188
msgid "Transportation Icons"
msgstr "Icônes de transport"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:189
msgid "Gender Icons"
msgstr "Icônes de genre"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:190
msgid "File Type Icons"
msgstr "Icônes de type de fichier "

#: ../includes/option-types/icon/class-fw-option-type-icon.php:193
msgid "Payment Icons"
msgstr "Icônes de paiement"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:195
msgid "Currency Icons"
msgstr "Icônes des devises"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:196
msgid "Text Editor Icons"
msgstr "Icônes de l'éditeur de texte"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:197
msgid "Directional Icons"
msgstr "Icônes de direction"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:198
msgid "Video Player Icons"
msgstr "Icônes du lecteur vidéo"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:199
msgid "Brand Icons"
msgstr "Icônes de marque"

#: ../includes/option-types/icon/class-fw-option-type-icon.php:200
msgid "Medical Icons"
msgstr "Icônes médical"

#: ../includes/option-types/icon/view.php:39
msgid "All Categories"
msgstr "Toutes les categories"

#: ../includes/option-types/datetime-range/view.php:41
#: ../includes/option-types/gradient/view.php:46
msgid "to"
msgstr "à"

#: ../includes/option-types/multi-picker/class-fw-option-type-multi-picker.php:179
#, php-format
msgid "No 'picker' key set for multi-picker option: %s"
msgstr "Aucune touche 'dispositif' pour les options multi-dispositif : %s"

#: ../includes/option-types/background-image/view.php:37
msgid "Predefined images"
msgstr "Images prédéfinies"

#: ../includes/option-types/background-image/view.php:38
msgid "Custom image"
msgstr "Image personnalisée"

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:160
msgid "Add Images"
msgstr "Ajouter des images"

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:188
msgid "1 File"
msgstr "1 Fichier"

#: ../includes/option-types/multi-upload/class-fw-option-type-multi-upload.php:189
#, php-format
msgid "%u Files"
msgstr "%u Fichiers"
